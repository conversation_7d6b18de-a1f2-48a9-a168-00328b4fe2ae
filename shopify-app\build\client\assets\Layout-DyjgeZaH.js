import{r as d,R as t}from"./index-y-Yv8VR0.js";import{c as l,v as y,T as s,a as p}from"./Page-CD-2c-Tv.js";const v=d.createContext(!1);var o={Layout:"Polaris-Layout",Section:"Polaris-Layout__Section","Section-fullWidth":"Polaris-Layout__Section--fullWidth","Section-oneHalf":"Polaris-Layout__Section--oneHalf","Section-oneThird":"Polaris-Layout__Section--oneThird",AnnotatedSection:"Polaris-Layout__AnnotatedSection",AnnotationWrapper:"Polaris-Layout__AnnotationWrapper",AnnotationContent:"Polaris-Layout__AnnotationContent",Annotation:"Polaris-Layout__Annotation"},c={TextContainer:"Polaris-TextContainer",spacingTight:"Polaris-TextContainer--spacingTight",spacingLoose:"Polaris-TextContainer--spacingLoose"};function S({spacing:n,children:a}){const e=l(c.TextContainer,n&&c[y("spacing",n)]);return t.createElement("div",{className:e},a)}function L({children:n,title:a,description:e,id:i}){const r=typeof e=="string"?t.createElement(s,{as:"p",variant:"bodyMd"},e):e;return t.createElement("div",{className:o.AnnotatedSection},t.createElement("div",{className:o.AnnotationWrapper},t.createElement("div",{className:o.Annotation},t.createElement(S,{spacing:"tight"},t.createElement(s,{id:i,variant:"headingMd",as:"h2"},a),r&&t.createElement(p,{color:"text-secondary"},r))),t.createElement("div",{className:o.AnnotationContent},n)))}function m({children:n,variant:a}){const e=l(o.Section,o[`Section-${a}`]);return t.createElement("div",{className:e},n)}const u=function({sectioned:a,children:e}){const i=a?t.createElement(m,null,e):e;return t.createElement("div",{className:o.Layout},i)};u.AnnotatedSection=L;u.Section=m;export{v as B,u as L};
