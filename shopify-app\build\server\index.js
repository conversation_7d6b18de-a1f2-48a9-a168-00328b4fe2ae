var _a;
import { jsx, jsxs } from "react/jsx-runtime";
import { PassThrough } from "stream";
import { renderToPipeableStream } from "react-dom/server";
import { RemixServer, Meta, Links, Outlet, ScrollRestoration, Scripts, useLoaderData, useActionData, Form, Link, useRouteError } from "@remix-run/react";
import { createReadableStreamFromReadable, json, redirect } from "@remix-run/node";
import { isbot } from "isbot";
import "@shopify/shopify-app-remix/adapters/node";
import { shopifyApp, AppDistribution, ApiVersion, LoginErrorType, boundary } from "@shopify/shopify-app-remix/server";
import { PrismaSessionStorage } from "@shopify/shopify-app-session-storage-prisma";
import { PrismaClient } from "@prisma/client";
import axios from "axios";
import { useState, useEffect } from "react";
import { App<PERSON>rov<PERSON>, <PERSON>, Card, FormLayout, Text, TextField, Button, Layout, BlockStack, Link as Link$1, List, Box, Spinner, InlineStack, Divider, Badge, Banner, useIndexResourceState, IndexTable, Modal, Select } from "@shopify/polaris";
import { AppProvider as AppProvider$1 } from "@shopify/shopify-app-remix/react";
import { NavMenu, TitleBar } from "@shopify/app-bridge-react";
import jsPDF from "jspdf";
import QRCode from "qrcode";
if (process.env.NODE_ENV !== "production") {
  if (!global.prismaGlobal) {
    global.prismaGlobal = new PrismaClient();
  }
}
const prisma = global.prismaGlobal ?? new PrismaClient();
const requiredEnvVars = {
  SHOPIFY_API_KEY: process.env.SHOPIFY_API_KEY,
  SHOPIFY_API_SECRET: process.env.SHOPIFY_API_SECRET,
  SHOPIFY_APP_URL: process.env.SHOPIFY_APP_URL,
  SCOPES: process.env.SCOPES
};
const missingVars = Object.entries(requiredEnvVars).filter(([key, value]) => !value).map(([key]) => key);
if (missingVars.length > 0) {
  console.warn(`⚠️  Missing environment variables: ${missingVars.join(", ")}`);
  console.warn("Using default values for development. Please set proper values for production.");
}
const shopify = shopifyApp({
  apiKey: process.env.SHOPIFY_API_KEY || "development_api_key",
  apiSecretKey: process.env.SHOPIFY_API_SECRET || "development_api_secret",
  apiVersion: ApiVersion.January25,
  scopes: ((_a = process.env.SCOPES) == null ? void 0 : _a.split(",")) || ["read_orders", "write_products", "read_customers"],
  appUrl: process.env.SHOPIFY_APP_URL || "http://localhost:3000",
  authPathPrefix: "/auth",
  sessionStorage: new PrismaSessionStorage(prisma),
  distribution: AppDistribution.AppStore,
  future: {
    unstable_newEmbeddedAuthStrategy: true,
    removeRest: true
  },
  ...process.env.SHOP_CUSTOM_DOMAIN ? { customShopDomains: [process.env.SHOP_CUSTOM_DOMAIN] } : {}
});
ApiVersion.January25;
const addDocumentResponseHeaders = shopify.addDocumentResponseHeaders;
const authenticate = shopify.authenticate;
shopify.unauthenticated;
const login = shopify.login;
shopify.registerWebhooks;
shopify.sessionStorage;
const streamTimeout = 5e3;
async function handleRequest(request, responseStatusCode, responseHeaders, remixContext) {
  addDocumentResponseHeaders(request, responseHeaders);
  const userAgent = request.headers.get("user-agent");
  const callbackName = isbot(userAgent ?? "") ? "onAllReady" : "onShellReady";
  return new Promise((resolve, reject) => {
    const { pipe, abort } = renderToPipeableStream(
      /* @__PURE__ */ jsx(RemixServer, { context: remixContext, url: request.url }),
      {
        [callbackName]: () => {
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);
          responseHeaders.set("Content-Type", "text/html");
          resolve(
            new Response(stream, {
              headers: responseHeaders,
              status: responseStatusCode
            })
          );
          pipe(body);
        },
        onShellError(error) {
          reject(error);
        },
        onError(error) {
          responseStatusCode = 500;
          console.error(error);
        }
      }
    );
    setTimeout(abort, streamTimeout + 1e3);
  });
}
const entryServer = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: handleRequest,
  streamTimeout
}, Symbol.toStringTag, { value: "Module" }));
function App$2() {
  return /* @__PURE__ */ jsxs("html", { children: [
    /* @__PURE__ */ jsxs("head", { children: [
      /* @__PURE__ */ jsx("meta", { charSet: "utf-8" }),
      /* @__PURE__ */ jsx("meta", { name: "viewport", content: "width=device-width,initial-scale=1" }),
      /* @__PURE__ */ jsx("link", { rel: "preconnect", href: "https://cdn.shopify.com/" }),
      /* @__PURE__ */ jsx(
        "link",
        {
          rel: "stylesheet",
          href: "https://cdn.shopify.com/static/fonts/inter/v4/styles.css"
        }
      ),
      /* @__PURE__ */ jsx(Meta, {}),
      /* @__PURE__ */ jsx(Links, {})
    ] }),
    /* @__PURE__ */ jsxs("body", { children: [
      /* @__PURE__ */ jsx(Outlet, {}),
      /* @__PURE__ */ jsx(ScrollRestoration, {}),
      /* @__PURE__ */ jsx(Scripts, {})
    ] })
  ] });
}
const route0 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: App$2
}, Symbol.toStringTag, { value: "Module" }));
const tokenStore = /* @__PURE__ */ new Map();
const saveToken = async (shopUrl, token) => {
  try {
    if (prisma) {
      await prisma.apiToken.upsert({
        where: { shop: shopUrl },
        update: {
          token,
          updatedAt: /* @__PURE__ */ new Date(),
          isActive: true
        },
        create: {
          shop: shopUrl,
          token,
          isActive: true
        }
      });
      console.log(`✅ Token saved to database for ${shopUrl}`);
      return true;
    }
  } catch (error) {
    console.warn("⚠️ Database save failed, using in-memory fallback:", error.message);
  }
  try {
    tokenStore.set(shopUrl, token);
    console.log(`✅ Token saved to memory for ${shopUrl}`);
    return true;
  } catch (error) {
    console.error("❌ Error saving token:", error);
    return false;
  }
};
const getToken = async (shopUrl) => {
  try {
    if (prisma) {
      const record = await prisma.apiToken.findUnique({
        where: { shop: shopUrl, isActive: true }
      });
      if (record) {
        console.log(`🔍 Token retrieved from database for ${shopUrl}`);
        return record.token;
      }
    }
  } catch (error) {
    console.warn("⚠️ Database read failed, using in-memory fallback:", error.message);
  }
  try {
    const token = tokenStore.get(shopUrl);
    if (token) {
      console.log(`🔍 Token retrieved from memory for ${shopUrl}`);
    }
    return token || null;
  } catch (error) {
    console.error("❌ Error retrieving token:", error);
    return null;
  }
};
const getAllTokens = async () => {
  try {
    if (prisma) {
      const records = await prisma.apiToken.findMany({
        where: { isActive: true }
      });
      console.log("📋 All stored tokens from database");
      return records.reduce((acc, record) => {
        acc[record.shop] = record.token;
        return acc;
      }, {});
    }
  } catch (error) {
    console.warn("⚠️ Database read failed, using in-memory fallback:", error.message);
  }
  console.log("📋 All stored tokens from memory:");
  tokenStore.forEach((token, shop) => {
    console.log(`  ${shop}: ${token}`);
  });
  return Object.fromEntries(tokenStore);
};
const corsHeaders = {
  "Access-Control-Allow-Origin": "https://extensions.shopifycdn.com",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization"
};
const loader$9 = () => {
  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
};
const fetchProductById = async (shop, accessToken, productId) => {
  try {
    const res = await axios.get(
      `https://${shop}/admin/api/2024-01/products/${productId}.json`,
      {
        headers: {
          "X-Shopify-Access-Token": accessToken,
          "Content-Type": "application/json"
        }
      }
    );
    return res.data.product;
  } catch (err) {
    console.warn(`⚠️ Failed to fetch product ${productId}`);
    return null;
  }
};
const action$6 = async ({ request }) => {
  var _a2;
  if (request.method !== "POST") {
    return json(
      { error: `Method ${request.method} not allowed` },
      {
        status: 405,
        headers: corsHeaders
      }
    );
  }
  try {
    const { session } = await authenticate.admin(request);
    const { shop, accessToken } = session;
    console.log("🏪 Processing request for shop:", shop);
    const rushrrApiToken = getToken(shop);
    if (!rushrrApiToken) {
      console.log("❌ No Rushrr API token found for shop:", shop);
      return json(
        { error: "Rushrr API token not found. Please setup the token first." },
        {
          status: 400,
          headers: corsHeaders
        }
      );
    }
    console.log("🔑 Using Rushrr API token:", rushrrApiToken);
    const { orderIds } = await request.json();
    if (!orderIds || !Array.isArray(orderIds)) {
      return json(
        { error: "Invalid or missing orderIds" },
        {
          status: 400,
          headers: corsHeaders
        }
      );
    }
    console.log("🔁 Fetching orders:", orderIds);
    console.log("🔑 Using Rushrr API token:", rushrrApiToken);
    const orders = await Promise.all(
      orderIds.map(async (id) => {
        var _a3;
        try {
          const res = await axios.get(
            `https://${shop}/admin/api/2024-01/orders/${id}.json`,
            {
              headers: {
                "X-Shopify-Access-Token": accessToken,
                "Content-Type": "application/json"
              }
            }
          );
          return res.data.order;
        } catch (error) {
          console.error(`❌ Order ${id} failed:`, ((_a3 = error == null ? void 0 : error.response) == null ? void 0 : _a3.data) || error.message);
          return null;
        }
      })
    );
    const filteredOrders = orders.filter(Boolean);
    const productIdSet = /* @__PURE__ */ new Set();
    filteredOrders.forEach((order) => {
      order.line_items.forEach((item) => {
        if (item.product_id) {
          productIdSet.add(item.product_id);
        }
      });
    });
    const uniqueProductIds = Array.from(productIdSet);
    const productMap = {};
    const productResults = await Promise.all(
      uniqueProductIds.map(async (pid) => {
        const product = await fetchProductById(shop, accessToken, pid);
        if (product) productMap[pid] = product;
        return product;
      })
    );
    const enrichedOrders = filteredOrders.map((order) => {
      const enrichedLineItems = order.line_items.map((item) => {
        var _a3;
        const product = productMap[item.product_id];
        return {
          ...item,
          product_details: product ? {
            title: product.title,
            vendor: product.vendor,
            image: ((_a3 = product.image) == null ? void 0 : _a3.src) || null,
            handle: product.handle,
            tags: product.tags
          } : null
        };
      });
      return {
        ...order,
        line_items: enrichedLineItems
      };
    });
    return json(
      {
        shopifyStoreUrl: shop,
        // e.g., rushrr.myshopify.com
        orders: enrichedOrders,
        token: rushrrApiToken
        // 👈 Return the Rushrr API token instead of Shopify access token
      },
      { headers: corsHeaders }
    );
  } catch (err) {
    console.error("❌ Error:", ((_a2 = err.response) == null ? void 0 : _a2.data) || err.message);
    return json(
      { error: "Bulk fetch failed" },
      {
        status: 500,
        headers: corsHeaders
      }
    );
  }
};
const route1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$6,
  loader: loader$9
}, Symbol.toStringTag, { value: "Module" }));
const action$5 = async ({ request }) => {
  const { payload, session, topic, shop } = await authenticate.webhook(request);
  console.log(`Received ${topic} webhook for ${shop}`);
  const current = payload.current;
  if (session) {
    await prisma.session.update({
      where: {
        id: session.id
      },
      data: {
        scope: current.toString()
      }
    });
  }
  return new Response();
};
const route2 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$5
}, Symbol.toStringTag, { value: "Module" }));
const action$4 = async ({ request }) => {
  const { shop, session, topic } = await authenticate.webhook(request);
  console.log(`Received ${topic} webhook for ${shop}`);
  if (session) {
    await prisma.session.deleteMany({ where: { shop } });
  }
  return new Response();
};
const route3 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$4
}, Symbol.toStringTag, { value: "Module" }));
const loader$8 = () => {
  return new Response(null, {
    status: 204,
    headers: {
      "Access-Control-Allow-Origin": "https://extensions.shopifycdn.com",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization"
    }
  });
};
const action$3 = async ({ request }) => {
  var _a2, _b, _c;
  if (request.method !== "POST") {
    return json(
      { error: `Method ${request.method} not allowed` },
      {
        status: 405,
        headers: {
          "Access-Control-Allow-Origin": "https://extensions.shopifycdn.com",
          "Access-Control-Allow-Headers": "Content-Type, Authorization"
        }
      }
    );
  }
  try {
    const { session } = await authenticate.admin(request);
    const { shop, accessToken } = session;
    const { orderId } = await request.json();
    if (!orderId) {
      return json(
        { error: "Missing orderId in request body" },
        {
          status: 400,
          headers: {
            "Access-Control-Allow-Origin": "https://extensions.shopifycdn.com",
            "Access-Control-Allow-Headers": "Content-Type, Authorization"
          }
        }
      );
    }
    const gqlQuery = {
      query: `
        query getOrder($id: ID!) {
          order(id: $id) {
            id
            name
            createdAt
            customer {
              firstName
              lastName
              email
            }
            lineItems(first: 100) {
              edges {
                node {
                  title
                  quantity
                  sku
                  product {
                    id
                    title
                    vendor
                  }
                }
              }
            }
          }
        }
      `,
      variables: {
        id: `gid://shopify/Order/${orderId}`
      }
    };
    const response = await axios.post(
      `https://${shop}/admin/api/2024-07/graphql.json`,
      JSON.stringify(gqlQuery),
      {
        headers: {
          "X-Shopify-Access-Token": accessToken,
          "Content-Type": "application/json"
        }
      }
    );
    const order = (_b = (_a2 = response.data) == null ? void 0 : _a2.data) == null ? void 0 : _b.order;
    if (!order) {
      console.error("❌ Order not found in Shopify response");
      return json(
        { error: "Order not found" },
        {
          status: 404,
          headers: {
            "Access-Control-Allow-Origin": "https://extensions.shopifycdn.com",
            "Access-Control-Allow-Headers": "Content-Type, Authorization"
          }
        }
      );
    }
    console.log("✅ Order Fetched:", JSON.stringify(order, null, 2));
    const customer = order.customer;
    const customerName = customer ? `${customer.firstName || ""} ${customer.lastName || ""}`.trim() : "Customer info not found";
    return json(
      {
        order,
        orderId: order.id,
        customerName
      },
      {
        headers: {
          "Access-Control-Allow-Origin": "https://extensions.shopifycdn.com",
          "Access-Control-Allow-Headers": "Content-Type, Authorization"
        }
      }
    );
  } catch (err) {
    console.error("❌ Failed to fetch order:", ((_c = err.response) == null ? void 0 : _c.data) || err.message);
    return json(
      { error: "Failed to fetch order" },
      {
        status: 500,
        headers: {
          "Access-Control-Allow-Origin": "https://extensions.shopifycdn.com",
          "Access-Control-Allow-Headers": "Content-Type, Authorization"
        }
      }
    );
  }
};
const route4 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$3,
  loader: loader$8
}, Symbol.toStringTag, { value: "Module" }));
const action$2 = async ({ request }) => {
  if (request.method !== "POST") {
    return json(
      { error: `Method ${request.method} not allowed` },
      { status: 405 }
    );
  }
  try {
    console.log("🔄 Starting token save process...");
    const { session } = await authenticate.admin(request);
    console.log("✅ Session authenticated for shop:", session.shop);
    const requestBody = await request.json();
    console.log("📝 Request body:", requestBody);
    const { token } = requestBody;
    if (!token) {
      console.log("❌ No token provided in request");
      return json(
        { error: "Token is required" },
        { status: 400 }
      );
    }
    console.log("🔑 Attempting to save token for shop:", session.shop);
    console.log("🔑 Token to save:", token);
    const success = saveToken(session.shop, token);
    if (success) {
      console.log("✅ Token saved successfully for shop:", session.shop);
      getAllTokens();
      return json({
        success: true,
        message: "Token saved successfully",
        shop: session.shop
      });
    } else {
      console.log("❌ Failed to save token");
      throw new Error("saveToken function returned false");
    }
  } catch (err) {
    console.error("❌ Error in save-token action:", err);
    console.error("❌ Error stack:", err.stack);
    return json(
      {
        error: "Failed to save token",
        details: err.message,
        stack: process.env.NODE_ENV === "development" ? err.stack : void 0
      },
      { status: 500 }
    );
  }
};
const route5 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$2
}, Symbol.toStringTag, { value: "Module" }));
const route6 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null
}, Symbol.toStringTag, { value: "Module" }));
const config = {
  // External API Configuration
  rushrr: {
    baseUrl: process.env.RUSHRR_API_BASE_URL || "https://backend.rushr-admin.com/api"
  },
  // Development Configuration
  dev: {
    logLevel: process.env.LOG_LEVEL || "info",
    corsOrigin: process.env.CORS_ORIGIN || "http://localhost:3000"
  },
  // Security Configuration
  security: {
    sessionSecret: process.env.SESSION_SECRET || "development_session_secret"
  },
  // Database Configuration
  database: {
    url: process.env.DATABASE_URL || "file:dev.sqlite"
  }
};
const getRushrApiUrl = (path) => {
  return `${config.rushrr.baseUrl}${path}`;
};
async function loader$7({ request }) {
  try {
    const { session } = await authenticate.admin(request);
    const shop = session.shop;
    const token = await getToken(shop);
    if (!token) {
      console.warn(`❌ No token found for shop: ${shop}`);
      return json({
        success: false,
        error: "Token not found for this store. Please configure your API token in settings."
      }, { status: 401 });
    }
    const apiUrl = getRushrApiUrl("/orders");
    const res = await fetch(apiUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json"
      }
    });
    if (!res.ok) {
      console.error(`❌ API request failed: ${res.status} ${res.statusText}`);
      return json({
        success: false,
        error: `Failed to fetch orders: ${res.statusText}`
      }, { status: res.status });
    }
    const data = await res.json();
    return json({
      success: true,
      orders: data.orders || [],
      message: `Fetched ${(data.orders || []).length} orders successfully`
    });
  } catch (err) {
    console.error("❌ Error fetching orders:", err);
    return json({
      success: false,
      error: "Failed to fetch orders. Please check your connection and try again."
    }, { status: 500 });
  }
}
const route7 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  loader: loader$7
}, Symbol.toStringTag, { value: "Module" }));
const Polaris = /* @__PURE__ */ JSON.parse('{"ActionMenu":{"Actions":{"moreActions":"More actions"},"RollupActions":{"rollupButton":"View actions"}},"ActionList":{"SearchField":{"clearButtonLabel":"Clear","search":"Search","placeholder":"Search actions"}},"Avatar":{"label":"Avatar","labelWithInitials":"Avatar with initials {initials}"},"Autocomplete":{"spinnerAccessibilityLabel":"Loading","ellipsis":"{content}…"},"Badge":{"PROGRESS_LABELS":{"incomplete":"Incomplete","partiallyComplete":"Partially complete","complete":"Complete"},"TONE_LABELS":{"info":"Info","success":"Success","warning":"Warning","critical":"Critical","attention":"Attention","new":"New","readOnly":"Read-only","enabled":"Enabled"},"progressAndTone":"{toneLabel} {progressLabel}"},"Banner":{"dismissButton":"Dismiss notification"},"Button":{"spinnerAccessibilityLabel":"Loading"},"Common":{"checkbox":"checkbox","undo":"Undo","cancel":"Cancel","clear":"Clear","close":"Close","submit":"Submit","more":"More"},"ContextualSaveBar":{"save":"Save","discard":"Discard"},"DataTable":{"sortAccessibilityLabel":"sort {direction} by","navAccessibilityLabel":"Scroll table {direction} one column","totalsRowHeading":"Totals","totalRowHeading":"Total"},"DatePicker":{"previousMonth":"Show previous month, {previousMonthName} {showPreviousYear}","nextMonth":"Show next month, {nextMonth} {nextYear}","today":"Today ","start":"Start of range","end":"End of range","months":{"january":"January","february":"February","march":"March","april":"April","may":"May","june":"June","july":"July","august":"August","september":"September","october":"October","november":"November","december":"December"},"days":{"monday":"Monday","tuesday":"Tuesday","wednesday":"Wednesday","thursday":"Thursday","friday":"Friday","saturday":"Saturday","sunday":"Sunday"},"daysAbbreviated":{"monday":"Mo","tuesday":"Tu","wednesday":"We","thursday":"Th","friday":"Fr","saturday":"Sa","sunday":"Su"}},"DiscardConfirmationModal":{"title":"Discard all unsaved changes","message":"If you discard changes, you’ll delete any edits you made since you last saved.","primaryAction":"Discard changes","secondaryAction":"Continue editing"},"DropZone":{"single":{"overlayTextFile":"Drop file to upload","overlayTextImage":"Drop image to upload","overlayTextVideo":"Drop video to upload","actionTitleFile":"Add file","actionTitleImage":"Add image","actionTitleVideo":"Add video","actionHintFile":"or drop file to upload","actionHintImage":"or drop image to upload","actionHintVideo":"or drop video to upload","labelFile":"Upload file","labelImage":"Upload image","labelVideo":"Upload video"},"allowMultiple":{"overlayTextFile":"Drop files to upload","overlayTextImage":"Drop images to upload","overlayTextVideo":"Drop videos to upload","actionTitleFile":"Add files","actionTitleImage":"Add images","actionTitleVideo":"Add videos","actionHintFile":"or drop files to upload","actionHintImage":"or drop images to upload","actionHintVideo":"or drop videos to upload","labelFile":"Upload files","labelImage":"Upload images","labelVideo":"Upload videos"},"errorOverlayTextFile":"File type is not valid","errorOverlayTextImage":"Image type is not valid","errorOverlayTextVideo":"Video type is not valid"},"EmptySearchResult":{"altText":"Empty search results"},"Frame":{"skipToContent":"Skip to content","navigationLabel":"Navigation","Navigation":{"closeMobileNavigationLabel":"Close navigation"}},"FullscreenBar":{"back":"Back","accessibilityLabel":"Exit fullscreen mode"},"Filters":{"moreFilters":"More filters","moreFiltersWithCount":"More filters ({count})","filter":"Filter {resourceName}","noFiltersApplied":"No filters applied","cancel":"Cancel","done":"Done","clearAllFilters":"Clear all filters","clear":"Clear","clearLabel":"Clear {filterName}","addFilter":"Add filter","clearFilters":"Clear all","searchInView":"in:{viewName}"},"FilterPill":{"clear":"Clear","unsavedChanges":"Unsaved changes - {label}"},"IndexFilters":{"searchFilterTooltip":"Search and filter","searchFilterTooltipWithShortcut":"Search and filter (F)","searchFilterAccessibilityLabel":"Search and filter results","sort":"Sort your results","addView":"Add a new view","newView":"Custom search","SortButton":{"ariaLabel":"Sort the results","tooltip":"Sort","title":"Sort by","sorting":{"asc":"Ascending","desc":"Descending","az":"A-Z","za":"Z-A"}},"EditColumnsButton":{"tooltip":"Edit columns","accessibilityLabel":"Customize table column order and visibility"},"UpdateButtons":{"cancel":"Cancel","update":"Update","save":"Save","saveAs":"Save as","modal":{"title":"Save view as","label":"Name","sameName":"A view with this name already exists. Please choose a different name.","save":"Save","cancel":"Cancel"}}},"IndexProvider":{"defaultItemSingular":"Item","defaultItemPlural":"Items","allItemsSelected":"All {itemsLength}+ {resourceNamePlural} are selected","selected":"{selectedItemsCount} selected","a11yCheckboxDeselectAllSingle":"Deselect {resourceNameSingular}","a11yCheckboxSelectAllSingle":"Select {resourceNameSingular}","a11yCheckboxDeselectAllMultiple":"Deselect all {itemsLength} {resourceNamePlural}","a11yCheckboxSelectAllMultiple":"Select all {itemsLength} {resourceNamePlural}"},"IndexTable":{"emptySearchTitle":"No {resourceNamePlural} found","emptySearchDescription":"Try changing the filters or search term","onboardingBadgeText":"New","resourceLoadingAccessibilityLabel":"Loading {resourceNamePlural}…","selectAllLabel":"Select all {resourceNamePlural}","selected":"{selectedItemsCount} selected","undo":"Undo","selectAllItems":"Select all {itemsLength}+ {resourceNamePlural}","selectItem":"Select {resourceName}","selectButtonText":"Select","sortAccessibilityLabel":"sort {direction} by"},"Loading":{"label":"Page loading bar"},"Modal":{"iFrameTitle":"body markup","modalWarning":"These required properties are missing from Modal: {missingProps}"},"Page":{"Header":{"rollupActionsLabel":"View actions for {title}","pageReadyAccessibilityLabel":"{title}. This page is ready"}},"Pagination":{"previous":"Previous","next":"Next","pagination":"Pagination"},"ProgressBar":{"negativeWarningMessage":"Values passed to the progress prop shouldn’t be negative. Resetting {progress} to 0.","exceedWarningMessage":"Values passed to the progress prop shouldn’t exceed 100. Setting {progress} to 100."},"ResourceList":{"sortingLabel":"Sort by","defaultItemSingular":"item","defaultItemPlural":"items","showing":"Showing {itemsCount} {resource}","showingTotalCount":"Showing {itemsCount} of {totalItemsCount} {resource}","loading":"Loading {resource}","selected":"{selectedItemsCount} selected","allItemsSelected":"All {itemsLength}+ {resourceNamePlural} in your store are selected","allFilteredItemsSelected":"All {itemsLength}+ {resourceNamePlural} in this filter are selected","selectAllItems":"Select all {itemsLength}+ {resourceNamePlural} in your store","selectAllFilteredItems":"Select all {itemsLength}+ {resourceNamePlural} in this filter","emptySearchResultTitle":"No {resourceNamePlural} found","emptySearchResultDescription":"Try changing the filters or search term","selectButtonText":"Select","a11yCheckboxDeselectAllSingle":"Deselect {resourceNameSingular}","a11yCheckboxSelectAllSingle":"Select {resourceNameSingular}","a11yCheckboxDeselectAllMultiple":"Deselect all {itemsLength} {resourceNamePlural}","a11yCheckboxSelectAllMultiple":"Select all {itemsLength} {resourceNamePlural}","Item":{"actionsDropdownLabel":"Actions for {accessibilityLabel}","actionsDropdown":"Actions dropdown","viewItem":"View details for {itemName}"},"BulkActions":{"actionsActivatorLabel":"Actions","moreActionsActivatorLabel":"More actions"}},"SkeletonPage":{"loadingLabel":"Page loading"},"Tabs":{"newViewAccessibilityLabel":"Create new view","newViewTooltip":"Create view","toggleTabsLabel":"More views","Tab":{"rename":"Rename view","duplicate":"Duplicate view","edit":"Edit view","editColumns":"Edit columns","delete":"Delete view","copy":"Copy of {name}","deleteModal":{"title":"Delete view?","description":"This can’t be undone. {viewName} view will no longer be available in your admin.","cancel":"Cancel","delete":"Delete view"}},"RenameModal":{"title":"Rename view","label":"Name","cancel":"Cancel","create":"Save","errors":{"sameName":"A view with this name already exists. Please choose a different name."}},"DuplicateModal":{"title":"Duplicate view","label":"Name","cancel":"Cancel","create":"Create view","errors":{"sameName":"A view with this name already exists. Please choose a different name."}},"CreateViewModal":{"title":"Create new view","label":"Name","cancel":"Cancel","create":"Create view","errors":{"sameName":"A view with this name already exists. Please choose a different name."}}},"Tag":{"ariaLabel":"Remove {children}"},"TextField":{"characterCount":"{count} characters","characterCountWithMaxLength":"{count} of {limit} characters used"},"TooltipOverlay":{"accessibilityLabel":"Tooltip: {label}"},"TopBar":{"toggleMenuLabel":"Toggle menu","SearchField":{"clearButtonLabel":"Clear","search":"Search"}},"MediaCard":{"dismissButton":"Dismiss","popoverButton":"Actions"},"VideoThumbnail":{"playButtonA11yLabel":{"default":"Play video","defaultWithDuration":"Play video of length {duration}","duration":{"hours":{"other":{"only":"{hourCount} hours","andMinutes":"{hourCount} hours and {minuteCount} minutes","andMinute":"{hourCount} hours and {minuteCount} minute","minutesAndSeconds":"{hourCount} hours, {minuteCount} minutes, and {secondCount} seconds","minutesAndSecond":"{hourCount} hours, {minuteCount} minutes, and {secondCount} second","minuteAndSeconds":"{hourCount} hours, {minuteCount} minute, and {secondCount} seconds","minuteAndSecond":"{hourCount} hours, {minuteCount} minute, and {secondCount} second","andSeconds":"{hourCount} hours and {secondCount} seconds","andSecond":"{hourCount} hours and {secondCount} second"},"one":{"only":"{hourCount} hour","andMinutes":"{hourCount} hour and {minuteCount} minutes","andMinute":"{hourCount} hour and {minuteCount} minute","minutesAndSeconds":"{hourCount} hour, {minuteCount} minutes, and {secondCount} seconds","minutesAndSecond":"{hourCount} hour, {minuteCount} minutes, and {secondCount} second","minuteAndSeconds":"{hourCount} hour, {minuteCount} minute, and {secondCount} seconds","minuteAndSecond":"{hourCount} hour, {minuteCount} minute, and {secondCount} second","andSeconds":"{hourCount} hour and {secondCount} seconds","andSecond":"{hourCount} hour and {secondCount} second"}},"minutes":{"other":{"only":"{minuteCount} minutes","andSeconds":"{minuteCount} minutes and {secondCount} seconds","andSecond":"{minuteCount} minutes and {secondCount} second"},"one":{"only":"{minuteCount} minute","andSeconds":"{minuteCount} minute and {secondCount} seconds","andSecond":"{minuteCount} minute and {secondCount} second"}},"seconds":{"other":"{secondCount} seconds","one":"{secondCount} second"}}}}}');
const polarisTranslations = {
  Polaris
};
const polarisStyles = "/assets/styles-BeiPL2RV.css";
function loginErrorMessage(loginErrors) {
  if ((loginErrors == null ? void 0 : loginErrors.shop) === LoginErrorType.MissingShop) {
    return { shop: "Please enter your shop domain to log in" };
  } else if ((loginErrors == null ? void 0 : loginErrors.shop) === LoginErrorType.InvalidShop) {
    return { shop: "Please enter a valid shop domain to log in" };
  }
  return {};
}
const links$1 = () => [{ rel: "stylesheet", href: polarisStyles }];
const loader$6 = async ({ request }) => {
  const errors = loginErrorMessage(await login(request));
  return { errors, polarisTranslations };
};
const action$1 = async ({ request }) => {
  const errors = loginErrorMessage(await login(request));
  return {
    errors
  };
};
function Auth() {
  const loaderData = useLoaderData();
  const actionData = useActionData();
  const [shop, setShop] = useState("");
  const { errors } = actionData || loaderData;
  return /* @__PURE__ */ jsx(AppProvider, { i18n: loaderData.polarisTranslations, children: /* @__PURE__ */ jsx(Page, { children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsx(Form, { method: "post", children: /* @__PURE__ */ jsxs(FormLayout, { children: [
    /* @__PURE__ */ jsx(Text, { variant: "headingMd", as: "h2", children: "Log in" }),
    /* @__PURE__ */ jsx(
      TextField,
      {
        type: "text",
        name: "shop",
        label: "Shop domain",
        helpText: "example.myshopify.com",
        value: shop,
        onChange: setShop,
        autoComplete: "on",
        error: errors.shop
      }
    ),
    /* @__PURE__ */ jsx(Button, { submit: true, children: "Log in" })
  ] }) }) }) }) });
}
const route8 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$1,
  default: Auth,
  links: links$1,
  loader: loader$6
}, Symbol.toStringTag, { value: "Module" }));
const loader$5 = async ({ request }) => {
  await authenticate.admin(request);
  return null;
};
const route9 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  loader: loader$5
}, Symbol.toStringTag, { value: "Module" }));
const index = "_index_1hqgz_1";
const heading = "_heading_1hqgz_21";
const text = "_text_1hqgz_23";
const content = "_content_1hqgz_43";
const form = "_form_1hqgz_53";
const label = "_label_1hqgz_69";
const input = "_input_1hqgz_85";
const button = "_button_1hqgz_93";
const list = "_list_1hqgz_101";
const styles = {
  index,
  heading,
  text,
  content,
  form,
  label,
  input,
  button,
  list
};
const loader$4 = async ({ request }) => {
  const url = new URL(request.url);
  if (url.searchParams.get("shop")) {
    throw redirect(`/app?${url.searchParams.toString()}`);
  }
  return { showForm: Boolean(login) };
};
function App$1() {
  const { showForm } = useLoaderData();
  return /* @__PURE__ */ jsx("div", { className: styles.index, children: /* @__PURE__ */ jsxs("div", { className: styles.content, children: [
    /* @__PURE__ */ jsx("h1", { className: styles.heading, children: "A short heading about [your app]" }),
    /* @__PURE__ */ jsx("p", { className: styles.text, children: "A tagline about [your app] that describes your value proposition." }),
    showForm && /* @__PURE__ */ jsxs(Form, { className: styles.form, method: "post", action: "/auth/login", children: [
      /* @__PURE__ */ jsxs("label", { className: styles.label, children: [
        /* @__PURE__ */ jsx("span", { children: "Shop domain" }),
        /* @__PURE__ */ jsx("input", { className: styles.input, type: "text", name: "shop" }),
        /* @__PURE__ */ jsx("span", { children: "e.g: my-shop-domain.myshopify.com" })
      ] }),
      /* @__PURE__ */ jsx("button", { className: styles.button, type: "submit", children: "Log in" })
    ] }),
    /* @__PURE__ */ jsxs("ul", { className: styles.list, children: [
      /* @__PURE__ */ jsxs("li", { children: [
        /* @__PURE__ */ jsx("strong", { children: "Product feature" }),
        ". Some detail about your feature and its benefit to your customer."
      ] }),
      /* @__PURE__ */ jsxs("li", { children: [
        /* @__PURE__ */ jsx("strong", { children: "Product feature" }),
        ". Some detail about your feature and its benefit to your customer."
      ] }),
      /* @__PURE__ */ jsxs("li", { children: [
        /* @__PURE__ */ jsx("strong", { children: "Product feature" }),
        ". Some detail about your feature and its benefit to your customer."
      ] })
    ] })
  ] }) });
}
const route10 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: App$1,
  loader: loader$4
}, Symbol.toStringTag, { value: "Module" }));
const links = () => [{ rel: "stylesheet", href: polarisStyles }];
const loader$3 = async ({ request }) => {
  await authenticate.admin(request);
  return { apiKey: process.env.SHOPIFY_API_KEY || "" };
};
function App() {
  const { apiKey } = useLoaderData();
  return /* @__PURE__ */ jsxs(AppProvider$1, { isEmbeddedApp: true, apiKey, children: [
    /* @__PURE__ */ jsxs(NavMenu, { children: [
      /* @__PURE__ */ jsx(Link, { to: "/app", rel: "home", children: "Home" }),
      /* @__PURE__ */ jsx(Link, { to: "/app/orders", children: "Orders" }),
      /* @__PURE__ */ jsx(Link, { to: "/app/analytics", children: "Analytics" }),
      /* @__PURE__ */ jsx(Link, { to: "/app/tracking", children: "Tracking" }),
      /* @__PURE__ */ jsx(Link, { to: "/app/settings", children: "Settings" }),
      /* @__PURE__ */ jsx(Link, { to: "/app/riders", children: "Riders" })
    ] }),
    /* @__PURE__ */ jsx(Outlet, {})
  ] });
}
function ErrorBoundary() {
  return boundary.error(useRouteError());
}
const headers = (headersArgs) => {
  return boundary.headers(headersArgs);
};
const route11 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  ErrorBoundary,
  default: App,
  headers,
  links,
  loader: loader$3
}, Symbol.toStringTag, { value: "Module" }));
function AdditionalPage() {
  return /* @__PURE__ */ jsxs(Page, { children: [
    /* @__PURE__ */ jsx(TitleBar, { title: "Additional page" }),
    /* @__PURE__ */ jsxs(Layout, { children: [
      /* @__PURE__ */ jsx(Layout.Section, { children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(BlockStack, { gap: "300", children: [
        /* @__PURE__ */ jsxs(Text, { as: "p", variant: "bodyMd", children: [
          "The app template comes with an additional page which demonstrates how to create multiple pages within app navigation using",
          " ",
          /* @__PURE__ */ jsx(
            Link$1,
            {
              url: "https://shopify.dev/docs/apps/tools/app-bridge",
              target: "_blank",
              removeUnderline: true,
              children: "App Bridge"
            }
          ),
          "."
        ] }),
        /* @__PURE__ */ jsxs(Text, { as: "p", variant: "bodyMd", children: [
          "To create your own page and have it show up in the app navigation, add a page inside ",
          /* @__PURE__ */ jsx(Code, { children: "app/routes" }),
          ", and a link to it in the ",
          /* @__PURE__ */ jsx(Code, { children: "<NavMenu>" }),
          " component found in ",
          /* @__PURE__ */ jsx(Code, { children: "app/routes/app.jsx" }),
          "."
        ] })
      ] }) }) }),
      /* @__PURE__ */ jsx(Layout.Section, { variant: "oneThird", children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(BlockStack, { gap: "200", children: [
        /* @__PURE__ */ jsx(Text, { as: "h2", variant: "headingMd", children: "Resources" }),
        /* @__PURE__ */ jsx(List, { children: /* @__PURE__ */ jsx(List.Item, { children: /* @__PURE__ */ jsx(
          Link$1,
          {
            url: "https://shopify.dev/docs/apps/design-guidelines/navigation#app-nav",
            target: "_blank",
            removeUnderline: true,
            children: "App nav best practices"
          }
        ) }) })
      ] }) }) })
    ] })
  ] });
}
function Code({ children }) {
  return /* @__PURE__ */ jsx(
    Box,
    {
      as: "span",
      padding: "025",
      paddingInlineStart: "100",
      paddingInlineEnd: "100",
      background: "bg-surface-active",
      borderWidth: "025",
      borderColor: "border",
      borderRadius: "100",
      children: /* @__PURE__ */ jsx("code", { children })
    }
  );
}
const route12 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: AdditionalPage
}, Symbol.toStringTag, { value: "Module" }));
function Analytics() {
  return /* @__PURE__ */ jsxs("main", { style: { padding: 20 }, children: [
    /* @__PURE__ */ jsx("h2", { children: "Analytics" }),
    /* @__PURE__ */ jsx("p", { children: "Here you will see your order analytics, revenue, and trends." })
  ] });
}
const route13 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Analytics
}, Symbol.toStringTag, { value: "Module" }));
const loader$2 = async () => {
  const apiToken = "example-api-token-abcdef123456";
  return { apiToken };
};
function Settings() {
  const { apiToken } = useLoaderData();
  return /* @__PURE__ */ jsxs("main", { style: { padding: 20 }, children: [
    /* @__PURE__ */ jsx("h2", { children: "Account Settings" }),
    /* @__PURE__ */ jsx("p", { children: /* @__PURE__ */ jsx("strong", { children: "API Token:" }) }),
    /* @__PURE__ */ jsx("code", { style: { wordBreak: "break-all", backgroundColor: "#f5f5f5", padding: 8, borderRadius: 4 }, children: apiToken }),
    /* @__PURE__ */ jsx("p", { children: "You can copy this API token and paste it into your Shopify app to connect your store." })
  ] });
}
const route14 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Settings,
  loader: loader$2
}, Symbol.toStringTag, { value: "Module" }));
const loader$1 = async () => {
  const trackingData = [
    { id: 1, orderNumber: "1001", trackingId: "TRACK12345", status: "In Transit" },
    { id: 2, orderNumber: "1002", trackingId: "TRACK54321", status: "Delivered" }
  ];
  return { trackingData };
};
function Tracking() {
  const { trackingData } = useLoaderData();
  return /* @__PURE__ */ jsxs("main", { style: { padding: 20 }, children: [
    /* @__PURE__ */ jsx("h2", { children: "Order Tracking" }),
    /* @__PURE__ */ jsx("ul", { children: trackingData.map(({ id, orderNumber, trackingId, status }) => /* @__PURE__ */ jsxs("li", { style: { marginBottom: 10 }, children: [
      /* @__PURE__ */ jsxs("strong", { children: [
        "Order ",
        orderNumber
      ] }),
      ": Tracking ID ",
      trackingId,
      " - Status: ",
      status
    ] }, id)) })
  ] });
}
const route15 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Tracking,
  loader: loader$1
}, Symbol.toStringTag, { value: "Module" }));
function Orders() {
  return /* @__PURE__ */ jsxs("main", { style: { padding: 20 }, children: [
    /* @__PURE__ */ jsx("h2", { children: "Orders Page" }),
    /* @__PURE__ */ jsx("p", { children: "List your orders here." })
  ] });
}
const route16 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Orders
}, Symbol.toStringTag, { value: "Module" }));
function Riders() {
  return /* @__PURE__ */ jsxs("main", { style: { padding: 20 }, children: [
    /* @__PURE__ */ jsx("h2", { children: "Riders" }),
    /* @__PURE__ */ jsx("p", { children: "Manage rider accounts and track delivery progress here." })
  ] });
}
const route17 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Riders
}, Symbol.toStringTag, { value: "Module" }));
const cities = [
  "Karachi",
  "Lahore",
  "Islamabad",
  "Rawalpindi",
  "Faisalabad",
  "Multan",
  "Peshawar",
  "Quetta",
  "Sialkot",
  "Hyderabad",
  "Gujranwala",
  "Bahawalpur",
  "Sargodha",
  "Sukkur",
  "Abbottabad",
  "Mardan",
  "Swat",
  "Dera Ghazi Khan",
  "Sheikhupura",
  "Jhelum"
];
const loader = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  return {
    shopifyStoreName: session.shop.split(".")[0],
    shopifyStoreUrl: `https://${session.shop}`
  };
};
const generateAirwayBill = async (order) => {
  const pdf = new jsPDF();
  const shopifyData = order.shopifyOrderData || {};
  const customer = shopifyData.customer || {};
  const billingAddress = shopifyData.billing_address || {};
  const shippingAddress = shopifyData.shipping_address || {};
  const qrData = JSON.stringify({
    orderNumber: shopifyData.order_number,
    customer: `${customer.first_name} ${customer.last_name}`,
    city: billingAddress.city,
    amount: shopifyData.total_price,
    currency: shopifyData.currency,
    phone: shippingAddress.phone,
    address: shippingAddress.address1
  });
  try {
    const qrCodeDataURL = await QRCode.toDataURL(qrData, { width: 100, margin: 1 });
    const pageWidth = pdf.internal.pageSize.width;
    const pageHeight = pdf.internal.pageSize.height;
    pdf.setFillColor(41, 128, 185);
    pdf.rect(0, 0, pageWidth, 40, "F");
    pdf.setTextColor(255, 255, 255);
    pdf.setFontSize(24);
    pdf.setFont("helvetica", "bold");
    pdf.text("RUSHRR COURIER", pageWidth / 2, 25, { align: "center" });
    pdf.setFontSize(12);
    pdf.setFont("helvetica", "normal");
    pdf.text("Express Delivery Service", pageWidth / 2, 35, { align: "center" });
    pdf.setTextColor(0, 0, 0);
    pdf.setFontSize(20);
    pdf.setFont("helvetica", "bold");
    pdf.text("AIRWAY BILL", pageWidth / 2, 55, { align: "center" });
    pdf.setDrawColor(52, 73, 94);
    pdf.setLineWidth(1);
    pdf.rect(15, 65, pageWidth - 30, 25);
    pdf.setFillColor(236, 240, 241);
    pdf.rect(15, 65, pageWidth - 30, 25, "F");
    pdf.setTextColor(52, 73, 94);
    pdf.setFontSize(14);
    pdf.setFont("helvetica", "bold");
    pdf.text(`Order #: ${shopifyData.order_number || "N/A"}`, 20, 75);
    pdf.text(`Date: ${(/* @__PURE__ */ new Date()).toLocaleDateString()}`, 20, 85);
    pdf.setFontSize(16);
    pdf.setFont("helvetica", "bold");
    pdf.setTextColor(52, 73, 94);
    pdf.text("CUSTOMER DETAILS", 20, 105);
    pdf.setDrawColor(52, 73, 94);
    pdf.line(20, 108, pageWidth - 20, 108);
    pdf.setFontSize(12);
    pdf.setFont("helvetica", "normal");
    pdf.setTextColor(0, 0, 0);
    const customerDetails = [
      `Name: ${customer.first_name || ""} ${customer.last_name || ""}`,
      `Email: ${customer.email || "N/A"}`,
      `Phone: ${shippingAddress.phone || "N/A"}`,
      `City: ${billingAddress.city || "N/A"}`
    ];
    customerDetails.forEach((detail, index2) => {
      pdf.text(detail, 20, 120 + index2 * 8);
    });
    pdf.setFontSize(16);
    pdf.setFont("helvetica", "bold");
    pdf.setTextColor(52, 73, 94);
    pdf.text("SHIPPING ADDRESS", 20, 160);
    pdf.line(20, 163, pageWidth - 20, 163);
    pdf.setFontSize(12);
    pdf.setFont("helvetica", "normal");
    pdf.setTextColor(0, 0, 0);
    pdf.text(shippingAddress.address1 || "N/A", 20, 175);
    pdf.text(`${shippingAddress.city || ""}, ${shippingAddress.country || ""}`, 20, 185);
    pdf.text(`Postal Code: ${shippingAddress.zip || "N/A"}`, 20, 195);
    pdf.setFontSize(16);
    pdf.setFont("helvetica", "bold");
    pdf.setTextColor(52, 73, 94);
    pdf.text("ORDER SUMMARY", 20, 215);
    pdf.line(20, 218, pageWidth - 20, 218);
    pdf.setFillColor(241, 196, 15);
    pdf.rect(15, 225, pageWidth - 30, 20, "F");
    pdf.setFontSize(14);
    pdf.setFont("helvetica", "bold");
    pdf.setTextColor(0, 0, 0);
    pdf.text(`Total Amount: ${shopifyData.total_price || "0.00"} ${shopifyData.currency || "PKR"}`, 20, 235);
    pdf.text(`COD: ${order.codCollected || "N/A"}`, 20, 242);
    pdf.addImage(qrCodeDataURL, "PNG", pageWidth - 60, 120, 40, 40);
    pdf.setFontSize(10);
    pdf.text("Scan for Details", pageWidth - 55, 170, { align: "center" });
    pdf.setFillColor(52, 73, 94);
    pdf.rect(0, pageHeight - 30, pageWidth, 30, "F");
    pdf.setTextColor(255, 255, 255);
    pdf.setFontSize(10);
    pdf.text("© 2025 Rushrr Courier - Express Delivery Service", pageWidth / 2, pageHeight - 15, { align: "center" });
    pdf.text("For support: <EMAIL> | Tel: +92-XXX-XXXXXXX", pageWidth / 2, pageHeight - 8, { align: "center" });
    pdf.save(`Airway-Bill-${shopifyData.order_number || order.id}.pdf`);
  } catch (error) {
    console.error("Error generating QR code:", error);
    alert("Error generating airway bill. Please try again.");
  }
};
function RushrrDashboard({ token }) {
  var _a2, _b, _c, _d;
  const [orders, setOrders] = useState([]);
  const [bookedOrders, setBookedOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeOrder, setActiveOrder] = useState(null);
  const [originalOrder, setOriginalOrder] = useState(null);
  const [editedOrder, setEditedOrder] = useState(null);
  const [bookingLoading, setBookingLoading] = useState(false);
  const [error, setError] = useState(null);
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setError(null);
        const res = await fetch("api/orders", { method: "GET" });
        const data = await res.json();
        if (data.success) {
          setOrders(data.orders);
        } else {
          setError(data.error || "Failed to fetch orders");
        }
      } catch (err) {
        console.error("Failed to fetch orders", err);
        setError("Network error: Unable to fetch orders. Please check your connection.");
      } finally {
        setLoading(false);
      }
    };
    fetchOrders();
  }, [token]);
  const { selectedResources, allResourcesSelected, handleSelectionChange } = useIndexResourceState(orders);
  const handleEditClick = (order) => {
    setActiveOrder(order);
    const mapped = mapOrderForEditing(order);
    setEditedOrder(mapped);
    setOriginalOrder(mapped);
  };
  function getAllowedOrderUpdates(original, edited) {
    var _a3, _b2, _c2, _d2, _e, _f, _g, _h, _i, _j;
    const updates = {};
    if (original.customerEmail !== edited.customerEmail) {
      updates.email = edited.customerEmail;
    }
    if (original.currency !== edited.currency) {
      updates.currency = edited.currency;
    }
    if (original.totalPrice !== edited.totalPrice) {
      updates.total_price = edited.totalPrice;
    }
    const shippingChangedFields = {};
    if (((_a3 = original.shippingAddress) == null ? void 0 : _a3.city) !== ((_b2 = edited.shippingAddress) == null ? void 0 : _b2.city)) {
      shippingChangedFields.city = edited.shippingAddress.city;
    }
    if (((_c2 = original.shippingAddress) == null ? void 0 : _c2.phone) !== ((_d2 = edited.shippingAddress) == null ? void 0 : _d2.phone)) {
      shippingChangedFields.phone = edited.shippingAddress.phone;
    }
    if (((_e = original.shippingAddress) == null ? void 0 : _e.address1) !== ((_f = edited.shippingAddress) == null ? void 0 : _f.address1)) {
      shippingChangedFields.address1 = edited.shippingAddress.address1;
    }
    if (Object.keys(shippingChangedFields).length > 0) {
      updates.shipping_address = shippingChangedFields;
    }
    const billingChangedFields = {};
    if (((_g = original.billingAddress) == null ? void 0 : _g.city) !== ((_h = edited.billingAddress) == null ? void 0 : _h.city)) {
      billingChangedFields.city = edited.billingAddress.city;
    }
    if (((_i = original.billingAddress) == null ? void 0 : _i.address1) !== ((_j = edited.billingAddress) == null ? void 0 : _j.address1)) {
      billingChangedFields.address1 = edited.billingAddress.address1;
    }
    if (Object.keys(billingChangedFields).length > 0) {
      updates.billing_address = billingChangedFields;
    }
    return updates;
  }
  const handleSaveEdit = async () => {
    if (!(activeOrder == null ? void 0 : activeOrder.id) || !originalOrder) return;
    const updates = getAllowedOrderUpdates(originalOrder, editedOrder);
    if (Object.keys(updates).length === 0) {
      alert("No changes to save.");
      return;
    }
    try {
      const res = await fetch(`https://backend.rushr-admin.com/api/orders/update?id=${activeOrder.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify(updates)
      });
      const data = await res.json();
      if (res.ok) {
        alert("Order updated successfully!");
        setActiveOrder(null);
      } else {
        alert((data == null ? void 0 : data.message) || "Update failed.");
      }
    } catch (err) {
      console.error("Update error:", err);
      alert("An error occurred while updating the order.");
    }
  };
  const handleUploadBookings = async () => {
    if (selectedResources.length === 0) {
      alert("Please select at least one order.");
      return;
    }
    setBookingLoading(true);
    try {
      const selectedOrders2 = orders.filter((order) => selectedResources.includes(order.id));
      const shopifyOrderIds = selectedOrders2.map((order) => order.shopifyOrderId);
      const res = await fetch("https://backend.rushr-admin.com/api/orders/book", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({ orderId: shopifyOrderIds })
        // <-- note: plural
      });
      const data = await res.json();
      if (res.ok && data.success) {
        for (const order of selectedOrders2) {
          await generateAirwayBill(order);
        }
        setBookedOrders((prev) => [...prev, ...selectedOrders2]);
        const remaining = orders.filter((order) => !selectedResources.includes(order.id));
        setOrders(remaining);
        alert(`${selectedOrders2.length} order(s) booked successfully! Airway bills have been downloaded.`);
      } else {
        alert(data.message || "Booking failed");
      }
    } catch (err) {
      console.error("Booking failed:", err);
      alert("An error occurred while booking orders.");
    } finally {
      setBookingLoading(false);
    }
  };
  const selectedOrders = orders.filter((order) => order.status === "unbooked");
  const rows = selectedOrders.map((order, index2) => {
    const shopifyData = order.shopifyOrderData || {};
    const customer = shopifyData.customer || {};
    const billingAddress = shopifyData.billing_address || {};
    const shippingAddress = shopifyData.shipping_address || {};
    return /* @__PURE__ */ jsxs(
      IndexTable.Row,
      {
        id: order.id,
        selected: selectedResources.includes(order.id),
        position: index2,
        children: [
          /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Text, { variant: "bodyMd", fontWeight: "medium", children: index2 + 1 }) }),
          /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Badge, { tone: "info", children: shopifyData.order_number || "-" }) }),
          /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Text, { variant: "bodyMd", fontWeight: "medium", children: `${customer.first_name || ""} ${customer.last_name || ""}` }) }),
          /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Badge, { tone: "attention", children: billingAddress.city || "-" }) }),
          /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Text, { variant: "bodyMd", children: order.codCollected ?? "N/A" }) }),
          /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Badge, { tone: "warning", children: order.status || "Unbooked" }) }),
          /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Text, { variant: "bodyMd", color: "subdued", children: shippingAddress.address1 || "N/A" }) }),
          /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Text, { variant: "bodyMd", fontWeight: "semibold", color: "success", children: `${shopifyData.total_price || "0.00"} ${shopifyData.currency || "PKR"}` }) }),
          /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Button, { size: "slim", onClick: () => handleEditClick(order), children: "Edit" }) })
        ]
      },
      order.id
    );
  });
  if (loading) {
    return /* @__PURE__ */ jsx(Page, { fullWidth: true, children: /* @__PURE__ */ jsx(Layout, { children: /* @__PURE__ */ jsx(Layout.Section, { children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(Box, { padding: "800", textAlign: "center", children: [
      /* @__PURE__ */ jsx(Spinner, { size: "large" }),
      /* @__PURE__ */ jsx(Box, { paddingBlockStart: "400", children: /* @__PURE__ */ jsx(Text, { variant: "headingMd", children: "Loading orders..." }) })
    ] }) }) }) }) });
  }
  return /* @__PURE__ */ jsxs(Page, { fullWidth: true, children: [
    /* @__PURE__ */ jsxs(Layout, { children: [
      error && /* @__PURE__ */ jsx(Layout.Section, { children: /* @__PURE__ */ jsx(
        Banner,
        {
          title: "Error",
          tone: "critical",
          onDismiss: () => setError(null),
          children: /* @__PURE__ */ jsx("p", { children: error })
        }
      ) }),
      /* @__PURE__ */ jsx(Layout.Section, { children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsx(Box, { padding: "600", children: /* @__PURE__ */ jsxs(InlineStack, { align: "space-between", blockAlign: "center", children: [
        /* @__PURE__ */ jsxs(InlineStack, { gap: "400", blockAlign: "center", children: [
          /* @__PURE__ */ jsx(
            "img",
            {
              src: "https://res.cloudinary.com/dgiqiysh5/image/upload/v1750681695/WhatsApp_Image_2025-06-23_at_16.02.36_vyjear.jpg",
              alt: "Rushrr Logo",
              style: {
                height: "50px",
                borderRadius: "8px",
                boxShadow: "0 2px 8px rgba(0,0,0,0.1)"
              }
            }
          ),
          /* @__PURE__ */ jsxs(BlockStack, { gap: "100", children: [
            /* @__PURE__ */ jsx(Text, { variant: "headingLg", as: "h1", children: "📦 Rushrr Courier Dashboard" }),
            /* @__PURE__ */ jsx(Text, { variant: "bodyMd", color: "subdued", children: "Manage and book orders from your Shopify store with automated airway bill generation" })
          ] })
        ] }),
        /* @__PURE__ */ jsx(Box, { children: /* @__PURE__ */ jsxs(Badge, { tone: "success", size: "large", children: [
          selectedOrders.length,
          " Unbooked Orders"
        ] }) })
      ] }) }) }) }),
      /* @__PURE__ */ jsx(Layout.Section, { children: /* @__PURE__ */ jsxs(InlineStack, { gap: "400", children: [
        /* @__PURE__ */ jsx("div", { style: { flex: 1 }, children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(Box, { padding: "400", textAlign: "center", children: [
          /* @__PURE__ */ jsx(Text, { variant: "headingXl", color: "success", children: selectedOrders.length }),
          /* @__PURE__ */ jsx(Text, { variant: "bodyMd", color: "subdued", children: "Unbooked Orders" })
        ] }) }) }),
        /* @__PURE__ */ jsx("div", { style: { flex: 1 }, children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(Box, { padding: "400", textAlign: "center", children: [
          /* @__PURE__ */ jsx(Text, { variant: "headingXl", color: "info", children: bookedOrders.length }),
          /* @__PURE__ */ jsx(Text, { variant: "bodyMd", color: "subdued", children: "Booked Orders" })
        ] }) }) })
      ] }) }),
      /* @__PURE__ */ jsx(Layout.Section, { children: /* @__PURE__ */ jsxs(Card, { children: [
        /* @__PURE__ */ jsx(Box, { padding: "400", children: /* @__PURE__ */ jsxs(InlineStack, { align: "space-between", blockAlign: "center", children: [
          /* @__PURE__ */ jsx(Text, { variant: "headingMd", children: "Unbooked Orders" }),
          /* @__PURE__ */ jsx(
            Button,
            {
              variant: "primary",
              onClick: handleUploadBookings,
              loading: bookingLoading,
              disabled: selectedResources.length === 0,
              size: "large",
              children: bookingLoading ? "Booking & Generating Bills..." : `Book ${selectedResources.length} Orders`
            }
          )
        ] }) }),
        /* @__PURE__ */ jsx(
          IndexTable,
          {
            resourceName: { singular: "order", plural: "orders" },
            itemCount: selectedOrders.length,
            selectedItemsCount: allResourcesSelected ? "All" : selectedResources.length,
            onSelectionChange: handleSelectionChange,
            headings: [
              { title: "#" },
              { title: "Order #" },
              { title: "Customer" },
              { title: "City" },
              { title: "COD" },
              { title: "Status" },
              { title: "Shipping Address" },
              { title: "Amount" },
              { title: "Actions" }
            ],
            selectable: true,
            children: rows
          }
        )
      ] }) }),
      bookedOrders.length > 0 && /* @__PURE__ */ jsx(Layout.Section, { children: /* @__PURE__ */ jsxs(Card, { children: [
        /* @__PURE__ */ jsx(Box, { padding: "400", children: /* @__PURE__ */ jsx(Text, { variant: "headingMd", children: "Recently Booked Orders" }) }),
        /* @__PURE__ */ jsx(
          IndexTable,
          {
            resourceName: {
              singular: "booked order",
              plural: "booked orders"
            },
            itemCount: bookedOrders.length,
            headings: [
              { title: "#" },
              { title: "Order #" },
              { title: "Customer" },
              { title: "City" },
              { title: "COD" },
              { title: "Status" },
              { title: "Shipping Address" },
              { title: "Amount" },
              { title: "Airway Bill" }
            ],
            selectable: false,
            children: bookedOrders.map((order, index2) => {
              const shopifyData = order.shopifyOrderData || {};
              const customer = shopifyData.customer || {};
              const billingAddress = shopifyData.billing_address || {};
              const shippingAddress = shopifyData.shipping_address || {};
              return /* @__PURE__ */ jsxs(
                IndexTable.Row,
                {
                  id: `booked-${order.id}`,
                  position: index2,
                  children: [
                    /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Text, { variant: "bodyMd", fontWeight: "medium", children: index2 + 1 }) }),
                    /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Badge, { tone: "info", children: shopifyData.order_number || "-" }) }),
                    /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Text, { variant: "bodyMd", fontWeight: "medium", children: `${customer.first_name || ""} ${customer.last_name || ""}` }) }),
                    /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Badge, { tone: "attention", children: billingAddress.city || "-" }) }),
                    /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Text, { variant: "bodyMd", children: order.codCollected ?? "N/A" }) }),
                    /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Badge, { tone: "success", children: "Booked" }) }),
                    /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Text, { variant: "bodyMd", color: "subdued", children: shippingAddress.address1 || "N/A" }) }),
                    /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(Text, { variant: "bodyMd", fontWeight: "semibold", color: "success", children: `${shopifyData.total_price || "0.00"} ${shopifyData.currency || "PKR"}` }) }),
                    /* @__PURE__ */ jsx(IndexTable.Cell, { children: /* @__PURE__ */ jsx(
                      Button,
                      {
                        size: "slim",
                        onClick: () => generateAirwayBill(order),
                        tone: "success",
                        children: "Download Bill"
                      }
                    ) })
                  ]
                },
                order.id
              );
            })
          }
        )
      ] }) })
    ] }),
    activeOrder && /* @__PURE__ */ jsx(
      Modal,
      {
        open: true,
        onClose: () => setActiveOrder(null),
        title: `Edit Order #${activeOrder.orderNumber}`,
        primaryAction: {
          content: "Save Changes",
          onAction: handleSaveEdit
        },
        secondaryActions: [
          { content: "Cancel", onAction: () => setActiveOrder(null) }
        ],
        children: /* @__PURE__ */ jsx(Modal.Section, { children: /* @__PURE__ */ jsxs(BlockStack, { gap: "400", children: [
          /* @__PURE__ */ jsx(
            TextField,
            {
              label: "Customer Name",
              value: editedOrder.customerName || "",
              onChange: (val) => setEditedOrder({ ...editedOrder, customerName: val })
            }
          ),
          /* @__PURE__ */ jsx(
            TextField,
            {
              label: "Customer Email",
              value: editedOrder.customerEmail || "",
              onChange: (val) => setEditedOrder({ ...editedOrder, customerEmail: val })
            }
          ),
          /* @__PURE__ */ jsx(
            Select,
            {
              label: "City",
              options: cities.map((city) => ({ label: city, value: city })),
              value: ((_a2 = editedOrder.shippingAddress) == null ? void 0 : _a2.city) || "",
              onChange: (val) => setEditedOrder({
                ...editedOrder,
                shippingAddress: {
                  ...editedOrder.shippingAddress,
                  city: val
                }
              })
            }
          ),
          /* @__PURE__ */ jsx(
            TextField,
            {
              label: "Billing Address",
              value: ((_b = editedOrder.billingAddress) == null ? void 0 : _b.address1) || "",
              onChange: (val) => setEditedOrder({
                ...editedOrder,
                billingAddress: {
                  ...editedOrder.billingAddress,
                  address1: val
                }
              })
            }
          ),
          /* @__PURE__ */ jsx(
            TextField,
            {
              label: "Shipping Address",
              value: ((_c = editedOrder.shippingAddress) == null ? void 0 : _c.address1) || "",
              onChange: (val) => setEditedOrder({
                ...editedOrder,
                shippingAddress: {
                  ...editedOrder.shippingAddress,
                  address1: val
                }
              })
            }
          ),
          /* @__PURE__ */ jsx(
            TextField,
            {
              label: "Phone (Shipping)",
              value: ((_d = editedOrder.shippingAddress) == null ? void 0 : _d.phone) || "",
              onChange: (val) => setEditedOrder({
                ...editedOrder,
                shippingAddress: {
                  ...editedOrder.shippingAddress,
                  phone: val
                }
              })
            }
          ),
          /* @__PURE__ */ jsx(
            TextField,
            {
              label: "Total Price",
              type: "number",
              value: editedOrder.totalPrice || "",
              onChange: (val) => setEditedOrder({ ...editedOrder, totalPrice: val })
            }
          ),
          /* @__PURE__ */ jsx(
            TextField,
            {
              label: "Currency",
              value: editedOrder.currency || "",
              onChange: (val) => setEditedOrder({ ...editedOrder, currency: val })
            }
          )
        ] }) })
      }
    )
  ] });
}
function mapOrderForEditing(order) {
  const shopify2 = order.shopifyOrderData || {};
  const customer = shopify2.customer || {};
  const billing = shopify2.billing_address || {};
  const shipping = shopify2.shipping_address || {};
  return {
    ...order,
    customerName: `${customer.first_name || ""} ${customer.last_name || ""}`.trim(),
    customerEmail: shopify2.email || shopify2.contact_email || "",
    billingAddress: {
      address1: billing.address1 || "",
      city: billing.city || "",
      country: billing.country || "",
      zip: billing.zip || ""
    },
    shippingAddress: {
      address1: shipping.address1 || "",
      city: shipping.city || "",
      country: shipping.country || "",
      zip: shipping.zip || "",
      phone: shipping.phone || shopify2.phone || ""
    },
    totalPrice: shopify2.total_price || "",
    currency: shopify2.currency || ""
  };
}
function SetupPage() {
  const { shopifyStoreName, shopifyStoreUrl } = useLoaderData();
  const [token, setToken] = useState("");
  const [responseMessage, setResponseMessage] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  useEffect(() => {
    const checkStoreConnection = async () => {
      try {
        const res = await fetch("https://backend.rushr-admin.com/api/auth/verify-shopify-store", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ shopifyStoreUrl })
        });
        const data = await res.json();
        if (res.ok && (data == null ? void 0 : data.success)) {
          if (data.token) {
            await saveTokenToSession(data.token);
            setToken(data.token);
          }
          setIsConnected(true);
        }
      } catch (err) {
        console.error("Error checking store status", err);
      } finally {
        setIsLoading(false);
      }
    };
    checkStoreConnection();
  }, [shopifyStoreUrl]);
  const saveTokenToSession = async (apiToken) => {
    try {
      await fetch("/api/save-token", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ token: apiToken })
      });
    } catch (err) {
      console.error("Error saving token to session:", err);
    }
  };
  const handleSave = async () => {
    try {
      const res = await fetch("https://backend.rushr-admin.com/api/auth/verify-api-key", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          apiKey: token,
          shopifyStoreUrl,
          shopifyStoreName
        })
      });
      const data = await res.json();
      if (res.ok) {
        setIsConnected(true);
        setResponseMessage({ type: "success", content: "Connection successful!" });
      } else {
        setResponseMessage({ type: "error", content: (data == null ? void 0 : data.error) || "Failed to verify token." });
      }
    } catch (err) {
      setResponseMessage({ type: "error", content: "Network error or server unavailable." });
    }
  };
  if (isLoading) {
    return /* @__PURE__ */ jsx(Page, { fullWidth: true, children: /* @__PURE__ */ jsx(Layout, { children: /* @__PURE__ */ jsx(Layout.Section, { children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(Box, { padding: "800", textAlign: "center", children: [
      /* @__PURE__ */ jsx(Spinner, { accessibilityLabel: "Checking connection...", size: "large" }),
      /* @__PURE__ */ jsx(Box, { paddingBlockStart: "400", children: /* @__PURE__ */ jsx(Text, { variant: "headingMd", children: "Checking store connection..." }) })
    ] }) }) }) }) });
  }
  if (isConnected) {
    return /* @__PURE__ */ jsx(RushrrDashboard, { token });
  }
  return /* @__PURE__ */ jsx(Page, { fullWidth: true, children: /* @__PURE__ */ jsxs(Layout, { children: [
    /* @__PURE__ */ jsx(Layout.Section, { children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(Box, { padding: "600", textAlign: "center", children: [
      /* @__PURE__ */ jsx(
        "img",
        {
          src: "https://res.cloudinary.com/dgiqiysh5/image/upload/v1750681695/WhatsApp_Image_2025-06-23_at_16.02.36_vyjear.jpg",
          alt: "Rushrr Logo",
          style: {
            height: "80px",
            marginBottom: "20px",
            borderRadius: "12px",
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)"
          }
        }
      ),
      /* @__PURE__ */ jsx(Text, { variant: "displayMd", as: "h1", children: "Welcome to Rushrr Courier" }),
      /* @__PURE__ */ jsx(Box, { paddingBlockStart: "200", children: /* @__PURE__ */ jsx(Text, { variant: "bodyLg", color: "subdued", children: "Connect your Shopify store to start managing deliveries with automated airway bill generation" }) })
    ] }) }) }),
    /* @__PURE__ */ jsx(Layout.Section, { children: /* @__PURE__ */ jsxs(InlineStack, { gap: "600", align: "start", children: [
      /* @__PURE__ */ jsx("div", { style: { flex: 1 }, children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsx(Box, { padding: "500", children: /* @__PURE__ */ jsxs(BlockStack, { gap: "400", children: [
        /* @__PURE__ */ jsx(Text, { variant: "headingMd", children: "🚀 Quick Setup Guide" }),
        /* @__PURE__ */ jsx(Divider, {}),
        /* @__PURE__ */ jsxs(BlockStack, { gap: "300", children: [
          /* @__PURE__ */ jsxs(InlineStack, { gap: "200", blockAlign: "start", children: [
            /* @__PURE__ */ jsx(Badge, { tone: "info", children: "1" }),
            /* @__PURE__ */ jsx(Text, { children: "Get your API token from your merchant dashboard" })
          ] }),
          /* @__PURE__ */ jsxs(InlineStack, { gap: "200", blockAlign: "start", children: [
            /* @__PURE__ */ jsx(Badge, { tone: "info", children: "2" }),
            /* @__PURE__ */ jsx(Text, { children: "Enter the token in the form and save settings" })
          ] }),
          /* @__PURE__ */ jsxs(InlineStack, { gap: "200", blockAlign: "start", children: [
            /* @__PURE__ */ jsx(Badge, { tone: "info", children: "3" }),
            /* @__PURE__ */ jsx(Text, { children: "Access the dashboard after successful verification" })
          ] }),
          /* @__PURE__ */ jsxs(InlineStack, { gap: "200", blockAlign: "start", children: [
            /* @__PURE__ */ jsx(Badge, { tone: "success", children: "✓" }),
            /* @__PURE__ */ jsx(Text, { children: "Start booking orders with auto airway bill generation" })
          ] })
        ] })
      ] }) }) }) }),
      /* @__PURE__ */ jsx("div", { style: { flex: 1 }, children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsx(Box, { padding: "500", children: /* @__PURE__ */ jsxs(BlockStack, { gap: "400", children: [
        /* @__PURE__ */ jsx(Text, { variant: "headingMd", children: "🔐 API Configuration" }),
        /* @__PURE__ */ jsx(Divider, {}),
        /* @__PURE__ */ jsx(
          TextField,
          {
            label: "API Token",
            value: token,
            onChange: (val) => setToken(val),
            placeholder: "Enter your Rushrr API token",
            helpText: "You can find this in your Rushrr merchant dashboard"
          }
        ),
        /* @__PURE__ */ jsx(
          Button,
          {
            variant: "primary",
            onClick: handleSave,
            size: "large",
            fullWidth: true,
            children: "🚀 Connect & Verify"
          }
        ),
        /* @__PURE__ */ jsx(Box, { children: /* @__PURE__ */ jsxs(Text, { variant: "bodyMd", color: "subdued", children: [
          /* @__PURE__ */ jsx("strong", { children: "Default Weight:" }),
          " 0.5 kg"
        ] }) }),
        responseMessage && /* @__PURE__ */ jsx(
          Banner,
          {
            title: responseMessage.content,
            status: responseMessage.type === "success" ? "success" : "critical"
          }
        )
      ] }) }) }) })
    ] }) }),
    /* @__PURE__ */ jsx(Layout.Section, { children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(Box, { padding: "500", children: [
      /* @__PURE__ */ jsx(Text, { variant: "headingMd", textAlign: "center", children: "✨ Features You'll Get" }),
      /* @__PURE__ */ jsx(Box, { paddingBlockStart: "400", children: /* @__PURE__ */ jsxs(InlineStack, { gap: "600", align: "start", children: [
        /* @__PURE__ */ jsxs("div", { style: { flex: 1, textAlign: "center" }, children: [
          /* @__PURE__ */ jsx(Box, { paddingBlockEnd: "300", children: /* @__PURE__ */ jsx(Text, { variant: "headingLg", children: "📦" }) }),
          /* @__PURE__ */ jsx(Text, { variant: "headingMd", children: "Order Management" }),
          /* @__PURE__ */ jsx(Box, { paddingBlockStart: "200", children: /* @__PURE__ */ jsx(Text, { variant: "bodyMd", color: "subdued", children: "View, edit, and manage all your Shopify orders in one place" }) })
        ] }),
        /* @__PURE__ */ jsxs("div", { style: { flex: 1, textAlign: "center" }, children: [
          /* @__PURE__ */ jsx(Box, { paddingBlockEnd: "300", children: /* @__PURE__ */ jsx(Text, { variant: "headingLg", children: "📄" }) }),
          /* @__PURE__ */ jsx(Text, { variant: "headingMd", children: "Airway Bills" }),
          /* @__PURE__ */ jsx(Box, { paddingBlockStart: "200", children: /* @__PURE__ */ jsx(Text, { variant: "bodyMd", color: "subdued", children: "Automatically generate professional airway bills with QR codes" }) })
        ] }),
        /* @__PURE__ */ jsxs("div", { style: { flex: 1, textAlign: "center" }, children: [
          /* @__PURE__ */ jsx(Box, { paddingBlockEnd: "300", children: /* @__PURE__ */ jsx(Text, { variant: "headingLg", children: "⚡" }) }),
          /* @__PURE__ */ jsx(Text, { variant: "headingMd", children: "Bulk Booking" }),
          /* @__PURE__ */ jsx(Box, { paddingBlockStart: "200", children: /* @__PURE__ */ jsx(Text, { variant: "bodyMd", color: "subdued", children: "Book multiple orders at once and download all airway bills" }) })
        ] })
      ] }) })
    ] }) }) })
  ] }) });
}
const route18 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: SetupPage,
  loader
}, Symbol.toStringTag, { value: "Module" }));
const action = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  const body = await request.json();
  const { token } = body;
  session.set("rushrr_token", token);
  return json(
    { success: true },
    {
      headers: {
        "Set-Cookie": await session.commit()
        // commit the session
      }
    }
  );
};
const route19 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action
}, Symbol.toStringTag, { value: "Module" }));
const serverManifest = { "entry": { "module": "/assets/entry.client-BMUJfrQ-.js", "imports": ["/assets/jsx-runtime-DlxonYWr.js", "/assets/index-y-Yv8VR0.js", "/assets/components-7DQbiWt_.js"], "css": [] }, "routes": { "root": { "id": "root", "parentId": void 0, "path": "", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/root-DsEoCCF4.js", "imports": ["/assets/jsx-runtime-DlxonYWr.js", "/assets/index-y-Yv8VR0.js", "/assets/components-7DQbiWt_.js"], "css": [] }, "routes/resources.bulk-order-details": { "id": "routes/resources.bulk-order-details", "parentId": "root", "path": "resources/bulk-order-details", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/resources.bulk-order-details-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/webhooks.app.scopes_update": { "id": "routes/webhooks.app.scopes_update", "parentId": "root", "path": "webhooks/app/scopes_update", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/webhooks.app.scopes_update-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/webhooks.app.uninstalled": { "id": "routes/webhooks.app.uninstalled", "parentId": "root", "path": "webhooks/app/uninstalled", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/webhooks.app.uninstalled-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/resources.order-details": { "id": "routes/resources.order-details", "parentId": "root", "path": "resources/order-details", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/resources.order-details-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/api.save-token": { "id": "routes/api.save-token", "parentId": "root", "path": "api/save-token", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/api.save-token-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/api.booking": { "id": "routes/api.booking", "parentId": "root", "path": "api/booking", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/api.booking-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/api.orders": { "id": "routes/api.orders", "parentId": "root", "path": "api/orders", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/api.orders-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/auth.login": { "id": "routes/auth.login", "parentId": "root", "path": "auth/login", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/route-Tm1Dupvw.js", "imports": ["/assets/jsx-runtime-DlxonYWr.js", "/assets/index-y-Yv8VR0.js", "/assets/styles-B33d-VxP.js", "/assets/components-7DQbiWt_.js", "/assets/Page-CD-2c-Tv.js", "/assets/context-QCCGjp8E.js", "/assets/context-C0av8SGa.js"], "css": [] }, "routes/auth.$": { "id": "routes/auth.$", "parentId": "root", "path": "auth/*", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/auth._-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/_index": { "id": "routes/_index", "parentId": "root", "path": void 0, "index": true, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/route-DbFXt4-c.js", "imports": ["/assets/jsx-runtime-DlxonYWr.js", "/assets/components-7DQbiWt_.js", "/assets/index-y-Yv8VR0.js"], "css": ["/assets/route-Cnm7FvdT.css"] }, "routes/app": { "id": "routes/app", "parentId": "root", "path": "app", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": true, "module": "/assets/app-BY82hW--.js", "imports": ["/assets/jsx-runtime-DlxonYWr.js", "/assets/index-y-Yv8VR0.js", "/assets/components-7DQbiWt_.js", "/assets/styles-B33d-VxP.js", "/assets/context-QCCGjp8E.js", "/assets/context-C0av8SGa.js"], "css": [] }, "routes/app.additional": { "id": "routes/app.additional", "parentId": "routes/app", "path": "additional", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.additional-DScXaeyM.js", "imports": ["/assets/jsx-runtime-DlxonYWr.js", "/assets/Page-CD-2c-Tv.js", "/assets/Layout-DyjgeZaH.js", "/assets/index-y-Yv8VR0.js", "/assets/context-QCCGjp8E.js"], "css": [] }, "routes/app.analytics": { "id": "routes/app.analytics", "parentId": "routes/app", "path": "analytics", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.analytics-Ifjm8sAL.js", "imports": ["/assets/jsx-runtime-DlxonYWr.js"], "css": [] }, "routes/app.settings": { "id": "routes/app.settings", "parentId": "routes/app", "path": "settings", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.settings-BLc2cfeF.js", "imports": ["/assets/jsx-runtime-DlxonYWr.js", "/assets/components-7DQbiWt_.js", "/assets/index-y-Yv8VR0.js"], "css": [] }, "routes/app.tracking": { "id": "routes/app.tracking", "parentId": "routes/app", "path": "tracking", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.tracking-BpwBPRtM.js", "imports": ["/assets/jsx-runtime-DlxonYWr.js", "/assets/components-7DQbiWt_.js", "/assets/index-y-Yv8VR0.js"], "css": [] }, "routes/app.orders": { "id": "routes/app.orders", "parentId": "routes/app", "path": "orders", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.orders-uQIK5qVY.js", "imports": ["/assets/jsx-runtime-DlxonYWr.js"], "css": [] }, "routes/app.riders": { "id": "routes/app.riders", "parentId": "routes/app", "path": "riders", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.riders-F5WKdmvx.js", "imports": ["/assets/jsx-runtime-DlxonYWr.js"], "css": [] }, "routes/app._index": { "id": "routes/app._index", "parentId": "routes/app", "path": void 0, "index": true, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app._index-Dm9g_uIE.js", "imports": ["/assets/app._index-Czrwxyor.js", "/assets/jsx-runtime-DlxonYWr.js", "/assets/index-y-Yv8VR0.js", "/assets/components-7DQbiWt_.js", "/assets/Page-CD-2c-Tv.js", "/assets/context-QCCGjp8E.js", "/assets/Layout-DyjgeZaH.js", "/assets/context-C0av8SGa.js"], "css": [] }, "routes/app.setup": { "id": "routes/app.setup", "parentId": "routes/app", "path": "setup", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.setup-l0sNRNKZ.js", "imports": [], "css": [] } }, "url": "/assets/manifest-81fd53ba.js", "version": "81fd53ba" };
const mode = "production";
const assetsBuildDirectory = "build\\client";
const basename = "/";
const future = { "v3_fetcherPersist": true, "v3_relativeSplatPath": true, "v3_throwAbortReason": true, "v3_routeConfig": true, "v3_singleFetch": false, "v3_lazyRouteDiscovery": true, "unstable_optimizeDeps": false };
const isSpaMode = false;
const publicPath = "/";
const entry = { module: entryServer };
const routes = {
  "root": {
    id: "root",
    parentId: void 0,
    path: "",
    index: void 0,
    caseSensitive: void 0,
    module: route0
  },
  "routes/resources.bulk-order-details": {
    id: "routes/resources.bulk-order-details",
    parentId: "root",
    path: "resources/bulk-order-details",
    index: void 0,
    caseSensitive: void 0,
    module: route1
  },
  "routes/webhooks.app.scopes_update": {
    id: "routes/webhooks.app.scopes_update",
    parentId: "root",
    path: "webhooks/app/scopes_update",
    index: void 0,
    caseSensitive: void 0,
    module: route2
  },
  "routes/webhooks.app.uninstalled": {
    id: "routes/webhooks.app.uninstalled",
    parentId: "root",
    path: "webhooks/app/uninstalled",
    index: void 0,
    caseSensitive: void 0,
    module: route3
  },
  "routes/resources.order-details": {
    id: "routes/resources.order-details",
    parentId: "root",
    path: "resources/order-details",
    index: void 0,
    caseSensitive: void 0,
    module: route4
  },
  "routes/api.save-token": {
    id: "routes/api.save-token",
    parentId: "root",
    path: "api/save-token",
    index: void 0,
    caseSensitive: void 0,
    module: route5
  },
  "routes/api.booking": {
    id: "routes/api.booking",
    parentId: "root",
    path: "api/booking",
    index: void 0,
    caseSensitive: void 0,
    module: route6
  },
  "routes/api.orders": {
    id: "routes/api.orders",
    parentId: "root",
    path: "api/orders",
    index: void 0,
    caseSensitive: void 0,
    module: route7
  },
  "routes/auth.login": {
    id: "routes/auth.login",
    parentId: "root",
    path: "auth/login",
    index: void 0,
    caseSensitive: void 0,
    module: route8
  },
  "routes/auth.$": {
    id: "routes/auth.$",
    parentId: "root",
    path: "auth/*",
    index: void 0,
    caseSensitive: void 0,
    module: route9
  },
  "routes/_index": {
    id: "routes/_index",
    parentId: "root",
    path: void 0,
    index: true,
    caseSensitive: void 0,
    module: route10
  },
  "routes/app": {
    id: "routes/app",
    parentId: "root",
    path: "app",
    index: void 0,
    caseSensitive: void 0,
    module: route11
  },
  "routes/app.additional": {
    id: "routes/app.additional",
    parentId: "routes/app",
    path: "additional",
    index: void 0,
    caseSensitive: void 0,
    module: route12
  },
  "routes/app.analytics": {
    id: "routes/app.analytics",
    parentId: "routes/app",
    path: "analytics",
    index: void 0,
    caseSensitive: void 0,
    module: route13
  },
  "routes/app.settings": {
    id: "routes/app.settings",
    parentId: "routes/app",
    path: "settings",
    index: void 0,
    caseSensitive: void 0,
    module: route14
  },
  "routes/app.tracking": {
    id: "routes/app.tracking",
    parentId: "routes/app",
    path: "tracking",
    index: void 0,
    caseSensitive: void 0,
    module: route15
  },
  "routes/app.orders": {
    id: "routes/app.orders",
    parentId: "routes/app",
    path: "orders",
    index: void 0,
    caseSensitive: void 0,
    module: route16
  },
  "routes/app.riders": {
    id: "routes/app.riders",
    parentId: "routes/app",
    path: "riders",
    index: void 0,
    caseSensitive: void 0,
    module: route17
  },
  "routes/app._index": {
    id: "routes/app._index",
    parentId: "routes/app",
    path: void 0,
    index: true,
    caseSensitive: void 0,
    module: route18
  },
  "routes/app.setup": {
    id: "routes/app.setup",
    parentId: "routes/app",
    path: "setup",
    index: void 0,
    caseSensitive: void 0,
    module: route19
  }
};
export {
  serverManifest as assets,
  assetsBuildDirectory,
  basename,
  entry,
  future,
  isSpaMode,
  mode,
  publicPath,
  routes
};
