import{j as e}from"./jsx-runtime-DlxonYWr.js";import{c as k,U as B,v as j,P as E,C as v,B as b,T as d,a as I}from"./Page-CD-2c-Tv.js";import{B as C,L as c}from"./Layout-DyjgeZaH.js";import{R as s}from"./index-y-Yv8VR0.js";import"./context-QCCGjp8E.js";var p={Link:"Polaris-Link",monochrome:"Polaris-Link--monochrome",removeUnderline:"Polaris-Link--removeUnderline"};function L({url:a,children:i,onClick:n,external:t,target:l,id:r,monochrome:f,removeUnderline:y,accessibilityLabel:h,dataPrimaryLink:x}){return s.createElement(C.Consumer,null,N=>{const P=f||N,g=k(p.Link,P&&p.monochrome,y&&p.removeUnderline);return a?s.createElement(B,{onClick:n,className:g,url:a,external:t,target:l,id:r,"aria-label":h,"data-primary-link":x},i):s.createElement("button",{type:"button",onClick:n,className:g,id:r,"aria-label":h,"data-primary-link":x},i)})}var o={List:"Polaris-List",typeNumber:"Polaris-List--typeNumber",Item:"Polaris-List__Item",spacingLoose:"Polaris-List--spacingLoose"};function U({children:a}){return s.createElement("li",{className:o.Item},a)}const u=function({children:i,gap:n="loose",type:t="bullet"}){const l=k(o.List,n&&o[j("spacing",n)],t&&o[j("type",t)]),r=t==="bullet"?"ul":"ol";return s.createElement(r,{className:l},i)};u.Item=U;const w="ui-title-bar";function _(){return e.jsxs(E,{children:[e.jsx(w,{title:"Additional page"}),e.jsxs(c,{children:[e.jsx(c.Section,{children:e.jsx(v,{children:e.jsxs(b,{gap:"300",children:[e.jsxs(d,{as:"p",variant:"bodyMd",children:["The app template comes with an additional page which demonstrates how to create multiple pages within app navigation using"," ",e.jsx(L,{url:"https://shopify.dev/docs/apps/tools/app-bridge",target:"_blank",removeUnderline:!0,children:"App Bridge"}),"."]}),e.jsxs(d,{as:"p",variant:"bodyMd",children:["To create your own page and have it show up in the app navigation, add a page inside ",e.jsx(m,{children:"app/routes"}),", and a link to it in the ",e.jsx(m,{children:"<NavMenu>"})," component found in ",e.jsx(m,{children:"app/routes/app.jsx"}),"."]})]})})}),e.jsx(c.Section,{variant:"oneThird",children:e.jsx(v,{children:e.jsxs(b,{gap:"200",children:[e.jsx(d,{as:"h2",variant:"headingMd",children:"Resources"}),e.jsx(u,{children:e.jsx(u.Item,{children:e.jsx(L,{url:"https://shopify.dev/docs/apps/design-guidelines/navigation#app-nav",target:"_blank",removeUnderline:!0,children:"App nav best practices"})})})]})})})]})]})}function m({children:a}){return e.jsx(I,{as:"span",padding:"025",paddingInlineStart:"100",paddingInlineEnd:"100",background:"bg-surface-active",borderWidth:"025",borderColor:"border",borderRadius:"100",children:e.jsx("code",{children:a})})}export{_ as default};
