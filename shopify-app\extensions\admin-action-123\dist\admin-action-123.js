(()=>{var IR=Object.create;var jd=Object.defineProperty,$R=Object.defineProperties,eT=Object.getOwnPropertyDescriptor,tT=Object.getOwnPropertyDescriptors,nT=Object.getOwnPropertyNames,ls=Object.getOwnPropertySymbols,aT=Object.getPrototypeOf,Fd=Object.prototype.hasOwnProperty,Yh=Object.prototype.propertyIsEnumerable;var wh=(v,c,y)=>c in v?jd(v,c,{enumerable:!0,configurable:!0,writable:!0,value:y}):v[c]=y,Za=(v,c)=>{for(var y in c||(c={}))Fd.call(c,y)&&wh(v,y,c[y]);if(ls)for(var y of ls(c))Yh.call(c,y)&&wh(v,y,c[y]);return v},qh=(v,c)=>$R(v,tT(c));var os=(v,c)=>{var y={};for(var T in v)Fd.call(v,T)&&c.indexOf(T)<0&&(y[T]=v[T]);if(v!=null&&ls)for(var T of ls(v))c.indexOf(T)<0&&Yh.call(v,T)&&(y[T]=v[T]);return y};var jr=(v,c)=>()=>(c||v((c={exports:{}}).exports,c),c.exports);var rT=(v,c,y,T)=>{if(c&&typeof c=="object"||typeof c=="function")for(let g of nT(c))!Fd.call(v,g)&&g!==y&&jd(v,g,{get:()=>c[g],enumerable:!(T=eT(c,g))||T.enumerable});return v};var Kn=(v,c,y)=>(y=v!=null?IR(aT(v)):{},rT(c||!v||!v.__esModule?jd(y,"default",{value:v,enumerable:!0}):y,v));var Ki=(v,c,y)=>new Promise((T,g)=>{var M=H=>{try{B(y.next(H))}catch(h){g(h)}},q=H=>{try{B(y.throw(H))}catch(h){g(h)}},B=H=>H.done?T(H.value):Promise.resolve(H.value).then(M,q);B((y=y.apply(v,c)).next())});var Qh=jr((Le,ss)=>{"use strict";(function(){"use strict";typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error);var v="18.3.1",c=Symbol.for("react.element"),y=Symbol.for("react.portal"),T=Symbol.for("react.fragment"),g=Symbol.for("react.strict_mode"),M=Symbol.for("react.profiler"),q=Symbol.for("react.provider"),B=Symbol.for("react.context"),H=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),j=Symbol.for("react.suspense_list"),Q=Symbol.for("react.memo"),pe=Symbol.for("react.lazy"),et=Symbol.for("react.offscreen"),Ye=Symbol.iterator,qe="@@iterator";function Xe(l){if(l===null||typeof l!="object")return null;var p=Ye&&l[Ye]||l[qe];return typeof p=="function"?p:null}var Re={current:null},xe={transition:null},ft={current:null,isBatchingLegacy:!1,didScheduleLegacyUpdate:!1},gt={current:null},zn={},Ne=null;function ye(l){Ne=l}zn.setExtraStackFrame=function(l){Ne=l},zn.getCurrentStack=null,zn.getStackAddendum=function(){var l="";Ne&&(l+=Ne);var p=zn.getCurrentStack;return p&&(l+=p()||""),l};var yn=!1,se=!1,Ue=!1,$=!1,ge=!1,Te={ReactCurrentDispatcher:Re,ReactCurrentBatchConfig:xe,ReactCurrentOwner:gt};Te.ReactDebugCurrentFrame=zn,Te.ReactCurrentActQueue=ft;function dt(l){{for(var p=arguments.length,R=new Array(p>1?p-1:0),x=1;x<p;x++)R[x-1]=arguments[x];Rt("warn",l,R)}}function ue(l){{for(var p=arguments.length,R=new Array(p>1?p-1:0),x=1;x<p;x++)R[x-1]=arguments[x];Rt("error",l,R)}}function Rt(l,p,R){{var x=Te.ReactDebugCurrentFrame,F=x.getStackAddendum();F!==""&&(p+="%s",R=R.concat([F]));var ie=R.map(function(Z){return String(Z)});ie.unshift("Warning: "+p),Function.prototype.apply.call(console[l],console,ie)}}var Me={};function Tt(l,p){{var R=l.constructor,x=R&&(R.displayName||R.name)||"ReactClass",F=x+"."+p;if(Me[F])return;ue("Can't call %s on a component that is not yet mounted. This is a no-op, but it might indicate a bug in your application. Instead, assign to `this.state` directly or define a `state = {};` class property with the desired state in the %s component.",p,x),Me[F]=!0}}var Fe={isMounted:function(l){return!1},enqueueForceUpdate:function(l,p,R){Tt(l,"forceUpdate")},enqueueReplaceState:function(l,p,R,x){Tt(l,"replaceState")},enqueueSetState:function(l,p,R,x){Tt(l,"setState")}},st=Object.assign,Ae={};Object.freeze(Ae);function Ct(l,p,R){this.props=l,this.context=p,this.refs=Ae,this.updater=R||Fe}Ct.prototype.isReactComponent={},Ct.prototype.setState=function(l,p){if(typeof l!="object"&&typeof l!="function"&&l!=null)throw new Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,l,p,"setState")},Ct.prototype.forceUpdate=function(l){this.updater.enqueueForceUpdate(this,l,"forceUpdate")};{var Zt={isMounted:["isMounted","Instead, make sure to clean up subscriptions and pending requests in componentWillUnmount to prevent memory leaks."],replaceState:["replaceState","Refactor your code to use setState instead (see https://github.com/facebook/react/issues/3236)."]},Xn=function(l,p){Object.defineProperty(Ct.prototype,l,{get:function(){dt("%s(...) is deprecated in plain JavaScript React classes. %s",p[0],p[1])}})};for(var St in Zt)Zt.hasOwnProperty(St)&&Xn(St,Zt[St])}function gn(){}gn.prototype=Ct.prototype;function rt(l,p,R){this.props=l,this.context=p,this.refs=Ae,this.updater=R||Fe}var kt=rt.prototype=new gn;kt.constructor=rt,st(kt,Ct.prototype),kt.isPureReactComponent=!0;function ia(){var l={current:null};return Object.seal(l),l}var Jn=Array.isArray;function Dn(l){return Jn(l)}function _n(l){{var p=typeof Symbol=="function"&&Symbol.toStringTag,R=p&&l[Symbol.toStringTag]||l.constructor.name||"Object";return R}}function Zn(l){try{return Hn(l),!1}catch(p){return!0}}function Hn(l){return""+l}function Bt(l){if(Zn(l))return ue("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",_n(l)),Hn(l)}function Sn(l,p,R){var x=l.displayName;if(x)return x;var F=p.displayName||p.name||"";return F!==""?R+"("+F+")":R}function Ln(l){return l.displayName||"Context"}function bt(l){if(l==null)return null;if(typeof l.tag=="number"&&ue("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof l=="function")return l.displayName||l.name||null;if(typeof l=="string")return l;switch(l){case T:return"Fragment";case y:return"Portal";case M:return"Profiler";case g:return"StrictMode";case h:return"Suspense";case j:return"SuspenseList"}if(typeof l=="object")switch(l.$$typeof){case B:var p=l;return Ln(p)+".Consumer";case q:var R=l;return Ln(R._context)+".Provider";case H:return Sn(l,l.render,"ForwardRef");case Q:var x=l.displayName||null;return x!==null?x:bt(l.type)||"Memo";case pe:{var F=l,ie=F._payload,Z=F._init;try{return bt(Z(ie))}catch(he){return null}}}return null}var Vt=Object.prototype.hasOwnProperty,ua={key:!0,ref:!0,__self:!0,__source:!0},kn,it,jn;jn={};function Ia(l){if(Vt.call(l,"ref")){var p=Object.getOwnPropertyDescriptor(l,"ref").get;if(p&&p.isReactWarning)return!1}return l.ref!==void 0}function Da(l){if(Vt.call(l,"key")){var p=Object.getOwnPropertyDescriptor(l,"key").get;if(p&&p.isReactWarning)return!1}return l.key!==void 0}function _a(l,p){var R=function(){kn||(kn=!0,ue("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",p))};R.isReactWarning=!0,Object.defineProperty(l,"key",{get:R,configurable:!0})}function Fn(l,p){var R=function(){it||(it=!0,ue("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",p))};R.isReactWarning=!0,Object.defineProperty(l,"ref",{get:R,configurable:!0})}function vr(l){if(typeof l.ref=="string"&&gt.current&&l.__self&&gt.current.stateNode!==l.__self){var p=bt(gt.current.type);jn[p]||(ue('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',p,l.ref),jn[p]=!0)}}var la=function(l,p,R,x,F,ie,Z){var he={$$typeof:c,type:l,key:p,ref:R,props:Z,_owner:ie};return he._store={},Object.defineProperty(he._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(he,"_self",{configurable:!1,enumerable:!1,writable:!1,value:x}),Object.defineProperty(he,"_source",{configurable:!1,enumerable:!1,writable:!1,value:F}),Object.freeze&&(Object.freeze(he.props),Object.freeze(he)),he};function pr(l,p,R){var x,F={},ie=null,Z=null,he=null,Oe=null;if(p!=null){Ia(p)&&(Z=p.ref,vr(p)),Da(p)&&(Bt(p.key),ie=""+p.key),he=p.__self===void 0?null:p.__self,Oe=p.__source===void 0?null:p.__source;for(x in p)Vt.call(p,x)&&!ua.hasOwnProperty(x)&&(F[x]=p[x])}var Ze=arguments.length-2;if(Ze===1)F.children=R;else if(Ze>1){for(var tt=Array(Ze),lt=0;lt<Ze;lt++)tt[lt]=arguments[lt+2];Object.freeze&&Object.freeze(tt),F.children=tt}if(l&&l.defaultProps){var ct=l.defaultProps;for(x in ct)F[x]===void 0&&(F[x]=ct[x])}if(ie||Z){var ht=typeof l=="function"?l.displayName||l.name||"Unknown":l;ie&&_a(F,ht),Z&&Fn(F,ht)}return la(l,ie,Z,he,Oe,gt.current,F)}function w(l,p){var R=la(l.type,p,l.ref,l._self,l._source,l._owner,l.props);return R}function ne(l,p,R){if(l==null)throw new Error("React.cloneElement(...): The argument must be a React element, but you passed "+l+".");var x,F=st({},l.props),ie=l.key,Z=l.ref,he=l._self,Oe=l._source,Ze=l._owner;if(p!=null){Ia(p)&&(Z=p.ref,Ze=gt.current),Da(p)&&(Bt(p.key),ie=""+p.key);var tt;l.type&&l.type.defaultProps&&(tt=l.type.defaultProps);for(x in p)Vt.call(p,x)&&!ua.hasOwnProperty(x)&&(p[x]===void 0&&tt!==void 0?F[x]=tt[x]:F[x]=p[x])}var lt=arguments.length-2;if(lt===1)F.children=R;else if(lt>1){for(var ct=Array(lt),ht=0;ht<lt;ht++)ct[ht]=arguments[ht+2];F.children=ct}return la(l.type,ie,Z,he,Oe,Ze,F)}function me(l){return typeof l=="object"&&l!==null&&l.$$typeof===c}var ee=".",ut=":";function wt(l){var p=/[=:]/g,R={"=":"=0",":":"=2"},x=l.replace(p,function(F){return R[F]});return"$"+x}var W=!1,X=/\/+/g;function $e(l){return l.replace(X,"$&/")}function Qe(l,p){return typeof l=="object"&&l!==null&&l.key!=null?(Bt(l.key),wt(""+l.key)):p.toString(36)}function ce(l,p,R,x,F){var ie=typeof l;(ie==="undefined"||ie==="boolean")&&(l=null);var Z=!1;if(l===null)Z=!0;else switch(ie){case"string":case"number":Z=!0;break;case"object":switch(l.$$typeof){case c:case y:Z=!0}}if(Z){var he=l,Oe=F(he),Ze=x===""?ee+Qe(he,0):x;if(Dn(Oe)){var tt="";Ze!=null&&(tt=$e(Ze)+"/"),ce(Oe,p,tt,"",function(Ls){return Ls})}else Oe!=null&&(me(Oe)&&(Oe.key&&(!he||he.key!==Oe.key)&&Bt(Oe.key),Oe=w(Oe,R+(Oe.key&&(!he||he.key!==Oe.key)?$e(""+Oe.key)+"/":"")+Ze)),p.push(Oe));return 1}var lt,ct,ht=0,Dt=x===""?ee:x+ut;if(Dn(l))for(var Er=0;Er<l.length;Er++)lt=l[Er],ct=Dt+Qe(lt,Er),ht+=ce(lt,p,R,ct,F);else{var cu=Xe(l);if(typeof cu=="function"){var Dl=l;cu===Dl.entries&&(W||dt("Using Maps as children is not supported. Use an array of keyed ReactElements instead."),W=!0);for(var pi=cu.call(Dl),_l,Hs=0;!(_l=pi.next()).done;)lt=_l.value,ct=Dt+Qe(lt,Hs++),ht+=ce(lt,p,R,ct,F)}else if(ie==="object"){var Ol=String(l);throw new Error("Objects are not valid as a React child (found: "+(Ol==="[object Object]"?"object with keys {"+Object.keys(l).join(", ")+"}":Ol)+"). If you meant to render a collection of children, use an array instead.")}}return ht}function Wt(l,p,R){if(l==null)return l;var x=[],F=0;return ce(l,x,"","",function(ie){return p.call(R,ie,F++)}),x}function oa(l){var p=0;return Wt(l,function(){p++}),p}function mr(l,p,R){Wt(l,function(){p.apply(this,arguments)},R)}function ze(l){return Wt(l,function(p){return p})||[]}function On(l){if(!me(l))throw new Error("React.Children.only expected to receive a single React element child.");return l}function Wn(l){var p={$$typeof:B,_currentValue:l,_currentValue2:l,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null};p.Provider={$$typeof:q,_context:p};var R=!1,x=!1,F=!1;{var ie={$$typeof:B,_context:p};Object.defineProperties(ie,{Provider:{get:function(){return x||(x=!0,ue("Rendering <Context.Consumer.Provider> is not supported and will be removed in a future major release. Did you mean to render <Context.Provider> instead?")),p.Provider},set:function(Z){p.Provider=Z}},_currentValue:{get:function(){return p._currentValue},set:function(Z){p._currentValue=Z}},_currentValue2:{get:function(){return p._currentValue2},set:function(Z){p._currentValue2=Z}},_threadCount:{get:function(){return p._threadCount},set:function(Z){p._threadCount=Z}},Consumer:{get:function(){return R||(R=!0,ue("Rendering <Context.Consumer.Consumer> is not supported and will be removed in a future major release. Did you mean to render <Context.Consumer> instead?")),p.Consumer}},displayName:{get:function(){return p.displayName},set:function(Z){F||(dt("Setting `displayName` on Context.Consumer has no effect. You should set it directly on the context with Context.displayName = '%s'.",Z),F=!0)}}}),p.Consumer=ie}return p._currentRenderer=null,p._currentRenderer2=null,p}var sn=-1,Yt=0,Nn=1,sa=2;function $a(l){if(l._status===sn){var p=l._result,R=p();if(R.then(function(ie){if(l._status===Yt||l._status===sn){var Z=l;Z._status=Nn,Z._result=ie}},function(ie){if(l._status===Yt||l._status===sn){var Z=l;Z._status=sa,Z._result=ie}}),l._status===sn){var x=l;x._status=Yt,x._result=R}}if(l._status===Nn){var F=l._result;return F===void 0&&ue(`lazy: Expected the result of a dynamic import() call. Instead received: %s

Your code should look like: 
  const MyComponent = lazy(() => import('./MyComponent'))

Did you accidentally put curly braces around the import?`,F),"default"in F||ue(`lazy: Expected the result of a dynamic import() call. Instead received: %s

Your code should look like: 
  const MyComponent = lazy(() => import('./MyComponent'))`,F),F.default}else throw l._result}function oi(l){var p={_status:sn,_result:l},R={$$typeof:pe,_payload:p,_init:$a};{var x,F;Object.defineProperties(R,{defaultProps:{configurable:!0,get:function(){return x},set:function(ie){ue("React.lazy(...): It is not supported to assign `defaultProps` to a lazy component import. Either specify them where the component is defined, or create a wrapping component around it."),x=ie,Object.defineProperty(R,"defaultProps",{enumerable:!0})}},propTypes:{configurable:!0,get:function(){return F},set:function(ie){ue("React.lazy(...): It is not supported to assign `propTypes` to a lazy component import. Either specify them where the component is defined, or create a wrapping component around it."),F=ie,Object.defineProperty(R,"propTypes",{enumerable:!0})}}})}return R}function $i(l){l!=null&&l.$$typeof===Q?ue("forwardRef requires a render function but received a `memo` component. Instead of forwardRef(memo(...)), use memo(forwardRef(...))."):typeof l!="function"?ue("forwardRef requires a render function but was given %s.",l===null?"null":typeof l):l.length!==0&&l.length!==2&&ue("forwardRef render functions accept exactly two parameters: props and ref. %s",l.length===1?"Did you forget to use the ref parameter?":"Any additional parameter will be undefined."),l!=null&&(l.defaultProps!=null||l.propTypes!=null)&&ue("forwardRef render functions do not support propTypes or defaultProps. Did you accidentally pass a React component?");var p={$$typeof:H,render:l};{var R;Object.defineProperty(p,"displayName",{enumerable:!1,configurable:!0,get:function(){return R},set:function(x){R=x,!l.name&&!l.displayName&&(l.displayName=x)}})}return p}var Oa;Oa=Symbol.for("react.module.reference");function xt(l){return!!(typeof l=="string"||typeof l=="function"||l===T||l===M||ge||l===g||l===h||l===j||$||l===et||yn||se||Ue||typeof l=="object"&&l!==null&&(l.$$typeof===pe||l.$$typeof===Q||l.$$typeof===q||l.$$typeof===B||l.$$typeof===H||l.$$typeof===Oa||l.getModuleId!==void 0))}function Vr(l,p){xt(l)||ue("memo: The first argument must be a component. Instead received: %s",l===null?"null":typeof l);var R={$$typeof:Q,type:l,compare:p===void 0?null:p};{var x;Object.defineProperty(R,"displayName",{enumerable:!1,configurable:!0,get:function(){return x},set:function(F){x=F,!l.name&&!l.displayName&&(l.displayName=F)}})}return R}function d(){var l=Re.current;return l===null&&ue(`Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.`),l}function z(l){var p=d();if(l._context!==void 0){var R=l._context;R.Consumer===l?ue("Calling useContext(Context.Consumer) is not supported, may cause bugs, and will be removed in a future major release. Did you mean to call useContext(Context) instead?"):R.Provider===l&&ue("Calling useContext(Context.Provider) is not supported. Did you mean to call useContext(Context) instead?")}return p.useContext(l)}function V(l){var p=d();return p.useState(l)}function re(l,p,R){var x=d();return x.useReducer(l,p,R)}function be(l){var p=d();return p.useRef(l)}function Ve(l,p){var R=d();return R.useEffect(l,p)}function De(l,p){var R=d();return R.useInsertionEffect(l,p)}function Se(l,p){var R=d();return R.useLayoutEffect(l,p)}function vt(l,p){var R=d();return R.useCallback(l,p)}function Je(l,p){var R=d();return R.useMemo(l,p)}function Pe(l,p,R){var x=d();return x.useImperativeHandle(l,p,R)}function cn(l,p){{var R=d();return R.useDebugValue(l,p)}}function Bn(){var l=d();return l.useTransition()}function ca(l){var p=d();return p.useDeferredValue(l)}function qt(){var l=d();return l.useId()}function hr(l,p,R){var x=d();return x.useSyncExternalStore(l,p,R)}var Na=0,yr,eu,vl,tu,pl,fn,si;function ml(){}ml.__reactDisabledLog=!0;function Es(){{if(Na===0){yr=console.log,eu=console.info,vl=console.warn,tu=console.error,pl=console.group,fn=console.groupCollapsed,si=console.groupEnd;var l={configurable:!0,enumerable:!0,value:ml,writable:!0};Object.defineProperties(console,{info:l,log:l,warn:l,error:l,group:l,groupCollapsed:l,groupEnd:l})}Na++}}function Rs(){{if(Na--,Na===0){var l={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:st({},l,{value:yr}),info:st({},l,{value:eu}),warn:st({},l,{value:vl}),error:st({},l,{value:tu}),group:st({},l,{value:pl}),groupCollapsed:st({},l,{value:fn}),groupEnd:st({},l,{value:si})})}Na<0&&ue("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var nu=Te.ReactCurrentDispatcher,au;function gr(l,p,R){{if(au===void 0)try{throw Error()}catch(F){var x=F.stack.trim().match(/\n( *(at )?)/);au=x&&x[1]||""}return`
`+au+l}}var ci=!1,wr;{var ru=typeof WeakMap=="function"?WeakMap:Map;wr=new ru}function iu(l,p){if(!l||ci)return"";{var R=wr.get(l);if(R!==void 0)return R}var x;ci=!0;var F=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var ie;ie=nu.current,nu.current=null,Es();try{if(p){var Z=function(){throw Error()};if(Object.defineProperty(Z.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Z,[])}catch(Dt){x=Dt}Reflect.construct(l,[],Z)}else{try{Z.call()}catch(Dt){x=Dt}l.call(Z.prototype)}}else{try{throw Error()}catch(Dt){x=Dt}l()}}catch(Dt){if(Dt&&x&&typeof Dt.stack=="string"){for(var he=Dt.stack.split(`
`),Oe=x.stack.split(`
`),Ze=he.length-1,tt=Oe.length-1;Ze>=1&&tt>=0&&he[Ze]!==Oe[tt];)tt--;for(;Ze>=1&&tt>=0;Ze--,tt--)if(he[Ze]!==Oe[tt]){if(Ze!==1||tt!==1)do if(Ze--,tt--,tt<0||he[Ze]!==Oe[tt]){var lt=`
`+he[Ze].replace(" at new "," at ");return l.displayName&&lt.includes("<anonymous>")&&(lt=lt.replace("<anonymous>",l.displayName)),typeof l=="function"&&wr.set(l,lt),lt}while(Ze>=1&&tt>=0);break}}}finally{ci=!1,nu.current=ie,Rs(),Error.prepareStackTrace=F}var ct=l?l.displayName||l.name:"",ht=ct?gr(ct):"";return typeof l=="function"&&wr.set(l,ht),ht}function hl(l,p,R){return iu(l,!1)}function Ts(l){var p=l.prototype;return!!(p&&p.isReactComponent)}function Sr(l,p,R){if(l==null)return"";if(typeof l=="function")return iu(l,Ts(l));if(typeof l=="string")return gr(l);switch(l){case h:return gr("Suspense");case j:return gr("SuspenseList")}if(typeof l=="object")switch(l.$$typeof){case H:return hl(l.render);case Q:return Sr(l.type,p,R);case pe:{var x=l,F=x._payload,ie=x._init;try{return Sr(ie(F),p,R)}catch(Z){}}}return""}var Yr={},yl=Te.ReactDebugCurrentFrame;function Qt(l){if(l){var p=l._owner,R=Sr(l.type,l._source,p?p.type:null);yl.setExtraStackFrame(R)}else yl.setExtraStackFrame(null)}function fi(l,p,R,x,F){{var ie=Function.call.bind(Vt);for(var Z in l)if(ie(l,Z)){var he=void 0;try{if(typeof l[Z]!="function"){var Oe=Error((x||"React class")+": "+R+" type `"+Z+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof l[Z]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw Oe.name="Invariant Violation",Oe}he=l[Z](p,Z,x,R,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(Ze){he=Ze}he&&!(he instanceof Error)&&(Qt(F),ue("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",x||"React class",R,Z,typeof he),Qt(null)),he instanceof Error&&!(he.message in Yr)&&(Yr[he.message]=!0,Qt(F),ue("Failed %s type: %s",R,he.message),Qt(null))}}}function Mt(l){if(l){var p=l._owner,R=Sr(l.type,l._source,p?p.type:null);ye(R)}else ye(null)}var uu;uu=!1;function Cs(){if(gt.current){var l=bt(gt.current.type);if(l)return`

Check the render method of \``+l+"`."}return""}function tv(l){if(l!==void 0){var p=l.fileName.replace(/^.*[\\\/]/,""),R=l.lineNumber;return`

Check your code at `+p+":"+R+"."}return""}function xs(l){return l!=null?tv(l.__source):""}var Ds={};function nv(l){var p=Cs();if(!p){var R=typeof l=="string"?l:l.displayName||l.name;R&&(p=`

Check the top-level render call using <`+R+">.")}return p}function gl(l,p){if(!(!l._store||l._store.validated||l.key!=null)){l._store.validated=!0;var R=nv(p);if(!Ds[R]){Ds[R]=!0;var x="";l&&l._owner&&l._owner!==gt.current&&(x=" It was passed a child from "+bt(l._owner.type)+"."),Mt(l),ue('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',R,x),Mt(null)}}}function Sl(l,p){if(typeof l=="object"){if(Dn(l))for(var R=0;R<l.length;R++){var x=l[R];me(x)&&gl(x,p)}else if(me(l))l._store&&(l._store.validated=!0);else if(l){var F=Xe(l);if(typeof F=="function"&&F!==l.entries)for(var ie=F.call(l),Z;!(Z=ie.next()).done;)me(Z.value)&&gl(Z.value,p)}}}function bl(l){{var p=l.type;if(p==null||typeof p=="string")return;var R;if(typeof p=="function")R=p.propTypes;else if(typeof p=="object"&&(p.$$typeof===H||p.$$typeof===Q))R=p.propTypes;else return;if(R){var x=bt(p);fi(R,l.props,"prop",x,l)}else if(p.PropTypes!==void 0&&!uu){uu=!0;var F=bt(p);ue("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",F||"Unknown")}typeof p.getDefaultProps=="function"&&!p.getDefaultProps.isReactClassApproved&&ue("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function _s(l){{for(var p=Object.keys(l.props),R=0;R<p.length;R++){var x=p[R];if(x!=="children"&&x!=="key"){Mt(l),ue("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",x),Mt(null);break}}l.ref!==null&&(Mt(l),ue("Invalid attribute `ref` supplied to `React.Fragment`."),Mt(null))}}function er(l,p,R){var x=xt(l);if(!x){var F="";(l===void 0||typeof l=="object"&&l!==null&&Object.keys(l).length===0)&&(F+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var ie=xs(p);ie?F+=ie:F+=Cs();var Z;l===null?Z="null":Dn(l)?Z="array":l!==void 0&&l.$$typeof===c?(Z="<"+(bt(l.type)||"Unknown")+" />",F=" Did you accidentally export a JSX literal instead of a component?"):Z=typeof l,ue("React.createElement: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",Z,F)}var he=pr.apply(this,arguments);if(he==null)return he;if(x)for(var Oe=2;Oe<arguments.length;Oe++)Sl(arguments[Oe],l);return l===T?_s(he):bl(he),he}var El=!1;function Os(l){var p=er.bind(null,l);return p.type=l,El||(El=!0,dt("React.createFactory() is deprecated and will be removed in a future major release. Consider using JSX or use React.createElement() directly instead.")),Object.defineProperty(p,"type",{enumerable:!1,get:function(){return dt("Factory.type is deprecated. Access the class directly before passing it to createFactory."),Object.defineProperty(this,"type",{value:l}),l}}),p}function Ns(l,p,R){for(var x=ne.apply(this,arguments),F=2;F<arguments.length;F++)Sl(arguments[F],x.type);return bl(x),x}function qr(l,p){var R=xe.transition;xe.transition={};var x=xe.transition;xe.transition._updatedFibers=new Set;try{l()}finally{if(xe.transition=R,R===null&&x._updatedFibers){var F=x._updatedFibers.size;F>10&&dt("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table."),x._updatedFibers.clear()}}}var Rl=!1,di=null;function Us(l){if(di===null)try{var p=("require"+Math.random()).slice(0,7),R=ss&&ss[p];di=R.call(ss,"timers").setImmediate}catch(x){di=function(F){Rl===!1&&(Rl=!0,typeof MessageChannel=="undefined"&&ue("This browser does not have a MessageChannel implementation, so enqueuing tasks via await act(async () => ...) will fail. Please file an issue at https://github.com/facebook/react/issues if you encounter this warning."));var ie=new MessageChannel;ie.port1.onmessage=F,ie.port2.postMessage(void 0)}}return di(l)}var br=0,Tl=!1;function Cl(l){{var p=br;br++,ft.current===null&&(ft.current=[]);var R=ft.isBatchingLegacy,x;try{if(ft.isBatchingLegacy=!0,x=l(),!R&&ft.didScheduleLegacyUpdate){var F=ft.current;F!==null&&(ft.didScheduleLegacyUpdate=!1,su(F))}}catch(ct){throw vi(p),ct}finally{ft.isBatchingLegacy=R}if(x!==null&&typeof x=="object"&&typeof x.then=="function"){var ie=x,Z=!1,he={then:function(ct,ht){Z=!0,ie.then(function(Dt){vi(p),br===0?lu(Dt,ct,ht):ct(Dt)},function(Dt){vi(p),ht(Dt)})}};return!Tl&&typeof Promise!="undefined"&&Promise.resolve().then(function(){}).then(function(){Z||(Tl=!0,ue("You called act(async () => ...) without await. This could lead to unexpected testing behaviour, interleaving multiple act calls and mixing their scopes. You should - await act(async () => ...);"))}),he}else{var Oe=x;if(vi(p),br===0){var Ze=ft.current;Ze!==null&&(su(Ze),ft.current=null);var tt={then:function(ct,ht){ft.current===null?(ft.current=[],lu(Oe,ct,ht)):ct(Oe)}};return tt}else{var lt={then:function(ct,ht){ct(Oe)}};return lt}}}}function vi(l){l!==br-1&&ue("You seem to have overlapping act() calls, this is not supported. Be sure to await previous act() calls before making a new one. "),br=l}function lu(l,p,R){{var x=ft.current;if(x!==null)try{su(x),Us(function(){x.length===0?(ft.current=null,p(l)):lu(l,p,R)})}catch(F){R(F)}else p(l)}}var ou=!1;function su(l){if(!ou){ou=!0;var p=0;try{for(;p<l.length;p++){var R=l[p];do R=R(!0);while(R!==null)}l.length=0}catch(x){throw l=l.slice(p+1),x}finally{ou=!1}}}var Ms=er,As=Ns,xl=Os,zs={map:Wt,forEach:mr,count:oa,toArray:ze,only:On};Le.Children=zs,Le.Component=Ct,Le.Fragment=T,Le.Profiler=M,Le.PureComponent=rt,Le.StrictMode=g,Le.Suspense=h,Le.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Te,Le.act=Cl,Le.cloneElement=As,Le.createContext=Wn,Le.createElement=Ms,Le.createFactory=xl,Le.createRef=ia,Le.forwardRef=$i,Le.isValidElement=me,Le.lazy=oi,Le.memo=Vr,Le.startTransition=qr,Le.unstable_act=Cl,Le.useCallback=vt,Le.useContext=z,Le.useDebugValue=cn,Le.useDeferredValue=ca,Le.useEffect=Ve,Le.useId=qt,Le.useImperativeHandle=Pe,Le.useInsertionEffect=De,Le.useLayoutEffect=Se,Le.useMemo=Je,Le.useReducer=re,Le.useRef=be,Le.useState=V,Le.useSyncExternalStore=hr,Le.useTransition=Bn,Le.version=v,typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error)})()});var ka=jr((TT,Ph)=>{"use strict";Ph.exports=Qh()});var ty=jr(at=>{"use strict";(function(){"use strict";typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error);var v=!1,c=!1,y=5;function T(w,ne){var me=w.length;w.push(ne),q(w,ne,me)}function g(w){return w.length===0?null:w[0]}function M(w){if(w.length===0)return null;var ne=w[0],me=w.pop();return me!==ne&&(w[0]=me,B(w,me,0)),ne}function q(w,ne,me){for(var ee=me;ee>0;){var ut=ee-1>>>1,wt=w[ut];if(H(wt,ne)>0)w[ut]=ne,w[ee]=wt,ee=ut;else return}}function B(w,ne,me){for(var ee=me,ut=w.length,wt=ut>>>1;ee<wt;){var W=(ee+1)*2-1,X=w[W],$e=W+1,Qe=w[$e];if(H(X,ne)<0)$e<ut&&H(Qe,X)<0?(w[ee]=Qe,w[$e]=ne,ee=$e):(w[ee]=X,w[W]=ne,ee=W);else if($e<ut&&H(Qe,ne)<0)w[ee]=Qe,w[$e]=ne,ee=$e;else return}}function H(w,ne){var me=w.sortIndex-ne.sortIndex;return me!==0?me:w.id-ne.id}var h=1,j=2,Q=3,pe=4,et=5;function Ye(w,ne){}var qe=typeof performance=="object"&&typeof performance.now=="function";if(qe){var Xe=performance;at.unstable_now=function(){return Xe.now()}}else{var Re=Date,xe=Re.now();at.unstable_now=function(){return Re.now()-xe}}var ft=1073741823,gt=-1,zn=250,Ne=5e3,ye=1e4,yn=ft,se=[],Ue=[],$=1,ge=null,Te=Q,dt=!1,ue=!1,Rt=!1,Me=typeof setTimeout=="function"?setTimeout:null,Tt=typeof clearTimeout=="function"?clearTimeout:null,Fe=typeof setImmediate!="undefined"?setImmediate:null,st=typeof navigator!="undefined"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0?navigator.scheduling.isInputPending.bind(navigator.scheduling):null;function Ae(w){for(var ne=g(Ue);ne!==null;){if(ne.callback===null)M(Ue);else if(ne.startTime<=w)M(Ue),ne.sortIndex=ne.expirationTime,T(se,ne);else return;ne=g(Ue)}}function Ct(w){if(Rt=!1,Ae(w),!ue)if(g(se)!==null)ue=!0,_a(Zt);else{var ne=g(Ue);ne!==null&&Fn(Ct,ne.startTime-w)}}function Zt(w,ne){ue=!1,Rt&&(Rt=!1,vr()),dt=!0;var me=Te;try{if(c)try{return Xn(w,ne)}catch(ut){if(ge!==null){var ee=at.unstable_now();ge.isQueued=!1}throw ut}else return Xn(w,ne)}finally{ge=null,Te=me,dt=!1}}function Xn(w,ne){var me=ne;for(Ae(me),ge=g(se);ge!==null&&!v&&!(ge.expirationTime>me&&(!w||Vt()));){var ee=ge.callback;if(typeof ee=="function"){ge.callback=null,Te=ge.priorityLevel;var ut=ge.expirationTime<=me,wt=ee(ut);me=at.unstable_now(),typeof wt=="function"?ge.callback=wt:ge===g(se)&&M(se),Ae(me)}else M(se);ge=g(se)}if(ge!==null)return!0;var W=g(Ue);return W!==null&&Fn(Ct,W.startTime-me),!1}function St(w,ne){switch(w){case h:case j:case Q:case pe:case et:break;default:w=Q}var me=Te;Te=w;try{return ne()}finally{Te=me}}function gn(w){var ne;switch(Te){case h:case j:case Q:ne=Q;break;default:ne=Te;break}var me=Te;Te=ne;try{return w()}finally{Te=me}}function rt(w){var ne=Te;return function(){var me=Te;Te=ne;try{return w.apply(this,arguments)}finally{Te=me}}}function kt(w,ne,me){var ee=at.unstable_now(),ut;if(typeof me=="object"&&me!==null){var wt=me.delay;typeof wt=="number"&&wt>0?ut=ee+wt:ut=ee}else ut=ee;var W;switch(w){case h:W=gt;break;case j:W=zn;break;case et:W=yn;break;case pe:W=ye;break;case Q:default:W=Ne;break}var X=ut+W,$e={id:$++,callback:ne,priorityLevel:w,startTime:ut,expirationTime:X,sortIndex:-1};return ut>ee?($e.sortIndex=ut,T(Ue,$e),g(se)===null&&$e===g(Ue)&&(Rt?vr():Rt=!0,Fn(Ct,ut-ee))):($e.sortIndex=X,T(se,$e),!ue&&!dt&&(ue=!0,_a(Zt))),$e}function ia(){}function Jn(){!ue&&!dt&&(ue=!0,_a(Zt))}function Dn(){return g(se)}function _n(w){w.callback=null}function Zn(){return Te}var Hn=!1,Bt=null,Sn=-1,Ln=y,bt=-1;function Vt(){var w=at.unstable_now()-bt;return!(w<Ln)}function ua(){}function kn(w){if(w<0||w>125){console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported");return}w>0?Ln=Math.floor(1e3/w):Ln=y}var it=function(){if(Bt!==null){var w=at.unstable_now();bt=w;var ne=!0,me=!0;try{me=Bt(ne,w)}finally{me?jn():(Hn=!1,Bt=null)}}else Hn=!1},jn;if(typeof Fe=="function")jn=function(){Fe(it)};else if(typeof MessageChannel!="undefined"){var Ia=new MessageChannel,Da=Ia.port2;Ia.port1.onmessage=it,jn=function(){Da.postMessage(null)}}else jn=function(){Me(it,0)};function _a(w){Bt=w,Hn||(Hn=!0,jn())}function Fn(w,ne){Sn=Me(function(){w(at.unstable_now())},ne)}function vr(){Tt(Sn),Sn=-1}var la=ua,pr=null;at.unstable_IdlePriority=et,at.unstable_ImmediatePriority=h,at.unstable_LowPriority=pe,at.unstable_NormalPriority=Q,at.unstable_Profiling=pr,at.unstable_UserBlockingPriority=j,at.unstable_cancelCallback=_n,at.unstable_continueExecution=Jn,at.unstable_forceFrameRate=kn,at.unstable_getCurrentPriorityLevel=Zn,at.unstable_getFirstCallbackNode=Dn,at.unstable_next=gn,at.unstable_pauseExecution=ia,at.unstable_requestPaint=la,at.unstable_runWithPriority=St,at.unstable_scheduleCallback=kt,at.unstable_shouldYield=Vt,at.unstable_wrapCallback=rt,typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error)})()});var ay=jr((sC,ny)=>{"use strict";ny.exports=ty()});var iy=jr((cC,ry)=>{"use strict";ry.exports=function(c){var y={},T=ka(),g=ay(),M=T.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,q=!1;function B(e){q=e}function H(e){if(!q){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];j("warn",e,n)}}function h(e){if(!q){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];j("error",e,n)}}function j(e,t,n){{var a=M.ReactDebugCurrentFrame,r=a.getStackAddendum();r!==""&&(t+="%s",n=n.concat([r]));var i=n.map(function(u){return String(u)});i.unshift("Warning: "+t),Function.prototype.apply.call(console[e],console,i)}}var Q=Object.assign;function pe(e){return e._reactInternals}function et(e,t){e._reactInternals=t}var Ye=!1,qe=!1,Xe=!1,Re=!1,xe=!1,ft=!0,gt=!0,zn=!0,Ne=0,ye=1,yn=2,se=3,Ue=4,$=5,ge=6,Te=7,dt=8,ue=9,Rt=10,Me=11,Tt=12,Fe=13,st=14,Ae=15,Ct=16,Zt=17,Xn=18,St=19,gn=21,rt=22,kt=23,ia=24,Jn=25,Dn=Symbol.for("react.element"),_n=Symbol.for("react.portal"),Zn=Symbol.for("react.fragment"),Hn=Symbol.for("react.strict_mode"),Bt=Symbol.for("react.profiler"),Sn=Symbol.for("react.provider"),Ln=Symbol.for("react.context"),bt=Symbol.for("react.forward_ref"),Vt=Symbol.for("react.suspense"),ua=Symbol.for("react.suspense_list"),kn=Symbol.for("react.memo"),it=Symbol.for("react.lazy"),jn=Symbol.for("react.scope"),Ia=Symbol.for("react.debug_trace_mode"),Da=Symbol.for("react.offscreen"),_a=Symbol.for("react.legacy_hidden"),Fn=Symbol.for("react.cache"),vr=Symbol.for("react.tracing_marker"),la=Symbol.iterator,pr="@@iterator";function w(e){if(e===null||typeof e!="object")return null;var t=la&&e[la]||e[pr];return typeof t=="function"?t:null}function ne(e,t,n){var a=e.displayName;if(a)return a;var r=t.displayName||t.name||"";return r!==""?n+"("+r+")":n}function me(e){return e.displayName||"Context"}function ee(e){if(e==null)return null;if(typeof e.tag=="number"&&h("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Zn:return"Fragment";case _n:return"Portal";case Bt:return"Profiler";case Hn:return"StrictMode";case Vt:return"Suspense";case ua:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Ln:var t=e;return me(t)+".Consumer";case Sn:var n=e;return me(n._context)+".Provider";case bt:return ne(e,e.render,"ForwardRef");case kn:var a=e.displayName||null;return a!==null?a:ee(e.type)||"Memo";case it:{var r=e,i=r._payload,u=r._init;try{return ee(u(i))}catch(o){return null}}}return null}function ut(e,t,n){var a=t.displayName||t.name||"";return e.displayName||(a!==""?n+"("+a+")":n)}function wt(e){return e.displayName||"Context"}function W(e){var t=e.tag,n=e.type;switch(t){case ia:return"Cache";case ue:var a=n;return wt(a)+".Consumer";case Rt:var r=n;return wt(r._context)+".Provider";case Xn:return"DehydratedFragment";case Me:return ut(n,n.render,"ForwardRef");case Te:return"Fragment";case $:return n;case Ue:return"Portal";case se:return"Root";case ge:return"Text";case Ct:return ee(n);case dt:return n===Hn?"StrictMode":"Mode";case rt:return"Offscreen";case Tt:return"Profiler";case gn:return"Scope";case Fe:return"Suspense";case St:return"SuspenseList";case Jn:return"TracingMarker";case ye:case Ne:case Zt:case yn:case st:case Ae:if(typeof n=="function")return n.displayName||n.name||null;if(typeof n=="string")return n;break}return null}var X=0,$e=1,Qe=2,ce=4,Wt=16,oa=32,mr=64,ze=128,On=256,Wn=512,sn=1024,Yt=2048,Nn=4096,sa=8192,$a=16384,oi=Yt|ce|mr|Wn|sn|$a,$i=32767,Oa=32768,xt=65536,Vr=131072,d=1048576,z=2097152,V=4194304,re=8388608,be=16777216,Ve=33554432,De=ce|sn|0,Se=Qe|ce|Wt|oa|Wn|Nn|sa,vt=ce|mr|Wn|sa,Je=Yt|Wt,Pe=V|re|z,cn=M.ReactCurrentOwner;function Bn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{var a=t;do t=a,(t.flags&(Qe|Nn))!==X&&(n=t.return),a=t.return;while(a)}return t.tag===se?n:null}function ca(e){return Bn(e)===e}function qt(e){{var t=cn.current;if(t!==null&&t.tag===ye){var n=t,a=n.stateNode;a._warnedAboutRefsInRender||h("%s is accessing isMounted inside its render() function. render() should be a pure function of props and state. It should never access something that requires stale data from the previous render, such as refs. Move this logic to componentDidMount and componentDidUpdate instead.",W(n)||"A component"),a._warnedAboutRefsInRender=!0}}var r=pe(e);return r?Bn(r)===r:!1}function hr(e){if(Bn(e)!==e)throw new Error("Unable to find node on an unmounted component.")}function Na(e){var t=e.alternate;if(!t){var n=Bn(e);if(n===null)throw new Error("Unable to find node on an unmounted component.");return n!==e?null:e}for(var a=e,r=t;;){var i=a.return;if(i===null)break;var u=i.alternate;if(u===null){var o=i.return;if(o!==null){a=r=o;continue}break}if(i.child===u.child){for(var s=i.child;s;){if(s===a)return hr(i),e;if(s===r)return hr(i),t;s=s.sibling}throw new Error("Unable to find node on an unmounted component.")}if(a.return!==r.return)a=i,r=u;else{for(var f=!1,m=i.child;m;){if(m===a){f=!0,a=i,r=u;break}if(m===r){f=!0,r=i,a=u;break}m=m.sibling}if(!f){for(m=u.child;m;){if(m===a){f=!0,a=u,r=i;break}if(m===r){f=!0,r=u,a=i;break}m=m.sibling}if(!f)throw new Error("Child was not found in either parent set. This indicates a bug in React related to the return pointer. Please file an issue.")}}if(a.alternate!==r)throw new Error("Return fibers should always be each others' alternates. This error is likely caused by a bug in React. Please file an issue.")}if(a.tag!==se)throw new Error("Unable to find node on an unmounted component.");return a.stateNode.current===a?e:t}function yr(e){var t=Na(e);return t!==null?eu(t):null}function eu(e){if(e.tag===$||e.tag===ge)return e;for(var t=e.child;t!==null;){var n=eu(t);if(n!==null)return n;t=t.sibling}return null}function vl(e){var t=Na(e);return t!==null?tu(t):null}function tu(e){if(e.tag===$||e.tag===ge)return e;for(var t=e.child;t!==null;){if(t.tag!==Ue){var n=tu(t);if(n!==null)return n}t=t.sibling}return null}var pl=Array.isArray;function fn(e){return pl(e)}var si=c.getPublicInstance,ml=c.getRootHostContext,Es=c.getChildHostContext,Rs=c.prepareForCommit,nu=c.resetAfterCommit,au=c.createInstance,gr=c.appendInitialChild,ci=c.finalizeInitialChildren,wr=c.prepareUpdate,ru=c.shouldSetTextContent,iu=c.createTextInstance,hl=c.scheduleTimeout,Ts=c.cancelTimeout,Sr=c.noTimeout,Yr=c.isPrimaryRenderer,yl=c.warnsIfNotActing,Qt=c.supportsMutation,fi=c.supportsPersistence,Mt=c.supportsHydration,uu=c.getInstanceFromNode,Cs=c.beforeActiveInstanceBlur,tv=c.afterActiveInstanceBlur,xs=c.preparePortalMount,Ds=c.prepareScopeUpdate,nv=c.getInstanceFromScope,gl=c.getCurrentEventPriority,Sl=c.detachDeletedInstance,bl=c.supportsMicrotasks,_s=c.scheduleMicrotask,er=c.supportsTestSelectors,El=c.findFiberRoot,Os=c.getBoundingRect,Ns=c.getTextContent,qr=c.isHiddenSubtree,Rl=c.matchAccessibilityRole,di=c.setFocusIfFocusable,Us=c.setupIntersectionObserver,br=c.appendChild,Tl=c.appendChildToContainer,Cl=c.commitTextUpdate,vi=c.commitMount,lu=c.commitUpdate,ou=c.insertBefore,su=c.insertInContainerBefore,Ms=c.removeChild,As=c.removeChildFromContainer,xl=c.resetTextContent,zs=c.hideInstance,l=c.hideTextInstance,p=c.unhideInstance,R=c.unhideTextInstance,x=c.clearContainer,F=c.cloneInstance,ie=c.createContainerChildSet,Z=c.appendChildToContainerChildSet,he=c.finalizeContainerChildren,Oe=c.replaceContainerChildren,Ze=c.cloneHiddenInstance,tt=c.cloneHiddenTextInstance,lt=c.canHydrateInstance,ct=c.canHydrateTextInstance,ht=c.canHydrateSuspenseInstance,Dt=c.isSuspenseInstancePending,Er=c.isSuspenseInstanceFallback,cu=c.getSuspenseInstanceFallbackErrorDetails,Dl=c.registerSuspenseInstanceRetry,pi=c.getNextHydratableSibling,_l=c.getFirstHydratableChild,Hs=c.getFirstHydratableChildWithinContainer,Ol=c.getFirstHydratableChildWithinSuspenseInstance,Ls=c.hydrateInstance,xy=c.hydrateTextInstance,Dy=c.hydrateSuspenseInstance,_y=c.getNextHydratableInstanceAfterSuspenseInstance,Oy=c.commitHydratedContainer,Ny=c.commitHydratedSuspenseInstance,Uy=c.clearSuspenseBoundary,My=c.clearSuspenseBoundaryFromContainer,Ay=c.shouldDeleteUnhydratedTailInstances,zy=c.didNotMatchHydratedContainerTextInstance,Hy=c.didNotMatchHydratedTextInstance,Ly=c.didNotHydrateInstanceWithinContainer,jy=c.didNotHydrateInstanceWithinSuspenseInstance,Fy=c.didNotHydrateInstance,By=c.didNotFindHydratableInstanceWithinContainer,Vy=c.didNotFindHydratableTextInstanceWithinContainer,wy=c.didNotFindHydratableSuspenseInstanceWithinContainer,Yy=c.didNotFindHydratableInstanceWithinSuspenseInstance,qy=c.didNotFindHydratableTextInstanceWithinSuspenseInstance,Qy=c.didNotFindHydratableSuspenseInstanceWithinSuspenseInstance,Py=c.didNotFindHydratableInstance,Gy=c.didNotFindHydratableTextInstance,Ky=c.didNotFindHydratableSuspenseInstance,Xy=c.errorHydratingContainer,fu=0,av,rv,iv,uv,lv,ov,sv;function cv(){}cv.__reactDisabledLog=!0;function Jy(){{if(fu===0){av=console.log,rv=console.info,iv=console.warn,uv=console.error,lv=console.group,ov=console.groupCollapsed,sv=console.groupEnd;var e={configurable:!0,enumerable:!0,value:cv,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}fu++}}function Zy(){{if(fu--,fu===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:Q({},e,{value:av}),info:Q({},e,{value:rv}),warn:Q({},e,{value:iv}),error:Q({},e,{value:uv}),group:Q({},e,{value:lv}),groupCollapsed:Q({},e,{value:ov}),groupEnd:Q({},e,{value:sv})})}fu<0&&h("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var js=M.ReactCurrentDispatcher,Fs;function Rr(e,t,n){{if(Fs===void 0)try{throw Error()}catch(r){var a=r.stack.trim().match(/\n( *(at )?)/);Fs=a&&a[1]||""}return`
`+Fs+e}}var Bs=!1,Nl;{var ky=typeof WeakMap=="function"?WeakMap:Map;Nl=new ky}function Vs(e,t){if(!e||Bs)return"";{var n=Nl.get(e);if(n!==void 0)return n}var a;Bs=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var i;i=js.current,js.current=null,Jy();try{if(t){var u=function(){throw Error()};if(Object.defineProperty(u.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(u,[])}catch(U){a=U}Reflect.construct(e,[],u)}else{try{u.call()}catch(U){a=U}e.call(u.prototype)}}else{try{throw Error()}catch(U){a=U}e()}}catch(U){if(U&&a&&typeof U.stack=="string"){for(var o=U.stack.split(`
`),s=a.stack.split(`
`),f=o.length-1,m=s.length-1;f>=1&&m>=0&&o[f]!==s[m];)m--;for(;f>=1&&m>=0;f--,m--)if(o[f]!==s[m]){if(f!==1||m!==1)do if(f--,m--,m<0||o[f]!==s[m]){var S=`
`+o[f].replace(" at new "," at ");return e.displayName&&S.includes("<anonymous>")&&(S=S.replace("<anonymous>",e.displayName)),typeof e=="function"&&Nl.set(e,S),S}while(f>=1&&m>=0);break}}}finally{Bs=!1,js.current=i,Zy(),Error.prepareStackTrace=r}var C=e?e.displayName||e.name:"",O=C?Rr(C):"";return typeof e=="function"&&Nl.set(e,O),O}function Wy(e,t,n){return Vs(e,!0)}function ws(e,t,n){return Vs(e,!1)}function Iy(e){var t=e.prototype;return!!(t&&t.isReactComponent)}function Ys(e,t,n){if(e==null)return"";if(typeof e=="function")return Vs(e,Iy(e));if(typeof e=="string")return Rr(e);switch(e){case Vt:return Rr("Suspense");case ua:return Rr("SuspenseList")}if(typeof e=="object")switch(e.$$typeof){case bt:return ws(e.render);case kn:return Ys(e.type,t,n);case it:{var a=e,r=a._payload,i=a._init;try{return Ys(i(r),t,n)}catch(u){}}}return""}var fv=Object.prototype.hasOwnProperty,dv={},vv=M.ReactDebugCurrentFrame;function Ul(e){if(e){var t=e._owner,n=Ys(e.type,e._source,t?t.type:null);vv.setExtraStackFrame(n)}else vv.setExtraStackFrame(null)}function fa(e,t,n,a,r){{var i=Function.call.bind(fv);for(var u in e)if(i(e,u)){var o=void 0;try{if(typeof e[u]!="function"){var s=Error((a||"React class")+": "+n+" type `"+u+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[u]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw s.name="Invariant Violation",s}o=e[u](t,u,a,n,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(f){o=f}o&&!(o instanceof Error)&&(Ul(r),h("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",a||"React class",n,u,typeof o),Ul(null)),o instanceof Error&&!(o.message in dv)&&(dv[o.message]=!0,Ul(r),h("Failed %s type: %s",n,o.message),Ul(null))}}}var qs=[],Ml;Ml=[];var tr=-1;function Tr(e){return{current:e}}function dn(e,t){if(tr<0){h("Unexpected pop.");return}t!==Ml[tr]&&h("Unexpected Fiber popped."),e.current=qs[tr],qs[tr]=null,Ml[tr]=null,tr--}function It(e,t,n){tr++,qs[tr]=e.current,Ml[tr]=n,e.current=t}var Qs;Qs={};var Vn={};Object.freeze(Vn);var nr=Tr(Vn),Ua=Tr(!1),Ps=Vn;function mi(e,t,n){return n&&Ma(t)?Ps:nr.current}function pv(e,t,n){{var a=e.stateNode;a.__reactInternalMemoizedUnmaskedChildContext=t,a.__reactInternalMemoizedMaskedChildContext=n}}function hi(e,t){{var n=e.type,a=n.contextTypes;if(!a)return Vn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={};for(var u in a)i[u]=t[u];{var o=W(e)||"Unknown";fa(a,i,"context",o)}return r&&pv(e,t,i),i}}function Al(){return Ua.current}function Ma(e){{var t=e.childContextTypes;return t!=null}}function zl(e){dn(Ua,e),dn(nr,e)}function Gs(e){dn(Ua,e),dn(nr,e)}function mv(e,t,n){{if(nr.current!==Vn)throw new Error("Unexpected context found on stack. This error is likely caused by a bug in React. Please file an issue.");It(nr,t,e),It(Ua,n,e)}}function hv(e,t,n){{var a=e.stateNode,r=t.childContextTypes;if(typeof a.getChildContext!="function"){{var i=W(e)||"Unknown";Qs[i]||(Qs[i]=!0,h("%s.childContextTypes is specified but there is no getChildContext() method on the instance. You can either define getChildContext() on %s or remove childContextTypes from it.",i,i))}return n}var u=a.getChildContext();for(var o in u)if(!(o in r))throw new Error((W(e)||"Unknown")+'.getChildContext(): key "'+o+'" is not defined in childContextTypes.');{var s=W(e)||"Unknown";fa(r,u,"child context",s)}return Q({},n,u)}}function Hl(e){{var t=e.stateNode,n=t&&t.__reactInternalMemoizedMergedChildContext||Vn;return Ps=nr.current,It(nr,n,e),It(Ua,Ua.current,e),!0}}function yv(e,t,n){{var a=e.stateNode;if(!a)throw new Error("Expected to have an instance by this point. This error is likely caused by a bug in React. Please file an issue.");if(n){var r=hv(e,t,Ps);a.__reactInternalMemoizedMergedChildContext=r,dn(Ua,e),dn(nr,e),It(nr,r,e),It(Ua,n,e)}else dn(Ua,e),It(Ua,n,e)}}function $y(e){{if(!ca(e)||e.tag!==ye)throw new Error("Expected subtree parent to be a mounted class component. This error is likely caused by a bug in React. Please file an issue.");var t=e;do{switch(t.tag){case se:return t.stateNode.context;case ye:{var n=t.type;if(Ma(n))return t.stateNode.__reactInternalMemoizedMergedChildContext;break}}t=t.return}while(t!==null);throw new Error("Found unexpected detached subtree parent. This error is likely caused by a bug in React. Please file an issue.")}}var yi=0,gv=1,le=0,Be=1,ke=2,_t=8,Aa=16,Sv=Math.clz32?Math.clz32:ng,eg=Math.log,tg=Math.LN2;function ng(e){var t=e>>>0;return t===0?32:31-(eg(t)/tg|0)|0}var Ks=31,A=0,Pt=0,fe=1,gi=2,ar=4,Qr=8,za=16,du=32,Si=4194240,vu=64,Xs=128,Js=256,Zs=512,ks=1024,Ws=2048,Is=4096,$s=8192,ec=16384,tc=32768,nc=65536,ac=131072,rc=262144,ic=524288,uc=1048576,lc=2097152,Ll=130023424,bi=4194304,oc=8388608,sc=16777216,cc=33554432,fc=67108864,bv=bi,pu=134217728,Ev=268435455,mu=268435456,Pr=536870912,wn=1073741824;function ag(e){{if(e&fe)return"Sync";if(e&gi)return"InputContinuousHydration";if(e&ar)return"InputContinuous";if(e&Qr)return"DefaultHydration";if(e&za)return"Default";if(e&du)return"TransitionHydration";if(e&Si)return"Transition";if(e&Ll)return"Retry";if(e&pu)return"SelectiveHydration";if(e&mu)return"IdleHydration";if(e&Pr)return"Idle";if(e&wn)return"Offscreen"}}var ot=-1,jl=vu,Fl=bi;function hu(e){switch(Gr(e)){case fe:return fe;case gi:return gi;case ar:return ar;case Qr:return Qr;case za:return za;case du:return du;case vu:case Xs:case Js:case Zs:case ks:case Ws:case Is:case $s:case ec:case tc:case nc:case ac:case rc:case ic:case uc:case lc:return e&Si;case bi:case oc:case sc:case cc:case fc:return e&Ll;case pu:return pu;case mu:return mu;case Pr:return Pr;case wn:return wn;default:return h("Should have found matching lanes. This is a bug in React."),e}}function Bl(e,t){var n=e.pendingLanes;if(n===A)return A;var a=A,r=e.suspendedLanes,i=e.pingedLanes,u=n&Ev;if(u!==A){var o=u&~r;if(o!==A)a=hu(o);else{var s=u&i;s!==A&&(a=hu(s))}}else{var f=n&~r;f!==A?a=hu(f):i!==A&&(a=hu(i))}if(a===A)return A;if(t!==A&&t!==a&&(t&r)===A){var m=Gr(a),S=Gr(t);if(m>=S||m===za&&(S&Si)!==A)return t}(a&ar)!==A&&(a|=n&za);var C=e.entangledLanes;if(C!==A)for(var O=e.entanglements,U=a&C;U>0;){var N=Kr(U),I=1<<N;a|=O[N],U&=~I}return a}function rg(e,t){for(var n=e.eventTimes,a=ot;t>0;){var r=Kr(t),i=1<<r,u=n[r];u>a&&(a=u),t&=~i}return a}function ig(e,t){switch(e){case fe:case gi:case ar:return t+250;case Qr:case za:case du:case vu:case Xs:case Js:case Zs:case ks:case Ws:case Is:case $s:case ec:case tc:case nc:case ac:case rc:case ic:case uc:case lc:return t+5e3;case bi:case oc:case sc:case cc:case fc:return ot;case pu:case mu:case Pr:case wn:return ot;default:return h("Should have found matching lanes. This is a bug in React."),ot}}function ug(e,t){for(var n=e.pendingLanes,a=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,u=n;u>0;){var o=Kr(u),s=1<<o,f=i[o];f===ot?((s&a)===A||(s&r)!==A)&&(i[o]=ig(s,t)):f<=t&&(e.expiredLanes|=s),u&=~s}}function lg(e){return hu(e.pendingLanes)}function dc(e){var t=e.pendingLanes&~wn;return t!==A?t:t&wn?wn:A}function og(e){return(e&fe)!==A}function vc(e){return(e&Ev)!==A}function Rv(e){return(e&Ll)===e}function sg(e){var t=fe|ar|za;return(e&t)===A}function cg(e){return(e&Si)===e}function Vl(e,t){var n=gi|ar|Qr|za;return(t&n)!==A}function fg(e,t){return(t&e.expiredLanes)!==A}function Tv(e){return(e&Si)!==A}function Cv(){var e=jl;return jl<<=1,(jl&Si)===A&&(jl=vu),e}function dg(){var e=Fl;return Fl<<=1,(Fl&Ll)===A&&(Fl=bi),e}function Gr(e){return e&-e}function yu(e){return Gr(e)}function Kr(e){return 31-Sv(e)}function pc(e){return Kr(e)}function Yn(e,t){return(e&t)!==A}function Ei(e,t){return(e&t)===t}function Ee(e,t){return e|t}function wl(e,t){return e&~t}function xv(e,t){return e&t}function ST(e){return e}function vg(e,t){return e!==Pt&&e<t?e:t}function mc(e){for(var t=[],n=0;n<Ks;n++)t.push(e);return t}function gu(e,t,n){e.pendingLanes|=t,t!==Pr&&(e.suspendedLanes=A,e.pingedLanes=A);var a=e.eventTimes,r=pc(t);a[r]=n}function pg(e,t){e.suspendedLanes|=t,e.pingedLanes&=~t;for(var n=e.expirationTimes,a=t;a>0;){var r=Kr(a),i=1<<r;n[r]=ot,a&=~i}}function Dv(e,t,n){e.pingedLanes|=e.suspendedLanes&t}function mg(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=A,e.pingedLanes=A,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t;for(var a=e.entanglements,r=e.eventTimes,i=e.expirationTimes,u=n;u>0;){var o=Kr(u),s=1<<o;a[o]=A,r[o]=ot,i[o]=ot,u&=~s}}function hc(e,t){for(var n=e.entangledLanes|=t,a=e.entanglements,r=n;r;){var i=Kr(r),u=1<<i;u&t|a[i]&t&&(a[i]|=t),r&=~u}}function hg(e,t){var n=Gr(t),a;switch(n){case ar:a=gi;break;case za:a=Qr;break;case vu:case Xs:case Js:case Zs:case ks:case Ws:case Is:case $s:case ec:case tc:case nc:case ac:case rc:case ic:case uc:case lc:case bi:case oc:case sc:case cc:case fc:a=du;break;case Pr:a=mu;break;default:a=Pt;break}return(a&(e.suspendedLanes|t))!==Pt?Pt:a}function _v(e,t,n){if(va)for(var a=e.pendingUpdatersLaneMap;n>0;){var r=pc(n),i=1<<r,u=a[r];u.add(t),n&=~i}}function Ov(e,t){if(va)for(var n=e.pendingUpdatersLaneMap,a=e.memoizedUpdaters;t>0;){var r=pc(t),i=1<<r,u=n[r];u.size>0&&(u.forEach(function(o){var s=o.alternate;(s===null||!a.has(s))&&a.add(o)}),u.clear()),t&=~i}}function Nv(e,t){return null}var Ha=fe,Su=ar,bu=za,yc=Pr,Eu=Pt;function da(){return Eu}function Gt(e){Eu=e}function yg(e,t){var n=Eu;try{return Eu=e,t()}finally{Eu=n}}function gg(e,t){return e!==0&&e<t?e:t}function Sg(e,t){return e===0||e>t?e:t}function Uv(e,t){return e!==0&&e<t}function Mv(e){var t=Gr(e);return Uv(Ha,t)?Uv(Su,t)?vc(t)?bu:yc:Su:Ha}var Av=g.unstable_scheduleCallback,bg=g.unstable_cancelCallback,Eg=g.unstable_shouldYield,Rg=g.unstable_requestPaint,Kt=g.unstable_now,Yl=g.unstable_ImmediatePriority,zv=g.unstable_UserBlockingPriority,Ri=g.unstable_NormalPriority,Hv=g.unstable_IdlePriority,Tg=g.unstable_yieldValue,Cg=g.unstable_setDisableYieldValue,Xr=null,$t=null,P=null,La=!1,va=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined";function xg(e){if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined")return!1;var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(t.isDisabled)return!0;if(!t.supportsFiber)return h("The installed version of React DevTools is too old and will not work with the current version of React. Please update React DevTools. https://reactjs.org/link/react-devtools"),!0;try{ft&&(e=Q({},e,{getLaneLabelMap:Mg,injectProfilingHooks:Ug})),Xr=t.inject(e),$t=t}catch(n){h("React instrumentation encountered an error: %s.",n)}return!!t.checkDCE}function Dg(e,t){if($t&&typeof $t.onScheduleFiberRoot=="function")try{$t.onScheduleFiberRoot(Xr,e,t)}catch(n){La||(La=!0,h("React instrumentation encountered an error: %s",n))}}function _g(e,t){if($t&&typeof $t.onCommitFiberRoot=="function")try{var n=(e.current.flags&ze)===ze;if(gt){var a;switch(t){case Ha:a=Yl;break;case Su:a=zv;break;case bu:a=Ri;break;case yc:a=Hv;break;default:a=Ri;break}$t.onCommitFiberRoot(Xr,e,a,n)}else $t.onCommitFiberRoot(Xr,e,void 0,n)}catch(r){La||(La=!0,h("React instrumentation encountered an error: %s",r))}}function Og(e){if($t&&typeof $t.onPostCommitFiberRoot=="function")try{$t.onPostCommitFiberRoot(Xr,e)}catch(t){La||(La=!0,h("React instrumentation encountered an error: %s",t))}}function Ng(e){if($t&&typeof $t.onCommitFiberUnmount=="function")try{$t.onCommitFiberUnmount(Xr,e)}catch(t){La||(La=!0,h("React instrumentation encountered an error: %s",t))}}function Xt(e){if(typeof Tg=="function"&&(Cg(e),B(e)),$t&&typeof $t.setStrictMode=="function")try{$t.setStrictMode(Xr,e)}catch(t){La||(La=!0,h("React instrumentation encountered an error: %s",t))}}function Ug(e){P=e}function Mg(){{for(var e=new Map,t=1,n=0;n<Ks;n++){var a=ag(t);e.set(t,a),t*=2}return e}}function Ag(e){P!==null&&typeof P.markCommitStarted=="function"&&P.markCommitStarted(e)}function Lv(){P!==null&&typeof P.markCommitStopped=="function"&&P.markCommitStopped()}function Ru(e){P!==null&&typeof P.markComponentRenderStarted=="function"&&P.markComponentRenderStarted(e)}function Ti(){P!==null&&typeof P.markComponentRenderStopped=="function"&&P.markComponentRenderStopped()}function zg(e){P!==null&&typeof P.markComponentPassiveEffectMountStarted=="function"&&P.markComponentPassiveEffectMountStarted(e)}function Hg(){P!==null&&typeof P.markComponentPassiveEffectMountStopped=="function"&&P.markComponentPassiveEffectMountStopped()}function Lg(e){P!==null&&typeof P.markComponentPassiveEffectUnmountStarted=="function"&&P.markComponentPassiveEffectUnmountStarted(e)}function jg(){P!==null&&typeof P.markComponentPassiveEffectUnmountStopped=="function"&&P.markComponentPassiveEffectUnmountStopped()}function Fg(e){P!==null&&typeof P.markComponentLayoutEffectMountStarted=="function"&&P.markComponentLayoutEffectMountStarted(e)}function Bg(){P!==null&&typeof P.markComponentLayoutEffectMountStopped=="function"&&P.markComponentLayoutEffectMountStopped()}function jv(e){P!==null&&typeof P.markComponentLayoutEffectUnmountStarted=="function"&&P.markComponentLayoutEffectUnmountStarted(e)}function Fv(){P!==null&&typeof P.markComponentLayoutEffectUnmountStopped=="function"&&P.markComponentLayoutEffectUnmountStopped()}function Vg(e,t,n){P!==null&&typeof P.markComponentErrored=="function"&&P.markComponentErrored(e,t,n)}function wg(e,t,n){P!==null&&typeof P.markComponentSuspended=="function"&&P.markComponentSuspended(e,t,n)}function Yg(e){P!==null&&typeof P.markLayoutEffectsStarted=="function"&&P.markLayoutEffectsStarted(e)}function qg(){P!==null&&typeof P.markLayoutEffectsStopped=="function"&&P.markLayoutEffectsStopped()}function Qg(e){P!==null&&typeof P.markPassiveEffectsStarted=="function"&&P.markPassiveEffectsStarted(e)}function Pg(){P!==null&&typeof P.markPassiveEffectsStopped=="function"&&P.markPassiveEffectsStopped()}function Bv(e){P!==null&&typeof P.markRenderStarted=="function"&&P.markRenderStarted(e)}function Gg(){P!==null&&typeof P.markRenderYielded=="function"&&P.markRenderYielded()}function Vv(){P!==null&&typeof P.markRenderStopped=="function"&&P.markRenderStopped()}function Kg(e){P!==null&&typeof P.markRenderScheduled=="function"&&P.markRenderScheduled(e)}function Xg(e,t){P!==null&&typeof P.markForceUpdateScheduled=="function"&&P.markForceUpdateScheduled(e,t)}function gc(e,t){P!==null&&typeof P.markStateUpdateScheduled=="function"&&P.markStateUpdateScheduled(e,t)}function Jg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var qn=typeof Object.is=="function"?Object.is:Jg,rr=null,Sc=!1,bc=!1;function wv(e){rr===null?rr=[e]:rr.push(e)}function Zg(e){Sc=!0,wv(e)}function Yv(){Sc&&ja()}function ja(){if(!bc&&rr!==null){bc=!0;var e=0,t=da();try{var n=!0,a=rr;for(Gt(Ha);e<a.length;e++){var r=a[e];do r=r(n);while(r!==null)}rr=null,Sc=!1}catch(i){throw rr!==null&&(rr=rr.slice(e+1)),Av(Yl,ja),i}finally{Gt(t),bc=!1}}return null}function qv(e){var t=e.current.memoizedState;return t.isDehydrated}var Ci=[],xi=0,ql=null,Ql=0,In=[],$n=0,Jr=null,ir=1,ur="";function kg(e){return kr(),(e.flags&d)!==X}function Wg(e){return kr(),Ql}function Ig(){var e=ur,t=ir,n=t&~$g(t);return n.toString(32)+e}function Zr(e,t){kr(),Ci[xi++]=Ql,Ci[xi++]=ql,ql=e,Ql=t}function Qv(e,t,n){kr(),In[$n++]=ir,In[$n++]=ur,In[$n++]=Jr,Jr=e;var a=ir,r=ur,i=Pl(a)-1,u=a&~(1<<i),o=n+1,s=Pl(t)+i;if(s>30){var f=i-i%5,m=(1<<f)-1,S=(u&m).toString(32),C=u>>f,O=i-f,U=Pl(t)+O,N=o<<O,I=N|C,oe=S+r;ir=1<<U|I,ur=oe}else{var te=o<<i,Ke=te|u,je=r;ir=1<<s|Ke,ur=je}}function Ec(e){kr();var t=e.return;if(t!==null){var n=1,a=0;Zr(e,n),Qv(e,n,a)}}function Pl(e){return 32-Sv(e)}function $g(e){return 1<<Pl(e)-1}function Rc(e){for(;e===ql;)ql=Ci[--xi],Ci[xi]=null,Ql=Ci[--xi],Ci[xi]=null;for(;e===Jr;)Jr=In[--$n],In[$n]=null,ur=In[--$n],In[$n]=null,ir=In[--$n],In[$n]=null}function eS(){return kr(),Jr!==null?{id:ir,overflow:ur}:null}function tS(e,t){kr(),In[$n++]=ir,In[$n++]=ur,In[$n++]=Jr,ir=t.id,ur=t.overflow,Jr=e}function kr(){tn()||h("Expected to be hydrating. This is a bug in React. Please file an issue.")}var en=null,ea=null,pa=!1,Cr=!1,xr=null;function nS(){pa&&h("We should not be hydrating here. This is a bug in React. Please file a bug.")}function Pv(){Cr=!0}function aS(){return Cr}function rS(e){if(!Mt)return!1;var t=e.stateNode.containerInfo;return ea=Hs(t),en=e,pa=!0,xr=null,Cr=!1,!0}function iS(e,t,n){return Mt?(ea=Ol(t),en=e,pa=!0,xr=null,Cr=!1,n!==null&&tS(e,n),!0):!1}function Gv(e,t){switch(e.tag){case se:{Ly(e.stateNode.containerInfo,t);break}case $:{var n=(e.mode&Be)!==le;Fy(e.type,e.memoizedProps,e.stateNode,t,n);break}case Fe:{var a=e.memoizedState;a.dehydrated!==null&&jy(a.dehydrated,t);break}}}function Kv(e,t){Gv(e,t);var n=MR();n.stateNode=t,n.return=e;var a=e.deletions;a===null?(e.deletions=[n],e.flags|=Wt):a.push(n)}function Tc(e,t){{if(Cr)return;switch(e.tag){case se:{var n=e.stateNode.containerInfo;switch(t.tag){case $:var a=t.type,r=t.pendingProps;By(n,a,r);break;case ge:var i=t.pendingProps;Vy(n,i);break;case Fe:wy(n);break}break}case $:{var u=e.type,o=e.memoizedProps,s=e.stateNode;switch(t.tag){case $:{var f=t.type,m=t.pendingProps,S=(e.mode&Be)!==le;Py(u,o,s,f,m,S);break}case ge:{var C=t.pendingProps,O=(e.mode&Be)!==le;Gy(u,o,s,C,O);break}case Fe:{Ky(u,o,s);break}}break}case Fe:{var U=e.memoizedState,N=U.dehydrated;if(N!==null)switch(t.tag){case $:var I=t.type,oe=t.pendingProps;Yy(N,I,oe);break;case ge:var te=t.pendingProps;qy(N,te);break;case Fe:Qy(N);break}break}default:return}}}function Xv(e,t){t.flags=t.flags&~Nn|Qe,Tc(e,t)}function Jv(e,t){switch(e.tag){case $:{var n=e.type,a=e.pendingProps,r=lt(t,n,a);return r!==null?(e.stateNode=r,en=e,ea=_l(r),!0):!1}case ge:{var i=e.pendingProps,u=ct(t,i);return u!==null?(e.stateNode=u,en=e,ea=null,!0):!1}case Fe:{var o=ht(t);if(o!==null){var s={dehydrated:o,treeContext:eS(),retryLane:wn};e.memoizedState=s;var f=AR(o);return f.return=e,e.child=f,en=e,ea=null,!0}return!1}default:return!1}}function Cc(e){return(e.mode&Be)!==le&&(e.flags&ze)===X}function xc(e){throw new Error("Hydration failed because the initial UI does not match what was rendered on the server.")}function Dc(e){if(pa){var t=ea;if(!t){Cc(e)&&(Tc(en,e),xc()),Xv(en,e),pa=!1,en=e;return}var n=t;if(!Jv(e,t)){Cc(e)&&(Tc(en,e),xc()),t=pi(n);var a=en;if(!t||!Jv(e,t)){Xv(en,e),pa=!1,en=e;return}Kv(a,n)}}}function uS(e,t,n){if(!Mt)throw new Error("Expected prepareToHydrateHostInstance() to never be called. This error is likely caused by a bug in React. Please file an issue.");var a=e.stateNode,r=!Cr,i=Ls(a,e.type,e.memoizedProps,t,n,e,r);return e.updateQueue=i,i!==null}function lS(e){if(!Mt)throw new Error("Expected prepareToHydrateHostTextInstance() to never be called. This error is likely caused by a bug in React. Please file an issue.");var t=e.stateNode,n=e.memoizedProps,a=!Cr,r=xy(t,n,e,a);if(r){var i=en;if(i!==null)switch(i.tag){case se:{var u=i.stateNode.containerInfo,o=(i.mode&Be)!==le;zy(u,t,n,o);break}case $:{var s=i.type,f=i.memoizedProps,m=i.stateNode,S=(i.mode&Be)!==le;Hy(s,f,m,t,n,S);break}}}return r}function oS(e){if(!Mt)throw new Error("Expected prepareToHydrateHostSuspenseInstance() to never be called. This error is likely caused by a bug in React. Please file an issue.");var t=e.memoizedState,n=t!==null?t.dehydrated:null;if(!n)throw new Error("Expected to have a hydrated suspense instance. This error is likely caused by a bug in React. Please file an issue.");Dy(n,e)}function sS(e){if(!Mt)throw new Error("Expected skipPastDehydratedSuspenseInstance() to never be called. This error is likely caused by a bug in React. Please file an issue.");var t=e.memoizedState,n=t!==null?t.dehydrated:null;if(!n)throw new Error("Expected to have a hydrated suspense instance. This error is likely caused by a bug in React. Please file an issue.");return _y(n)}function Zv(e){for(var t=e.return;t!==null&&t.tag!==$&&t.tag!==se&&t.tag!==Fe;)t=t.return;en=t}function Gl(e){if(!Mt||e!==en)return!1;if(!pa)return Zv(e),pa=!0,!1;if(e.tag!==se&&(e.tag!==$||Ay(e.type)&&!ru(e.type,e.memoizedProps))){var t=ea;if(t)if(Cc(e))kv(e),xc();else for(;t;)Kv(e,t),t=pi(t)}return Zv(e),e.tag===Fe?ea=sS(e):ea=en?pi(e.stateNode):null,!0}function cS(){return pa&&ea!==null}function kv(e){for(var t=ea;t;)Gv(e,t),t=pi(t)}function Di(){Mt&&(en=null,ea=null,pa=!1,Cr=!1)}function Wv(){xr!==null&&(Wm(xr),xr=null)}function tn(){return pa}function _c(e){xr===null?xr=[e]:xr.push(e)}var fS=M.ReactCurrentBatchConfig,dS=null;function vS(){return fS.transition}function Kl(e,t){if(qn(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),a=Object.keys(t);if(n.length!==a.length)return!1;for(var r=0;r<n.length;r++){var i=n[r];if(!fv.call(t,i)||!qn(e[i],t[i]))return!1}return!0}function pS(e){var t=e._debugOwner?e._debugOwner.type:null,n=e._debugSource;switch(e.tag){case $:return Rr(e.type);case Ct:return Rr("Lazy");case Fe:return Rr("Suspense");case St:return Rr("SuspenseList");case Ne:case yn:case Ae:return ws(e.type);case Me:return ws(e.type.render);case ye:return Wy(e.type);default:return""}}function Iv(e){try{var t="",n=e;do t+=pS(n),n=n.return;while(n);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}var $v=M.ReactDebugCurrentFrame,Qn=null,Tu=!1;function mS(){{if(Qn===null)return null;var e=Qn._debugOwner;if(e!==null&&typeof e!="undefined")return W(e)}return null}function hS(){return Qn===null?"":Iv(Qn)}function bn(){$v.getCurrentStack=null,Qn=null,Tu=!1}function Et(e){$v.getCurrentStack=e===null?null:hS,Qn=e,Tu=!1}function yS(){return Qn}function Fa(e){Tu=e}var ma={recordUnsafeLifecycleWarnings:function(e,t){},flushPendingUnsafeLifecycleWarnings:function(){},recordLegacyContextWarning:function(e,t){},flushLegacyContextWarning:function(){},discardPendingWarnings:function(){}};{var gS=function(e){for(var t=null,n=e;n!==null;)n.mode&_t&&(t=n),n=n.return;return t},Wr=function(e){var t=[];return e.forEach(function(n){t.push(n)}),t.sort().join(", ")},Cu=[],xu=[],Du=[],_u=[],Ou=[],Nu=[],Ir=new Set;ma.recordUnsafeLifecycleWarnings=function(e,t){Ir.has(e.type)||(typeof t.componentWillMount=="function"&&t.componentWillMount.__suppressDeprecationWarning!==!0&&Cu.push(e),e.mode&_t&&typeof t.UNSAFE_componentWillMount=="function"&&xu.push(e),typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps.__suppressDeprecationWarning!==!0&&Du.push(e),e.mode&_t&&typeof t.UNSAFE_componentWillReceiveProps=="function"&&_u.push(e),typeof t.componentWillUpdate=="function"&&t.componentWillUpdate.__suppressDeprecationWarning!==!0&&Ou.push(e),e.mode&_t&&typeof t.UNSAFE_componentWillUpdate=="function"&&Nu.push(e))},ma.flushPendingUnsafeLifecycleWarnings=function(){var e=new Set;Cu.length>0&&(Cu.forEach(function(C){e.add(W(C)||"Component"),Ir.add(C.type)}),Cu=[]);var t=new Set;xu.length>0&&(xu.forEach(function(C){t.add(W(C)||"Component"),Ir.add(C.type)}),xu=[]);var n=new Set;Du.length>0&&(Du.forEach(function(C){n.add(W(C)||"Component"),Ir.add(C.type)}),Du=[]);var a=new Set;_u.length>0&&(_u.forEach(function(C){a.add(W(C)||"Component"),Ir.add(C.type)}),_u=[]);var r=new Set;Ou.length>0&&(Ou.forEach(function(C){r.add(W(C)||"Component"),Ir.add(C.type)}),Ou=[]);var i=new Set;if(Nu.length>0&&(Nu.forEach(function(C){i.add(W(C)||"Component"),Ir.add(C.type)}),Nu=[]),t.size>0){var u=Wr(t);h(`Using UNSAFE_componentWillMount in strict mode is not recommended and may indicate bugs in your code. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move code with side effects to componentDidMount, and set initial state in the constructor.

Please update the following components: %s`,u)}if(a.size>0){var o=Wr(a);h(`Using UNSAFE_componentWillReceiveProps in strict mode is not recommended and may indicate bugs in your code. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://reactjs.org/link/derived-state

Please update the following components: %s`,o)}if(i.size>0){var s=Wr(i);h(`Using UNSAFE_componentWillUpdate in strict mode is not recommended and may indicate bugs in your code. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.

Please update the following components: %s`,s)}if(e.size>0){var f=Wr(e);H(`componentWillMount has been renamed, and is not recommended for use. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move code with side effects to componentDidMount, and set initial state in the constructor.
* Rename componentWillMount to UNSAFE_componentWillMount to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run \`npx react-codemod rename-unsafe-lifecycles\` in your project source folder.

Please update the following components: %s`,f)}if(n.size>0){var m=Wr(n);H(`componentWillReceiveProps has been renamed, and is not recommended for use. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://reactjs.org/link/derived-state
* Rename componentWillReceiveProps to UNSAFE_componentWillReceiveProps to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run \`npx react-codemod rename-unsafe-lifecycles\` in your project source folder.

Please update the following components: %s`,m)}if(r.size>0){var S=Wr(r);H(`componentWillUpdate has been renamed, and is not recommended for use. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* Rename componentWillUpdate to UNSAFE_componentWillUpdate to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run \`npx react-codemod rename-unsafe-lifecycles\` in your project source folder.

Please update the following components: %s`,S)}};var Xl=new Map,ep=new Set;ma.recordLegacyContextWarning=function(e,t){var n=gS(e);if(n===null){h("Expected to find a StrictMode component in a strict mode tree. This error is likely caused by a bug in React. Please file an issue.");return}if(!ep.has(e.type)){var a=Xl.get(n);(e.type.contextTypes!=null||e.type.childContextTypes!=null||t!==null&&typeof t.getChildContext=="function")&&(a===void 0&&(a=[],Xl.set(n,a)),a.push(e))}},ma.flushLegacyContextWarning=function(){Xl.forEach(function(e,t){if(e.length!==0){var n=e[0],a=new Set;e.forEach(function(i){a.add(W(i)||"Component"),ep.add(i.type)});var r=Wr(a);try{Et(n),h(`Legacy context API has been detected within a strict-mode tree.

The old API will be supported in all 16.x releases, but applications using it should migrate to the new version.

Please update the following components: %s

Learn more about this warning here: https://reactjs.org/link/legacy-context`,r)}finally{bn()}}})},ma.discardPendingWarnings=function(){Cu=[],xu=[],Du=[],_u=[],Ou=[],Nu=[],Xl=new Map}}function tp(e){{var t=typeof Symbol=="function"&&Symbol.toStringTag,n=t&&e[Symbol.toStringTag]||e.constructor.name||"Object";return n}}function np(e){try{return Oc(e),!1}catch(t){return!0}}function Oc(e){return""+e}function SS(e){if(np(e))return h("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",tp(e)),Oc(e)}function bS(e,t){if(np(e))return h("The provided `%s` prop is an unsupported type %s. This value must be coerced to a string before before using it here.",t,tp(e)),Oc(e)}function ha(e,t){if(e&&e.defaultProps){var n=Q({},t),a=e.defaultProps;for(var r in a)n[r]===void 0&&(n[r]=a[r]);return n}return t}var Jl=Tr(null),Uu;Uu={};var Zl=null,_i=null,Nc=null,kl=!1;function Wl(){Zl=null,_i=null,Nc=null,kl=!1}function ap(){kl=!0}function rp(){kl=!1}function ip(e,t,n){Yr?(It(Jl,t._currentValue,e),t._currentValue=n,t._currentRenderer!==void 0&&t._currentRenderer!==null&&t._currentRenderer!==Uu&&h("Detected multiple renderers concurrently rendering the same context provider. This is currently unsupported."),t._currentRenderer=Uu):(It(Jl,t._currentValue2,e),t._currentValue2=n,t._currentRenderer2!==void 0&&t._currentRenderer2!==null&&t._currentRenderer2!==Uu&&h("Detected multiple renderers concurrently rendering the same context provider. This is currently unsupported."),t._currentRenderer2=Uu)}function Uc(e,t){var n=Jl.current;dn(Jl,t),Yr?e._currentValue=n:e._currentValue2=n}function Mc(e,t,n){for(var a=e;a!==null;){var r=a.alternate;if(Ei(a.childLanes,t)?r!==null&&!Ei(r.childLanes,t)&&(r.childLanes=Ee(r.childLanes,t)):(a.childLanes=Ee(a.childLanes,t),r!==null&&(r.childLanes=Ee(r.childLanes,t))),a===n)break;a=a.return}a!==n&&h("Expected to find the propagation root when scheduling context work. This error is likely caused by a bug in React. Please file an issue.")}function ES(e,t,n){RS(e,t,n)}function RS(e,t,n){var a=e.child;for(a!==null&&(a.return=e);a!==null;){var r=void 0,i=a.dependencies;if(i!==null){r=a.child;for(var u=i.firstContext;u!==null;){if(u.context===t){if(a.tag===ye){var o=yu(n),s=lr(ot,o);s.tag=$l;var f=a.updateQueue;if(f!==null){var m=f.shared,S=m.pending;S===null?s.next=s:(s.next=S.next,S.next=s),m.pending=s}}a.lanes=Ee(a.lanes,n);var C=a.alternate;C!==null&&(C.lanes=Ee(C.lanes,n)),Mc(a.return,n,e),i.lanes=Ee(i.lanes,n);break}u=u.next}}else if(a.tag===Rt)r=a.type===e.type?null:a.child;else if(a.tag===Xn){var O=a.return;if(O===null)throw new Error("We just came from a parent so we must have had a parent. This is a bug in React.");O.lanes=Ee(O.lanes,n);var U=O.alternate;U!==null&&(U.lanes=Ee(U.lanes,n)),Mc(O,n,e),r=a.sibling}else r=a.child;if(r!==null)r.return=a;else for(r=a;r!==null;){if(r===e){r=null;break}var N=r.sibling;if(N!==null){N.return=r.return,r=N;break}r=r.return}a=r}}function Oi(e,t){Zl=e,_i=null,Nc=null;var n=e.dependencies;if(n!==null){var a=n.firstContext;a!==null&&(Yn(n.lanes,t)&&Gu(),n.firstContext=null)}}function Ot(e){kl&&h("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().");var t=Yr?e._currentValue:e._currentValue2;if(Nc!==e){var n={context:e,memoizedValue:t,next:null};if(_i===null){if(Zl===null)throw new Error("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().");_i=n,Zl.dependencies={lanes:A,firstContext:n}}else _i=_i.next=n}return t}var $r=null;function Ac(e){$r===null?$r=[e]:$r.push(e)}function TS(){if($r!==null){for(var e=0;e<$r.length;e++){var t=$r[e],n=t.interleaved;if(n!==null){t.interleaved=null;var a=n.next,r=t.pending;if(r!==null){var i=r.next;r.next=a,n.next=i}t.pending=n}}$r=null}}function up(e,t,n,a){var r=t.interleaved;return r===null?(n.next=n,Ac(t)):(n.next=r.next,r.next=n),t.interleaved=n,Il(e,a)}function CS(e,t,n,a){var r=t.interleaved;r===null?(n.next=n,Ac(t)):(n.next=r.next,r.next=n),t.interleaved=n}function xS(e,t,n,a){var r=t.interleaved;return r===null?(n.next=n,Ac(t)):(n.next=r.next,r.next=n),t.interleaved=n,Il(e,a)}function En(e,t){return Il(e,t)}var DS=Il;function Il(e,t){e.lanes=Ee(e.lanes,t);var n=e.alternate;n!==null&&(n.lanes=Ee(n.lanes,t)),n===null&&(e.flags&(Qe|Nn))!==X&&oh(e);for(var a=e,r=e.return;r!==null;)r.childLanes=Ee(r.childLanes,t),n=r.alternate,n!==null?n.childLanes=Ee(n.childLanes,t):(r.flags&(Qe|Nn))!==X&&oh(e),a=r,r=r.return;if(a.tag===se){var i=a.stateNode;return i}else return null}var lp=0,op=1,$l=2,zc=3,eo=!1,Hc,to;Hc=!1,to=null;function Lc(e){var t={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:A},effects:null};e.updateQueue=t}function sp(e,t){var n=t.updateQueue,a=e.updateQueue;if(n===a){var r={baseState:a.baseState,firstBaseUpdate:a.firstBaseUpdate,lastBaseUpdate:a.lastBaseUpdate,shared:a.shared,effects:a.effects};t.updateQueue=r}}function lr(e,t){var n={eventTime:e,lane:t,tag:lp,payload:null,callback:null,next:null};return n}function Dr(e,t,n){var a=e.updateQueue;if(a===null)return null;var r=a.shared;if(to===r&&!Hc&&(h("An update (setState, replaceState, or forceUpdate) was scheduled from inside an update function. Update functions should be pure, with zero side-effects. Consider using componentDidUpdate or a callback."),Hc=!0),VE()){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,DS(e,n)}else return xS(e,r,t,n)}function no(e,t,n){var a=t.updateQueue;if(a!==null){var r=a.shared;if(Tv(n)){var i=r.lanes;i=xv(i,e.pendingLanes);var u=Ee(i,n);r.lanes=u,hc(e,u)}}}function jc(e,t){var n=e.updateQueue,a=e.alternate;if(a!==null){var r=a.updateQueue;if(n===r){var i=null,u=null,o=n.firstBaseUpdate;if(o!==null){var s=o;do{var f={eventTime:s.eventTime,lane:s.lane,tag:s.tag,payload:s.payload,callback:s.callback,next:null};u===null?i=u=f:(u.next=f,u=f),s=s.next}while(s!==null);u===null?i=u=t:(u.next=t,u=t)}else i=u=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:u,shared:r.shared,effects:r.effects},e.updateQueue=n;return}}var m=n.lastBaseUpdate;m===null?n.firstBaseUpdate=t:m.next=t,n.lastBaseUpdate=t}function _S(e,t,n,a,r,i){switch(n.tag){case op:{var u=n.payload;if(typeof u=="function"){ap();var o=u.call(i,a,r);{if(e.mode&_t){Xt(!0);try{u.call(i,a,r)}finally{Xt(!1)}}rp()}return o}return u}case zc:e.flags=e.flags&~xt|ze;case lp:{var s=n.payload,f;if(typeof s=="function"){ap(),f=s.call(i,a,r);{if(e.mode&_t){Xt(!0);try{s.call(i,a,r)}finally{Xt(!1)}}rp()}}else f=s;return f==null?a:Q({},a,f)}case $l:return eo=!0,a}return a}function ao(e,t,n,a){var r=e.updateQueue;eo=!1,to=r.shared;var i=r.firstBaseUpdate,u=r.lastBaseUpdate,o=r.shared.pending;if(o!==null){r.shared.pending=null;var s=o,f=s.next;s.next=null,u===null?i=f:u.next=f,u=s;var m=e.alternate;if(m!==null){var S=m.updateQueue,C=S.lastBaseUpdate;C!==u&&(C===null?S.firstBaseUpdate=f:C.next=f,S.lastBaseUpdate=s)}}if(i!==null){var O=r.baseState,U=A,N=null,I=null,oe=null,te=i;do{var Ke=te.lane,je=te.eventTime;if(Ei(a,Ke)){if(oe!==null){var D={eventTime:je,lane:Pt,tag:te.tag,payload:te.payload,callback:te.callback,next:null};oe=oe.next=D}O=_S(e,r,te,O,t,n);var b=te.callback;if(b!==null&&te.lane!==Pt){e.flags|=mr;var L=r.effects;L===null?r.effects=[te]:L.push(te)}}else{var E={eventTime:je,lane:Ke,tag:te.tag,payload:te.payload,callback:te.callback,next:null};oe===null?(I=oe=E,N=O):oe=oe.next=E,U=Ee(U,Ke)}if(te=te.next,te===null){if(o=r.shared.pending,o===null)break;var k=o,K=k.next;k.next=null,te=K,r.lastBaseUpdate=k,r.shared.pending=null}}while(!0);oe===null&&(N=O),r.baseState=N,r.firstBaseUpdate=I,r.lastBaseUpdate=oe;var He=r.shared.interleaved;if(He!==null){var de=He;do U=Ee(U,de.lane),de=de.next;while(de!==He)}else i===null&&(r.shared.lanes=A);il(U),e.lanes=U,e.memoizedState=O}to=null}function OS(e,t){if(typeof e!="function")throw new Error("Invalid argument passed as callback. Expected a function. Instead "+("received: "+e));e.call(t)}function cp(){eo=!1}function ro(){return eo}function fp(e,t,n){var a=t.effects;if(t.effects=null,a!==null)for(var r=0;r<a.length;r++){var i=a[r],u=i.callback;u!==null&&(i.callback=null,OS(u,n))}}var Fc={},dp=new T.Component().refs,Bc,Vc,wc,Yc,qc,vp,io,Qc,Pc,Gc;{Bc=new Set,Vc=new Set,wc=new Set,Yc=new Set,Qc=new Set,qc=new Set,Pc=new Set,Gc=new Set;var pp=new Set;io=function(e,t){if(!(e===null||typeof e=="function")){var n=t+"_"+e;pp.has(n)||(pp.add(n),h("%s(...): Expected the last optional `callback` argument to be a function. Instead received: %s.",t,e))}},vp=function(e,t){if(t===void 0){var n=ee(e)||"Component";qc.has(n)||(qc.add(n),h("%s.getDerivedStateFromProps(): A valid state object (or null) must be returned. You have returned undefined.",n))}},Object.defineProperty(Fc,"_processChildContext",{enumerable:!1,value:function(){throw new Error("_processChildContext is not available in React 16+. This likely means you have multiple copies of React and are attempting to nest a React 15 tree inside a React 16 tree using unstable_renderSubtreeIntoContainer, which isn't supported. Try to make sure you have only one copy of React (and ideally, switch to ReactDOM.createPortal).")}}),Object.freeze(Fc)}function Kc(e,t,n,a){var r=e.memoizedState,i=n(a,r);{if(e.mode&_t){Xt(!0);try{i=n(a,r)}finally{Xt(!1)}}vp(t,i)}var u=i==null?r:Q({},r,i);if(e.memoizedState=u,e.lanes===A){var o=e.updateQueue;o.baseState=u}}var Xc={isMounted:qt,enqueueSetState:function(e,t,n){var a=pe(e),r=pn(),i=zr(a),u=lr(r,i);u.payload=t,n!=null&&(io(n,"setState"),u.callback=n);var o=Dr(a,u,i);o!==null&&(Ut(o,a,i,r),no(o,a,i)),gc(a,i)},enqueueReplaceState:function(e,t,n){var a=pe(e),r=pn(),i=zr(a),u=lr(r,i);u.tag=op,u.payload=t,n!=null&&(io(n,"replaceState"),u.callback=n);var o=Dr(a,u,i);o!==null&&(Ut(o,a,i,r),no(o,a,i)),gc(a,i)},enqueueForceUpdate:function(e,t){var n=pe(e),a=pn(),r=zr(n),i=lr(a,r);i.tag=$l,t!=null&&(io(t,"forceUpdate"),i.callback=t);var u=Dr(n,i,r);u!==null&&(Ut(u,n,r,a),no(u,n,r)),Xg(n,r)}};function mp(e,t,n,a,r,i,u){var o=e.stateNode;if(typeof o.shouldComponentUpdate=="function"){var s=o.shouldComponentUpdate(a,i,u);{if(e.mode&_t){Xt(!0);try{s=o.shouldComponentUpdate(a,i,u)}finally{Xt(!1)}}s===void 0&&h("%s.shouldComponentUpdate(): Returned undefined instead of a boolean value. Make sure to return true or false.",ee(t)||"Component")}return s}return t.prototype&&t.prototype.isPureReactComponent?!Kl(n,a)||!Kl(r,i):!0}function NS(e,t,n){var a=e.stateNode;{var r=ee(t)||"Component",i=a.render;i||(t.prototype&&typeof t.prototype.render=="function"?h("%s(...): No `render` method found on the returned component instance: did you accidentally return an object from the constructor?",r):h("%s(...): No `render` method found on the returned component instance: you may have forgotten to define `render`.",r)),a.getInitialState&&!a.getInitialState.isReactClassApproved&&!a.state&&h("getInitialState was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Did you mean to define a state property instead?",r),a.getDefaultProps&&!a.getDefaultProps.isReactClassApproved&&h("getDefaultProps was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Use a static property to define defaultProps instead.",r),a.propTypes&&h("propTypes was defined as an instance property on %s. Use a static property to define propTypes instead.",r),a.contextType&&h("contextType was defined as an instance property on %s. Use a static property to define contextType instead.",r),a.contextTypes&&h("contextTypes was defined as an instance property on %s. Use a static property to define contextTypes instead.",r),t.contextType&&t.contextTypes&&!Pc.has(t)&&(Pc.add(t),h("%s declares both contextTypes and contextType static properties. The legacy contextTypes property will be ignored.",r)),typeof a.componentShouldUpdate=="function"&&h("%s has a method called componentShouldUpdate(). Did you mean shouldComponentUpdate()? The name is phrased as a question because the function is expected to return a value.",r),t.prototype&&t.prototype.isPureReactComponent&&typeof a.shouldComponentUpdate!="undefined"&&h("%s has a method called shouldComponentUpdate(). shouldComponentUpdate should not be used when extending React.PureComponent. Please extend React.Component if shouldComponentUpdate is used.",ee(t)||"A pure component"),typeof a.componentDidUnmount=="function"&&h("%s has a method called componentDidUnmount(). But there is no such lifecycle method. Did you mean componentWillUnmount()?",r),typeof a.componentDidReceiveProps=="function"&&h("%s has a method called componentDidReceiveProps(). But there is no such lifecycle method. If you meant to update the state in response to changing props, use componentWillReceiveProps(). If you meant to fetch data or run side-effects or mutations after React has updated the UI, use componentDidUpdate().",r),typeof a.componentWillRecieveProps=="function"&&h("%s has a method called componentWillRecieveProps(). Did you mean componentWillReceiveProps()?",r),typeof a.UNSAFE_componentWillRecieveProps=="function"&&h("%s has a method called UNSAFE_componentWillRecieveProps(). Did you mean UNSAFE_componentWillReceiveProps()?",r);var u=a.props!==n;a.props!==void 0&&u&&h("%s(...): When calling super() in `%s`, make sure to pass up the same props that your component's constructor was passed.",r,r),a.defaultProps&&h("Setting defaultProps as an instance property on %s is not supported and will be ignored. Instead, define defaultProps as a static property on %s.",r,r),typeof a.getSnapshotBeforeUpdate=="function"&&typeof a.componentDidUpdate!="function"&&!wc.has(t)&&(wc.add(t),h("%s: getSnapshotBeforeUpdate() should be used with componentDidUpdate(). This component defines getSnapshotBeforeUpdate() only.",ee(t))),typeof a.getDerivedStateFromProps=="function"&&h("%s: getDerivedStateFromProps() is defined as an instance method and will be ignored. Instead, declare it as a static method.",r),typeof a.getDerivedStateFromError=="function"&&h("%s: getDerivedStateFromError() is defined as an instance method and will be ignored. Instead, declare it as a static method.",r),typeof t.getSnapshotBeforeUpdate=="function"&&h("%s: getSnapshotBeforeUpdate() is defined as a static method and will be ignored. Instead, declare it as an instance method.",r);var o=a.state;o&&(typeof o!="object"||fn(o))&&h("%s.state: must be set to an object or null",r),typeof a.getChildContext=="function"&&typeof t.childContextTypes!="object"&&h("%s.getChildContext(): childContextTypes must be defined in order to use getChildContext().",r)}}function hp(e,t){t.updater=Xc,e.stateNode=t,et(t,e),t._reactInternalInstance=Fc}function yp(e,t,n){var a=!1,r=Vn,i=Vn,u=t.contextType;if("contextType"in t){var o=u===null||u!==void 0&&u.$$typeof===Ln&&u._context===void 0;if(!o&&!Gc.has(t)){Gc.add(t);var s="";u===void 0?s=" However, it is set to undefined. This can be caused by a typo or by mixing up named and default imports. This can also happen due to a circular dependency, so try moving the createContext() call to a separate file.":typeof u!="object"?s=" However, it is set to a "+typeof u+".":u.$$typeof===Sn?s=" Did you accidentally pass the Context.Provider instead?":u._context!==void 0?s=" Did you accidentally pass the Context.Consumer instead?":s=" However, it is set to an object with keys {"+Object.keys(u).join(", ")+"}.",h("%s defines an invalid contextType. contextType should point to the Context object returned by React.createContext().%s",ee(t)||"Component",s)}}if(typeof u=="object"&&u!==null)i=Ot(u);else{r=mi(e,t,!0);var f=t.contextTypes;a=f!=null,i=a?hi(e,r):Vn}var m=new t(n,i);if(e.mode&_t){Xt(!0);try{m=new t(n,i)}finally{Xt(!1)}}var S=e.memoizedState=m.state!==null&&m.state!==void 0?m.state:null;hp(e,m);{if(typeof t.getDerivedStateFromProps=="function"&&S===null){var C=ee(t)||"Component";Vc.has(C)||(Vc.add(C),h("`%s` uses `getDerivedStateFromProps` but its initial state is %s. This is not recommended. Instead, define the initial state by assigning an object to `this.state` in the constructor of `%s`. This ensures that `getDerivedStateFromProps` arguments have a consistent shape.",C,m.state===null?"null":"undefined",C))}if(typeof t.getDerivedStateFromProps=="function"||typeof m.getSnapshotBeforeUpdate=="function"){var O=null,U=null,N=null;if(typeof m.componentWillMount=="function"&&m.componentWillMount.__suppressDeprecationWarning!==!0?O="componentWillMount":typeof m.UNSAFE_componentWillMount=="function"&&(O="UNSAFE_componentWillMount"),typeof m.componentWillReceiveProps=="function"&&m.componentWillReceiveProps.__suppressDeprecationWarning!==!0?U="componentWillReceiveProps":typeof m.UNSAFE_componentWillReceiveProps=="function"&&(U="UNSAFE_componentWillReceiveProps"),typeof m.componentWillUpdate=="function"&&m.componentWillUpdate.__suppressDeprecationWarning!==!0?N="componentWillUpdate":typeof m.UNSAFE_componentWillUpdate=="function"&&(N="UNSAFE_componentWillUpdate"),O!==null||U!==null||N!==null){var I=ee(t)||"Component",oe=typeof t.getDerivedStateFromProps=="function"?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";Yc.has(I)||(Yc.add(I),h(`Unsafe legacy lifecycles will not be called for components using new component APIs.

%s uses %s but also contains the following legacy lifecycles:%s%s%s

The above lifecycles should be removed. Learn more about this warning here:
https://reactjs.org/link/unsafe-component-lifecycles`,I,oe,O!==null?`
  `+O:"",U!==null?`
  `+U:"",N!==null?`
  `+N:""))}}}return a&&pv(e,r,i),m}function US(e,t){var n=t.state;typeof t.componentWillMount=="function"&&t.componentWillMount(),typeof t.UNSAFE_componentWillMount=="function"&&t.UNSAFE_componentWillMount(),n!==t.state&&(h("%s.componentWillMount(): Assigning directly to this.state is deprecated (except inside a component's constructor). Use setState instead.",W(e)||"Component"),Xc.enqueueReplaceState(t,t.state,null))}function gp(e,t,n,a){var r=t.state;if(typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,a),t.state!==r){{var i=W(e)||"Component";Bc.has(i)||(Bc.add(i),h("%s.componentWillReceiveProps(): Assigning directly to this.state is deprecated (except inside a component's constructor). Use setState instead.",i))}Xc.enqueueReplaceState(t,t.state,null)}}function Jc(e,t,n,a){NS(e,t,n);var r=e.stateNode;r.props=n,r.state=e.memoizedState,r.refs=dp,Lc(e);var i=t.contextType;if(typeof i=="object"&&i!==null)r.context=Ot(i);else{var u=mi(e,t,!0);r.context=hi(e,u)}{if(r.state===n){var o=ee(t)||"Component";Qc.has(o)||(Qc.add(o),h("%s: It is not recommended to assign props directly to state because updates to props won't be reflected in state. In most cases, it is better to use props directly.",o))}e.mode&_t&&ma.recordLegacyContextWarning(e,r),ma.recordUnsafeLifecycleWarnings(e,r)}r.state=e.memoizedState;var s=t.getDerivedStateFromProps;if(typeof s=="function"&&(Kc(e,t,s,n),r.state=e.memoizedState),typeof t.getDerivedStateFromProps!="function"&&typeof r.getSnapshotBeforeUpdate!="function"&&(typeof r.UNSAFE_componentWillMount=="function"||typeof r.componentWillMount=="function")&&(US(e,r),ao(e,n,r,a),r.state=e.memoizedState),typeof r.componentDidMount=="function"){var f=ce;f|=V,(e.mode&Aa)!==le&&(f|=be),e.flags|=f}}function MS(e,t,n,a){var r=e.stateNode,i=e.memoizedProps;r.props=i;var u=r.context,o=t.contextType,s=Vn;if(typeof o=="object"&&o!==null)s=Ot(o);else{var f=mi(e,t,!0);s=hi(e,f)}var m=t.getDerivedStateFromProps,S=typeof m=="function"||typeof r.getSnapshotBeforeUpdate=="function";!S&&(typeof r.UNSAFE_componentWillReceiveProps=="function"||typeof r.componentWillReceiveProps=="function")&&(i!==n||u!==s)&&gp(e,r,n,s),cp();var C=e.memoizedState,O=r.state=C;if(ao(e,n,r,a),O=e.memoizedState,i===n&&C===O&&!Al()&&!ro()){if(typeof r.componentDidMount=="function"){var U=ce;U|=V,(e.mode&Aa)!==le&&(U|=be),e.flags|=U}return!1}typeof m=="function"&&(Kc(e,t,m,n),O=e.memoizedState);var N=ro()||mp(e,t,i,n,C,O,s);if(N){if(!S&&(typeof r.UNSAFE_componentWillMount=="function"||typeof r.componentWillMount=="function")&&(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"){var I=ce;I|=V,(e.mode&Aa)!==le&&(I|=be),e.flags|=I}}else{if(typeof r.componentDidMount=="function"){var oe=ce;oe|=V,(e.mode&Aa)!==le&&(oe|=be),e.flags|=oe}e.memoizedProps=n,e.memoizedState=O}return r.props=n,r.state=O,r.context=s,N}function AS(e,t,n,a,r){var i=t.stateNode;sp(e,t);var u=t.memoizedProps,o=t.type===t.elementType?u:ha(t.type,u);i.props=o;var s=t.pendingProps,f=i.context,m=n.contextType,S=Vn;if(typeof m=="object"&&m!==null)S=Ot(m);else{var C=mi(t,n,!0);S=hi(t,C)}var O=n.getDerivedStateFromProps,U=typeof O=="function"||typeof i.getSnapshotBeforeUpdate=="function";!U&&(typeof i.UNSAFE_componentWillReceiveProps=="function"||typeof i.componentWillReceiveProps=="function")&&(u!==s||f!==S)&&gp(t,i,a,S),cp();var N=t.memoizedState,I=i.state=N;if(ao(t,a,i,r),I=t.memoizedState,u===s&&N===I&&!Al()&&!ro()&&!qe)return typeof i.componentDidUpdate=="function"&&(u!==e.memoizedProps||N!==e.memoizedState)&&(t.flags|=ce),typeof i.getSnapshotBeforeUpdate=="function"&&(u!==e.memoizedProps||N!==e.memoizedState)&&(t.flags|=sn),!1;typeof O=="function"&&(Kc(t,n,O,a),I=t.memoizedState);var oe=ro()||mp(t,n,o,a,N,I,S)||qe;return oe?(!U&&(typeof i.UNSAFE_componentWillUpdate=="function"||typeof i.componentWillUpdate=="function")&&(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(a,I,S),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(a,I,S)),typeof i.componentDidUpdate=="function"&&(t.flags|=ce),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=sn)):(typeof i.componentDidUpdate=="function"&&(u!==e.memoizedProps||N!==e.memoizedState)&&(t.flags|=ce),typeof i.getSnapshotBeforeUpdate=="function"&&(u!==e.memoizedProps||N!==e.memoizedState)&&(t.flags|=sn),t.memoizedProps=a,t.memoizedState=I),i.props=a,i.state=I,i.context=S,oe}var Zc,kc,Wc,Ic,$c,Sp=function(e,t){};Zc=!1,kc=!1,Wc={},Ic={},$c={},Sp=function(e,t){if(!(e===null||typeof e!="object")&&!(!e._store||e._store.validated||e.key!=null)){if(typeof e._store!="object")throw new Error("React Component in warnForMissingKey should have a _store. This error is likely caused by a bug in React. Please file an issue.");e._store.validated=!0;var n=W(t)||"Component";Ic[n]||(Ic[n]=!0,h('Each child in a list should have a unique "key" prop. See https://reactjs.org/link/warning-keys for more information.'))}};function Mu(e,t,n){var a=n.ref;if(a!==null&&typeof a!="function"&&typeof a!="object"){if((e.mode&_t||xe)&&!(n._owner&&n._self&&n._owner.stateNode!==n._self)){var r=W(e)||"Component";Wc[r]||(h('A string ref, "%s", has been found within a strict mode tree. String refs are a source of potential bugs and should be avoided. We recommend using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',a),Wc[r]=!0)}if(n._owner){var i=n._owner,u;if(i){var o=i;if(o.tag!==ye)throw new Error("Function components cannot have string refs. We recommend using useRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref");u=o.stateNode}if(!u)throw new Error("Missing owner for string ref "+a+". This error is likely caused by a bug in React. Please file an issue.");var s=u;bS(a,"ref");var f=""+a;if(t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===f)return t.ref;var m=function(S){var C=s.refs;C===dp&&(C=s.refs={}),S===null?delete C[f]:C[f]=S};return m._stringRef=f,m}else{if(typeof a!="string")throw new Error("Expected ref to be a function, a string, an object returned by React.createRef(), or null.");if(!n._owner)throw new Error("Element ref was specified as a string ("+a+`) but no owner was set. This could happen for one of the following reasons:
1. You may be adding a ref to a function component
2. You may be adding a ref to a component that was not created inside a component's render method
3. You have multiple copies of React loaded
See https://reactjs.org/link/refs-must-have-owner for more information.`)}}return a}function uo(e,t){var n=Object.prototype.toString.call(t);throw new Error("Objects are not valid as a React child (found: "+(n==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":n)+"). If you meant to render a collection of children, use an array instead.")}function lo(e){{var t=W(e)||"Component";if($c[t])return;$c[t]=!0,h("Functions are not valid as a React child. This may happen if you return a Component instead of <Component /> from render. Or maybe you meant to call this function rather than return it.")}}function bp(e){var t=e._payload,n=e._init;return n(t)}function Ep(e){function t(E,D){if(e){var b=E.deletions;b===null?(E.deletions=[D],E.flags|=Wt):b.push(D)}}function n(E,D){if(!e)return null;for(var b=D;b!==null;)t(E,b),b=b.sibling;return null}function a(E,D){for(var b=new Map,L=D;L!==null;)L.key!==null?b.set(L.key,L):b.set(L.index,L),L=L.sibling;return b}function r(E,D){var b=li(E,D);return b.index=0,b.sibling=null,b}function i(E,D,b){if(E.index=b,!e)return E.flags|=d,D;var L=E.alternate;if(L!==null){var k=L.index;return k<D?(E.flags|=Qe,D):k}else return E.flags|=Qe,D}function u(E){return e&&E.alternate===null&&(E.flags|=Qe),E}function o(E,D,b,L){if(D===null||D.tag!==ge){var k=Md(b,E.mode,L);return k.return=E,k}else{var K=r(D,b);return K.return=E,K}}function s(E,D,b,L){var k=b.type;if(k===Zn)return m(E,D,b.props.children,L,b.key);if(D!==null&&(D.elementType===k||dh(D,b)||typeof k=="object"&&k!==null&&k.$$typeof===it&&bp(k)===D.type)){var K=r(D,b.props);return K.ref=Mu(E,D,b),K.return=E,K._debugSource=b._source,K._debugOwner=b._owner,K}var He=Ud(b,E.mode,L);return He.ref=Mu(E,D,b),He.return=E,He}function f(E,D,b,L){if(D===null||D.tag!==Ue||D.stateNode.containerInfo!==b.containerInfo||D.stateNode.implementation!==b.implementation){var k=Ad(b,E.mode,L);return k.return=E,k}else{var K=r(D,b.children||[]);return K.return=E,K}}function m(E,D,b,L,k){if(D===null||D.tag!==Te){var K=Lr(b,E.mode,L,k);return K.return=E,K}else{var He=r(D,b);return He.return=E,He}}function S(E,D,b){if(typeof D=="string"&&D!==""||typeof D=="number"){var L=Md(""+D,E.mode,b);return L.return=E,L}if(typeof D=="object"&&D!==null){switch(D.$$typeof){case Dn:{var k=Ud(D,E.mode,b);return k.ref=Mu(E,null,D),k.return=E,k}case _n:{var K=Ad(D,E.mode,b);return K.return=E,K}case it:{var He=D._payload,de=D._init;return S(E,de(He),b)}}if(fn(D)||w(D)){var we=Lr(D,E.mode,b,null);return we.return=E,we}uo(E,D)}return typeof D=="function"&&lo(E),null}function C(E,D,b,L){var k=D!==null?D.key:null;if(typeof b=="string"&&b!==""||typeof b=="number")return k!==null?null:o(E,D,""+b,L);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case Dn:return b.key===k?s(E,D,b,L):null;case _n:return b.key===k?f(E,D,b,L):null;case it:{var K=b._payload,He=b._init;return C(E,D,He(K),L)}}if(fn(b)||w(b))return k!==null?null:m(E,D,b,L,null);uo(E,b)}return typeof b=="function"&&lo(E),null}function O(E,D,b,L,k){if(typeof L=="string"&&L!==""||typeof L=="number"){var K=E.get(b)||null;return o(D,K,""+L,k)}if(typeof L=="object"&&L!==null){switch(L.$$typeof){case Dn:{var He=E.get(L.key===null?b:L.key)||null;return s(D,He,L,k)}case _n:{var de=E.get(L.key===null?b:L.key)||null;return f(D,de,L,k)}case it:var we=L._payload,_e=L._init;return O(E,D,b,_e(we),k)}if(fn(L)||w(L)){var nt=E.get(b)||null;return m(D,nt,L,k,null)}uo(D,L)}return typeof L=="function"&&lo(D),null}function U(E,D,b){{if(typeof E!="object"||E===null)return D;switch(E.$$typeof){case Dn:case _n:Sp(E,b);var L=E.key;if(typeof L!="string")break;if(D===null){D=new Set,D.add(L);break}if(!D.has(L)){D.add(L);break}h("Encountered two children with the same key, `%s`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted \u2014 the behavior is unsupported and could change in a future version.",L);break;case it:var k=E._payload,K=E._init;U(K(k),D,b);break}}return D}function N(E,D,b,L){for(var k=null,K=0;K<b.length;K++){var He=b[K];k=U(He,k,E)}for(var de=null,we=null,_e=D,nt=0,ae=0,yt=null;_e!==null&&ae<b.length;ae++){_e.index>ae?(yt=_e,_e=null):yt=_e.sibling;var mn=C(E,_e,b[ae],L);if(mn===null){_e===null&&(_e=yt);break}e&&_e&&mn.alternate===null&&t(E,_e),nt=i(mn,nt,ae),we===null?de=mn:we.sibling=mn,we=mn,_e=yt}if(ae===b.length){if(n(E,_e),tn()){var on=ae;Zr(E,on)}return de}if(_e===null){for(;ae<b.length;ae++){var Gn=S(E,b[ae],L);Gn!==null&&(nt=i(Gn,nt,ae),we===null?de=Gn:we.sibling=Gn,we=Gn)}if(tn()){var Cn=ae;Zr(E,Cn)}return de}for(var xn=a(E,_e);ae<b.length;ae++){var hn=O(xn,E,ae,b[ae],L);hn!==null&&(e&&hn.alternate!==null&&xn.delete(hn.key===null?ae:hn.key),nt=i(hn,nt,ae),we===null?de=hn:we.sibling=hn,we=hn)}if(e&&xn.forEach(function(Gi){return t(E,Gi)}),tn()){var fr=ae;Zr(E,fr)}return de}function I(E,D,b,L){var k=w(b);if(typeof k!="function")throw new Error("An object is not an iterable. This error is likely caused by a bug in React. Please file an issue.");{typeof Symbol=="function"&&b[Symbol.toStringTag]==="Generator"&&(kc||h("Using Generators as children is unsupported and will likely yield unexpected results because enumerating a generator mutates it. You may convert it to an array with `Array.from()` or the `[...spread]` operator before rendering. Keep in mind you might need to polyfill these features for older browsers."),kc=!0),b.entries===k&&(Zc||h("Using Maps as children is not supported. Use an array of keyed ReactElements instead."),Zc=!0);var K=k.call(b);if(K)for(var He=null,de=K.next();!de.done;de=K.next()){var we=de.value;He=U(we,He,E)}}var _e=k.call(b);if(_e==null)throw new Error("An iterable object provided no iterator.");for(var nt=null,ae=null,yt=D,mn=0,on=0,Gn=null,Cn=_e.next();yt!==null&&!Cn.done;on++,Cn=_e.next()){yt.index>on?(Gn=yt,yt=null):Gn=yt.sibling;var xn=C(E,yt,Cn.value,L);if(xn===null){yt===null&&(yt=Gn);break}e&&yt&&xn.alternate===null&&t(E,yt),mn=i(xn,mn,on),ae===null?nt=xn:ae.sibling=xn,ae=xn,yt=Gn}if(Cn.done){if(n(E,yt),tn()){var hn=on;Zr(E,hn)}return nt}if(yt===null){for(;!Cn.done;on++,Cn=_e.next()){var fr=S(E,Cn.value,L);fr!==null&&(mn=i(fr,mn,on),ae===null?nt=fr:ae.sibling=fr,ae=fr)}if(tn()){var Gi=on;Zr(E,Gi)}return nt}for(var ol=a(E,yt);!Cn.done;on++,Cn=_e.next()){var Ja=O(ol,E,on,Cn.value,L);Ja!==null&&(e&&Ja.alternate!==null&&ol.delete(Ja.key===null?on:Ja.key),mn=i(Ja,mn,on),ae===null?nt=Ja:ae.sibling=Ja,ae=Ja)}if(e&&ol.forEach(function(WR){return t(E,WR)}),tn()){var kR=on;Zr(E,kR)}return nt}function oe(E,D,b,L){if(D!==null&&D.tag===ge){n(E,D.sibling);var k=r(D,b);return k.return=E,k}n(E,D);var K=Md(b,E.mode,L);return K.return=E,K}function te(E,D,b,L){for(var k=b.key,K=D;K!==null;){if(K.key===k){var He=b.type;if(He===Zn){if(K.tag===Te){n(E,K.sibling);var de=r(K,b.props.children);return de.return=E,de._debugSource=b._source,de._debugOwner=b._owner,de}}else if(K.elementType===He||dh(K,b)||typeof He=="object"&&He!==null&&He.$$typeof===it&&bp(He)===K.type){n(E,K.sibling);var we=r(K,b.props);return we.ref=Mu(E,K,b),we.return=E,we._debugSource=b._source,we._debugOwner=b._owner,we}n(E,K);break}else t(E,K);K=K.sibling}if(b.type===Zn){var _e=Lr(b.props.children,E.mode,L,b.key);return _e.return=E,_e}else{var nt=Ud(b,E.mode,L);return nt.ref=Mu(E,D,b),nt.return=E,nt}}function Ke(E,D,b,L){for(var k=b.key,K=D;K!==null;){if(K.key===k)if(K.tag===Ue&&K.stateNode.containerInfo===b.containerInfo&&K.stateNode.implementation===b.implementation){n(E,K.sibling);var He=r(K,b.children||[]);return He.return=E,He}else{n(E,K);break}else t(E,K);K=K.sibling}var de=Ad(b,E.mode,L);return de.return=E,de}function je(E,D,b,L){var k=typeof b=="object"&&b!==null&&b.type===Zn&&b.key===null;if(k&&(b=b.props.children),typeof b=="object"&&b!==null){switch(b.$$typeof){case Dn:return u(te(E,D,b,L));case _n:return u(Ke(E,D,b,L));case it:var K=b._payload,He=b._init;return je(E,D,He(K),L)}if(fn(b))return N(E,D,b,L);if(w(b))return I(E,D,b,L);uo(E,b)}return typeof b=="string"&&b!==""||typeof b=="number"?u(oe(E,D,""+b,L)):(typeof b=="function"&&lo(E),n(E,D))}return je}var Ni=Ep(!0),Rp=Ep(!1);function zS(e,t){if(e!==null&&t.child!==e.child)throw new Error("Resuming work not yet implemented.");if(t.child!==null){var n=t.child,a=li(n,n.pendingProps);for(t.child=a,a.return=t;n.sibling!==null;)n=n.sibling,a=a.sibling=li(n,n.pendingProps),a.return=t;a.sibling=null}}function HS(e,t){for(var n=e.child;n!==null;)DR(n,t),n=n.sibling}var Au={},_r=Tr(Au),zu=Tr(Au),oo=Tr(Au);function so(e){if(e===Au)throw new Error("Expected host context to exist. This error is likely caused by a bug in React. Please file an issue.");return e}function ef(){var e=so(oo.current);return e}function tf(e,t){It(oo,t,e),It(zu,e,e),It(_r,Au,e);var n=ml(t);dn(_r,e),It(_r,n,e)}function Ui(e){dn(_r,e),dn(zu,e),dn(oo,e)}function Hu(){var e=so(_r.current);return e}function Tp(e){var t=so(oo.current),n=so(_r.current),a=Es(n,e.type,t);n!==a&&(It(zu,e,e),It(_r,a,e))}function nf(e){zu.current===e&&(dn(_r,e),dn(zu,e))}var LS=0,Cp=1,xp=1,Lu=2,ya=Tr(LS);function af(e,t){return(e&t)!==0}function Mi(e){return e&Cp}function rf(e,t){return e&Cp|t}function jS(e,t){return e|t}function Or(e,t){It(ya,t,e)}function Ai(e){dn(ya,e)}function FS(e,t){var n=e.memoizedState;if(n!==null)return n.dehydrated!==null;var a=e.memoizedProps;return!0}function co(e){for(var t=e;t!==null;){if(t.tag===Fe){var n=t.memoizedState;if(n!==null){var a=n.dehydrated;if(a===null||Dt(a)||Er(a))return t}}else if(t.tag===St&&t.memoizedProps.revealOrder!==void 0){var r=(t.flags&ze)!==X;if(r)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)return null;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Un=0,At=1,Ba=2,zt=4,nn=8,uf=[];function lf(){for(var e=0;e<uf.length;e++){var t=uf[e];Yr?t._workInProgressVersionPrimary=null:t._workInProgressVersionSecondary=null}uf.length=0}function BS(e,t){var n=t._getVersion,a=n(t._source);e.mutableSourceEagerHydrationData==null?e.mutableSourceEagerHydrationData=[t,a]:e.mutableSourceEagerHydrationData.push(t,a)}var G=M.ReactCurrentDispatcher,ju=M.ReactCurrentBatchConfig,of,zi;of=new Set;var ei=A,We=null,Ht=null,Lt=null,fo=!1,Fu=!1,Bu=0,VS=0,wS=25,_=null,ta=null,Nr=-1,sf=!1;function Ge(){{var e=_;ta===null?ta=[e]:ta.push(e)}}function Y(){{var e=_;ta!==null&&(Nr++,ta[Nr]!==e&&YS(e))}}function Hi(e){e!=null&&!fn(e)&&h("%s received a final argument that is not an array (instead, received `%s`). When specified, the final argument must be an array.",_,typeof e)}function YS(e){{var t=W(We);if(!of.has(t)&&(of.add(t),ta!==null)){for(var n="",a=30,r=0;r<=Nr;r++){for(var i=ta[r],u=r===Nr?e:i,o=r+1+". "+i;o.length<a;)o+=" ";o+=u+`
`,n+=o}h(`React has detected a change in the order of Hooks called by %s. This will lead to bugs and errors if not fixed. For more information, read the Rules of Hooks: https://reactjs.org/link/rules-of-hooks

   Previous render            Next render
   ------------------------------------------------------
%s   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
`,t,n)}}}function vn(){throw new Error(`Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.`)}function cf(e,t){if(sf)return!1;if(t===null)return h("%s received a final argument during this render, but not during the previous render. Even though the final argument is optional, its type cannot change between renders.",_),!1;e.length!==t.length&&h(`The final argument passed to %s changed size between renders. The order and size of this array must remain constant.

Previous: %s
Incoming: %s`,_,"["+t.join(", ")+"]","["+e.join(", ")+"]");for(var n=0;n<t.length&&n<e.length;n++)if(!qn(e[n],t[n]))return!1;return!0}function Li(e,t,n,a,r,i){ei=i,We=t,ta=e!==null?e._debugHookTypes:null,Nr=-1,sf=e!==null&&e.type!==t.type,t.memoizedState=null,t.updateQueue=null,t.lanes=A,e!==null&&e.memoizedState!==null?G.current=Xp:ta!==null?G.current=Kp:G.current=Gp;var u=n(a,r);if(Fu){var o=0;do{if(Fu=!1,Bu=0,o>=wS)throw new Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");o+=1,sf=!1,Ht=null,Lt=null,t.updateQueue=null,Nr=-1,G.current=Jp,u=n(a,r)}while(Fu)}G.current=xo,t._debugHookTypes=ta;var s=Ht!==null&&Ht.next!==null;if(ei=A,We=null,Ht=null,Lt=null,_=null,ta=null,Nr=-1,e!==null&&(e.flags&Pe)!==(t.flags&Pe)&&(e.mode&Be)!==le&&h("Internal React error: Expected static flag was missing. Please notify the React team."),fo=!1,s)throw new Error("Rendered fewer hooks than expected. This may be caused by an accidental early return statement.");return u}function ji(){var e=Bu!==0;return Bu=0,e}function Dp(e,t,n){t.updateQueue=e.updateQueue,(t.mode&Aa)!==le?t.flags&=~(Ve|be|Yt|ce):t.flags&=~(Yt|ce),e.lanes=wl(e.lanes,n)}function _p(){if(G.current=xo,fo){for(var e=We.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}fo=!1}ei=A,We=null,Ht=null,Lt=null,ta=null,Nr=-1,_=null,wp=!1,Fu=!1,Bu=0}function Va(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Lt===null?We.memoizedState=Lt=e:Lt=Lt.next=e,Lt}function na(){var e;if(Ht===null){var t=We.alternate;t!==null?e=t.memoizedState:e=null}else e=Ht.next;var n;if(Lt===null?n=We.memoizedState:n=Lt.next,n!==null)Lt=n,n=Lt.next,Ht=e;else{if(e===null)throw new Error("Rendered more hooks than during the previous render.");Ht=e;var a={memoizedState:Ht.memoizedState,baseState:Ht.baseState,baseQueue:Ht.baseQueue,queue:Ht.queue,next:null};Lt===null?We.memoizedState=Lt=a:Lt=Lt.next=a}return Lt}function Op(){return{lastEffect:null,stores:null}}function ff(e,t){return typeof t=="function"?t(e):t}function df(e,t,n){var a=Va(),r;n!==void 0?r=n(t):r=t,a.memoizedState=a.baseState=r;var i={pending:null,interleaved:null,lanes:A,dispatch:null,lastRenderedReducer:e,lastRenderedState:r};a.queue=i;var u=i.dispatch=GS.bind(null,We,i);return[a.memoizedState,u]}function vf(e,t,n){var a=na(),r=a.queue;if(r===null)throw new Error("Should have a queue. This is likely a bug in React. Please file an issue.");r.lastRenderedReducer=e;var i=Ht,u=i.baseQueue,o=r.pending;if(o!==null){if(u!==null){var s=u.next,f=o.next;u.next=f,o.next=s}i.baseQueue!==u&&h("Internal error: Expected work-in-progress queue to be a clone. This is a bug in React."),i.baseQueue=u=o,r.pending=null}if(u!==null){var m=u.next,S=i.baseState,C=null,O=null,U=null,N=m;do{var I=N.lane;if(Ei(ei,I)){if(U!==null){var te={lane:Pt,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null};U=U.next=te}if(N.hasEagerState)S=N.eagerState;else{var Ke=N.action;S=e(S,Ke)}}else{var oe={lane:I,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null};U===null?(O=U=oe,C=S):U=U.next=oe,We.lanes=Ee(We.lanes,I),il(I)}N=N.next}while(N!==null&&N!==m);U===null?C=S:U.next=O,qn(S,a.memoizedState)||Gu(),a.memoizedState=S,a.baseState=C,a.baseQueue=U,r.lastRenderedState=S}var je=r.interleaved;if(je!==null){var E=je;do{var D=E.lane;We.lanes=Ee(We.lanes,D),il(D),E=E.next}while(E!==je)}else u===null&&(r.lanes=A);var b=r.dispatch;return[a.memoizedState,b]}function pf(e,t,n){var a=na(),r=a.queue;if(r===null)throw new Error("Should have a queue. This is likely a bug in React. Please file an issue.");r.lastRenderedReducer=e;var i=r.dispatch,u=r.pending,o=a.memoizedState;if(u!==null){r.pending=null;var s=u.next,f=s;do{var m=f.action;o=e(o,m),f=f.next}while(f!==s);qn(o,a.memoizedState)||Gu(),a.memoizedState=o,a.baseQueue===null&&(a.baseState=o),r.lastRenderedState=o}return[o,i]}function bT(e,t,n){}function ET(e,t,n){}function mf(e,t,n){var a=We,r=Va(),i,u=tn();if(u){if(n===void 0)throw new Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");i=n(),zi||i!==n()&&(h("The result of getServerSnapshot should be cached to avoid an infinite loop"),zi=!0)}else{if(i=t(),!zi){var o=t();qn(i,o)||(h("The result of getSnapshot should be cached to avoid an infinite loop"),zi=!0)}var s=$o();if(s===null)throw new Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");Vl(s,ei)||Np(a,t,i)}r.memoizedState=i;var f={value:i,getSnapshot:t};return r.queue=f,yo(Mp.bind(null,a,f,e),[e]),a.flags|=Yt,Vu(At|nn,Up.bind(null,a,f,i,t),void 0,null),i}function vo(e,t,n){var a=We,r=na(),i=t();if(!zi){var u=t();qn(i,u)||(h("The result of getSnapshot should be cached to avoid an infinite loop"),zi=!0)}var o=r.memoizedState,s=!qn(o,i);s&&(r.memoizedState=i,Gu());var f=r.queue;if(Yu(Mp.bind(null,a,f,e),[e]),f.getSnapshot!==t||s||Lt!==null&&Lt.memoizedState.tag&At){a.flags|=Yt,Vu(At|nn,Up.bind(null,a,f,i,t),void 0,null);var m=$o();if(m===null)throw new Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");Vl(m,ei)||Np(a,t,i)}return i}function Np(e,t,n){e.flags|=$a;var a={getSnapshot:t,value:n},r=We.updateQueue;if(r===null)r=Op(),We.updateQueue=r,r.stores=[a];else{var i=r.stores;i===null?r.stores=[a]:i.push(a)}}function Up(e,t,n,a){t.value=n,t.getSnapshot=a,Ap(t)&&zp(e)}function Mp(e,t,n){var a=function(){Ap(t)&&zp(e)};return n(a)}function Ap(e){var t=e.getSnapshot,n=e.value;try{var a=t();return!qn(n,a)}catch(r){return!0}}function zp(e){var t=En(e,fe);t!==null&&Ut(t,e,fe,ot)}function po(e){var t=Va();typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e;var n={pending:null,interleaved:null,lanes:A,dispatch:null,lastRenderedReducer:ff,lastRenderedState:e};t.queue=n;var a=n.dispatch=KS.bind(null,We,n);return[t.memoizedState,a]}function hf(e){return vf(ff)}function yf(e){return pf(ff)}function Vu(e,t,n,a){var r={tag:e,create:t,destroy:n,deps:a,next:null},i=We.updateQueue;if(i===null)i=Op(),We.updateQueue=i,i.lastEffect=r.next=r;else{var u=i.lastEffect;if(u===null)i.lastEffect=r.next=r;else{var o=u.next;u.next=r,r.next=o,i.lastEffect=r}}return r}function gf(e){var t=Va();{var n={current:e};return t.memoizedState=n,n}}function mo(e){var t=na();return t.memoizedState}function wu(e,t,n,a){var r=Va(),i=a===void 0?null:a;We.flags|=e,r.memoizedState=Vu(At|t,n,void 0,i)}function ho(e,t,n,a){var r=na(),i=a===void 0?null:a,u=void 0;if(Ht!==null){var o=Ht.memoizedState;if(u=o.destroy,i!==null){var s=o.deps;if(cf(i,s)){r.memoizedState=Vu(t,n,u,i);return}}}We.flags|=e,r.memoizedState=Vu(At|t,n,u,i)}function yo(e,t){return(We.mode&Aa)!==le?wu(Ve|Yt|re,nn,e,t):wu(Yt|re,nn,e,t)}function Yu(e,t){return ho(Yt,nn,e,t)}function Sf(e,t){return wu(ce,Ba,e,t)}function go(e,t){return ho(ce,Ba,e,t)}function bf(e,t){var n=ce;return n|=V,(We.mode&Aa)!==le&&(n|=be),wu(n,zt,e,t)}function So(e,t){return ho(ce,zt,e,t)}function Hp(e,t){if(typeof t=="function"){var n=t,a=e();return n(a),function(){n(null)}}else if(t!=null){var r=t;r.hasOwnProperty("current")||h("Expected useImperativeHandle() first argument to either be a ref callback or React.createRef() object. Instead received: %s.","an object with keys {"+Object.keys(r).join(", ")+"}");var i=e();return r.current=i,function(){r.current=null}}}function Ef(e,t,n){typeof t!="function"&&h("Expected useImperativeHandle() second argument to be a function that creates a handle. Instead received: %s.",t!==null?typeof t:"null");var a=n!=null?n.concat([e]):null,r=ce;return r|=V,(We.mode&Aa)!==le&&(r|=be),wu(r,zt,Hp.bind(null,t,e),a)}function bo(e,t,n){typeof t!="function"&&h("Expected useImperativeHandle() second argument to be a function that creates a handle. Instead received: %s.",t!==null?typeof t:"null");var a=n!=null?n.concat([e]):null;return ho(ce,zt,Hp.bind(null,t,e),a)}function qS(e,t){}var Eo=qS;function Rf(e,t){var n=Va(),a=t===void 0?null:t;return n.memoizedState=[e,a],e}function Ro(e,t){var n=na(),a=t===void 0?null:t,r=n.memoizedState;if(r!==null&&a!==null){var i=r[1];if(cf(a,i))return r[0]}return n.memoizedState=[e,a],e}function Tf(e,t){var n=Va(),a=t===void 0?null:t,r=e();return n.memoizedState=[r,a],r}function To(e,t){var n=na(),a=t===void 0?null:t,r=n.memoizedState;if(r!==null&&a!==null){var i=r[1];if(cf(a,i))return r[0]}var u=e();return n.memoizedState=[u,a],u}function Cf(e){var t=Va();return t.memoizedState=e,e}function Lp(e){var t=na(),n=Ht,a=n.memoizedState;return Fp(t,a,e)}function jp(e){var t=na();if(Ht===null)return t.memoizedState=e,e;var n=Ht.memoizedState;return Fp(t,n,e)}function Fp(e,t,n){var a=!sg(ei);if(a){if(!qn(n,t)){var r=Cv();We.lanes=Ee(We.lanes,r),il(r),e.baseState=!0}return t}else return e.baseState&&(e.baseState=!1,Gu()),e.memoizedState=n,n}function QS(e,t,n){var a=da();Gt(gg(a,Su)),e(!0);var r=ju.transition;ju.transition={};var i=ju.transition;ju.transition._updatedFibers=new Set;try{e(!1),t()}finally{if(Gt(a),ju.transition=r,r===null&&i._updatedFibers){var u=i._updatedFibers.size;u>10&&H("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table."),i._updatedFibers.clear()}}}function xf(){var e=po(!1),t=e[0],n=e[1],a=QS.bind(null,n),r=Va();return r.memoizedState=a,[t,a]}function Bp(){var e=hf(),t=e[0],n=na(),a=n.memoizedState;return[t,a]}function Vp(){var e=yf(),t=e[0],n=na(),a=n.memoizedState;return[t,a]}var wp=!1;function PS(){return wp}function Df(){var e=Va(),t=$o(),n=t.identifierPrefix,a;if(tn()){var r=Ig();a=":"+n+"R"+r;var i=Bu++;i>0&&(a+="H"+i.toString(32)),a+=":"}else{var u=VS++;a=":"+n+"r"+u.toString(32)+":"}return e.memoizedState=a,a}function Co(){var e=na(),t=e.memoizedState;return t}function GS(e,t,n){typeof arguments[3]=="function"&&h("State updates from the useState() and useReducer() Hooks don't support the second callback argument. To execute a side effect after rendering, declare it in the component body with useEffect().");var a=zr(e),r={lane:a,action:n,hasEagerState:!1,eagerState:null,next:null};if(Yp(e))qp(t,r);else{var i=up(e,t,r,a);if(i!==null){var u=pn();Ut(i,e,a,u),Qp(i,t,a)}}Pp(e,a)}function KS(e,t,n){typeof arguments[3]=="function"&&h("State updates from the useState() and useReducer() Hooks don't support the second callback argument. To execute a side effect after rendering, declare it in the component body with useEffect().");var a=zr(e),r={lane:a,action:n,hasEagerState:!1,eagerState:null,next:null};if(Yp(e))qp(t,r);else{var i=e.alternate;if(e.lanes===A&&(i===null||i.lanes===A)){var u=t.lastRenderedReducer;if(u!==null){var o;o=G.current,G.current=ga;try{var s=t.lastRenderedState,f=u(s,n);if(r.hasEagerState=!0,r.eagerState=f,qn(f,s)){CS(e,t,r,a);return}}catch(C){}finally{G.current=o}}}var m=up(e,t,r,a);if(m!==null){var S=pn();Ut(m,e,a,S),Qp(m,t,a)}}Pp(e,a)}function Yp(e){var t=e.alternate;return e===We||t!==null&&t===We}function qp(e,t){Fu=fo=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Qp(e,t,n){if(Tv(n)){var a=t.lanes;a=xv(a,e.pendingLanes);var r=Ee(a,n);t.lanes=r,hc(e,r)}}function Pp(e,t,n){gc(e,t)}var xo={readContext:Ot,useCallback:vn,useContext:vn,useEffect:vn,useImperativeHandle:vn,useInsertionEffect:vn,useLayoutEffect:vn,useMemo:vn,useReducer:vn,useRef:vn,useState:vn,useDebugValue:vn,useDeferredValue:vn,useTransition:vn,useMutableSource:vn,useSyncExternalStore:vn,useId:vn,unstable_isNewReconciler:Ye},Gp=null,Kp=null,Xp=null,Jp=null,wa=null,ga=null,Do=null;{var _f=function(){h("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().")},ve=function(){h("Do not call Hooks inside useEffect(...), useMemo(...), or other built-in Hooks. You can only call Hooks at the top level of your React function. For more information, see https://reactjs.org/link/rules-of-hooks")};Gp={readContext:function(e){return Ot(e)},useCallback:function(e,t){return _="useCallback",Ge(),Hi(t),Rf(e,t)},useContext:function(e){return _="useContext",Ge(),Ot(e)},useEffect:function(e,t){return _="useEffect",Ge(),Hi(t),yo(e,t)},useImperativeHandle:function(e,t,n){return _="useImperativeHandle",Ge(),Hi(n),Ef(e,t,n)},useInsertionEffect:function(e,t){return _="useInsertionEffect",Ge(),Hi(t),Sf(e,t)},useLayoutEffect:function(e,t){return _="useLayoutEffect",Ge(),Hi(t),bf(e,t)},useMemo:function(e,t){_="useMemo",Ge(),Hi(t);var n=G.current;G.current=wa;try{return Tf(e,t)}finally{G.current=n}},useReducer:function(e,t,n){_="useReducer",Ge();var a=G.current;G.current=wa;try{return df(e,t,n)}finally{G.current=a}},useRef:function(e){return _="useRef",Ge(),gf(e)},useState:function(e){_="useState",Ge();var t=G.current;G.current=wa;try{return po(e)}finally{G.current=t}},useDebugValue:function(e,t){return _="useDebugValue",Ge(),void 0},useDeferredValue:function(e){return _="useDeferredValue",Ge(),Cf(e)},useTransition:function(){return _="useTransition",Ge(),xf()},useMutableSource:function(e,t,n){return _="useMutableSource",Ge(),void 0},useSyncExternalStore:function(e,t,n){return _="useSyncExternalStore",Ge(),mf(e,t,n)},useId:function(){return _="useId",Ge(),Df()},unstable_isNewReconciler:Ye},Kp={readContext:function(e){return Ot(e)},useCallback:function(e,t){return _="useCallback",Y(),Rf(e,t)},useContext:function(e){return _="useContext",Y(),Ot(e)},useEffect:function(e,t){return _="useEffect",Y(),yo(e,t)},useImperativeHandle:function(e,t,n){return _="useImperativeHandle",Y(),Ef(e,t,n)},useInsertionEffect:function(e,t){return _="useInsertionEffect",Y(),Sf(e,t)},useLayoutEffect:function(e,t){return _="useLayoutEffect",Y(),bf(e,t)},useMemo:function(e,t){_="useMemo",Y();var n=G.current;G.current=wa;try{return Tf(e,t)}finally{G.current=n}},useReducer:function(e,t,n){_="useReducer",Y();var a=G.current;G.current=wa;try{return df(e,t,n)}finally{G.current=a}},useRef:function(e){return _="useRef",Y(),gf(e)},useState:function(e){_="useState",Y();var t=G.current;G.current=wa;try{return po(e)}finally{G.current=t}},useDebugValue:function(e,t){return _="useDebugValue",Y(),void 0},useDeferredValue:function(e){return _="useDeferredValue",Y(),Cf(e)},useTransition:function(){return _="useTransition",Y(),xf()},useMutableSource:function(e,t,n){return _="useMutableSource",Y(),void 0},useSyncExternalStore:function(e,t,n){return _="useSyncExternalStore",Y(),mf(e,t,n)},useId:function(){return _="useId",Y(),Df()},unstable_isNewReconciler:Ye},Xp={readContext:function(e){return Ot(e)},useCallback:function(e,t){return _="useCallback",Y(),Ro(e,t)},useContext:function(e){return _="useContext",Y(),Ot(e)},useEffect:function(e,t){return _="useEffect",Y(),Yu(e,t)},useImperativeHandle:function(e,t,n){return _="useImperativeHandle",Y(),bo(e,t,n)},useInsertionEffect:function(e,t){return _="useInsertionEffect",Y(),go(e,t)},useLayoutEffect:function(e,t){return _="useLayoutEffect",Y(),So(e,t)},useMemo:function(e,t){_="useMemo",Y();var n=G.current;G.current=ga;try{return To(e,t)}finally{G.current=n}},useReducer:function(e,t,n){_="useReducer",Y();var a=G.current;G.current=ga;try{return vf(e,t,n)}finally{G.current=a}},useRef:function(e){return _="useRef",Y(),mo()},useState:function(e){_="useState",Y();var t=G.current;G.current=ga;try{return hf(e)}finally{G.current=t}},useDebugValue:function(e,t){return _="useDebugValue",Y(),Eo()},useDeferredValue:function(e){return _="useDeferredValue",Y(),Lp(e)},useTransition:function(){return _="useTransition",Y(),Bp()},useMutableSource:function(e,t,n){return _="useMutableSource",Y(),void 0},useSyncExternalStore:function(e,t,n){return _="useSyncExternalStore",Y(),vo(e,t)},useId:function(){return _="useId",Y(),Co()},unstable_isNewReconciler:Ye},Jp={readContext:function(e){return Ot(e)},useCallback:function(e,t){return _="useCallback",Y(),Ro(e,t)},useContext:function(e){return _="useContext",Y(),Ot(e)},useEffect:function(e,t){return _="useEffect",Y(),Yu(e,t)},useImperativeHandle:function(e,t,n){return _="useImperativeHandle",Y(),bo(e,t,n)},useInsertionEffect:function(e,t){return _="useInsertionEffect",Y(),go(e,t)},useLayoutEffect:function(e,t){return _="useLayoutEffect",Y(),So(e,t)},useMemo:function(e,t){_="useMemo",Y();var n=G.current;G.current=Do;try{return To(e,t)}finally{G.current=n}},useReducer:function(e,t,n){_="useReducer",Y();var a=G.current;G.current=Do;try{return pf(e,t,n)}finally{G.current=a}},useRef:function(e){return _="useRef",Y(),mo()},useState:function(e){_="useState",Y();var t=G.current;G.current=Do;try{return yf(e)}finally{G.current=t}},useDebugValue:function(e,t){return _="useDebugValue",Y(),Eo()},useDeferredValue:function(e){return _="useDeferredValue",Y(),jp(e)},useTransition:function(){return _="useTransition",Y(),Vp()},useMutableSource:function(e,t,n){return _="useMutableSource",Y(),void 0},useSyncExternalStore:function(e,t,n){return _="useSyncExternalStore",Y(),vo(e,t)},useId:function(){return _="useId",Y(),Co()},unstable_isNewReconciler:Ye},wa={readContext:function(e){return _f(),Ot(e)},useCallback:function(e,t){return _="useCallback",ve(),Ge(),Rf(e,t)},useContext:function(e){return _="useContext",ve(),Ge(),Ot(e)},useEffect:function(e,t){return _="useEffect",ve(),Ge(),yo(e,t)},useImperativeHandle:function(e,t,n){return _="useImperativeHandle",ve(),Ge(),Ef(e,t,n)},useInsertionEffect:function(e,t){return _="useInsertionEffect",ve(),Ge(),Sf(e,t)},useLayoutEffect:function(e,t){return _="useLayoutEffect",ve(),Ge(),bf(e,t)},useMemo:function(e,t){_="useMemo",ve(),Ge();var n=G.current;G.current=wa;try{return Tf(e,t)}finally{G.current=n}},useReducer:function(e,t,n){_="useReducer",ve(),Ge();var a=G.current;G.current=wa;try{return df(e,t,n)}finally{G.current=a}},useRef:function(e){return _="useRef",ve(),Ge(),gf(e)},useState:function(e){_="useState",ve(),Ge();var t=G.current;G.current=wa;try{return po(e)}finally{G.current=t}},useDebugValue:function(e,t){return _="useDebugValue",ve(),Ge(),void 0},useDeferredValue:function(e){return _="useDeferredValue",ve(),Ge(),Cf(e)},useTransition:function(){return _="useTransition",ve(),Ge(),xf()},useMutableSource:function(e,t,n){return _="useMutableSource",ve(),Ge(),void 0},useSyncExternalStore:function(e,t,n){return _="useSyncExternalStore",ve(),Ge(),mf(e,t,n)},useId:function(){return _="useId",ve(),Ge(),Df()},unstable_isNewReconciler:Ye},ga={readContext:function(e){return _f(),Ot(e)},useCallback:function(e,t){return _="useCallback",ve(),Y(),Ro(e,t)},useContext:function(e){return _="useContext",ve(),Y(),Ot(e)},useEffect:function(e,t){return _="useEffect",ve(),Y(),Yu(e,t)},useImperativeHandle:function(e,t,n){return _="useImperativeHandle",ve(),Y(),bo(e,t,n)},useInsertionEffect:function(e,t){return _="useInsertionEffect",ve(),Y(),go(e,t)},useLayoutEffect:function(e,t){return _="useLayoutEffect",ve(),Y(),So(e,t)},useMemo:function(e,t){_="useMemo",ve(),Y();var n=G.current;G.current=ga;try{return To(e,t)}finally{G.current=n}},useReducer:function(e,t,n){_="useReducer",ve(),Y();var a=G.current;G.current=ga;try{return vf(e,t,n)}finally{G.current=a}},useRef:function(e){return _="useRef",ve(),Y(),mo()},useState:function(e){_="useState",ve(),Y();var t=G.current;G.current=ga;try{return hf(e)}finally{G.current=t}},useDebugValue:function(e,t){return _="useDebugValue",ve(),Y(),Eo()},useDeferredValue:function(e){return _="useDeferredValue",ve(),Y(),Lp(e)},useTransition:function(){return _="useTransition",ve(),Y(),Bp()},useMutableSource:function(e,t,n){return _="useMutableSource",ve(),Y(),void 0},useSyncExternalStore:function(e,t,n){return _="useSyncExternalStore",ve(),Y(),vo(e,t)},useId:function(){return _="useId",ve(),Y(),Co()},unstable_isNewReconciler:Ye},Do={readContext:function(e){return _f(),Ot(e)},useCallback:function(e,t){return _="useCallback",ve(),Y(),Ro(e,t)},useContext:function(e){return _="useContext",ve(),Y(),Ot(e)},useEffect:function(e,t){return _="useEffect",ve(),Y(),Yu(e,t)},useImperativeHandle:function(e,t,n){return _="useImperativeHandle",ve(),Y(),bo(e,t,n)},useInsertionEffect:function(e,t){return _="useInsertionEffect",ve(),Y(),go(e,t)},useLayoutEffect:function(e,t){return _="useLayoutEffect",ve(),Y(),So(e,t)},useMemo:function(e,t){_="useMemo",ve(),Y();var n=G.current;G.current=ga;try{return To(e,t)}finally{G.current=n}},useReducer:function(e,t,n){_="useReducer",ve(),Y();var a=G.current;G.current=ga;try{return pf(e,t,n)}finally{G.current=a}},useRef:function(e){return _="useRef",ve(),Y(),mo()},useState:function(e){_="useState",ve(),Y();var t=G.current;G.current=ga;try{return yf(e)}finally{G.current=t}},useDebugValue:function(e,t){return _="useDebugValue",ve(),Y(),Eo()},useDeferredValue:function(e){return _="useDeferredValue",ve(),Y(),jp(e)},useTransition:function(){return _="useTransition",ve(),Y(),Vp()},useMutableSource:function(e,t,n){return _="useMutableSource",ve(),Y(),void 0},useSyncExternalStore:function(e,t,n){return _="useSyncExternalStore",ve(),Y(),vo(e,t)},useId:function(){return _="useId",ve(),Y(),Co()},unstable_isNewReconciler:Ye}}var Ur=g.unstable_now,Zp=0,_o=-1,qu=-1,Oo=-1,Of=!1,No=!1;function kp(){return Of}function XS(){No=!0}function JS(){Of=!1,No=!1}function ZS(){Of=No,No=!1}function Wp(){return Zp}function Ip(){Zp=Ur()}function Nf(e){qu=Ur(),e.actualStartTime<0&&(e.actualStartTime=Ur())}function $p(e){qu=-1}function Uo(e,t){if(qu>=0){var n=Ur()-qu;e.actualDuration+=n,t&&(e.selfBaseDuration=n),qu=-1}}function Ya(e){if(_o>=0){var t=Ur()-_o;_o=-1;for(var n=e.return;n!==null;){switch(n.tag){case se:var a=n.stateNode;a.effectDuration+=t;return;case Tt:var r=n.stateNode;r.effectDuration+=t;return}n=n.return}}}function Uf(e){if(Oo>=0){var t=Ur()-Oo;Oo=-1;for(var n=e.return;n!==null;){switch(n.tag){case se:var a=n.stateNode;a!==null&&(a.passiveEffectDuration+=t);return;case Tt:var r=n.stateNode;r!==null&&(r.passiveEffectDuration+=t);return}n=n.return}}}function qa(){_o=Ur()}function Mf(){Oo=Ur()}function Af(e){for(var t=e.child;t;)e.actualDuration+=t.actualDuration,t=t.sibling}function ti(e,t){return{value:e,source:t,stack:Iv(t),digest:null}}function zf(e,t,n){return{value:e,source:null,stack:n!=null?n:null,digest:t!=null?t:null}}function kS(e,t){return!0}function Hf(e,t){try{var n=kS(e,t);if(n===!1)return;var a=t.value,r=t.source,i=t.stack,u=i!==null?i:"";if(a!=null&&a._suppressLogging){if(e.tag===ye)return;console.error(a)}var o=r?W(r):null,s=o?"The above error occurred in the <"+o+"> component:":"The above error occurred in one of your React components:",f;if(e.tag===se)f=`Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.`;else{var m=W(e)||"Anonymous";f="React will try to recreate this component tree from scratch "+("using the error boundary you provided, "+m+".")}var S=s+`
`+u+`

`+(""+f);console.error(S)}catch(C){setTimeout(function(){throw C})}}var WS=typeof WeakMap=="function"?WeakMap:Map;function em(e,t,n){var a=lr(ot,n);a.tag=zc,a.payload={element:null};var r=t.value;return a.callback=function(){uR(r),Hf(e,t)},a}function Lf(e,t,n){var a=lr(ot,n);a.tag=zc;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;a.payload=function(){return r(i)},a.callback=function(){vh(e),Hf(e,t)}}var u=e.stateNode;return u!==null&&typeof u.componentDidCatch=="function"&&(a.callback=function(){vh(e),Hf(e,t),typeof r!="function"&&rR(this);var s=t.value,f=t.stack;this.componentDidCatch(s,{componentStack:f!==null?f:""}),typeof r!="function"&&(Yn(e.lanes,fe)||h("%s: Error boundaries should implement getDerivedStateFromError(). In that method, return a state update to display an error message or fallback UI.",W(e)||"Unknown"))}),a}function tm(e,t,n){var a=e.pingCache,r;if(a===null?(a=e.pingCache=new WS,r=new Set,a.set(t,r)):(r=a.get(t),r===void 0&&(r=new Set,a.set(t,r))),!r.has(n)){r.add(n);var i=lR.bind(null,e,t,n);va&&ul(e,n),t.then(i,i)}}function IS(e,t,n,a){var r=e.updateQueue;if(r===null){var i=new Set;i.add(n),e.updateQueue=i}else r.add(n)}function $S(e,t){var n=e.tag;if((e.mode&Be)===le&&(n===Ne||n===Me||n===Ae)){var a=e.alternate;a?(e.updateQueue=a.updateQueue,e.memoizedState=a.memoizedState,e.lanes=a.lanes):(e.updateQueue=null,e.memoizedState=null)}}function nm(e){var t=e;do{if(t.tag===Fe&&FS(t))return t;t=t.return}while(t!==null);return null}function am(e,t,n,a,r){if((e.mode&Be)===le){if(e===t)e.flags|=xt;else{if(e.flags|=ze,n.flags|=Vr,n.flags&=~(oi|Oa),n.tag===ye){var i=n.alternate;if(i===null)n.tag=Zt;else{var u=lr(ot,fe);u.tag=$l,Dr(n,u,fe)}}n.lanes=Ee(n.lanes,fe)}return e}return e.flags|=xt,e.lanes=r,e}function eb(e,t,n,a,r){if(n.flags|=Oa,va&&ul(e,r),a!==null&&typeof a=="object"&&typeof a.then=="function"){var i=a;$S(n),tn()&&n.mode&Be&&Pv();var u=nm(t);if(u!==null){u.flags&=~On,am(u,t,n,e,r),u.mode&Be&&tm(e,i,r),IS(u,e,i);return}else{if(!og(r)){tm(e,i,r),Sd();return}var o=new Error("A component suspended while responding to synchronous input. This will cause the UI to be replaced with a loading indicator. To fix, updates that suspend should be wrapped with startTransition.");a=o}}else if(tn()&&n.mode&Be){Pv();var s=nm(t);if(s!==null){(s.flags&xt)===X&&(s.flags|=On),am(s,t,n,e,r),_c(ti(a,n));return}}a=ti(a,n),kE(a);var f=t;do{switch(f.tag){case se:{var m=a;f.flags|=xt;var S=yu(r);f.lanes=Ee(f.lanes,S);var C=em(f,m,S);jc(f,C);return}case ye:var O=a,U=f.type,N=f.stateNode;if((f.flags&ze)===X&&(typeof U.getDerivedStateFromError=="function"||N!==null&&typeof N.componentDidCatch=="function"&&!rh(N))){f.flags|=xt;var I=yu(r);f.lanes=Ee(f.lanes,I);var oe=Lf(f,O,I);jc(f,oe);return}break}f=f.return}while(f!==null)}function tb(){return null}var Qu=M.ReactCurrentOwner,Sa=!1,jf,Pu,Ff,Bf,Vf,ni,wf,Mo;jf={},Pu={},Ff={},Bf={},Vf={},ni=!1,wf={},Mo={};function Rn(e,t,n,a){e===null?t.child=Rp(t,null,n,a):t.child=Ni(t,e.child,n,a)}function nb(e,t,n,a){t.child=Ni(t,e.child,null,a),t.child=Ni(t,null,n,a)}function rm(e,t,n,a,r){if(t.type!==t.elementType){var i=n.propTypes;i&&fa(i,a,"prop",ee(n))}var u=n.render,o=t.ref,s,f;Oi(t,r),Ru(t);{if(Qu.current=t,Fa(!0),s=Li(e,t,u,a,o,r),f=ji(),t.mode&_t){Xt(!0);try{s=Li(e,t,u,a,o,r),f=ji()}finally{Xt(!1)}}Fa(!1)}return Ti(),e!==null&&!Sa?(Dp(e,t,r),or(e,t,r)):(tn()&&f&&Ec(t),t.flags|=$e,Rn(e,t,s,r),t.child)}function im(e,t,n,a,r){if(e===null){var i=n.type;if(CR(i)&&n.compare===null&&n.defaultProps===void 0){var u=i;return u=Pi(i),t.tag=Ae,t.type=u,Qf(t,i),um(e,t,u,a,r)}{var o=i.propTypes;o&&fa(o,a,"prop",ee(i))}var s=Nd(n.type,null,a,t,t.mode,r);return s.ref=t.ref,s.return=t,t.child=s,s}{var f=n.type,m=f.propTypes;m&&fa(m,a,"prop",ee(f))}var S=e.child,C=Zf(e,r);if(!C){var O=S.memoizedProps,U=n.compare;if(U=U!==null?U:Kl,U(O,a)&&e.ref===t.ref)return or(e,t,r)}t.flags|=$e;var N=li(S,a);return N.ref=t.ref,N.return=t,t.child=N,N}function um(e,t,n,a,r){if(t.type!==t.elementType){var i=t.elementType;if(i.$$typeof===it){var u=i,o=u._payload,s=u._init;try{i=s(o)}catch(S){i=null}var f=i&&i.propTypes;f&&fa(f,a,"prop",ee(i))}}if(e!==null){var m=e.memoizedProps;if(Kl(m,a)&&e.ref===t.ref&&t.type===e.type)if(Sa=!1,t.pendingProps=a=m,Zf(e,r))(e.flags&Vr)!==X&&(Sa=!0);else return t.lanes=e.lanes,or(e,t,r)}return Yf(e,t,n,a,r)}function lm(e,t,n){var a=t.pendingProps,r=a.children,i=e!==null?e.memoizedState:null;if(a.mode==="hidden"||Xe)if((t.mode&Be)===le){var u={baseLanes:A,cachePool:null,transitions:null};t.memoizedState=u,ts(t,n)}else if(Yn(n,wn)){var S={baseLanes:A,cachePool:null,transitions:null};t.memoizedState=S;var C=i!==null?i.baseLanes:n;ts(t,C)}else{var o=null,s;if(i!==null){var f=i.baseLanes;s=Ee(f,n)}else s=n;t.lanes=t.childLanes=wn;var m={baseLanes:s,cachePool:o,transitions:null};return t.memoizedState=m,t.updateQueue=null,ts(t,s),null}else{var O;i!==null?(O=Ee(i.baseLanes,n),t.memoizedState=null):O=n,ts(t,O)}return Rn(e,t,r,n),t.child}function ab(e,t,n){var a=t.pendingProps;return Rn(e,t,a,n),t.child}function rb(e,t,n){var a=t.pendingProps.children;return Rn(e,t,a,n),t.child}function ib(e,t,n){{t.flags|=ce;{var a=t.stateNode;a.effectDuration=0,a.passiveEffectDuration=0}}var r=t.pendingProps,i=r.children;return Rn(e,t,i,n),t.child}function om(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=Wn,t.flags|=z)}function Yf(e,t,n,a,r){if(t.type!==t.elementType){var i=n.propTypes;i&&fa(i,a,"prop",ee(n))}var u;{var o=mi(t,n,!0);u=hi(t,o)}var s,f;Oi(t,r),Ru(t);{if(Qu.current=t,Fa(!0),s=Li(e,t,n,a,u,r),f=ji(),t.mode&_t){Xt(!0);try{s=Li(e,t,n,a,u,r),f=ji()}finally{Xt(!1)}}Fa(!1)}return Ti(),e!==null&&!Sa?(Dp(e,t,r),or(e,t,r)):(tn()&&f&&Ec(t),t.flags|=$e,Rn(e,t,s,r),t.child)}function sm(e,t,n,a,r){{switch(Rh(t)){case!1:{var i=t.stateNode,u=t.type,o=new u(t.memoizedProps,i.context),s=o.state;i.updater.enqueueSetState(i,s,null);break}case!0:{t.flags|=ze,t.flags|=xt;var f=new Error("Simulated error coming from DevTools"),m=yu(r);t.lanes=Ee(t.lanes,m);var S=Lf(t,ti(f,t),m);jc(t,S);break}}if(t.type!==t.elementType){var C=n.propTypes;C&&fa(C,a,"prop",ee(n))}}var O;Ma(n)?(O=!0,Hl(t)):O=!1,Oi(t,r);var U=t.stateNode,N;U===null?(zo(e,t),yp(t,n,a),Jc(t,n,a,r),N=!0):e===null?N=MS(t,n,a,r):N=AS(e,t,n,a,r);var I=qf(e,t,n,N,O,r);{var oe=t.stateNode;N&&oe.props!==a&&(ni||h("It looks like %s is reassigning its own `this.props` while rendering. This is not supported and can lead to confusing bugs.",W(t)||"a component"),ni=!0)}return I}function qf(e,t,n,a,r,i){om(e,t);var u=(t.flags&ze)!==X;if(!a&&!u)return r&&yv(t,n,!1),or(e,t,i);var o=t.stateNode;Qu.current=t;var s;if(u&&typeof n.getDerivedStateFromError!="function")s=null,$p();else{Ru(t);{if(Fa(!0),s=o.render(),t.mode&_t){Xt(!0);try{o.render()}finally{Xt(!1)}}Fa(!1)}Ti()}return t.flags|=$e,e!==null&&u?nb(e,t,s,i):Rn(e,t,s,i),t.memoizedState=o.state,r&&yv(t,n,!0),t.child}function cm(e){var t=e.stateNode;t.pendingContext?mv(e,t.pendingContext,t.pendingContext!==t.context):t.context&&mv(e,t.context,!1),tf(e,t.containerInfo)}function ub(e,t,n){if(cm(t),e===null)throw new Error("Should have a current fiber. This is a bug in React.");var a=t.pendingProps,r=t.memoizedState,i=r.element;sp(e,t),ao(t,a,null,n);var u=t.memoizedState,o=t.stateNode,s=u.element;if(Mt&&r.isDehydrated){var f={element:s,isDehydrated:!1,cache:u.cache,pendingSuspenseBoundaries:u.pendingSuspenseBoundaries,transitions:u.transitions},m=t.updateQueue;if(m.baseState=f,t.memoizedState=f,t.flags&On){var S=ti(new Error("There was an error while hydrating. Because the error happened outside of a Suspense boundary, the entire root will switch to client rendering."),t);return fm(e,t,s,n,S)}else if(s!==i){var C=ti(new Error("This root received an early update, before anything was able hydrate. Switched the entire root to client rendering."),t);return fm(e,t,s,n,C)}else{rS(t);var O=Rp(t,null,s,n);t.child=O;for(var U=O;U;)U.flags=U.flags&~Qe|Nn,U=U.sibling}}else{if(Di(),s===i)return or(e,t,n);Rn(e,t,s,n)}return t.child}function fm(e,t,n,a,r){return Di(),_c(r),t.flags|=On,Rn(e,t,n,a),t.child}function lb(e,t,n){Tp(t),e===null&&Dc(t);var a=t.type,r=t.pendingProps,i=e!==null?e.memoizedProps:null,u=r.children,o=ru(a,r);return o?u=null:i!==null&&ru(a,i)&&(t.flags|=oa),om(e,t),Rn(e,t,u,n),t.child}function ob(e,t){return e===null&&Dc(t),null}function sb(e,t,n,a){zo(e,t);var r=t.pendingProps,i=n,u=i._payload,o=i._init,s=o(u);t.type=s;var f=t.tag=xR(s),m=ha(s,r),S;switch(f){case Ne:return Qf(t,s),t.type=s=Pi(s),S=Yf(null,t,s,m,a),S;case ye:return t.type=s=Td(s),S=sm(null,t,s,m,a),S;case Me:return t.type=s=Cd(s),S=rm(null,t,s,m,a),S;case st:{if(t.type!==t.elementType){var C=s.propTypes;C&&fa(C,m,"prop",ee(s))}return S=im(null,t,s,ha(s.type,m),a),S}}var O="";throw s!==null&&typeof s=="object"&&s.$$typeof===it&&(O=" Did you wrap a component in React.lazy() more than once?"),new Error("Element type is invalid. Received a promise that resolves to: "+s+". "+("Lazy element type must resolve to a class or function."+O))}function cb(e,t,n,a,r){zo(e,t),t.tag=ye;var i;return Ma(n)?(i=!0,Hl(t)):i=!1,Oi(t,r),yp(t,n,a),Jc(t,n,a,r),qf(null,t,n,!0,i,r)}function fb(e,t,n,a){zo(e,t);var r=t.pendingProps,i;{var u=mi(t,n,!1);i=hi(t,u)}Oi(t,a);var o,s;Ru(t);{if(n.prototype&&typeof n.prototype.render=="function"){var f=ee(n)||"Unknown";jf[f]||(h("The <%s /> component appears to have a render method, but doesn't extend React.Component. This is likely to cause errors. Change %s to extend React.Component instead.",f,f),jf[f]=!0)}t.mode&_t&&ma.recordLegacyContextWarning(t,null),Fa(!0),Qu.current=t,o=Li(null,t,n,r,i,a),s=ji(),Fa(!1)}if(Ti(),t.flags|=$e,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0){var m=ee(n)||"Unknown";Pu[m]||(h("The <%s /> component appears to be a function component that returns a class instance. Change %s to a class that extends React.Component instead. If you can't use a class try assigning the prototype on the function as a workaround. `%s.prototype = React.Component.prototype`. Don't use an arrow function since it cannot be called with `new` by React.",m,m,m),Pu[m]=!0)}if(typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0){{var S=ee(n)||"Unknown";Pu[S]||(h("The <%s /> component appears to be a function component that returns a class instance. Change %s to a class that extends React.Component instead. If you can't use a class try assigning the prototype on the function as a workaround. `%s.prototype = React.Component.prototype`. Don't use an arrow function since it cannot be called with `new` by React.",S,S,S),Pu[S]=!0)}t.tag=ye,t.memoizedState=null,t.updateQueue=null;var C=!1;return Ma(n)?(C=!0,Hl(t)):C=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Lc(t),hp(t,o),Jc(t,n,r,a),qf(null,t,n,!0,C,a)}else{if(t.tag=Ne,t.mode&_t){Xt(!0);try{o=Li(null,t,n,r,i,a),s=ji()}finally{Xt(!1)}}return tn()&&s&&Ec(t),Rn(null,t,o,a),Qf(t,n),t.child}}function Qf(e,t){{if(t&&t.childContextTypes&&h("%s(...): childContextTypes cannot be defined on a function component.",t.displayName||t.name||"Component"),e.ref!==null){var n="",a=mS();a&&(n+=`

Check the render method of \``+a+"`.");var r=a||"",i=e._debugSource;i&&(r=i.fileName+":"+i.lineNumber),Vf[r]||(Vf[r]=!0,h("Function components cannot be given refs. Attempts to access this ref will fail. Did you mean to use React.forwardRef()?%s",n))}if(typeof t.getDerivedStateFromProps=="function"){var u=ee(t)||"Unknown";Bf[u]||(h("%s: Function components do not support getDerivedStateFromProps.",u),Bf[u]=!0)}if(typeof t.contextType=="object"&&t.contextType!==null){var o=ee(t)||"Unknown";Ff[o]||(h("%s: Function components do not support contextType.",o),Ff[o]=!0)}}}var Pf={dehydrated:null,treeContext:null,retryLane:Pt};function Gf(e){return{baseLanes:e,cachePool:tb(),transitions:null}}function db(e,t){var n=null;return{baseLanes:Ee(e.baseLanes,t),cachePool:n,transitions:e.transitions}}function vb(e,t,n,a){if(t!==null){var r=t.memoizedState;if(r===null)return!1}return af(e,Lu)}function pb(e,t){return wl(e.childLanes,t)}function dm(e,t,n){var a=t.pendingProps;Ch(t)&&(t.flags|=ze);var r=ya.current,i=!1,u=(t.flags&ze)!==X;if(u||vb(r,e)?(i=!0,t.flags&=~ze):(e===null||e.memoizedState!==null)&&(r=jS(r,xp)),r=Mi(r),Or(t,r),e===null){Dc(t);var o=t.memoizedState;if(o!==null){var s=o.dehydrated;if(s!==null)return Sb(t,s)}var f=a.children,m=a.fallback;if(i){var S=mb(t,f,m,n),C=t.child;return C.memoizedState=Gf(n),t.memoizedState=Pf,S}else return Kf(t,f)}else{var O=e.memoizedState;if(O!==null){var U=O.dehydrated;if(U!==null)return bb(e,t,u,a,U,O,n)}if(i){var N=a.fallback,I=a.children,oe=yb(e,t,I,N,n),te=t.child,Ke=e.child.memoizedState;return te.memoizedState=Ke===null?Gf(n):db(Ke,n),te.childLanes=pb(e,n),t.memoizedState=Pf,oe}else{var je=a.children,E=hb(e,t,je,n);return t.memoizedState=null,E}}}function Kf(e,t,n){var a=e.mode,r={mode:"visible",children:t},i=Xf(r,a);return i.return=e,e.child=i,i}function mb(e,t,n,a){var r=e.mode,i=e.child,u={mode:"hidden",children:t},o,s;return(r&Be)===le&&i!==null?(o=i,o.childLanes=A,o.pendingProps=u,e.mode&ke&&(o.actualDuration=0,o.actualStartTime=-1,o.selfBaseDuration=0,o.treeBaseDuration=0),s=Lr(n,r,a,null)):(o=Xf(u,r),s=Lr(n,r,a,null)),o.return=e,s.return=e,o.sibling=s,e.child=o,s}function Xf(e,t,n){return mh(e,t,A,null)}function vm(e,t){return li(e,t)}function hb(e,t,n,a){var r=e.child,i=r.sibling,u=vm(r,{mode:"visible",children:n});if((t.mode&Be)===le&&(u.lanes=a),u.return=t,u.sibling=null,i!==null){var o=t.deletions;o===null?(t.deletions=[i],t.flags|=Wt):o.push(i)}return t.child=u,u}function yb(e,t,n,a,r){var i=t.mode,u=e.child,o=u.sibling,s={mode:"hidden",children:n},f;if((i&Be)===le&&t.child!==u){var m=t.child;f=m,f.childLanes=A,f.pendingProps=s,t.mode&ke&&(f.actualDuration=0,f.actualStartTime=-1,f.selfBaseDuration=u.selfBaseDuration,f.treeBaseDuration=u.treeBaseDuration),t.deletions=null}else f=vm(u,s),f.subtreeFlags=u.subtreeFlags&Pe;var S;return o!==null?S=li(o,a):(S=Lr(a,i,r,null),S.flags|=Qe),S.return=t,f.return=t,f.sibling=S,t.child=f,S}function Ao(e,t,n,a){a!==null&&_c(a),Ni(t,e.child,null,n);var r=t.pendingProps,i=r.children,u=Kf(t,i);return u.flags|=Qe,t.memoizedState=null,u}function gb(e,t,n,a,r){var i=t.mode,u={mode:"visible",children:n},o=Xf(u,i),s=Lr(a,i,r,null);return s.flags|=Qe,o.return=t,s.return=t,o.sibling=s,t.child=o,(t.mode&Be)!==le&&Ni(t,e.child,null,r),s}function Sb(e,t,n){return(e.mode&Be)===le?(h("Cannot hydrate Suspense in legacy mode. Switch from ReactDOM.hydrate(element, container) to ReactDOMClient.hydrateRoot(container, <App />).render(element) or remove the Suspense components from the server rendered components."),e.lanes=fe):Er(t)?e.lanes=Qr:e.lanes=wn,null}function bb(e,t,n,a,r,i,u){if(n)if(t.flags&On){t.flags&=~On;var E=zf(new Error("There was an error while hydrating this Suspense boundary. Switched to client rendering."));return Ao(e,t,u,E)}else{if(t.memoizedState!==null)return t.child=e.child,t.flags|=ze,null;var D=a.children,b=a.fallback,L=gb(e,t,D,b,u),k=t.child;return k.memoizedState=Gf(u),t.memoizedState=Pf,L}else{if(nS(),(t.mode&Be)===le)return Ao(e,t,u,null);if(Er(r)){var o,s,f;{var m=cu(r);o=m.digest,s=m.message,f=m.stack}var S;s?S=new Error(s):S=new Error("The server could not finish this Suspense boundary, likely due to an error during server rendering. Switched to client rendering.");var C=zf(S,o,f);return Ao(e,t,u,C)}var O=Yn(u,e.childLanes);if(Sa||O){var U=$o();if(U!==null){var N=hg(U,u);if(N!==Pt&&N!==i.retryLane){i.retryLane=N;var I=ot;En(e,N),Ut(U,e,N,I)}}Sd();var oe=zf(new Error("This Suspense boundary received an update before it finished hydrating. This caused the boundary to switch to client rendering. The usual way to fix this is to wrap the original update in startTransition."));return Ao(e,t,u,oe)}else if(Dt(r)){t.flags|=ze,t.child=e.child;var te=oR.bind(null,e);return Dl(r,te),null}else{iS(t,r,i.treeContext);var Ke=a.children,je=Kf(t,Ke);return je.flags|=Nn,je}}}function pm(e,t,n){e.lanes=Ee(e.lanes,t);var a=e.alternate;a!==null&&(a.lanes=Ee(a.lanes,t)),Mc(e.return,t,n)}function Eb(e,t,n){for(var a=t;a!==null;){if(a.tag===Fe){var r=a.memoizedState;r!==null&&pm(a,n,e)}else if(a.tag===St)pm(a,n,e);else if(a.child!==null){a.child.return=a,a=a.child;continue}if(a===e)return;for(;a.sibling===null;){if(a.return===null||a.return===e)return;a=a.return}a.sibling.return=a.return,a=a.sibling}}function Rb(e){for(var t=e,n=null;t!==null;){var a=t.alternate;a!==null&&co(a)===null&&(n=t),t=t.sibling}return n}function Tb(e){if(e!==void 0&&e!=="forwards"&&e!=="backwards"&&e!=="together"&&!wf[e])if(wf[e]=!0,typeof e=="string")switch(e.toLowerCase()){case"together":case"forwards":case"backwards":{h('"%s" is not a valid value for revealOrder on <SuspenseList />. Use lowercase "%s" instead.',e,e.toLowerCase());break}case"forward":case"backward":{h('"%s" is not a valid value for revealOrder on <SuspenseList />. React uses the -s suffix in the spelling. Use "%ss" instead.',e,e.toLowerCase());break}default:h('"%s" is not a supported revealOrder on <SuspenseList />. Did you mean "together", "forwards" or "backwards"?',e);break}else h('%s is not a supported value for revealOrder on <SuspenseList />. Did you mean "together", "forwards" or "backwards"?',e)}function Cb(e,t){e!==void 0&&!Mo[e]&&(e!=="collapsed"&&e!=="hidden"?(Mo[e]=!0,h('"%s" is not a supported value for tail on <SuspenseList />. Did you mean "collapsed" or "hidden"?',e)):t!=="forwards"&&t!=="backwards"&&(Mo[e]=!0,h('<SuspenseList tail="%s" /> is only valid if revealOrder is "forwards" or "backwards". Did you mean to specify revealOrder="forwards"?',e)))}function mm(e,t){{var n=fn(e),a=!n&&typeof w(e)=="function";if(n||a){var r=n?"array":"iterable";return h("A nested %s was passed to row #%s in <SuspenseList />. Wrap it in an additional SuspenseList to configure its revealOrder: <SuspenseList revealOrder=...> ... <SuspenseList revealOrder=...>{%s}</SuspenseList> ... </SuspenseList>",r,t,r),!1}}return!0}function xb(e,t){if((t==="forwards"||t==="backwards")&&e!==void 0&&e!==null&&e!==!1)if(fn(e)){for(var n=0;n<e.length;n++)if(!mm(e[n],n))return}else{var a=w(e);if(typeof a=="function"){var r=a.call(e);if(r)for(var i=r.next(),u=0;!i.done;i=r.next()){if(!mm(i.value,u))return;u++}}else h('A single row was passed to a <SuspenseList revealOrder="%s" />. This is not useful since it needs multiple rows. Did you mean to pass multiple children or an array?',t)}}function Jf(e,t,n,a,r){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:r}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=a,i.tail=n,i.tailMode=r)}function hm(e,t,n){var a=t.pendingProps,r=a.revealOrder,i=a.tail,u=a.children;Tb(r),Cb(i,r),xb(u,r),Rn(e,t,u,n);var o=ya.current,s=af(o,Lu);if(s)o=rf(o,Lu),t.flags|=ze;else{var f=e!==null&&(e.flags&ze)!==X;f&&Eb(t,t.child,n),o=Mi(o)}if(Or(t,o),(t.mode&Be)===le)t.memoizedState=null;else switch(r){case"forwards":{var m=Rb(t.child),S;m===null?(S=t.child,t.child=null):(S=m.sibling,m.sibling=null),Jf(t,!1,S,m,i);break}case"backwards":{var C=null,O=t.child;for(t.child=null;O!==null;){var U=O.alternate;if(U!==null&&co(U)===null){t.child=O;break}var N=O.sibling;O.sibling=C,C=O,O=N}Jf(t,!0,C,null,i);break}case"together":{Jf(t,!1,null,null,void 0);break}default:t.memoizedState=null}return t.child}function Db(e,t,n){tf(t,t.stateNode.containerInfo);var a=t.pendingProps;return e===null?t.child=Ni(t,null,a,n):Rn(e,t,a,n),t.child}var ym=!1;function _b(e,t,n){var a=t.type,r=a._context,i=t.pendingProps,u=t.memoizedProps,o=i.value;{"value"in i||ym||(ym=!0,h("The `value` prop is required for the `<Context.Provider>`. Did you misspell it or forget to pass it?"));var s=t.type.propTypes;s&&fa(s,i,"prop","Context.Provider")}if(ip(t,r,o),u!==null){var f=u.value;if(qn(f,o)){if(u.children===i.children&&!Al())return or(e,t,n)}else ES(t,r,n)}var m=i.children;return Rn(e,t,m,n),t.child}var gm=!1;function Ob(e,t,n){var a=t.type;a._context===void 0?a!==a.Consumer&&(gm||(gm=!0,h("Rendering <Context> directly is not supported and will be removed in a future major release. Did you mean to render <Context.Consumer> instead?"))):a=a._context;var r=t.pendingProps,i=r.children;typeof i!="function"&&h("A context consumer was rendered with multiple children, or a child that isn't a function. A context consumer expects a single child that is a function. If you did pass a function, make sure there is no trailing or leading whitespace around it."),Oi(t,n);var u=Ot(a);Ru(t);var o;return Qu.current=t,Fa(!0),o=i(u),Fa(!1),Ti(),t.flags|=$e,Rn(e,t,o,n),t.child}function Gu(){Sa=!0}function zo(e,t){(t.mode&Be)===le&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=Qe)}function or(e,t,n){return e!==null&&(t.dependencies=e.dependencies),$p(),il(t.lanes),Yn(n,t.childLanes)?(zS(e,t),t.child):null}function Nb(e,t,n){{var a=t.return;if(a===null)throw new Error("Cannot swap the root fiber.");if(e.alternate=null,t.alternate=null,n.index=t.index,n.sibling=t.sibling,n.return=t.return,n.ref=t.ref,t===a.child)a.child=n;else{var r=a.child;if(r===null)throw new Error("Expected parent to have a child.");for(;r.sibling!==t;)if(r=r.sibling,r===null)throw new Error("Expected to find the previous sibling.");r.sibling=n}var i=a.deletions;return i===null?(a.deletions=[e],a.flags|=Wt):i.push(e),n.flags|=Qe,n}}function Zf(e,t){var n=e.lanes;return!!Yn(n,t)}function Ub(e,t,n){switch(t.tag){case se:cm(t);var a=t.stateNode;Di();break;case $:Tp(t);break;case ye:{var r=t.type;Ma(r)&&Hl(t);break}case Ue:tf(t,t.stateNode.containerInfo);break;case Rt:{var i=t.memoizedProps.value,u=t.type._context;ip(t,u,i);break}case Tt:{var o=Yn(n,t.childLanes);o&&(t.flags|=ce);{var s=t.stateNode;s.effectDuration=0,s.passiveEffectDuration=0}}break;case Fe:{var f=t.memoizedState;if(f!==null){if(f.dehydrated!==null)return Or(t,Mi(ya.current)),t.flags|=ze,null;var m=t.child,S=m.childLanes;if(Yn(n,S))return dm(e,t,n);Or(t,Mi(ya.current));var C=or(e,t,n);return C!==null?C.sibling:null}else Or(t,Mi(ya.current));break}case St:{var O=(e.flags&ze)!==X,U=Yn(n,t.childLanes);if(O){if(U)return hm(e,t,n);t.flags|=ze}var N=t.memoizedState;if(N!==null&&(N.rendering=null,N.tail=null,N.lastEffect=null),Or(t,ya.current),U)break;return null}case rt:case kt:return t.lanes=A,lm(e,t,n)}return or(e,t,n)}function Sm(e,t,n){if(t._debugNeedsRemount&&e!==null)return Nb(e,t,Nd(t.type,t.key,t.pendingProps,t._debugOwner||null,t.mode,t.lanes));if(e!==null){var a=e.memoizedProps,r=t.pendingProps;if(a!==r||Al()||t.type!==e.type)Sa=!0;else{var i=Zf(e,n);if(!i&&(t.flags&ze)===X)return Sa=!1,Ub(e,t,n);(e.flags&Vr)!==X?Sa=!0:Sa=!1}}else if(Sa=!1,tn()&&kg(t)){var u=t.index,o=Wg();Qv(t,o,u)}switch(t.lanes=A,t.tag){case yn:return fb(e,t,t.type,n);case Ct:{var s=t.elementType;return sb(e,t,s,n)}case Ne:{var f=t.type,m=t.pendingProps,S=t.elementType===f?m:ha(f,m);return Yf(e,t,f,S,n)}case ye:{var C=t.type,O=t.pendingProps,U=t.elementType===C?O:ha(C,O);return sm(e,t,C,U,n)}case se:return ub(e,t,n);case $:return lb(e,t,n);case ge:return ob(e,t);case Fe:return dm(e,t,n);case Ue:return Db(e,t,n);case Me:{var N=t.type,I=t.pendingProps,oe=t.elementType===N?I:ha(N,I);return rm(e,t,N,oe,n)}case Te:return ab(e,t,n);case dt:return rb(e,t,n);case Tt:return ib(e,t,n);case Rt:return _b(e,t,n);case ue:return Ob(e,t,n);case st:{var te=t.type,Ke=t.pendingProps,je=ha(te,Ke);if(t.type!==t.elementType){var E=te.propTypes;E&&fa(E,je,"prop",ee(te))}return je=ha(te.type,je),im(e,t,te,je,n)}case Ae:return um(e,t,t.type,t.pendingProps,n);case Zt:{var D=t.type,b=t.pendingProps,L=t.elementType===D?b:ha(D,b);return cb(e,t,D,L,n)}case St:return hm(e,t,n);case gn:break;case rt:return lm(e,t,n)}throw new Error("Unknown unit of work tag ("+t.tag+"). This error is likely caused by a bug in React. Please file an issue.")}function Qa(e){e.flags|=ce}function bm(e){e.flags|=Wn,e.flags|=z}function Em(e,t){var n=e!==null&&e.child===t.child;if(n)return!0;if((t.flags&Wt)!==X)return!1;for(var a=t.child;a!==null;){if((a.flags&Se)!==X||(a.subtreeFlags&Se)!==X)return!1;a=a.sibling}return!0}var Ku,Xu,Ho,Lo;if(Qt)Ku=function(e,t,n,a){for(var r=t.child;r!==null;){if(r.tag===$||r.tag===ge)gr(e,r.stateNode);else if(r.tag!==Ue){if(r.child!==null){r.child.return=r,r=r.child;continue}}if(r===t)return;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}},Xu=function(e,t){},Ho=function(e,t,n,a,r){var i=e.memoizedProps;if(i!==a){var u=t.stateNode,o=Hu(),s=wr(u,n,i,a,r,o);t.updateQueue=s,s&&Qa(t)}},Lo=function(e,t,n,a){n!==a&&Qa(t)};else if(fi){Ku=function(e,t,n,a){for(var r=t.child;r!==null;){if(r.tag===$){var i=r.stateNode;if(n&&a){var u=r.memoizedProps,o=r.type;i=Ze(i,o,u,r)}gr(e,i)}else if(r.tag===ge){var s=r.stateNode;if(n&&a){var f=r.memoizedProps;s=tt(s,f,r)}gr(e,s)}else if(r.tag!==Ue){if(r.tag===rt&&r.memoizedState!==null){var m=r.child;m!==null&&(m.return=r),Ku(e,r,!0,!0)}else if(r.child!==null){r.child.return=r,r=r.child;continue}}if(r=r,r===t)return;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};var Rm=function(e,t,n,a){for(var r=t.child;r!==null;){if(r.tag===$){var i=r.stateNode;if(n&&a){var u=r.memoizedProps,o=r.type;i=Ze(i,o,u,r)}Z(e,i)}else if(r.tag===ge){var s=r.stateNode;if(n&&a){var f=r.memoizedProps;s=tt(s,f,r)}Z(e,s)}else if(r.tag!==Ue){if(r.tag===rt&&r.memoizedState!==null){var m=r.child;m!==null&&(m.return=r),Rm(e,r,!0,!0)}else if(r.child!==null){r.child.return=r,r=r.child;continue}}if(r=r,r===t)return;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};Xu=function(e,t){var n=t.stateNode,a=Em(e,t);if(!a){var r=n.containerInfo,i=ie(r);Rm(i,t,!1,!1),n.pendingChildren=i,Qa(t),he(r,i)}},Ho=function(e,t,n,a,r){var i=e.stateNode,u=e.memoizedProps,o=Em(e,t);if(o&&u===a){t.stateNode=i;return}var s=t.stateNode,f=Hu(),m=null;if(u!==a&&(m=wr(s,n,u,a,r,f)),o&&m===null){t.stateNode=i;return}var S=F(i,m,n,u,a,t,o,s);ci(S,n,a,r,f)&&Qa(t),t.stateNode=S,o?Qa(t):Ku(S,t,!1,!1)},Lo=function(e,t,n,a){if(n!==a){var r=ef(),i=Hu();t.stateNode=iu(a,r,i,t),Qa(t)}else t.stateNode=e.stateNode}}else Xu=function(e,t){},Ho=function(e,t,n,a,r){},Lo=function(e,t,n,a){};function Ju(e,t){if(!tn())switch(e.tailMode){case"hidden":{for(var n=e.tail,a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?e.tail=null:a.sibling=null;break}case"collapsed":{for(var r=e.tail,i=null;r!==null;)r.alternate!==null&&(i=r),r=r.sibling;i===null?!t&&e.tail!==null?e.tail.sibling=null:e.tail=null:i.sibling=null;break}}}function an(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=A,a=X;if(t){if((e.mode&ke)!==le){for(var s=e.selfBaseDuration,f=e.child;f!==null;)n=Ee(n,Ee(f.lanes,f.childLanes)),a|=f.subtreeFlags&Pe,a|=f.flags&Pe,s+=f.treeBaseDuration,f=f.sibling;e.treeBaseDuration=s}else for(var m=e.child;m!==null;)n=Ee(n,Ee(m.lanes,m.childLanes)),a|=m.subtreeFlags&Pe,a|=m.flags&Pe,m.return=e,m=m.sibling;e.subtreeFlags|=a}else{if((e.mode&ke)!==le){for(var r=e.actualDuration,i=e.selfBaseDuration,u=e.child;u!==null;)n=Ee(n,Ee(u.lanes,u.childLanes)),a|=u.subtreeFlags,a|=u.flags,r+=u.actualDuration,i+=u.treeBaseDuration,u=u.sibling;e.actualDuration=r,e.treeBaseDuration=i}else for(var o=e.child;o!==null;)n=Ee(n,Ee(o.lanes,o.childLanes)),a|=o.subtreeFlags,a|=o.flags,o.return=e,o=o.sibling;e.subtreeFlags|=a}return e.childLanes=n,t}function Mb(e,t,n){if(cS()&&(t.mode&Be)!==le&&(t.flags&ze)===X)return kv(t),Di(),t.flags|=On|Oa|xt,!1;var a=Gl(t);if(n!==null&&n.dehydrated!==null)if(e===null){if(!a)throw new Error("A dehydrated suspense component was completed without a hydrated node. This is probably a bug in React.");if(oS(t),an(t),(t.mode&ke)!==le){var r=n!==null;if(r){var i=t.child;i!==null&&(t.treeBaseDuration-=i.treeBaseDuration)}}return!1}else{if(Di(),(t.flags&ze)===X&&(t.memoizedState=null),t.flags|=ce,an(t),(t.mode&ke)!==le){var u=n!==null;if(u){var o=t.child;o!==null&&(t.treeBaseDuration-=o.treeBaseDuration)}}return!1}else return Wv(),!0}function Tm(e,t,n){var a=t.pendingProps;switch(Rc(t),t.tag){case yn:case Ct:case Ae:case Ne:case Me:case Te:case dt:case Tt:case ue:case st:return an(t),null;case ye:{var r=t.type;return Ma(r)&&zl(t),an(t),null}case se:{var i=t.stateNode;if(Ui(t),Gs(t),lf(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),e===null||e.child===null){var u=Gl(t);if(u)Qa(t);else if(e!==null){var o=e.memoizedState;(!o.isDehydrated||(t.flags&On)!==X)&&(t.flags|=sn,Wv())}}return Xu(e,t),an(t),null}case $:{nf(t);var s=ef(),f=t.type;if(e!==null&&t.stateNode!=null)Ho(e,t,f,a,s),e.ref!==t.ref&&bm(t);else{if(!a){if(t.stateNode===null)throw new Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");return an(t),null}var m=Hu(),S=Gl(t);if(S)uS(t,s,m)&&Qa(t);else{var C=au(f,a,s,m,t);Ku(C,t,!1,!1),t.stateNode=C,ci(C,f,a,s,m)&&Qa(t)}t.ref!==null&&bm(t)}return an(t),null}case ge:{var O=a;if(e&&t.stateNode!=null){var U=e.memoizedProps;Lo(e,t,U,O)}else{if(typeof O!="string"&&t.stateNode===null)throw new Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");var N=ef(),I=Hu(),oe=Gl(t);oe?lS(t)&&Qa(t):t.stateNode=iu(O,N,I,t)}return an(t),null}case Fe:{Ai(t);var te=t.memoizedState;if(e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){var Ke=Mb(e,t,te);if(!Ke)return t.flags&xt?t:null}if((t.flags&ze)!==X)return t.lanes=n,(t.mode&ke)!==le&&Af(t),t;var je=te!==null,E=e!==null&&e.memoizedState!==null;if(je!==E&&je){var D=t.child;if(D.flags|=sa,(t.mode&Be)!==le){var b=e===null&&(t.memoizedProps.unstable_avoidThisFallback!==!0||!Re);b||af(ya.current,xp)?ZE():Sd()}}var L=t.updateQueue;if(L!==null&&(t.flags|=ce),an(t),(t.mode&ke)!==le&&je){var k=t.child;k!==null&&(t.treeBaseDuration-=k.treeBaseDuration)}return null}case Ue:return Ui(t),Xu(e,t),e===null&&xs(t.stateNode.containerInfo),an(t),null;case Rt:var K=t.type._context;return Uc(K,t),an(t),null;case Zt:{var He=t.type;return Ma(He)&&zl(t),an(t),null}case St:{Ai(t);var de=t.memoizedState;if(de===null)return an(t),null;var we=(t.flags&ze)!==X,_e=de.rendering;if(_e===null)if(we)Ju(de,!1);else{var nt=WE()&&(e===null||(e.flags&ze)===X);if(!nt)for(var ae=t.child;ae!==null;){var yt=co(ae);if(yt!==null){we=!0,t.flags|=ze,Ju(de,!1);var mn=yt.updateQueue;return mn!==null&&(t.updateQueue=mn,t.flags|=ce),t.subtreeFlags=X,HS(t,n),Or(t,rf(ya.current,Lu)),t.child}ae=ae.sibling}de.tail!==null&&Kt()>Jm()&&(t.flags|=ze,we=!0,Ju(de,!1),t.lanes=bv)}else{if(!we){var on=co(_e);if(on!==null){t.flags|=ze,we=!0;var Gn=on.updateQueue;if(Gn!==null&&(t.updateQueue=Gn,t.flags|=ce),Ju(de,!0),de.tail===null&&de.tailMode==="hidden"&&!_e.alternate&&!tn())return an(t),null}else Kt()*2-de.renderingStartTime>Jm()&&n!==wn&&(t.flags|=ze,we=!0,Ju(de,!1),t.lanes=bv)}if(de.isBackwards)_e.sibling=t.child,t.child=_e;else{var Cn=de.last;Cn!==null?Cn.sibling=_e:t.child=_e,de.last=_e}}if(de.tail!==null){var xn=de.tail;de.rendering=xn,de.tail=xn.sibling,de.renderingStartTime=Kt(),xn.sibling=null;var hn=ya.current;return we?hn=rf(hn,Lu):hn=Mi(hn),Or(t,hn),xn}return an(t),null}case gn:break;case rt:case kt:{gd(t);var fr=t.memoizedState,Gi=fr!==null;if(e!==null){var ol=e.memoizedState,Ja=ol!==null;Ja!==Gi&&!Xe&&(t.flags|=sa)}return!Gi||(t.mode&Be)===le?an(t):Yn(Ka,wn)&&(an(t),Qt&&t.subtreeFlags&(Qe|ce)&&(t.flags|=sa)),null}case ia:return null;case Jn:return null}throw new Error("Unknown unit of work tag ("+t.tag+"). This error is likely caused by a bug in React. Please file an issue.")}function Ab(e,t,n){switch(Rc(t),t.tag){case ye:{var a=t.type;Ma(a)&&zl(t);var r=t.flags;return r&xt?(t.flags=r&~xt|ze,(t.mode&ke)!==le&&Af(t),t):null}case se:{var i=t.stateNode;Ui(t),Gs(t),lf();var u=t.flags;return(u&xt)!==X&&(u&ze)===X?(t.flags=u&~xt|ze,t):null}case $:return nf(t),null;case Fe:{Ai(t);var o=t.memoizedState;if(o!==null&&o.dehydrated!==null){if(t.alternate===null)throw new Error("Threw in newly mounted dehydrated component. This is likely a bug in React. Please file an issue.");Di()}var s=t.flags;return s&xt?(t.flags=s&~xt|ze,(t.mode&ke)!==le&&Af(t),t):null}case St:return Ai(t),null;case Ue:return Ui(t),null;case Rt:var f=t.type._context;return Uc(f,t),null;case rt:case kt:return gd(t),null;case ia:return null;default:return null}}function Cm(e,t,n){switch(Rc(t),t.tag){case ye:{var a=t.type.childContextTypes;a!=null&&zl(t);break}case se:{var r=t.stateNode;Ui(t),Gs(t),lf();break}case $:{nf(t);break}case Ue:Ui(t);break;case Fe:Ai(t);break;case St:Ai(t);break;case Rt:var i=t.type._context;Uc(i,t);break;case rt:case kt:gd(t);break}}function xm(e,t,n,a,r,i,u,o,s){var f=Array.prototype.slice.call(arguments,3);try{t.apply(n,f)}catch(m){this.onError(m)}}var Dm=xm;if(typeof window!="undefined"&&typeof window.dispatchEvent=="function"&&typeof document!="undefined"&&typeof document.createEvent=="function"){var kf=document.createElement("react");Dm=function(t,n,a,r,i,u,o,s,f){if(typeof document=="undefined"||document===null)throw new Error("The `document` global was defined when React was initialized, but is not defined anymore. This can happen in a test environment if a component schedules an update from an asynchronous callback, but the test has already finished running. To solve this, you can either unmount the component at the end of your test (and ensure that any asynchronous operations get canceled in `componentWillUnmount`), or you can change the test itself to be asynchronous.");var m=document.createEvent("Event"),S=!1,C=!0,O=window.event,U=Object.getOwnPropertyDescriptor(window,"event");function N(){kf.removeEventListener(D,oe,!1),typeof window.event!="undefined"&&window.hasOwnProperty("event")&&(window.event=O)}var I=Array.prototype.slice.call(arguments,3);function oe(){S=!0,N(),n.apply(a,I),C=!1}var te,Ke=!1,je=!1;function E(b){if(te=b.error,Ke=!0,te===null&&b.colno===0&&b.lineno===0&&(je=!0),b.defaultPrevented&&te!=null&&typeof te=="object")try{te._suppressLogging=!0}catch(L){}}var D="react-"+(t||"invokeguardedcallback");if(window.addEventListener("error",E),kf.addEventListener(D,oe,!1),m.initEvent(D,!1,!1),kf.dispatchEvent(m),U&&Object.defineProperty(window,"event",U),S&&C&&(Ke?je&&(te=new Error("A cross-origin error was thrown. React doesn't have access to the actual error object in development. See https://reactjs.org/link/crossorigin-error for more information.")):te=new Error(`An error was thrown inside one of your components, but React doesn't know what it was. This is likely due to browser flakiness. React does its best to preserve the "Pause on exceptions" behavior of the DevTools, which requires some DEV-mode only tricks. It's possible that these don't work in your browser. Try triggering the error in production mode, or switching to a modern browser. If you suspect that this is actually an issue with React, please file an issue.`),this.onError(te)),window.removeEventListener("error",E),!S)return N(),xm.apply(this,arguments)}}var zb=Dm,Zu=!1,jo=null,Hb={onError:function(e){Zu=!0,jo=e}};function _m(e,t,n,a,r,i,u,o,s){Zu=!1,jo=null,zb.apply(Hb,arguments)}function Lb(){return Zu}function Om(){if(Zu){var e=jo;return Zu=!1,jo=null,e}else throw new Error("clearCaughtError was called but no error was captured. This error is likely caused by a bug in React. Please file an issue.")}var Nm=null;Nm=new Set;var Fo=!1,rn=!1,jb=typeof WeakSet=="function"?WeakSet:Set,J=null,Fi=null,Bi=null;function Fb(e){_m(null,function(){throw e}),Om()}var Bb=function(e,t){if(t.props=e.memoizedProps,t.state=e.memoizedState,e.mode&ke)try{qa(),t.componentWillUnmount()}finally{Ya(e)}else t.componentWillUnmount()};function Um(e,t){try{Mr(zt,e)}catch(n){Ie(e,t,n)}}function Wf(e,t,n){try{Bb(e,n)}catch(a){Ie(e,t,a)}}function Vb(e,t,n){try{n.componentDidMount()}catch(a){Ie(e,t,a)}}function Mm(e,t){try{Hm(e)}catch(n){Ie(e,t,n)}}function Vi(e,t){var n=e.ref;if(n!==null)if(typeof n=="function"){var a;try{if(gt&&zn&&e.mode&ke)try{qa(),a=n(null)}finally{Ya(e)}else a=n(null)}catch(r){Ie(e,t,r)}typeof a=="function"&&h("Unexpected return value from a callback ref in %s. A callback ref should not return a function.",W(e))}else n.current=null}function Bo(e,t,n){try{n()}catch(a){Ie(e,t,a)}}var Am=null,zm=!1;function wb(e,t){Am=Rs(e.containerInfo),J=t,Yb();var n=zm;return zm=!1,Am=null,n}function Yb(){for(;J!==null;){var e=J,t=e.child;(e.subtreeFlags&De)!==X&&t!==null?(t.return=e,J=t):qb()}}function qb(){for(;J!==null;){var e=J;Et(e);try{Qb(e)}catch(n){Ie(e,e.return,n)}bn();var t=e.sibling;if(t!==null){t.return=e.return,J=t;return}J=e.return}}function Qb(e){var t=e.alternate,n=e.flags;if((n&sn)!==X){switch(Et(e),e.tag){case Ne:case Me:case Ae:break;case ye:{if(t!==null){var a=t.memoizedProps,r=t.memoizedState,i=e.stateNode;e.type===e.elementType&&!ni&&(i.props!==e.memoizedProps&&h("Expected %s props to match memoized props before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",W(e)||"instance"),i.state!==e.memoizedState&&h("Expected %s state to match memoized state before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",W(e)||"instance"));var u=i.getSnapshotBeforeUpdate(e.elementType===e.type?a:ha(e.type,a),r);{var o=Nm;u===void 0&&!o.has(e.type)&&(o.add(e.type),h("%s.getSnapshotBeforeUpdate(): A snapshot value (or null) must be returned. You have returned undefined.",W(e)))}i.__reactInternalSnapshotBeforeUpdate=u}break}case se:{if(Qt){var s=e.stateNode;x(s.containerInfo)}break}case $:case ge:case Ue:case Zt:break;default:throw new Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.")}bn()}}function ba(e,t,n){var a=t.updateQueue,r=a!==null?a.lastEffect:null;if(r!==null){var i=r.next,u=i;do{if((u.tag&e)===e){var o=u.destroy;u.destroy=void 0,o!==void 0&&((e&nn)!==Un?Lg(t):(e&zt)!==Un&&jv(t),(e&Ba)!==Un&&ll(!0),Bo(t,n,o),(e&Ba)!==Un&&ll(!1),(e&nn)!==Un?jg():(e&zt)!==Un&&Fv())}u=u.next}while(u!==i)}}function Mr(e,t){var n=t.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var r=a.next,i=r;do{if((i.tag&e)===e){(e&nn)!==Un?zg(t):(e&zt)!==Un&&Fg(t);var u=i.create;(e&Ba)!==Un&&ll(!0),i.destroy=u(),(e&Ba)!==Un&&ll(!1),(e&nn)!==Un?Hg():(e&zt)!==Un&&Bg();{var o=i.destroy;if(o!==void 0&&typeof o!="function"){var s=void 0;(i.tag&zt)!==X?s="useLayoutEffect":(i.tag&Ba)!==X?s="useInsertionEffect":s="useEffect";var f=void 0;o===null?f=" You returned null. If your effect does not require clean up, return undefined (or nothing).":typeof o.then=="function"?f=`

It looks like you wrote `+s+`(async () => ...) or returned a Promise. Instead, write the async function inside your effect and call it immediately:

`+s+`(() => {
  async function fetchData() {
    // You can await here
    const response = await MyAPI.getData(someId);
    // ...
  }
  fetchData();
}, [someId]); // Or [] if effect doesn't need props or state

Learn more about data fetching with Hooks: https://reactjs.org/link/hooks-data-fetching`:f=" You returned: "+o,h("%s must not return anything besides a function, which is used for clean-up.%s",s,f)}}}i=i.next}while(i!==r)}}function Pb(e,t){if((t.flags&ce)!==X)switch(t.tag){case Tt:{var n=t.stateNode.passiveEffectDuration,a=t.memoizedProps,r=a.id,i=a.onPostCommit,u=Wp(),o=t.alternate===null?"mount":"update";kp()&&(o="nested-update"),typeof i=="function"&&i(r,o,n,u);var s=t.return;e:for(;s!==null;){switch(s.tag){case se:var f=s.stateNode;f.passiveEffectDuration+=n;break e;case Tt:var m=s.stateNode;m.passiveEffectDuration+=n;break e}s=s.return}break}}}function Gb(e,t,n,a){if((n.flags&vt)!==X)switch(n.tag){case Ne:case Me:case Ae:{if(!rn)if(n.mode&ke)try{qa(),Mr(zt|At,n)}finally{Ya(n)}else Mr(zt|At,n);break}case ye:{var r=n.stateNode;if(n.flags&ce&&!rn)if(t===null)if(n.type===n.elementType&&!ni&&(r.props!==n.memoizedProps&&h("Expected %s props to match memoized props before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",W(n)||"instance"),r.state!==n.memoizedState&&h("Expected %s state to match memoized state before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",W(n)||"instance")),n.mode&ke)try{qa(),r.componentDidMount()}finally{Ya(n)}else r.componentDidMount();else{var i=n.elementType===n.type?t.memoizedProps:ha(n.type,t.memoizedProps),u=t.memoizedState;if(n.type===n.elementType&&!ni&&(r.props!==n.memoizedProps&&h("Expected %s props to match memoized props before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",W(n)||"instance"),r.state!==n.memoizedState&&h("Expected %s state to match memoized state before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",W(n)||"instance")),n.mode&ke)try{qa(),r.componentDidUpdate(i,u,r.__reactInternalSnapshotBeforeUpdate)}finally{Ya(n)}else r.componentDidUpdate(i,u,r.__reactInternalSnapshotBeforeUpdate)}var o=n.updateQueue;o!==null&&(n.type===n.elementType&&!ni&&(r.props!==n.memoizedProps&&h("Expected %s props to match memoized props before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",W(n)||"instance"),r.state!==n.memoizedState&&h("Expected %s state to match memoized state before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",W(n)||"instance")),fp(n,o,r));break}case se:{var s=n.updateQueue;if(s!==null){var f=null;if(n.child!==null)switch(n.child.tag){case $:f=si(n.child.stateNode);break;case ye:f=n.child.stateNode;break}fp(n,s,f)}break}case $:{var m=n.stateNode;if(t===null&&n.flags&ce){var S=n.type,C=n.memoizedProps;vi(m,S,C,n)}break}case ge:break;case Ue:break;case Tt:{{var O=n.memoizedProps,U=O.onCommit,N=O.onRender,I=n.stateNode.effectDuration,oe=Wp(),te=t===null?"mount":"update";kp()&&(te="nested-update"),typeof N=="function"&&N(n.memoizedProps.id,te,n.actualDuration,n.treeBaseDuration,n.actualStartTime,oe);{typeof U=="function"&&U(n.memoizedProps.id,te,I,oe),nR(n);var Ke=n.return;e:for(;Ke!==null;){switch(Ke.tag){case se:var je=Ke.stateNode;je.effectDuration+=I;break e;case Tt:var E=Ke.stateNode;E.effectDuration+=I;break e}Ke=Ke.return}}}break}case Fe:{eE(e,n);break}case St:case Zt:case gn:case rt:case kt:case Jn:break;default:throw new Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.")}rn||n.flags&Wn&&Hm(n)}function Kb(e){switch(e.tag){case Ne:case Me:case Ae:{if(e.mode&ke)try{qa(),Um(e,e.return)}finally{Ya(e)}else Um(e,e.return);break}case ye:{var t=e.stateNode;typeof t.componentDidMount=="function"&&Vb(e,e.return,t),Mm(e,e.return);break}case $:{Mm(e,e.return);break}}}function Xb(e,t){var n=null;if(Qt)for(var a=e;;){if(a.tag===$){if(n===null){n=a;try{var r=a.stateNode;t?zs(r):p(a.stateNode,a.memoizedProps)}catch(u){Ie(e,e.return,u)}}}else if(a.tag===ge){if(n===null)try{var i=a.stateNode;t?l(i):R(i,a.memoizedProps)}catch(u){Ie(e,e.return,u)}}else if(!((a.tag===rt||a.tag===kt)&&a.memoizedState!==null&&a!==e)){if(a.child!==null){a.child.return=a,a=a.child;continue}}if(a===e)return;for(;a.sibling===null;){if(a.return===null||a.return===e)return;n===a&&(n=null),a=a.return}n===a&&(n=null),a.sibling.return=a.return,a=a.sibling}}function Hm(e){var t=e.ref;if(t!==null){var n=e.stateNode,a;switch(e.tag){case $:a=si(n);break;default:a=n}if(typeof t=="function"){var r;if(e.mode&ke)try{qa(),r=t(a)}finally{Ya(e)}else r=t(a);typeof r=="function"&&h("Unexpected return value from a callback ref in %s. A callback ref should not return a function.",W(e))}else t.hasOwnProperty("current")||h("Unexpected ref object provided for %s. Use either a ref-setter function or React.createRef().",W(e)),t.current=a}}function Jb(e){var t=e.alternate;t!==null&&(t.return=null),e.return=null}function Lm(e){var t=e.alternate;t!==null&&(e.alternate=null,Lm(t));{if(e.child=null,e.deletions=null,e.sibling=null,e.tag===$){var n=e.stateNode;n!==null&&Sl(n)}e.stateNode=null,e._debugOwner=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}}function Zb(e){if(fi){var t=e.stateNode,n=t.containerInfo,a=ie(n);Oe(n,a)}}function kb(e){for(var t=e.return;t!==null;){if(jm(t))return t;t=t.return}throw new Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.")}function jm(e){return e.tag===$||e.tag===se||e.tag===Ue}function Fm(e){var t=e;e:for(;;){for(;t.sibling===null;){if(t.return===null||jm(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==$&&t.tag!==ge&&t.tag!==Xn;){if(t.flags&Qe||t.child===null||t.tag===Ue)continue e;t.child.return=t,t=t.child}if(!(t.flags&Qe))return t.stateNode}}function Wb(e){if(Qt){var t=kb(e);switch(t.tag){case $:{var n=t.stateNode;t.flags&oa&&(xl(n),t.flags&=~oa);var a=Fm(e);$f(e,a,n);break}case se:case Ue:{var r=t.stateNode.containerInfo,i=Fm(e);If(e,i,r);break}default:throw new Error("Invalid host parent fiber. This error is likely caused by a bug in React. Please file an issue.")}}}function If(e,t,n){var a=e.tag,r=a===$||a===ge;if(r){var i=e.stateNode;t?su(n,i,t):Tl(n,i)}else if(a!==Ue){var u=e.child;if(u!==null){If(u,t,n);for(var o=u.sibling;o!==null;)If(o,t,n),o=o.sibling}}}function $f(e,t,n){var a=e.tag,r=a===$||a===ge;if(r){var i=e.stateNode;t?ou(n,i,t):br(n,i)}else if(a!==Ue){var u=e.child;if(u!==null){$f(u,t,n);for(var o=u.sibling;o!==null;)$f(o,t,n),o=o.sibling}}}var un=null,Ea=!1;function Ib(e,t,n){if(Qt){var a=t;e:for(;a!==null;){switch(a.tag){case $:{un=a.stateNode,Ea=!1;break e}case se:{un=a.stateNode.containerInfo,Ea=!0;break e}case Ue:{un=a.stateNode.containerInfo,Ea=!0;break e}}a=a.return}if(un===null)throw new Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.");ed(e,t,n),un=null,Ea=!1}else ed(e,t,n);Jb(n)}function Pa(e,t,n){for(var a=n.child;a!==null;)ed(e,t,a),a=a.sibling}function ed(e,t,n){switch(Ng(n),n.tag){case $:rn||Vi(n,t);case ge:{if(Qt){var a=un,r=Ea;un=null,Pa(e,t,n),un=a,Ea=r,un!==null&&(Ea?As(un,n.stateNode):Ms(un,n.stateNode))}else Pa(e,t,n);return}case Xn:{Qt&&un!==null&&(Ea?My(un,n.stateNode):Uy(un,n.stateNode));return}case Ue:{if(Qt){var i=un,u=Ea;un=n.stateNode.containerInfo,Ea=!0,Pa(e,t,n),un=i,Ea=u}else Zb(n),Pa(e,t,n);return}case Ne:case Me:case st:case Ae:{if(!rn){var o=n.updateQueue;if(o!==null){var s=o.lastEffect;if(s!==null){var f=s.next,m=f;do{var S=m,C=S.destroy,O=S.tag;C!==void 0&&((O&Ba)!==Un?Bo(n,t,C):(O&zt)!==Un&&(jv(n),n.mode&ke?(qa(),Bo(n,t,C),Ya(n)):Bo(n,t,C),Fv())),m=m.next}while(m!==f)}}}Pa(e,t,n);return}case ye:{if(!rn){Vi(n,t);var U=n.stateNode;typeof U.componentWillUnmount=="function"&&Wf(n,t,U)}Pa(e,t,n);return}case gn:{Pa(e,t,n);return}case rt:{if(n.mode&Be){var N=rn;rn=N||n.memoizedState!==null,Pa(e,t,n),rn=N}else Pa(e,t,n);break}default:{Pa(e,t,n);return}}}function $b(e){var t=e.memoizedState}function eE(e,t){if(Mt){var n=t.memoizedState;if(n===null){var a=t.alternate;if(a!==null){var r=a.memoizedState;if(r!==null){var i=r.dehydrated;i!==null&&Ny(i)}}}}}function Bm(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new jb),t.forEach(function(a){var r=sR.bind(null,e,a);if(!n.has(a)){if(n.add(a),va)if(Fi!==null&&Bi!==null)ul(Bi,Fi);else throw Error("Expected finished root and lanes to be set. This is a bug in React.");a.then(r,r)}})}}function tE(e,t,n){Fi=n,Bi=e,Et(t),Vm(t,e),Et(t),Fi=null,Bi=null}function Ra(e,t,n){var a=t.deletions;if(a!==null)for(var r=0;r<a.length;r++){var i=a[r];try{Ib(e,t,i)}catch(s){Ie(i,t,s)}}var u=yS();if(t.subtreeFlags&Se)for(var o=t.child;o!==null;)Et(o),Vm(o,e),o=o.sibling;Et(u)}function Vm(e,t,n){var a=e.alternate,r=e.flags;switch(e.tag){case Ne:case Me:case st:case Ae:{if(Ra(t,e),Ga(e),r&ce){try{ba(Ba|At,e,e.return),Mr(Ba|At,e)}catch(ae){Ie(e,e.return,ae)}if(e.mode&ke){try{qa(),ba(zt|At,e,e.return)}catch(ae){Ie(e,e.return,ae)}Ya(e)}else try{ba(zt|At,e,e.return)}catch(ae){Ie(e,e.return,ae)}}return}case ye:{Ra(t,e),Ga(e),r&Wn&&a!==null&&Vi(a,a.return);return}case $:{if(Ra(t,e),Ga(e),r&Wn&&a!==null&&Vi(a,a.return),Qt){if(e.flags&oa){var i=e.stateNode;try{xl(i)}catch(ae){Ie(e,e.return,ae)}}if(r&ce){var u=e.stateNode;if(u!=null){var o=e.memoizedProps,s=a!==null?a.memoizedProps:o,f=e.type,m=e.updateQueue;if(e.updateQueue=null,m!==null)try{lu(u,m,f,s,o,e)}catch(ae){Ie(e,e.return,ae)}}}}return}case ge:{if(Ra(t,e),Ga(e),r&ce&&Qt){if(e.stateNode===null)throw new Error("This should have a text node initialized. This error is likely caused by a bug in React. Please file an issue.");var S=e.stateNode,C=e.memoizedProps,O=a!==null?a.memoizedProps:C;try{Cl(S,O,C)}catch(ae){Ie(e,e.return,ae)}}return}case se:{if(Ra(t,e),Ga(e),r&ce){if(Qt&&Mt&&a!==null){var U=a.memoizedState;if(U.isDehydrated)try{Oy(t.containerInfo)}catch(ae){Ie(e,e.return,ae)}}if(fi){var N=t.containerInfo,I=t.pendingChildren;try{Oe(N,I)}catch(ae){Ie(e,e.return,ae)}}}return}case Ue:{if(Ra(t,e),Ga(e),r&ce&&fi){var oe=e.stateNode,te=oe.containerInfo,Ke=oe.pendingChildren;try{Oe(te,Ke)}catch(ae){Ie(e,e.return,ae)}}return}case Fe:{Ra(t,e),Ga(e);var je=e.child;if(je.flags&sa){var E=je.stateNode,D=je.memoizedState,b=D!==null;if(E.isHidden=b,b){var L=je.alternate!==null&&je.alternate.memoizedState!==null;L||JE()}}if(r&ce){try{$b(e)}catch(ae){Ie(e,e.return,ae)}Bm(e)}return}case rt:{var k=a!==null&&a.memoizedState!==null;if(e.mode&Be){var K=rn;rn=K||k,Ra(t,e),rn=K}else Ra(t,e);if(Ga(e),r&sa){var He=e.stateNode,de=e.memoizedState,we=de!==null,_e=e;if(He.isHidden=we,we&&!k&&(_e.mode&Be)!==le){J=_e;for(var nt=_e.child;nt!==null;)J=nt,aE(nt),nt=nt.sibling}Qt&&Xb(_e,we)}return}case St:{Ra(t,e),Ga(e),r&ce&&Bm(e);return}case gn:return;default:{Ra(t,e),Ga(e);return}}}function Ga(e){var t=e.flags;if(t&Qe){try{Wb(e)}catch(n){Ie(e,e.return,n)}e.flags&=~Qe}t&Nn&&(e.flags&=~Nn)}function nE(e,t,n){Fi=n,Bi=t,J=e,wm(e,t,n),Fi=null,Bi=null}function wm(e,t,n){for(var a=(e.mode&Be)!==le;J!==null;){var r=J,i=r.child;if(r.tag===rt&&a){var u=r.memoizedState!==null,o=u||Fo;if(o){td(e,t,n);continue}else{var s=r.alternate,f=s!==null&&s.memoizedState!==null,m=f||rn,S=Fo,C=rn;Fo=o,rn=m,rn&&!C&&(J=r,rE(r));for(var O=i;O!==null;)J=O,wm(O,t,n),O=O.sibling;J=r,Fo=S,rn=C,td(e,t,n);continue}}(r.subtreeFlags&vt)!==X&&i!==null?(i.return=r,J=i):td(e,t,n)}}function td(e,t,n){for(;J!==null;){var a=J;if((a.flags&vt)!==X){var r=a.alternate;Et(a);try{Gb(t,r,a,n)}catch(u){Ie(a,a.return,u)}bn()}if(a===e){J=null;return}var i=a.sibling;if(i!==null){i.return=a.return,J=i;return}J=a.return}}function aE(e){for(;J!==null;){var t=J,n=t.child;switch(t.tag){case Ne:case Me:case st:case Ae:{if(t.mode&ke)try{qa(),ba(zt,t,t.return)}finally{Ya(t)}else ba(zt,t,t.return);break}case ye:{Vi(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&Wf(t,t.return,a);break}case $:{Vi(t,t.return);break}case rt:{var r=t.memoizedState!==null;if(r){Ym(e);continue}break}}n!==null?(n.return=t,J=n):Ym(e)}}function Ym(e){for(;J!==null;){var t=J;if(t===e){J=null;return}var n=t.sibling;if(n!==null){n.return=t.return,J=n;return}J=t.return}}function rE(e){for(;J!==null;){var t=J,n=t.child;if(t.tag===rt){var a=t.memoizedState!==null;if(a){qm(e);continue}}n!==null?(n.return=t,J=n):qm(e)}}function qm(e){for(;J!==null;){var t=J;Et(t);try{Kb(t)}catch(a){Ie(t,t.return,a)}if(bn(),t===e){J=null;return}var n=t.sibling;if(n!==null){n.return=t.return,J=n;return}J=t.return}}function iE(e,t,n,a){J=t,uE(t,e,n,a)}function uE(e,t,n,a){for(;J!==null;){var r=J,i=r.child;(r.subtreeFlags&Je)!==X&&i!==null?(i.return=r,J=i):lE(e,t,n,a)}}function lE(e,t,n,a){for(;J!==null;){var r=J;if((r.flags&Yt)!==X){Et(r);try{oE(t,r,n,a)}catch(u){Ie(r,r.return,u)}bn()}if(r===e){J=null;return}var i=r.sibling;if(i!==null){i.return=r.return,J=i;return}J=r.return}}function oE(e,t,n,a){switch(t.tag){case Ne:case Me:case Ae:{if(t.mode&ke){Mf();try{Mr(nn|At,t)}finally{Uf(t)}}else Mr(nn|At,t);break}}}function sE(e){J=e,cE()}function cE(){for(;J!==null;){var e=J,t=e.child;if((J.flags&Wt)!==X){var n=e.deletions;if(n!==null){for(var a=0;a<n.length;a++){var r=n[a];J=r,vE(r,e)}{var i=e.alternate;if(i!==null){var u=i.child;if(u!==null){i.child=null;do{var o=u.sibling;u.sibling=null,u=o}while(u!==null)}}}J=e}}(e.subtreeFlags&Je)!==X&&t!==null?(t.return=e,J=t):fE()}}function fE(){for(;J!==null;){var e=J;(e.flags&Yt)!==X&&(Et(e),dE(e),bn());var t=e.sibling;if(t!==null){t.return=e.return,J=t;return}J=e.return}}function dE(e){switch(e.tag){case Ne:case Me:case Ae:{e.mode&ke?(Mf(),ba(nn|At,e,e.return),Uf(e)):ba(nn|At,e,e.return);break}}}function vE(e,t){for(;J!==null;){var n=J;Et(n),mE(n,t),bn();var a=n.child;a!==null?(a.return=n,J=a):pE(e)}}function pE(e){for(;J!==null;){var t=J,n=t.sibling,a=t.return;if(Lm(t),t===e){J=null;return}if(n!==null){n.return=a,J=n;return}J=a}}function mE(e,t){switch(e.tag){case Ne:case Me:case Ae:{e.mode&ke?(Mf(),ba(nn,e,t),Uf(e)):ba(nn,e,t);break}}}function hE(e){switch(e.tag){case Ne:case Me:case Ae:{try{Mr(zt|At,e)}catch(n){Ie(e,e.return,n)}break}case ye:{var t=e.stateNode;try{t.componentDidMount()}catch(n){Ie(e,e.return,n)}break}}}function yE(e){switch(e.tag){case Ne:case Me:case Ae:{try{Mr(nn|At,e)}catch(t){Ie(e,e.return,t)}break}}}function gE(e){switch(e.tag){case Ne:case Me:case Ae:{try{ba(zt|At,e,e.return)}catch(n){Ie(e,e.return,n)}break}case ye:{var t=e.stateNode;typeof t.componentWillUnmount=="function"&&Wf(e,e.return,t);break}}}function SE(e){switch(e.tag){case Ne:case Me:case Ae:try{ba(nn|At,e,e.return)}catch(t){Ie(e,e.return,t)}}}var Vo=0,wo=1,Yo=2,qo=3,Qo=4;if(typeof Symbol=="function"&&Symbol.for){var ku=Symbol.for;Vo=ku("selector.component"),wo=ku("selector.has_pseudo_class"),Yo=ku("selector.role"),qo=ku("selector.test_id"),Qo=ku("selector.text")}function bE(e){return{$$typeof:Vo,value:e}}function EE(e){return{$$typeof:wo,value:e}}function RE(e){return{$$typeof:Yo,value:e}}function TE(e){return{$$typeof:Qo,value:e}}function CE(e){return{$$typeof:qo,value:e}}function nd(e){var t=uu(e);if(t!=null){if(typeof t.memoizedProps["data-testname"]!="string")throw new Error("Invalid host root specified. Should be either a React container or a node with a testname attribute.");return t}else{var n=El(e);if(n===null)throw new Error("Could not find React container within specified host subtree.");return n.stateNode.current}}function ad(e,t){switch(t.$$typeof){case Vo:if(e.type===t.value)return!0;break;case wo:return xE(e,t.value);case Yo:if(e.tag===$){var n=e.stateNode;if(Rl(n,t.value))return!0}break;case Qo:if(e.tag===$||e.tag===ge){var a=Ns(e);if(a!==null&&a.indexOf(t.value)>=0)return!0}break;case qo:if(e.tag===$){var r=e.memoizedProps["data-testname"];if(typeof r=="string"&&r.toLowerCase()===t.value.toLowerCase())return!0}break;default:throw new Error("Invalid selector type specified.")}return!1}function rd(e){switch(e.$$typeof){case Vo:var t=ee(e.value)||"Unknown";return"<"+t+">";case wo:return":has("+(rd(e)||"")+")";case Yo:return'[role="'+e.value+'"]';case Qo:return'"'+e.value+'"';case qo:return'[data-testname="'+e.value+'"]';default:throw new Error("Invalid selector type specified.")}}function Qm(e,t){for(var n=[],a=[e,0],r=0;r<a.length;){var i=a[r++],u=a[r++],o=t[u];if(!(i.tag===$&&qr(i))){for(;o!=null&&ad(i,o);)u++,o=t[u];if(u===t.length)n.push(i);else for(var s=i.child;s!==null;)a.push(s,u),s=s.sibling}}return n}function xE(e,t){for(var n=[e,0],a=0;a<n.length;){var r=n[a++],i=n[a++],u=t[i];if(!(r.tag===$&&qr(r))){for(;u!=null&&ad(r,u);)i++,u=t[i];if(i===t.length)return!0;for(var o=r.child;o!==null;)n.push(o,i),o=o.sibling}}return!1}function Po(e,t){if(!er)throw new Error("Test selector API is not supported by this renderer.");for(var n=nd(e),a=Qm(n,t),r=[],i=Array.from(a),u=0;u<i.length;){var o=i[u++];if(o.tag===$){if(qr(o))continue;r.push(o.stateNode)}else for(var s=o.child;s!==null;)i.push(s),s=s.sibling}return r}function DE(e,t){if(!er)throw new Error("Test selector API is not supported by this renderer.");for(var n=nd(e),a=0,r=[],i=[n,0],u=0;u<i.length;){var o=i[u++],s=i[u++],f=t[s];if(!(o.tag===$&&qr(o))&&(ad(o,f)&&(r.push(rd(f)),s++,s>a&&(a=s)),s<t.length))for(var m=o.child;m!==null;)i.push(m,s),m=m.sibling}if(a<t.length){for(var S=[],C=a;C<t.length;C++)S.push(rd(t[C]));return`findAllNodes was able to match part of the selector:
`+("  "+r.join(" > ")+`

`)+`No matching component was found for:
`+("  "+S.join(" > "))}return null}function _E(e,t){if(!er)throw new Error("Test selector API is not supported by this renderer.");for(var n=Po(e,t),a=[],r=0;r<n.length;r++)a.push(Os(n[r]));for(var i=a.length-1;i>0;i--)for(var u=a[i],o=u.x,s=o+u.width,f=u.y,m=f+u.height,S=i-1;S>=0;S--)if(i!==S){var C=a[S],O=C.x,U=O+C.width,N=C.y,I=N+C.height;if(o>=O&&f>=N&&s<=U&&m<=I){a.splice(i,1);break}else if(o===O&&u.width===C.width&&!(I<f)&&!(N>m)){N>f&&(C.height+=N-f,C.y=f),I<m&&(C.height=m-N),a.splice(i,1);break}else if(f===N&&u.height===C.height&&!(U<o)&&!(O>s)){O>o&&(C.width+=O-o,C.x=o),U<s&&(C.width=s-O),a.splice(i,1);break}}return a}function OE(e,t){if(!er)throw new Error("Test selector API is not supported by this renderer.");for(var n=nd(e),a=Qm(n,t),r=Array.from(a),i=0;i<r.length;){var u=r[i++];if(!qr(u)){if(u.tag===$){var o=u.stateNode;if(di(o))return!0}for(var s=u.child;s!==null;)r.push(s),s=s.sibling}}return!1}var Go=[];function NE(){er&&Go.forEach(function(e){return e()})}function UE(e,t,n,a){if(!er)throw new Error("Test selector API is not supported by this renderer.");var r=Po(e,t),i=Us(r,n,a),u=i.disconnect,o=i.observe,s=i.unobserve,f=function(){var m=Po(e,t);r.forEach(function(S){m.indexOf(S)<0&&s(S)}),m.forEach(function(S){r.indexOf(S)<0&&o(S)})};return Go.push(f),{disconnect:function(){var m=Go.indexOf(f);m>=0&&Go.splice(m,1),u()}}}var ME=M.ReactCurrentActQueue;function AE(e){{var t=typeof IS_REACT_ACT_ENVIRONMENT!="undefined"?IS_REACT_ACT_ENVIRONMENT:void 0,n=typeof jest!="undefined";return yl&&n&&t!==!1}}function Pm(){{var e=typeof IS_REACT_ACT_ENVIRONMENT!="undefined"?IS_REACT_ACT_ENVIRONMENT:void 0;return!e&&ME.current!==null&&h("The current testing environment is not configured to support act(...)"),e}}var zE=Math.ceil,id=M.ReactCurrentDispatcher,ud=M.ReactCurrentOwner,pt=M.ReactCurrentBatchConfig,Ta=M.ReactCurrentActQueue,Nt=0,ld=1,ln=2,aa=4,sr=0,Wu=1,ai=2,Ko=3,Iu=4,Gm=5,od=6,Ce=Nt,Tn=null,mt=null,jt=A,Ka=A,sd=Tr(A),Ft=sr,$u=null,cd=A,Xo=A,el=A,Jo=A,tl=null,Mn=null,fd=0,Km=500,Xm=1/0,HE=500,cr=null;function wi(){Xm=Kt()+HE}function Jm(){return Xm}var Zo=!1,dd=null,Yi=null,ri=!1,Ar=null,nl=A,vd=[],pd=null,LE=50,al=0,md=null,hd=!1,ko=!1,jE=50,qi=0,Wo=null,rl=ot,Io=A,Zm=!1;function $o(){return Tn}function pn(){return(Ce&(ln|aa))!==Nt?Kt():(rl!==ot||(rl=Kt()),rl)}function zr(e){var t=e.mode;if((t&Be)===le)return fe;if((Ce&ln)!==Nt&&jt!==A)return yu(jt);var n=vS()!==dS;if(n){if(pt.transition!==null){var a=pt.transition;a._updatedFibers||(a._updatedFibers=new Set),a._updatedFibers.add(e)}return Io===Pt&&(Io=Cv()),Io}var r=da();if(r!==Pt)return r;var i=gl();return i}function FE(e){var t=e.mode;return(t&Be)===le?fe:dg()}function Ut(e,t,n,a){fR(),Zm&&h("useInsertionEffect must not schedule updates."),hd&&(ko=!0),gu(e,n,a),(Ce&ln)!==A&&e===Tn?pR(t):(va&&_v(e,t,n),mR(t),e===Tn&&((Ce&ln)===Nt&&(el=Ee(el,n)),Ft===Iu&&Hr(e,jt)),An(e,a),n===fe&&Ce===Nt&&(t.mode&Be)===le&&!Ta.isBatchingLegacy&&(wi(),Yv()))}function BE(e,t,n){var a=e.current;a.lanes=t,gu(e,t,n),An(e,n)}function VE(e){return(Ce&ln)!==Nt}function An(e,t){var n=e.callbackNode;ug(e,t);var a=Bl(e,e===Tn?jt:A);if(a===A){n!==null&&ch(n),e.callbackNode=null,e.callbackPriority=Pt;return}var r=Gr(a),i=e.callbackPriority;if(i===r&&!(Ta.current!==null&&n!==Rd)){n==null&&i!==fe&&h("Expected scheduled callback to exist. This error is likely caused by a bug in React. Please file an issue.");return}n!=null&&ch(n);var u;if(r===fe)e.tag===yi?(Ta.isBatchingLegacy!==null&&(Ta.didScheduleLegacyUpdate=!0),Zg(Im.bind(null,e))):wv(Im.bind(null,e)),bl?Ta.current!==null?Ta.current.push(ja):_s(function(){(Ce&(ln|aa))===Nt&&ja()}):is(Yl,ja),u=null;else{var o;switch(Mv(a)){case Ha:o=Yl;break;case Su:o=zv;break;case bu:o=Ri;break;case yc:o=Hv;break;default:o=Ri;break}u=is(o,km.bind(null,e))}e.callbackPriority=r,e.callbackNode=u}function km(e,t){if(JS(),rl=ot,Io=A,(Ce&(ln|aa))!==Nt)throw new Error("Should not already be working.");var n=e.callbackNode,a=Xa();if(a&&e.callbackNode!==n)return null;var r=Bl(e,e===Tn?jt:A);if(r===A)return null;var i=!Vl(e,r)&&!fg(e,r)&&!t,u=i?$E(e,r):ns(e,r);if(u!==sr){if(u===ai){var o=dc(e);o!==A&&(r=o,u=yd(e,o))}if(u===Wu){var s=$u;throw ii(e,A),Hr(e,r),An(e,Kt()),s}if(u===od)Hr(e,r);else{var f=!Vl(e,r),m=e.current.alternate;if(f&&!YE(m)){if(u=ns(e,r),u===ai){var S=dc(e);S!==A&&(r=S,u=yd(e,S))}if(u===Wu){var C=$u;throw ii(e,A),Hr(e,r),An(e,Kt()),C}}e.finishedWork=m,e.finishedLanes=r,wE(e,u,r)}}return An(e,Kt()),e.callbackNode===n?km.bind(null,e):null}function yd(e,t){var n=tl;if(qv(e)){var a=ii(e,t);a.flags|=On,Xy(e.containerInfo)}var r=ns(e,t);if(r!==ai){var i=Mn;Mn=n,i!==null&&Wm(i)}return r}function Wm(e){Mn===null?Mn=e:Mn.push.apply(Mn,e)}function wE(e,t,n){switch(t){case sr:case Wu:throw new Error("Root did not complete. This is a bug in React.");case ai:{ui(e,Mn,cr);break}case Ko:{if(Hr(e,n),Rv(n)&&!fh()){var a=fd+Km-Kt();if(a>10){var r=Bl(e,A);if(r!==A)break;var i=e.suspendedLanes;if(!Ei(i,n)){var u=pn();Dv(e,i);break}e.timeoutHandle=hl(ui.bind(null,e,Mn,cr),a);break}}ui(e,Mn,cr);break}case Iu:{if(Hr(e,n),cg(n))break;if(!fh()){var o=rg(e,n),s=o,f=Kt()-s,m=cR(f)-f;if(m>10){e.timeoutHandle=hl(ui.bind(null,e,Mn,cr),m);break}}ui(e,Mn,cr);break}case Gm:{ui(e,Mn,cr);break}default:throw new Error("Unknown root exit status.")}}function YE(e){for(var t=e;;){if(t.flags&$a){var n=t.updateQueue;if(n!==null){var a=n.stores;if(a!==null)for(var r=0;r<a.length;r++){var i=a[r],u=i.getSnapshot,o=i.value;try{if(!qn(u(),o))return!1}catch(f){return!1}}}}var s=t.child;if(t.subtreeFlags&$a&&s!==null){s.return=t,t=s;continue}if(t===e)return!0;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}return!0}function Hr(e,t){t=wl(t,Jo),t=wl(t,el),pg(e,t)}function Im(e){if(ZS(),(Ce&(ln|aa))!==Nt)throw new Error("Should not already be working.");Xa();var t=Bl(e,A);if(!Yn(t,fe))return An(e,Kt()),null;var n=ns(e,t);if(e.tag!==yi&&n===ai){var a=dc(e);a!==A&&(t=a,n=yd(e,a))}if(n===Wu){var r=$u;throw ii(e,A),Hr(e,t),An(e,Kt()),r}if(n===od)throw new Error("Root did not complete. This is a bug in React.");var i=e.current.alternate;return e.finishedWork=i,e.finishedLanes=t,ui(e,Mn,cr),An(e,Kt()),null}function qE(e,t){t!==A&&(hc(e,Ee(t,fe)),An(e,Kt()),(Ce&(ln|aa))===Nt&&(wi(),ja()))}function QE(e){var t=da(),n=pt.transition;try{return pt.transition=null,Gt(bu),e()}finally{Gt(t),pt.transition=n}}function PE(e,t){var n=Ce;Ce|=ld;try{return e(t)}finally{Ce=n,Ce===Nt&&!Ta.isBatchingLegacy&&(wi(),Yv())}}function GE(e,t,n,a,r){var i=da(),u=pt.transition;try{return pt.transition=null,Gt(Ha),e(t,n,a,r)}finally{Gt(i),pt.transition=u,Ce===Nt&&wi()}}function es(e){Ar!==null&&Ar.tag===yi&&(Ce&(ln|aa))===Nt&&Xa();var t=Ce;Ce|=ld;var n=pt.transition,a=da();try{return pt.transition=null,Gt(Ha),e?e():void 0}finally{Gt(a),pt.transition=n,Ce=t,(Ce&(ln|aa))===Nt&&ja()}}function KE(){return(Ce&(ln|aa))!==Nt}function XE(e){var t=Ce;Ce|=ld;var n=pt.transition,a=da();try{pt.transition=null,Gt(Ha),e()}finally{Gt(a),pt.transition=n,Ce=t,Ce===Nt&&(wi(),ja())}}function ts(e,t){It(sd,Ka,e),Ka=Ee(Ka,t),cd=Ee(cd,t)}function gd(e){Ka=sd.current,dn(sd,e)}function ii(e,t){e.finishedWork=null,e.finishedLanes=A;var n=e.timeoutHandle;if(n!==Sr&&(e.timeoutHandle=Sr,Ts(n)),mt!==null)for(var a=mt.return;a!==null;){var r=a.alternate;Cm(r,a),a=a.return}Tn=e;var i=li(e.current,null);return mt=i,jt=Ka=cd=t,Ft=sr,$u=null,Xo=A,el=A,Jo=A,tl=null,Mn=null,TS(),ma.discardPendingWarnings(),i}function $m(e,t){do{var n=mt;try{if(Wl(),_p(),bn(),ud.current=null,n===null||n.return===null){Ft=Wu,$u=t,mt=null;return}if(gt&&n.mode&ke&&Uo(n,!0),ft)if(Ti(),t!==null&&typeof t=="object"&&typeof t.then=="function"){var a=t;wg(n,a,jt)}else Vg(n,t,jt);eb(e,n.return,n,t,jt),ah(n)}catch(r){t=r,mt===n&&n!==null?(n=n.return,mt=n):n=mt;continue}return}while(!0)}function eh(){var e=id.current;return id.current=xo,e===null?xo:e}function th(e){id.current=e}function JE(){fd=Kt()}function il(e){Xo=Ee(e,Xo)}function ZE(){Ft===sr&&(Ft=Ko)}function Sd(){(Ft===sr||Ft===Ko||Ft===ai)&&(Ft=Iu),Tn!==null&&(vc(Xo)||vc(el))&&Hr(Tn,jt)}function kE(e){Ft!==Iu&&(Ft=ai),tl===null?tl=[e]:tl.push(e)}function WE(){return Ft===sr}function ns(e,t){var n=Ce;Ce|=ln;var a=eh();if(Tn!==e||jt!==t){if(va){var r=e.memoizedUpdaters;r.size>0&&(ul(e,jt),r.clear()),Ov(e,t)}cr=Nv(),ii(e,t)}Bv(t);do try{IE();break}catch(i){$m(e,i)}while(!0);if(Wl(),Ce=n,th(a),mt!==null)throw new Error("Cannot commit an incomplete root. This error is likely caused by a bug in React. Please file an issue.");return Vv(),Tn=null,jt=A,Ft}function IE(){for(;mt!==null;)nh(mt)}function $E(e,t){var n=Ce;Ce|=ln;var a=eh();if(Tn!==e||jt!==t){if(va){var r=e.memoizedUpdaters;r.size>0&&(ul(e,jt),r.clear()),Ov(e,t)}cr=Nv(),wi(),ii(e,t)}Bv(t);do try{eR();break}catch(i){$m(e,i)}while(!0);return Wl(),th(a),Ce=n,mt!==null?(Gg(),sr):(Vv(),Tn=null,jt=A,Ft)}function eR(){for(;mt!==null&&!Eg();)nh(mt)}function nh(e){var t=e.alternate;Et(e);var n;(e.mode&ke)!==le?(Nf(e),n=bd(t,e,Ka),Uo(e,!0)):n=bd(t,e,Ka),bn(),e.memoizedProps=e.pendingProps,n===null?ah(e):mt=n,ud.current=null}function ah(e){var t=e;do{var n=t.alternate,a=t.return;if((t.flags&Oa)===X){Et(t);var r=void 0;if((t.mode&ke)===le?r=Tm(n,t,Ka):(Nf(t),r=Tm(n,t,Ka),Uo(t,!1)),bn(),r!==null){mt=r;return}}else{var i=Ab(n,t);if(i!==null){i.flags&=$i,mt=i;return}if((t.mode&ke)!==le){Uo(t,!1);for(var u=t.actualDuration,o=t.child;o!==null;)u+=o.actualDuration,o=o.sibling;t.actualDuration=u}if(a!==null)a.flags|=Oa,a.subtreeFlags=X,a.deletions=null;else{Ft=od,mt=null;return}}var s=t.sibling;if(s!==null){mt=s;return}t=a,mt=t}while(t!==null);Ft===sr&&(Ft=Gm)}function ui(e,t,n){var a=da(),r=pt.transition;try{pt.transition=null,Gt(Ha),tR(e,t,n,a)}finally{pt.transition=r,Gt(a)}return null}function tR(e,t,n,a){do Xa();while(Ar!==null);if(dR(),(Ce&(ln|aa))!==Nt)throw new Error("Should not already be working.");var r=e.finishedWork,i=e.finishedLanes;if(Ag(i),r===null)return Lv(),null;if(i===A&&h("root.finishedLanes should not be empty during a commit. This is a bug in React."),e.finishedWork=null,e.finishedLanes=A,r===e.current)throw new Error("Cannot commit the same tree as before. This error is likely caused by a bug in React. Please file an issue.");e.callbackNode=null,e.callbackPriority=Pt;var u=Ee(r.lanes,r.childLanes);mg(e,u),e===Tn&&(Tn=null,mt=null,jt=A),((r.subtreeFlags&Je)!==X||(r.flags&Je)!==X)&&(ri||(ri=!0,pd=n,is(Ri,function(){return Xa(),null})));var o=(r.subtreeFlags&(De|Se|vt|Je))!==X,s=(r.flags&(De|Se|vt|Je))!==X;if(o||s){var f=pt.transition;pt.transition=null;var m=da();Gt(Ha);var S=Ce;Ce|=aa,ud.current=null;var C=wb(e,r);Ip(),tE(e,r,i),nu(e.containerInfo),e.current=r,Yg(i),nE(r,e,i),qg(),Rg(),Ce=S,Gt(m),pt.transition=f}else e.current=r,Ip();var O=ri;if(ri?(ri=!1,Ar=e,nl=i):(qi=0,Wo=null),u=e.pendingLanes,u===A&&(Yi=null),O||lh(e.current,!1),_g(r.stateNode,a),va&&e.memoizedUpdaters.clear(),NE(),An(e,Kt()),t!==null)for(var U=e.onRecoverableError,N=0;N<t.length;N++){var I=t[N],oe=I.stack,te=I.digest;U(I.value,{componentStack:oe,digest:te})}if(Zo){Zo=!1;var Ke=dd;throw dd=null,Ke}return Yn(nl,fe)&&e.tag!==yi&&Xa(),u=e.pendingLanes,Yn(u,fe)?(XS(),e===md?al++:(al=0,md=e)):al=0,ja(),Lv(),null}function Xa(){if(Ar!==null){var e=Mv(nl),t=Sg(bu,e),n=pt.transition,a=da();try{return pt.transition=null,Gt(t),aR()}finally{Gt(a),pt.transition=n}}return!1}function nR(e){vd.push(e),ri||(ri=!0,is(Ri,function(){return Xa(),null}))}function aR(){if(Ar===null)return!1;var e=pd;pd=null;var t=Ar,n=nl;if(Ar=null,nl=A,(Ce&(ln|aa))!==Nt)throw new Error("Cannot flush passive effects while already rendering.");hd=!0,ko=!1,Qg(n);var a=Ce;Ce|=aa,sE(t.current),iE(t,t.current,n,e);{var r=vd;vd=[];for(var i=0;i<r.length;i++){var u=r[i];Pb(t,u)}}Pg(),lh(t.current,!0),Ce=a,ja(),ko?t===Wo?qi++:(qi=0,Wo=t):qi=0,hd=!1,ko=!1,Og(t);{var o=t.current.stateNode;o.effectDuration=0,o.passiveEffectDuration=0}return!0}function rh(e){return Yi!==null&&Yi.has(e)}function rR(e){Yi===null?Yi=new Set([e]):Yi.add(e)}function iR(e){Zo||(Zo=!0,dd=e)}var uR=iR;function ih(e,t,n){var a=ti(n,t),r=em(e,a,fe),i=Dr(e,r,fe),u=pn();i!==null&&(gu(i,fe,u),An(i,u))}function Ie(e,t,n){if(Fb(n),ll(!1),e.tag===se){ih(e,e,n);return}var a=null;for(a=t;a!==null;){if(a.tag===se){ih(a,e,n);return}else if(a.tag===ye){var r=a.type,i=a.stateNode;if(typeof r.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&!rh(i)){var u=ti(n,e),o=Lf(a,u,fe),s=Dr(a,o,fe),f=pn();s!==null&&(gu(s,fe,f),An(s,f));return}}a=a.return}h(`Internal React error: Attempted to capture a commit phase error inside a detached tree. This indicates a bug in React. Likely causes include deleting the same fiber more than once, committing an already-finished tree, or an inconsistent return pointer.

Error message:

%s`,n)}function lR(e,t,n){var a=e.pingCache;a!==null&&a.delete(t);var r=pn();Dv(e,n),hR(e),Tn===e&&Ei(jt,n)&&(Ft===Iu||Ft===Ko&&Rv(jt)&&Kt()-fd<Km?ii(e,A):Jo=Ee(Jo,n)),An(e,r)}function uh(e,t){t===Pt&&(t=FE(e));var n=pn(),a=En(e,t);a!==null&&(gu(a,t,n),An(a,n))}function oR(e){var t=e.memoizedState,n=Pt;t!==null&&(n=t.retryLane),uh(e,n)}function sR(e,t){var n=Pt,a;switch(e.tag){case Fe:a=e.stateNode;var r=e.memoizedState;r!==null&&(n=r.retryLane);break;case St:a=e.stateNode;break;default:throw new Error("Pinged unknown suspense boundary type. This is probably a bug in React.")}a!==null&&a.delete(t),uh(e,n)}function cR(e){return e<120?120:e<480?480:e<1080?1080:e<1920?1920:e<3e3?3e3:e<4320?4320:zE(e/1960)*1960}function fR(){if(al>LE)throw al=0,md=null,new Error("Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.");qi>jE&&(qi=0,Wo=null,h("Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render."))}function dR(){ma.flushLegacyContextWarning(),ma.flushPendingUnsafeLifecycleWarnings()}function lh(e,t){Et(e),as(e,be,gE),t&&as(e,Ve,SE),as(e,be,hE),t&&as(e,Ve,yE),bn()}function as(e,t,n){for(var a=e,r=null;a!==null;){var i=a.subtreeFlags&t;a!==r&&a.child!==null&&i!==X?a=a.child:((a.flags&t)!==X&&n(a),a.sibling!==null?a=a.sibling:a=r=a.return)}}var rs=null;function oh(e){{if((Ce&ln)!==Nt||!(e.mode&Be))return;var t=e.tag;if(t!==yn&&t!==se&&t!==ye&&t!==Ne&&t!==Me&&t!==st&&t!==Ae)return;var n=W(e)||"ReactComponent";if(rs!==null){if(rs.has(n))return;rs.add(n)}else rs=new Set([n]);var a=Qn;try{Et(e),h("Can't perform a React state update on a component that hasn't mounted yet. This indicates that you have a side-effect in your render function that asynchronously later calls tries to update the component. Move this work to useEffect instead.")}finally{a?Et(e):bn()}}}var bd;{var vR=null;bd=function(e,t,n){var a=hh(vR,t);try{return Sm(e,t,n)}catch(i){if(aS()||i!==null&&typeof i=="object"&&typeof i.then=="function")throw i;if(Wl(),_p(),Cm(e,t),hh(t,a),t.mode&ke&&Nf(t),_m(null,Sm,null,e,t,n),Lb()){var r=Om();typeof r=="object"&&r!==null&&r._suppressLogging&&typeof i=="object"&&i!==null&&!i._suppressLogging&&(i._suppressLogging=!0)}throw i}}}var sh=!1,Ed;Ed=new Set;function pR(e){if(Tu&&!PS())switch(e.tag){case Ne:case Me:case Ae:{var t=mt&&W(mt)||"Unknown",n=t;if(!Ed.has(n)){Ed.add(n);var a=W(e)||"Unknown";h("Cannot update a component (`%s`) while rendering a different component (`%s`). To locate the bad setState() call inside `%s`, follow the stack trace as described in https://reactjs.org/link/setstate-in-render",a,t,t)}break}case ye:{sh||(h("Cannot update during an existing state transition (such as within `render`). Render methods should be a pure function of props and state."),sh=!0);break}}}function ul(e,t){if(va){var n=e.memoizedUpdaters;n.forEach(function(a){_v(e,a,t)})}}var Rd={};function is(e,t){{var n=Ta.current;return n!==null?(n.push(t),Rd):Av(e,t)}}function ch(e){if(e!==Rd)return bg(e)}function fh(){return Ta.current!==null}function mR(e){{if(e.mode&Be){if(!Pm())return}else if(!AE()||Ce!==Nt||e.tag!==Ne&&e.tag!==Me&&e.tag!==Ae)return;if(Ta.current===null){var t=Qn;try{Et(e),h(`An update to %s inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act`,W(e))}finally{t?Et(e):bn()}}}}function hR(e){e.tag!==yi&&Pm()&&Ta.current===null&&h(`A suspended resource finished loading inside a test, but the event was not wrapped in act(...).

When testing, code that resolves suspended data should be wrapped into act(...):

act(() => {
  /* finish loading suspended data */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act`)}function ll(e){Zm=e}var ra=null,Qi=null,yR=function(e){ra=e};function Pi(e){{if(ra===null)return e;var t=ra(e);return t===void 0?e:t.current}}function Td(e){return Pi(e)}function Cd(e){{if(ra===null)return e;var t=ra(e);if(t===void 0){if(e!=null&&typeof e.render=="function"){var n=Pi(e.render);if(e.render!==n){var a={$$typeof:bt,render:n};return e.displayName!==void 0&&(a.displayName=e.displayName),a}}return e}return t.current}}function dh(e,t){{if(ra===null)return!1;var n=e.elementType,a=t.type,r=!1,i=typeof a=="object"&&a!==null?a.$$typeof:null;switch(e.tag){case ye:{typeof a=="function"&&(r=!0);break}case Ne:{(typeof a=="function"||i===it)&&(r=!0);break}case Me:{(i===bt||i===it)&&(r=!0);break}case st:case Ae:{(i===kn||i===it)&&(r=!0);break}default:return!1}if(r){var u=ra(n);if(u!==void 0&&u===ra(a))return!0}return!1}}function vh(e){{if(ra===null||typeof WeakSet!="function")return;Qi===null&&(Qi=new WeakSet),Qi.add(e)}}var gR=function(e,t){{if(ra===null)return;var n=t.staleFamilies,a=t.updatedFamilies;Xa(),es(function(){xd(e.current,a,n)})}},SR=function(e,t){{if(e.context!==Vn)return;Xa(),es(function(){Sh(t,e,null,null)})}};function xd(e,t,n){{var a=e.alternate,r=e.child,i=e.sibling,u=e.tag,o=e.type,s=null;switch(u){case Ne:case Ae:case ye:s=o;break;case Me:s=o.render;break}if(ra===null)throw new Error("Expected resolveFamily to be set during hot reload.");var f=!1,m=!1;if(s!==null){var S=ra(s);S!==void 0&&(n.has(S)?m=!0:t.has(S)&&(u===ye?m=!0:f=!0))}if(Qi!==null&&(Qi.has(e)||a!==null&&Qi.has(a))&&(m=!0),m&&(e._debugNeedsRemount=!0),m||f){var C=En(e,fe);C!==null&&Ut(C,e,fe,ot)}r!==null&&!m&&xd(r,t,n),i!==null&&xd(i,t,n)}}var bR=function(e,t){{var n=new Set,a=new Set(t.map(function(r){return r.current}));return Dd(e.current,a,n),n}};function Dd(e,t,n){{var a=e.child,r=e.sibling,i=e.tag,u=e.type,o=null;switch(i){case Ne:case Ae:case ye:o=u;break;case Me:o=u.render;break}var s=!1;o!==null&&t.has(o)&&(s=!0),s?ER(e,n):a!==null&&Dd(a,t,n),r!==null&&Dd(r,t,n)}}function ER(e,t){{var n=RR(e,t);if(n)return;for(var a=e;;){switch(a.tag){case $:t.add(a.stateNode);return;case Ue:t.add(a.stateNode.containerInfo);return;case se:t.add(a.stateNode.containerInfo);return}if(a.return===null)throw new Error("Expected to reach root first.");a=a.return}}}function RR(e,t){for(var n=e,a=!1;;){if(n.tag===$)a=!0,t.add(n.stateNode);else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)return a;for(;n.sibling===null;){if(n.return===null||n.return===e)return a;n=n.return}n.sibling.return=n.return,n=n.sibling}return!1}var _d;{_d=!1;try{var ph=Object.preventExtensions({})}catch(e){_d=!0}}function TR(e,t,n,a){this.tag=e,this.key=n,this.elementType=null,this.type=null,this.stateNode=null,this.return=null,this.child=null,this.sibling=null,this.index=0,this.ref=null,this.pendingProps=t,this.memoizedProps=null,this.updateQueue=null,this.memoizedState=null,this.dependencies=null,this.mode=a,this.flags=X,this.subtreeFlags=X,this.deletions=null,this.lanes=A,this.childLanes=A,this.alternate=null,this.actualDuration=Number.NaN,this.actualStartTime=Number.NaN,this.selfBaseDuration=Number.NaN,this.treeBaseDuration=Number.NaN,this.actualDuration=0,this.actualStartTime=-1,this.selfBaseDuration=0,this.treeBaseDuration=0,this._debugSource=null,this._debugOwner=null,this._debugNeedsRemount=!1,this._debugHookTypes=null,!_d&&typeof Object.preventExtensions=="function"&&Object.preventExtensions(this)}var Pn=function(e,t,n,a){return new TR(e,t,n,a)};function Od(e){var t=e.prototype;return!!(t&&t.isReactComponent)}function CR(e){return typeof e=="function"&&!Od(e)&&e.defaultProps===void 0}function xR(e){if(typeof e=="function")return Od(e)?ye:Ne;if(e!=null){var t=e.$$typeof;if(t===bt)return Me;if(t===kn)return st}return yn}function li(e,t){var n=e.alternate;n===null?(n=Pn(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n._debugSource=e._debugSource,n._debugOwner=e._debugOwner,n._debugHookTypes=e._debugHookTypes,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=X,n.subtreeFlags=X,n.deletions=null,n.actualDuration=0,n.actualStartTime=-1),n.flags=e.flags&Pe,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue;var a=e.dependencies;switch(n.dependencies=a===null?null:{lanes:a.lanes,firstContext:a.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.selfBaseDuration=e.selfBaseDuration,n.treeBaseDuration=e.treeBaseDuration,n._debugNeedsRemount=e._debugNeedsRemount,n.tag){case yn:case Ne:case Ae:n.type=Pi(e.type);break;case ye:n.type=Td(e.type);break;case Me:n.type=Cd(e.type);break}return n}function DR(e,t){e.flags&=Pe|Qe;var n=e.alternate;if(n===null)e.childLanes=A,e.lanes=t,e.child=null,e.subtreeFlags=X,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null,e.selfBaseDuration=0,e.treeBaseDuration=0;else{e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=X,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type;var a=n.dependencies;e.dependencies=a===null?null:{lanes:a.lanes,firstContext:a.firstContext},e.selfBaseDuration=n.selfBaseDuration,e.treeBaseDuration=n.treeBaseDuration}return e}function _R(e,t,n){var a;return e===gv?(a=Be,t===!0&&(a|=_t,a|=Aa)):a=le,va&&(a|=ke),Pn(se,null,null,a)}function Nd(e,t,n,a,r,i){var u=yn,o=e;if(typeof e=="function")Od(e)?(u=ye,o=Td(o)):o=Pi(o);else if(typeof e=="string")u=$;else e:switch(e){case Zn:return Lr(n.children,r,i,t);case Hn:u=dt,r|=_t,(r&Be)!==le&&(r|=Aa);break;case Bt:return OR(n,r,i,t);case Vt:return NR(n,r,i,t);case ua:return UR(n,r,i,t);case Da:return mh(n,r,i,t);case _a:case jn:case Fn:case vr:case Ia:default:{if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Sn:u=Rt;break e;case Ln:u=ue;break e;case bt:u=Me,o=Cd(o);break e;case kn:u=st;break e;case it:u=Ct,o=null;break e}var s="";{(e===void 0||typeof e=="object"&&e!==null&&Object.keys(e).length===0)&&(s+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var f=a?W(a):null;f&&(s+=`

Check the render method of \``+f+"`.")}throw new Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) "+("but got: "+(e==null?e:typeof e)+"."+s))}}var m=Pn(u,n,t,r);return m.elementType=e,m.type=o,m.lanes=i,m._debugOwner=a,m}function Ud(e,t,n){var a=null;a=e._owner;var r=e.type,i=e.key,u=e.props,o=Nd(r,i,u,a,t,n);return o._debugSource=e._source,o._debugOwner=e._owner,o}function Lr(e,t,n,a){var r=Pn(Te,e,a,t);return r.lanes=n,r}function OR(e,t,n,a){typeof e.id!="string"&&h('Profiler must specify an "id" of type `string` as a prop. Received the type `%s` instead.',typeof e.id);var r=Pn(Tt,e,a,t|ke);return r.elementType=Bt,r.lanes=n,r.stateNode={effectDuration:0,passiveEffectDuration:0},r}function NR(e,t,n,a){var r=Pn(Fe,e,a,t);return r.elementType=Vt,r.lanes=n,r}function UR(e,t,n,a){var r=Pn(St,e,a,t);return r.elementType=ua,r.lanes=n,r}function mh(e,t,n,a){var r=Pn(rt,e,a,t);r.elementType=Da,r.lanes=n;var i={isHidden:!1};return r.stateNode=i,r}function Md(e,t,n){var a=Pn(ge,e,null,t);return a.lanes=n,a}function MR(){var e=Pn($,null,null,le);return e.elementType="DELETED",e}function AR(e){var t=Pn(Xn,null,null,le);return t.stateNode=e,t}function Ad(e,t,n){var a=e.children!==null?e.children:[],r=Pn(Ue,a,e.key,t);return r.lanes=n,r.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},r}function hh(e,t){return e===null&&(e=Pn(yn,null,null,le)),e.tag=t.tag,e.key=t.key,e.elementType=t.elementType,e.type=t.type,e.stateNode=t.stateNode,e.return=t.return,e.child=t.child,e.sibling=t.sibling,e.index=t.index,e.ref=t.ref,e.pendingProps=t.pendingProps,e.memoizedProps=t.memoizedProps,e.updateQueue=t.updateQueue,e.memoizedState=t.memoizedState,e.dependencies=t.dependencies,e.mode=t.mode,e.flags=t.flags,e.subtreeFlags=t.subtreeFlags,e.deletions=t.deletions,e.lanes=t.lanes,e.childLanes=t.childLanes,e.alternate=t.alternate,e.actualDuration=t.actualDuration,e.actualStartTime=t.actualStartTime,e.selfBaseDuration=t.selfBaseDuration,e.treeBaseDuration=t.treeBaseDuration,e._debugSource=t._debugSource,e._debugOwner=t._debugOwner,e._debugNeedsRemount=t._debugNeedsRemount,e._debugHookTypes=t._debugHookTypes,e}function zR(e,t,n,a,r){this.tag=t,this.containerInfo=e,this.pendingChildren=null,this.current=null,this.pingCache=null,this.finishedWork=null,this.timeoutHandle=Sr,this.context=null,this.pendingContext=null,this.callbackNode=null,this.callbackPriority=Pt,this.eventTimes=mc(A),this.expirationTimes=mc(ot),this.pendingLanes=A,this.suspendedLanes=A,this.pingedLanes=A,this.expiredLanes=A,this.mutableReadLanes=A,this.finishedLanes=A,this.entangledLanes=A,this.entanglements=mc(A),this.identifierPrefix=a,this.onRecoverableError=r,Mt&&(this.mutableSourceEagerHydrationData=null),this.effectDuration=0,this.passiveEffectDuration=0;{this.memoizedUpdaters=new Set;for(var i=this.pendingUpdatersLaneMap=[],u=0;u<Ks;u++)i.push(new Set)}switch(t){case gv:this._debugRootType=n?"hydrateRoot()":"createRoot()";break;case yi:this._debugRootType=n?"hydrate()":"render()";break}}function yh(e,t,n,a,r,i,u,o,s,f){var m=new zR(e,t,n,o,s),S=_R(t,i);m.current=S,S.stateNode=m;{var C={element:a,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null};S.memoizedState=C}return Lc(S),m}var HR="18.2.0";function LR(e,t,n){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null;return SS(a),{$$typeof:_n,key:a==null?null:""+a,children:e,containerInfo:t,implementation:n}}var zd,Hd;zd=!1,Hd={};function gh(e){if(!e)return Vn;var t=pe(e),n=$y(t);if(t.tag===ye){var a=t.type;if(Ma(a))return hv(t,a,n)}return n}function jR(e){var t=pe(e);if(t===void 0){if(typeof e.render=="function")throw new Error("Unable to find node on an unmounted component.");var n=Object.keys(e).join(",");throw new Error("Argument appears to not be a ReactComponent. Keys: "+n)}var a=yr(t);return a===null?null:a.stateNode}function FR(e,t){{var n=pe(e);if(n===void 0){if(typeof e.render=="function")throw new Error("Unable to find node on an unmounted component.");var a=Object.keys(e).join(",");throw new Error("Argument appears to not be a ReactComponent. Keys: "+a)}var r=yr(n);if(r===null)return null;if(r.mode&_t){var i=W(n)||"Component";if(!Hd[i]){Hd[i]=!0;var u=Qn;try{Et(r),n.mode&_t?h("%s is deprecated in StrictMode. %s was passed an instance of %s which is inside StrictMode. Instead, add a ref directly to the element you want to reference. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-find-node",t,t,i):h("%s is deprecated in StrictMode. %s was passed an instance of %s which renders StrictMode children. Instead, add a ref directly to the element you want to reference. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-find-node",t,t,i)}finally{u?Et(u):bn()}}}return r.stateNode}}function BR(e,t,n,a,r,i,u,o){var s=!1,f=null;return yh(e,t,s,f,n,a,r,i,u)}function VR(e,t,n,a,r,i,u,o,s,f){var m=!0,S=yh(n,a,m,e,r,i,u,o,s);S.context=gh(null);var C=S.current,O=pn(),U=zr(C),N=lr(O,U);return N.callback=t!=null?t:null,Dr(C,N,U),BE(S,U,O),S}function Sh(e,t,n,a){Dg(t,e);var r=t.current,i=pn(),u=zr(r);Kg(u);var o=gh(n);t.context===null?t.context=o:t.pendingContext=o,Tu&&Qn!==null&&!zd&&(zd=!0,h(`Render methods should be a pure function of props and state; triggering nested component updates from render is not allowed. If necessary, trigger nested updates in componentDidUpdate.

Check the render method of %s.`,W(Qn)||"Unknown"));var s=lr(i,u);s.payload={element:e},a=a===void 0?null:a,a!==null&&(typeof a!="function"&&h("render(...): Expected the last optional `callback` argument to be a function. Instead received: %s.",a),s.callback=a);var f=Dr(r,s,u);return f!==null&&(Ut(f,r,u,i),no(f,r,u)),u}function wR(e){var t=e.current;if(!t.child)return null;switch(t.child.tag){case $:return si(t.child.stateNode);default:return t.child.stateNode}}function YR(e){switch(e.tag){case se:{var t=e.stateNode;if(qv(t)){var n=lg(t);qE(t,n)}break}case Fe:{es(function(){var r=En(e,fe);if(r!==null){var i=pn();Ut(r,e,fe,i)}});var a=fe;us(e,a);break}}}function bh(e,t){var n=e.memoizedState;n!==null&&n.dehydrated!==null&&(n.retryLane=vg(n.retryLane,t))}function us(e,t){bh(e,t);var n=e.alternate;n&&bh(n,t)}function qR(e){if(e.tag===Fe){var t=fe,n=En(e,t);if(n!==null){var a=pn();Ut(n,e,t,a)}us(e,t)}}function QR(e){if(e.tag===Fe){var t=pu,n=En(e,t);if(n!==null){var a=pn();Ut(n,e,t,a)}us(e,t)}}function PR(e){if(e.tag===Fe){var t=zr(e),n=En(e,t);if(n!==null){var a=pn();Ut(n,e,t,a)}us(e,t)}}function GR(e){var t=vl(e);return t===null?null:t.stateNode}var Eh=function(e){return null};function Rh(e){return Eh(e)}var Th=function(e){return!1};function Ch(e){return Th(e)}var xh=null,Dh=null,_h=null,Oh=null,Nh=null,Uh=null,Mh=null,Ah=null,zh=null;{var Hh=function(e,t,n){var a=t[n],r=fn(e)?e.slice():Q({},e);return n+1===t.length?(fn(r)?r.splice(a,1):delete r[a],r):(r[a]=Hh(e[a],t,n+1),r)},Lh=function(e,t){return Hh(e,t,0)},jh=function(e,t,n,a){var r=t[a],i=fn(e)?e.slice():Q({},e);if(a+1===t.length){var u=n[a];i[u]=i[r],fn(i)?i.splice(r,1):delete i[r]}else i[r]=jh(e[r],t,n,a+1);return i},Fh=function(e,t,n){if(t.length!==n.length){H("copyWithRename() expects paths of the same length");return}else for(var a=0;a<n.length-1;a++)if(t[a]!==n[a]){H("copyWithRename() expects paths to be the same except for the deepest key");return}return jh(e,t,n,0)},Bh=function(e,t,n,a){if(n>=t.length)return a;var r=t[n],i=fn(e)?e.slice():Q({},e);return i[r]=Bh(e[r],t,n+1,a),i},Vh=function(e,t,n){return Bh(e,t,0,n)},Ld=function(e,t){for(var n=e.memoizedState;n!==null&&t>0;)n=n.next,t--;return n};xh=function(e,t,n,a){var r=Ld(e,t);if(r!==null){var i=Vh(r.memoizedState,n,a);r.memoizedState=i,r.baseState=i,e.memoizedProps=Q({},e.memoizedProps);var u=En(e,fe);u!==null&&Ut(u,e,fe,ot)}},Dh=function(e,t,n){var a=Ld(e,t);if(a!==null){var r=Lh(a.memoizedState,n);a.memoizedState=r,a.baseState=r,e.memoizedProps=Q({},e.memoizedProps);var i=En(e,fe);i!==null&&Ut(i,e,fe,ot)}},_h=function(e,t,n,a){var r=Ld(e,t);if(r!==null){var i=Fh(r.memoizedState,n,a);r.memoizedState=i,r.baseState=i,e.memoizedProps=Q({},e.memoizedProps);var u=En(e,fe);u!==null&&Ut(u,e,fe,ot)}},Oh=function(e,t,n){e.pendingProps=Vh(e.memoizedProps,t,n),e.alternate&&(e.alternate.pendingProps=e.pendingProps);var a=En(e,fe);a!==null&&Ut(a,e,fe,ot)},Nh=function(e,t){e.pendingProps=Lh(e.memoizedProps,t),e.alternate&&(e.alternate.pendingProps=e.pendingProps);var n=En(e,fe);n!==null&&Ut(n,e,fe,ot)},Uh=function(e,t,n){e.pendingProps=Fh(e.memoizedProps,t,n),e.alternate&&(e.alternate.pendingProps=e.pendingProps);var a=En(e,fe);a!==null&&Ut(a,e,fe,ot)},Mh=function(e){var t=En(e,fe);t!==null&&Ut(t,e,fe,ot)},Ah=function(e){Eh=e},zh=function(e){Th=e}}function KR(e){var t=yr(e);return t===null?null:t.stateNode}function XR(e){return null}function JR(){return Qn}function ZR(e){var t=e.findFiberByHostInstance,n=M.ReactCurrentDispatcher;return xg({bundleType:e.bundleType,version:e.version,rendererPackageName:e.rendererPackageName,rendererConfig:e.rendererConfig,overrideHookState:xh,overrideHookStateDeletePath:Dh,overrideHookStateRenamePath:_h,overrideProps:Oh,overridePropsDeletePath:Nh,overridePropsRenamePath:Uh,setErrorHandler:Ah,setSuspenseHandler:zh,scheduleUpdate:Mh,currentDispatcherRef:n,findHostInstanceByFiber:KR,findFiberByHostInstance:t||XR,findHostInstancesForRefresh:bR,scheduleRefresh:gR,scheduleRoot:SR,setRefreshHandler:yR,getCurrentFiber:JR,reconcilerVersion:HR})}return y.attemptContinuousHydration=QR,y.attemptDiscreteHydration=qR,y.attemptHydrationAtCurrentPriority=PR,y.attemptSynchronousHydration=YR,y.batchedUpdates=PE,y.createComponentSelector=bE,y.createContainer=BR,y.createHasPseudoClassSelector=EE,y.createHydrationContainer=VR,y.createPortal=LR,y.createRoleSelector=RE,y.createTestNameSelector=CE,y.createTextSelector=TE,y.deferredUpdates=QE,y.discreteUpdates=GE,y.findAllNodes=Po,y.findBoundingRects=_E,y.findHostInstance=jR,y.findHostInstanceWithNoPortals=GR,y.findHostInstanceWithWarning=FR,y.flushControlled=XE,y.flushPassiveEffects=Xa,y.flushSync=es,y.focusWithin=OE,y.getCurrentUpdatePriority=da,y.getFindAllNodesFailureDescription=DE,y.getPublicRootInstance=wR,y.injectIntoDevTools=ZR,y.isAlreadyRendering=KE,y.observeVisibleRects=UE,y.registerMutableSourceForHydration=BS,y.runWithPriority=yg,y.shouldError=Rh,y.shouldSuspend=Ch,y.updateContainer=Sh,y}});var ly=jr((fC,uy)=>{"use strict";uy.exports=iy()});var vy=jr(hs=>{"use strict";(function(){"use strict";var v=ka(),c=Symbol.for("react.element"),y=Symbol.for("react.portal"),T=Symbol.for("react.fragment"),g=Symbol.for("react.strict_mode"),M=Symbol.for("react.profiler"),q=Symbol.for("react.provider"),B=Symbol.for("react.context"),H=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),j=Symbol.for("react.suspense_list"),Q=Symbol.for("react.memo"),pe=Symbol.for("react.lazy"),et=Symbol.for("react.offscreen"),Ye=Symbol.iterator,qe="@@iterator";function Xe(d){if(d===null||typeof d!="object")return null;var z=Ye&&d[Ye]||d[qe];return typeof z=="function"?z:null}var Re=v.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function xe(d){{for(var z=arguments.length,V=new Array(z>1?z-1:0),re=1;re<z;re++)V[re-1]=arguments[re];ft("error",d,V)}}function ft(d,z,V){{var re=Re.ReactDebugCurrentFrame,be=re.getStackAddendum();be!==""&&(z+="%s",V=V.concat([be]));var Ve=V.map(function(De){return String(De)});Ve.unshift("Warning: "+z),Function.prototype.apply.call(console[d],console,Ve)}}var gt=!1,zn=!1,Ne=!1,ye=!1,yn=!1,se;se=Symbol.for("react.module.reference");function Ue(d){return!!(typeof d=="string"||typeof d=="function"||d===T||d===M||yn||d===g||d===h||d===j||ye||d===et||gt||zn||Ne||typeof d=="object"&&d!==null&&(d.$$typeof===pe||d.$$typeof===Q||d.$$typeof===q||d.$$typeof===B||d.$$typeof===H||d.$$typeof===se||d.getModuleId!==void 0))}function $(d,z,V){var re=d.displayName;if(re)return re;var be=z.displayName||z.name||"";return be!==""?V+"("+be+")":V}function ge(d){return d.displayName||"Context"}function Te(d){if(d==null)return null;if(typeof d.tag=="number"&&xe("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof d=="function")return d.displayName||d.name||null;if(typeof d=="string")return d;switch(d){case T:return"Fragment";case y:return"Portal";case M:return"Profiler";case g:return"StrictMode";case h:return"Suspense";case j:return"SuspenseList"}if(typeof d=="object")switch(d.$$typeof){case B:var z=d;return ge(z)+".Consumer";case q:var V=d;return ge(V._context)+".Provider";case H:return $(d,d.render,"ForwardRef");case Q:var re=d.displayName||null;return re!==null?re:Te(d.type)||"Memo";case pe:{var be=d,Ve=be._payload,De=be._init;try{return Te(De(Ve))}catch(Se){return null}}}return null}var dt=Object.assign,ue=0,Rt,Me,Tt,Fe,st,Ae,Ct;function Zt(){}Zt.__reactDisabledLog=!0;function Xn(){{if(ue===0){Rt=console.log,Me=console.info,Tt=console.warn,Fe=console.error,st=console.group,Ae=console.groupCollapsed,Ct=console.groupEnd;var d={configurable:!0,enumerable:!0,value:Zt,writable:!0};Object.defineProperties(console,{info:d,log:d,warn:d,error:d,group:d,groupCollapsed:d,groupEnd:d})}ue++}}function St(){{if(ue--,ue===0){var d={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:dt({},d,{value:Rt}),info:dt({},d,{value:Me}),warn:dt({},d,{value:Tt}),error:dt({},d,{value:Fe}),group:dt({},d,{value:st}),groupCollapsed:dt({},d,{value:Ae}),groupEnd:dt({},d,{value:Ct})})}ue<0&&xe("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var gn=Re.ReactCurrentDispatcher,rt;function kt(d,z,V){{if(rt===void 0)try{throw Error()}catch(be){var re=be.stack.trim().match(/\n( *(at )?)/);rt=re&&re[1]||""}return`
`+rt+d}}var ia=!1,Jn;{var Dn=typeof WeakMap=="function"?WeakMap:Map;Jn=new Dn}function _n(d,z){if(!d||ia)return"";{var V=Jn.get(d);if(V!==void 0)return V}var re;ia=!0;var be=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var Ve;Ve=gn.current,gn.current=null,Xn();try{if(z){var De=function(){throw Error()};if(Object.defineProperty(De.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(De,[])}catch(qt){re=qt}Reflect.construct(d,[],De)}else{try{De.call()}catch(qt){re=qt}d.call(De.prototype)}}else{try{throw Error()}catch(qt){re=qt}d()}}catch(qt){if(qt&&re&&typeof qt.stack=="string"){for(var Se=qt.stack.split(`
`),vt=re.stack.split(`
`),Je=Se.length-1,Pe=vt.length-1;Je>=1&&Pe>=0&&Se[Je]!==vt[Pe];)Pe--;for(;Je>=1&&Pe>=0;Je--,Pe--)if(Se[Je]!==vt[Pe]){if(Je!==1||Pe!==1)do if(Je--,Pe--,Pe<0||Se[Je]!==vt[Pe]){var cn=`
`+Se[Je].replace(" at new "," at ");return d.displayName&&cn.includes("<anonymous>")&&(cn=cn.replace("<anonymous>",d.displayName)),typeof d=="function"&&Jn.set(d,cn),cn}while(Je>=1&&Pe>=0);break}}}finally{ia=!1,gn.current=Ve,St(),Error.prepareStackTrace=be}var Bn=d?d.displayName||d.name:"",ca=Bn?kt(Bn):"";return typeof d=="function"&&Jn.set(d,ca),ca}function Zn(d,z,V){return _n(d,!1)}function Hn(d){var z=d.prototype;return!!(z&&z.isReactComponent)}function Bt(d,z,V){if(d==null)return"";if(typeof d=="function")return _n(d,Hn(d));if(typeof d=="string")return kt(d);switch(d){case h:return kt("Suspense");case j:return kt("SuspenseList")}if(typeof d=="object")switch(d.$$typeof){case H:return Zn(d.render);case Q:return Bt(d.type,z,V);case pe:{var re=d,be=re._payload,Ve=re._init;try{return Bt(Ve(be),z,V)}catch(De){}}}return""}var Sn=Object.prototype.hasOwnProperty,Ln={},bt=Re.ReactDebugCurrentFrame;function Vt(d){if(d){var z=d._owner,V=Bt(d.type,d._source,z?z.type:null);bt.setExtraStackFrame(V)}else bt.setExtraStackFrame(null)}function ua(d,z,V,re,be){{var Ve=Function.call.bind(Sn);for(var De in d)if(Ve(d,De)){var Se=void 0;try{if(typeof d[De]!="function"){var vt=Error((re||"React class")+": "+V+" type `"+De+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof d[De]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw vt.name="Invariant Violation",vt}Se=d[De](z,De,re,V,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(Je){Se=Je}Se&&!(Se instanceof Error)&&(Vt(be),xe("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",re||"React class",V,De,typeof Se),Vt(null)),Se instanceof Error&&!(Se.message in Ln)&&(Ln[Se.message]=!0,Vt(be),xe("Failed %s type: %s",V,Se.message),Vt(null))}}}var kn=Array.isArray;function it(d){return kn(d)}function jn(d){{var z=typeof Symbol=="function"&&Symbol.toStringTag,V=z&&d[Symbol.toStringTag]||d.constructor.name||"Object";return V}}function Ia(d){try{return Da(d),!1}catch(z){return!0}}function Da(d){return""+d}function _a(d){if(Ia(d))return xe("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",jn(d)),Da(d)}var Fn=Re.ReactCurrentOwner,vr={key:!0,ref:!0,__self:!0,__source:!0},la,pr,w;w={};function ne(d){if(Sn.call(d,"ref")){var z=Object.getOwnPropertyDescriptor(d,"ref").get;if(z&&z.isReactWarning)return!1}return d.ref!==void 0}function me(d){if(Sn.call(d,"key")){var z=Object.getOwnPropertyDescriptor(d,"key").get;if(z&&z.isReactWarning)return!1}return d.key!==void 0}function ee(d,z){if(typeof d.ref=="string"&&Fn.current&&z&&Fn.current.stateNode!==z){var V=Te(Fn.current.type);w[V]||(xe('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',Te(Fn.current.type),d.ref),w[V]=!0)}}function ut(d,z){{var V=function(){la||(la=!0,xe("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",z))};V.isReactWarning=!0,Object.defineProperty(d,"key",{get:V,configurable:!0})}}function wt(d,z){{var V=function(){pr||(pr=!0,xe("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",z))};V.isReactWarning=!0,Object.defineProperty(d,"ref",{get:V,configurable:!0})}}var W=function(d,z,V,re,be,Ve,De){var Se={$$typeof:c,type:d,key:z,ref:V,props:De,_owner:Ve};return Se._store={},Object.defineProperty(Se._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(Se,"_self",{configurable:!1,enumerable:!1,writable:!1,value:re}),Object.defineProperty(Se,"_source",{configurable:!1,enumerable:!1,writable:!1,value:be}),Object.freeze&&(Object.freeze(Se.props),Object.freeze(Se)),Se};function X(d,z,V,re,be){{var Ve,De={},Se=null,vt=null;V!==void 0&&(_a(V),Se=""+V),me(z)&&(_a(z.key),Se=""+z.key),ne(z)&&(vt=z.ref,ee(z,be));for(Ve in z)Sn.call(z,Ve)&&!vr.hasOwnProperty(Ve)&&(De[Ve]=z[Ve]);if(d&&d.defaultProps){var Je=d.defaultProps;for(Ve in Je)De[Ve]===void 0&&(De[Ve]=Je[Ve])}if(Se||vt){var Pe=typeof d=="function"?d.displayName||d.name||"Unknown":d;Se&&ut(De,Pe),vt&&wt(De,Pe)}return W(d,Se,vt,be,re,Fn.current,De)}}var $e=Re.ReactCurrentOwner,Qe=Re.ReactDebugCurrentFrame;function ce(d){if(d){var z=d._owner,V=Bt(d.type,d._source,z?z.type:null);Qe.setExtraStackFrame(V)}else Qe.setExtraStackFrame(null)}var Wt;Wt=!1;function oa(d){return typeof d=="object"&&d!==null&&d.$$typeof===c}function mr(){{if($e.current){var d=Te($e.current.type);if(d)return`

Check the render method of \``+d+"`."}return""}}function ze(d){{if(d!==void 0){var z=d.fileName.replace(/^.*[\\\/]/,""),V=d.lineNumber;return`

Check your code at `+z+":"+V+"."}return""}}var On={};function Wn(d){{var z=mr();if(!z){var V=typeof d=="string"?d:d.displayName||d.name;V&&(z=`

Check the top-level render call using <`+V+">.")}return z}}function sn(d,z){{if(!d._store||d._store.validated||d.key!=null)return;d._store.validated=!0;var V=Wn(z);if(On[V])return;On[V]=!0;var re="";d&&d._owner&&d._owner!==$e.current&&(re=" It was passed a child from "+Te(d._owner.type)+"."),ce(d),xe('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',V,re),ce(null)}}function Yt(d,z){{if(typeof d!="object")return;if(it(d))for(var V=0;V<d.length;V++){var re=d[V];oa(re)&&sn(re,z)}else if(oa(d))d._store&&(d._store.validated=!0);else if(d){var be=Xe(d);if(typeof be=="function"&&be!==d.entries)for(var Ve=be.call(d),De;!(De=Ve.next()).done;)oa(De.value)&&sn(De.value,z)}}}function Nn(d){{var z=d.type;if(z==null||typeof z=="string")return;var V;if(typeof z=="function")V=z.propTypes;else if(typeof z=="object"&&(z.$$typeof===H||z.$$typeof===Q))V=z.propTypes;else return;if(V){var re=Te(z);ua(V,d.props,"prop",re,d)}else if(z.PropTypes!==void 0&&!Wt){Wt=!0;var be=Te(z);xe("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",be||"Unknown")}typeof z.getDefaultProps=="function"&&!z.getDefaultProps.isReactClassApproved&&xe("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function sa(d){{for(var z=Object.keys(d.props),V=0;V<z.length;V++){var re=z[V];if(re!=="children"&&re!=="key"){ce(d),xe("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",re),ce(null);break}}d.ref!==null&&(ce(d),xe("Invalid attribute `ref` supplied to `React.Fragment`."),ce(null))}}var $a={};function oi(d,z,V,re,be,Ve){{var De=Ue(d);if(!De){var Se="";(d===void 0||typeof d=="object"&&d!==null&&Object.keys(d).length===0)&&(Se+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var vt=ze(be);vt?Se+=vt:Se+=mr();var Je;d===null?Je="null":it(d)?Je="array":d!==void 0&&d.$$typeof===c?(Je="<"+(Te(d.type)||"Unknown")+" />",Se=" Did you accidentally export a JSX literal instead of a component?"):Je=typeof d,xe("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",Je,Se)}var Pe=X(d,z,V,be,Ve);if(Pe==null)return Pe;if(De){var cn=z.children;if(cn!==void 0)if(re)if(it(cn)){for(var Bn=0;Bn<cn.length;Bn++)Yt(cn[Bn],d);Object.freeze&&Object.freeze(cn)}else xe("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else Yt(cn,d)}if(Sn.call(z,"key")){var ca=Te(d),qt=Object.keys(z).filter(function(yr){return yr!=="key"}),hr=qt.length>0?"{key: someKey, "+qt.join(": ..., ")+": ...}":"{key: someKey}";if(!$a[ca+hr]){var Na=qt.length>0?"{"+qt.join(": ..., ")+": ...}":"{}";xe(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,hr,ca,Na,ca),$a[ca+hr]=!0}}return d===T?sa(Pe):Nn(Pe),Pe}}function $i(d,z,V){return oi(d,z,V,!0)}function Oa(d,z,V){return oi(d,z,V,!1)}var xt=Oa,Vr=$i;hs.Fragment=T,hs.jsx=xt,hs.jsxs=Vr})()});var ki=jr((hC,py)=>{"use strict";py.exports=vy()});var Ii=Kn(ka());function Xi(v){if(v==null||typeof v!="object")return!1;let c=Object.getPrototypeOf(v);return c==null||c===Object.prototype}function Ca(v){return v!=null&&v.kind===3}var sl="__current",Bd={},lT=[];function qd(v,{strict:c=!0,components:y}={}){let T=0,g={strict:c,mounted:!1,channel:v,children:lT,nodes:new WeakSet,parents:new WeakMap,tops:new WeakMap,components:new WeakMap,fragments:new WeakMap};c&&Object.freeze(y);let M={kind:0,options:c?Object.freeze({strict:c,components:y}):{strict:c,components:y},get children(){return g.children},createComponent(q,...B){if(y&&y.indexOf(q)<0)throw new Error(`Unsupported component: ${q}`);let[H,h,...j]=B,Q=H!=null?H:{},pe=[],et={};if(H)for(let Re of Object.keys(H))Re!=="children"&&(et[Re]=Br(Wh(H[Re])));if(h)if(Array.isArray(h))for(let Re of h)pe.push(Jt(Re,M));else{pe.push(Jt(h,M));for(let Re of j)pe.push(Jt(Re,M))}let Ye=`${T++}`,qe={externalProps:c?Object.freeze(Q):Q,internalProps:et,children:c?Object.freeze(pe):pe},Xe=Za({kind:1,get children(){return qe.children},get props(){return qe.externalProps},get remoteProps(){return qe.internalProps},remove:()=>Kh(Xe),updateProps:Re=>cT(Xe,Re,qe,g),append:(...Re)=>cs(Xe,Re.map(xe=>Jt(xe,M)),qe,g),appendChild:Re=>fs(Xe,Jt(Re,M),qe,g),removeChild:Re=>ds(Xe,Re,qe,g),replaceChildren:(...Re)=>Vd(Xe,Re.map(xe=>Jt(xe,M)),qe,g),insertBefore:(Re,xe)=>Ji(Xe,Jt(Re,M),xe,qe,g),insertChildBefore:(Re,xe)=>Ji(Xe,Jt(Re,M),xe,qe,g)},Bd);g.components.set(Xe,qe),Object.defineProperty(Xe,"type",{value:q,configurable:!1,writable:!1,enumerable:!0}),wd(Xe,g),Yd(Xe,Ye,M);for(let Re of qe.children)fl(Xe,Re,g);return Xe},createText(q=""){let B=`${T++}`,H={text:q},h=Q=>sT(j,Q,H,g),j=Za({kind:2,get text(){return H.text},update:h,updateText:h,remove:()=>Kh(j)},Bd);return wd(j,g),Yd(j,B,M),j},createFragment(){let q=`${T++}`,B={children:c?Object.freeze([]):[]},H=Za({kind:3,get children(){return B.children},append:(...h)=>cs(H,h.map(j=>Jt(j,M)),B,g),appendChild:h=>fs(H,Jt(h,M),B,g),removeChild:h=>ds(H,h,B,g),replaceChildren:(...h)=>Vd(H,h.map(j=>Jt(j,M)),B,g),insertBefore:(h,j)=>Ji(H,Jt(h,M),j,B,g),insertChildBefore:(h,j)=>Ji(H,Jt(h,M),j,B,g)},Bd);return g.fragments.set(H,B),wd(H,g),Yd(H,q,M),H},append:(...q)=>cs(M,q.map(B=>Jt(B,M)),g,g),appendChild:q=>fs(M,Jt(q,M),g,g),replaceChildren:(...q)=>Vd(M,q.map(B=>Jt(B,M)),g,g),removeChild:q=>ds(M,q,g,g),insertBefore:(q,B)=>Ji(M,Jt(q,M),B,g,g),insertChildBefore:(q,B)=>Ji(M,Jt(q,M),B,g,g),mount(){return g.mounted?Promise.resolve():(g.mounted=!0,Promise.resolve(v(0,g.children.map(dl))))}};return M}function oT(v,{tops:c}){var y;return((y=c.get(v))===null||y===void 0?void 0:y.kind)===0}function kh(v,c){let y=T=>{if("children"in T)for(let g of T.children)c(g),y(g)};y(v)}function cl(v,c,{remote:y,local:T}){let{mounted:g,channel:M}=c;g&&(v.kind===0||oT(v,c))&&y(M),T()}function sT(v,c,y,T){return cl(v,T,{remote:g=>g(3,v.id,c),local:()=>{y.text=c}})}var Fr=Symbol("ignore");function cT(v,c,y,T){let{strict:g}=T,{internalProps:M,externalProps:q}=y,B={},H=[],h=!1;for(let j of Object.keys(c)){if(j==="children")continue;let Q=q[j],pe=c[j],et=M[j],Ye=Wh(pe);if(et===Ye&&(Ye==null||typeof Ye!="object"))continue;let[qe,Xe]=Qd(et,Ye);Xe&&H.push(...Xe),qe!==Fr&&(h=!0,B[j]=qe,Ca(Q)&&Pd(Q,T),Ca(pe)&&fl(v,pe,T))}return cl(v,T,{remote:j=>{h&&j(4,v.id,B)},local:()=>{let j=Za(Za({},q),c);y.externalProps=g?Object.freeze(j):j,y.internalProps=Za(Za({},y.internalProps),B);for(let[Q,pe]of H)Q[sl]=pe}})}function Qd(v,c,y=new Set){return y.has(v)?[Fr]:typeof v=="function"&&sl in v?(y.add(v),[typeof c=="function"?Fr:Br(c),[[v,c]]]):Array.isArray(v)?(y.add(v),vT(v,c,y)):Xi(v)&&!Ca(v)?(y.add(v),dT(v,c,y)):[v===c?Fr:c]}function Br(v,c=new Map){let y=c.get(v);if(y)return y;if(Ca(v))return c.set(v,v),v;if(Array.isArray(v)){let T=[];c.set(v,T);for(let g of v)T.push(Br(g,c));return T}if(Xi(v)){let T={};c.set(v,T);for(let g of Object.keys(v))T[g]=Br(v[g],c);return T}if(typeof v=="function"){let T=(...g)=>T[sl](...g);return Object.defineProperty(T,sl,{enumerable:!1,configurable:!1,writable:!0,value:v}),c.set(v,T),T}return v}function Zi(v,c=new Set){if(!c.has(v)){if(c.add(v),Array.isArray(v))return v.reduce((y,T)=>{let g=Zi(T,c);return g?[...y,...g]:y},[]);if(Xi(v))return Object.keys(v).reduce((y,T)=>{let g=Zi(v[T],c);return g?[...y,...g]:y},[]);if(typeof v=="function")return sl in v?[v]:void 0}}function Kh(v){var c;(c=v.parent)===null||c===void 0||c.removeChild(v)}function cs(v,c,y,T){for(let g of c)fs(v,g,y,T)}function fs(v,c,y,T){var g;let{nodes:M,strict:q}=T;if(!M.has(c))throw new Error("Cannot append a node that was not created by this remote root");let B=c.parent,H=(g=B==null?void 0:B.children.indexOf(c))!==null&&g!==void 0?g:-1;return cl(v,T,{remote:h=>{h(1,v.id,H<0?v.children.length:v.children.length-1,dl(c),B?B.id:!1)},local:()=>{fl(v,c,T);let h;if(B){let j=Ih(B,T),Q=[...j.children];Q.splice(H,1),B===v?h=Q:(j.children=q?Object.freeze(Q):Q,h=[...y.children])}else h=[...y.children];h.push(c),y.children=q?Object.freeze(h):h}})}function Vd(v,c,y,T){for(let g of v.children)ds(v,g,y,T);cs(v,c,y,T)}function ds(v,c,y,T){let{strict:g}=T,M=v.children.indexOf(c);if(M!==-1)return cl(v,T,{remote:q=>q(2,v.id,M),local:()=>{Pd(c,T);let q=[...y.children];q.splice(q.indexOf(c),1),y.children=g?Object.freeze(q):q}})}function Ji(v,c,y,T,g){var M;let{strict:q,nodes:B}=g;if(!B.has(c))throw new Error("Cannot insert a node that was not created by this remote root");let H=c.parent,h=(M=H==null?void 0:H.children.indexOf(c))!==null&&M!==void 0?M:-1;return cl(v,g,{remote:j=>{let Q=y==null?v.children.length-1:v.children.indexOf(y);j(1,v.id,Q<h||h<0?Q:Q-1,dl(c),H?H.id:!1)},local:()=>{fl(v,c,g);let j;if(H){let Q=Ih(H,g),pe=[...Q.children];pe.splice(h,1),H===v?j=pe:(Q.children=q?Object.freeze(pe):pe,j=[...T.children])}else j=[...T.children];y==null?j.push(c):j.splice(j.indexOf(y),0,c),T.children=q?Object.freeze(j):j}})}function Jt(v,c){return typeof v=="string"?c.createText(v):v}function fl(v,c,y){let{tops:T,parents:g}=y,M=v.kind===0?v:T.get(v);T.set(c,M),g.set(c,v),Xh(c,y),kh(c,q=>{T.set(q,M),Xh(q,y)})}function Xh(v,c){if(v.kind!==1)return;let y=v.props;y&&Object.values(y).forEach(T=>{Ca(T)&&fl(v,T,c)})}function Pd(v,c){let{tops:y,parents:T}=c;y.delete(v),T.delete(v),kh(v,g=>{y.delete(g),Jh(g,c)}),Jh(v,c)}function Jh(v,c){if(v.kind!==1)return;let y=v.remoteProps;for(let T of Object.keys(y!=null?y:{})){let g=y[T];Ca(g)&&Pd(g,c)}}function wd(v,{parents:c,tops:y,nodes:T}){T.add(v),Object.defineProperty(v,"parent",{get(){return c.get(v)},configurable:!0,enumerable:!0}),Object.defineProperty(v,"top",{get(){return y.get(v)},configurable:!0,enumerable:!0})}function dl(v){return v.kind===2?{id:v.id,kind:v.kind,text:v.text}:{id:v.id,kind:v.kind,type:v.type,props:v.remoteProps,children:v.children.map(c=>dl(c))}}function Wh(v){return Ca(v)?fT(v):v}function fT(v){return{id:v.id,kind:v.kind,get children(){return v.children.map(c=>dl(c))}}}function Ih(v,c){return v.kind===0?c:v.kind===3?c.fragments.get(v):c.components.get(v)}function Yd(v,c,y){Object.defineProperty(v,"id",{value:c,configurable:!0,writable:!1,enumerable:!1}),Object.defineProperty(v,"root",{value:y,configurable:!0,writable:!1,enumerable:!1})}function dT(v,c,y){if(!Xi(c)){var T;return[Br(c),(T=Zi(v))===null||T===void 0?void 0:T.map(B=>[B,void 0])]}let g=!1,M=[],q={};for(let B in v){let H=v[B];if(!(B in c)){g=!0;let pe=Zi(H);pe&&M.push(...pe.map(et=>[et,void 0]))}let h=c[B],[j,Q]=Qd(H,h,y);Q&&M.push(...Q),j!==Fr&&(g=!0,q[B]=j)}for(let B in c)B in q||(g=!0,q[B]=Br(c[B]));return[g?q:Fr,M]}function vT(v,c,y){if(!Array.isArray(c)){var T;return[Br(c),(T=Zi(v))===null||T===void 0?void 0:T.map(j=>[j,void 0])]}let g=!1,M=[],q=c.length,B=v.length,H=Math.max(B,q),h=[];for(let j=0;j<H;j++){let Q=v[j],pe=c[j];if(j<q){if(j>=B){g=!0,h[j]=Br(pe);continue}let[et,Ye]=Qd(Q,pe,y);if(Ye&&M.push(...Ye),et===Fr){h[j]=Q;continue}g=!0,h[j]=et}else{g=!0;let et=Zi(Q);et&&M.push(...et.map(Ye=>[Ye,void 0]))}}return[g?h:Fr,M]}function $h(){return(c,y)=>{var T;function g(...M){return Ki(this,null,function*(){if(M.length===1)return y(...M);let[{channel:q,components:B},H]=M,h=qd(q,{components:B,strict:!0}),j=y(h,H);return typeof j=="object"&&j!=null&&"then"in j&&(j=yield j),h.mount(),j})}return(T=globalThis.shopify)===null||T===void 0||T.extend(c,g),g}}var Gd=$h();var Kd="AdminAction";var Xd="BlockStack";var Jd="Button";var Zd="Text";var ey=Kn(ka(),1),ps=(0,ey.createContext)(null);var hy=Kn(ka(),1);var cy=Kn(ly(),1);var fy=v=>{var c;return(0,cy.default)({now:Date.now,scheduleTimeout:setTimeout,cancelTimeout:clearTimeout,noTimeout:!1,supportsMicrotasks:!0,scheduleMicrotask:oy,queueMicrotask:oy,isPrimaryRenderer:(c=v==null?void 0:v.primary)!==null&&c!==void 0?c:!0,supportsMutation:!0,supportsHydration:!1,supportsPersistence:!1,getRootHostContext(){return{}},getChildHostContext(y){return y},createTextInstance(y,T){return T.createText(y)},createInstance(y,T,g){let B=T,{children:M}=B,q=os(B,["children"]);return g.createComponent(y,q)},commitTextUpdate(y,T,g){y.update(g)},prepareUpdate(y,T,g,M){let q={},B=!1;for(let H in g)!sy(g,H)||H==="children"||(H in M?g[H]!==M[H]&&(B=!0,q[H]=M[H]):(B=!0,q[H]=void 0));for(let H in M)!sy(M,H)||H==="children"||H in g||(B=!0,q[H]=M[H]);return B?q:null},commitUpdate(y,T){y.updateProps(T)},appendChildToContainer(y,T){y.append(T)},insertInContainerBefore(y,T,g){y.insertBefore(T,g)},removeChildFromContainer(y,T){y.removeChild(T)},clearContainer(y){for(let T of y.children)y.removeChild(T)},appendInitialChild(y,T){y.append(T)},appendChild(y,T){y.append(T)},insertBefore(y,T,g){y.insertBefore(T,g)},removeChild(y,T){y.removeChild(T)},finalizeInitialChildren(){return!1},shouldSetTextContent(){return!1},getPublicInstance(){},prepareForCommit(){return null},resetAfterCommit(){},commitMount(){},preparePortalMount(){},detachDeletedInstance(){}})};function oy(v){return typeof queueMicrotask=="function"?queueMicrotask:Promise.resolve(null).then(v).catch(pT)}function pT(v){setTimeout(()=>{throw v})}var{hasOwnProperty:mT}={};function sy(v,c){return mT.call(v,c)}var dy=Kn(ka(),1),ms=(0,dy.createContext)(null);var yy=Kn(ki(),1),ys=new WeakMap,my=0,hT=fy();function Wd(v){return{render(c){kd(c,v)},unmount(){ys.has(v)&&(kd(null,v),ys.delete(v))}}}function kd(v,c,y,T=hT){let g=ys.get(c);if(!g){var M;let h={container:Number(((M=hy.version.split("."))===null||M===void 0?void 0:M[0])||18)>=18?T.createContainer(c,my,null,!1,null,"r-ui",()=>null,null):T.createContainer(c,my,!1,null),renderContext:{root:c,reconciler:T}};ys.set(c,h),g=h}let{container:q,renderContext:B}=g;T.updateContainer(v&&(0,yy.jsx)(ms.Provider,{value:B,children:v}),q,null,y)}var Wa=Kn(ka(),1);var by=Kn(ki(),1);var gy=Kn(ka(),1);function Sy(){let v=(0,gy.useContext)(ms);if(v==null)throw new Error("No remote-ui Render instance found in context");return v}function dr(v,{fragmentProps:c}={}){if(!c||!c.length)return v;let y=yT(v,c);return y.displayName=v,y}function yT(v,c){let y=v;return(0,Wa.memo)(function(q){var B=q,{children:g=[]}=B,M=os(B,["children"]);let H=(0,Wa.useRef)({}),{root:h,reconciler:j}=Sy(),{props:Q,children:pe}=(0,Wa.useMemo)(()=>{let et=[],Ye={};for(let qe of Object.keys(M)){let Xe=M[qe];if(c.includes(qe)&&(0,Wa.isValidElement)(Xe)){let Re=H.current[qe],xe=Ca(Re)?Re:h.createFragment();H.current[qe]=xe,Object.assign(xe,{createText(...gt){return h.createText(...gt)},createComponent(gt,...zn){return h.createComponent(gt,...zn)}});let ft=j.createPortal(Xe,xe,null,null);et.push(ft),Ye[qe]=xe}else Ye[qe]=Xe,delete H.current[qe]}return{props:Ye,children:[...Wa.Children.toArray(g),...et]}},[g,M,h,j,H]);return(0,by.jsx)(y,qh(Za({},Q),{children:pe}))})}var Ey=(v,c)=>new Promise((y,T)=>{try{let g=Wd(c);g.render(v),y(()=>g.unmount())}catch(g){console.error(g),T(g)}});var Ry=Kn(ki(),1);function gs(v,c){return Gd(v,(y,T)=>Ki(null,null,function*(){if(!v.match(/\.render$/))throw new Error(`reactExtension can only be used for .render extension targets, got: ${v}`);let g=yield c(T);return Ey((0,Ry.jsx)(ps.Provider,{value:T,children:g}),y)}))}var Id=dr(Kd,{fragmentProps:["primaryAction","secondaryAction"]});var $d=dr(Xd);var Ss=dr(Jd);var Wi=dr(Zd);var Ty=Kn(ka(),1);var bs=class extends Error{constructor(...c){super(...c),this.name="AdminUIExtensionError"}};function ev(v){let c=(0,Ty.useContext)(ps);if(c==null)throw new bs("No extension api found.");return c}var xa=Kn(ki()),Cy="admin.order-index.selection-action.render",lx=gs(Cy,()=>(0,xa.jsx)(gT,{}));function gT(){let{close:v,data:c}=ev(Cy),[y,T]=(0,Ii.useState)([]),[g,M]=(0,Ii.useState)(!1),[q,B]=(0,Ii.useState)("");(0,Ii.useEffect)(()=>{var h;if(console.log("\u{1F50D} Full data object from useApi:",c),((h=c==null?void 0:c.selected)==null?void 0:h.length)>0){let j=c.selected.map(Q=>Q.id.split("/").pop());T(j)}},[c]);let H=()=>Ki(null,null,function*(){M(!0);try{let j=yield(yield fetch("/resources/bulk-order-details",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderIds:y})})).json();console.log("\u2705 Order details received from Remix:",j);for(let Q of j.orders){Q.orderReferenceNumber=String(Q.order_number);let pe={shopifyStoreUrl:`https://${j.shopifyStoreUrl}`,orders:[Q]},Ye=yield(yield fetch("https://backend.rushr-admin.com/api/orders/create-order",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${j.token}`},body:JSON.stringify(pe)})).json();console.log("\u2705 Sent order:",Q.id,"Response:",Ye)}B("\u2705 Orders sent to external app successfully!")}catch(h){console.error("\u274C Error in sending orders:",h),B("\u274C Failed to send orders.")}finally{M(!1)}});return(0,xa.jsx)(Id,{primaryAction:(0,xa.jsx)(Ss,{disabled:!y.length,loading:g,onPress:H,children:"Send to App"}),secondaryAction:(0,xa.jsx)(Ss,{onPress:v,children:"Cancel"}),children:(0,xa.jsxs)($d,{spacing:"tight",children:[(0,xa.jsx)(Wi,{fontWeight:"bold",children:"Selected Orders"}),y.length===0?(0,xa.jsx)(Wi,{children:"No orders selected."}):(0,xa.jsxs)(Wi,{children:["Orders: ",y.join(", ")]}),q&&(0,xa.jsx)(Wi,{children:q})]})})}})();
//# sourceMappingURL=admin-action-123.js.map
