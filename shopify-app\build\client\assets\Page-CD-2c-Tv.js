import{R as r,r as u,d as Or}from"./index-y-Yv8VR0.js";import{h as Tn,b as In,T as Rr,e as Wr,g as $r,c as Hr,j as Ue,k as Mt,u as Ft,L as Dr,l as rn,I as zr,a as jr,P as An,m as Gr,R as Lt,d as Ur,f as Vr,s as yn,S as Zr,o as We,p as qr,E as ct,q as wn,r as Xr,v as Qr,w as Yr,x as Jr,M as Kr}from"./context-QCCGjp8E.js";let de;(function(e){e[e.Backspace=8]="Backspace",e[e.Tab=9]="Tab",e[e.Enter=13]="Enter",e[e.Shift=16]="Shift",e[e.Ctrl=17]="Ctrl",e[e.Alt=18]="Alt",e[e.Pause=19]="Pause",e[e.CapsLock=20]="CapsLock",e[e.Escape=27]="Escape",e[e.Space=32]="Space",e[e.PageUp=33]="PageUp",e[e.PageDown=34]="PageDown",e[e.End=35]="End",e[e.Home=36]="Home",e[e.LeftArrow=37]="LeftArrow",e[e.UpArrow=38]="UpArrow",e[e.RightArrow=39]="RightArrow",e[e.DownArrow=40]="DownArrow",e[e.Insert=45]="Insert",e[e.Delete=46]="Delete",e[e.Key0=48]="Key0",e[e.Key1=49]="Key1",e[e.Key2=50]="Key2",e[e.Key3=51]="Key3",e[e.Key4=52]="Key4",e[e.Key5=53]="Key5",e[e.Key6=54]="Key6",e[e.Key7=55]="Key7",e[e.Key8=56]="Key8",e[e.Key9=57]="Key9",e[e.KeyA=65]="KeyA",e[e.KeyB=66]="KeyB",e[e.KeyC=67]="KeyC",e[e.KeyD=68]="KeyD",e[e.KeyE=69]="KeyE",e[e.KeyF=70]="KeyF",e[e.KeyG=71]="KeyG",e[e.KeyH=72]="KeyH",e[e.KeyI=73]="KeyI",e[e.KeyJ=74]="KeyJ",e[e.KeyK=75]="KeyK",e[e.KeyL=76]="KeyL",e[e.KeyM=77]="KeyM",e[e.KeyN=78]="KeyN",e[e.KeyO=79]="KeyO",e[e.KeyP=80]="KeyP",e[e.KeyQ=81]="KeyQ",e[e.KeyR=82]="KeyR",e[e.KeyS=83]="KeyS",e[e.KeyT=84]="KeyT",e[e.KeyU=85]="KeyU",e[e.KeyV=86]="KeyV",e[e.KeyW=87]="KeyW",e[e.KeyX=88]="KeyX",e[e.KeyY=89]="KeyY",e[e.KeyZ=90]="KeyZ",e[e.LeftMeta=91]="LeftMeta",e[e.RightMeta=92]="RightMeta",e[e.Select=93]="Select",e[e.Numpad0=96]="Numpad0",e[e.Numpad1=97]="Numpad1",e[e.Numpad2=98]="Numpad2",e[e.Numpad3=99]="Numpad3",e[e.Numpad4=100]="Numpad4",e[e.Numpad5=101]="Numpad5",e[e.Numpad6=102]="Numpad6",e[e.Numpad7=103]="Numpad7",e[e.Numpad8=104]="Numpad8",e[e.Numpad9=105]="Numpad9",e[e.Multiply=106]="Multiply",e[e.Add=107]="Add",e[e.Subtract=109]="Subtract",e[e.Decimal=110]="Decimal",e[e.Divide=111]="Divide",e[e.F1=112]="F1",e[e.F2=113]="F2",e[e.F3=114]="F3",e[e.F4=115]="F4",e[e.F5=116]="F5",e[e.F6=117]="F6",e[e.F7=118]="F7",e[e.F8=119]="F8",e[e.F9=120]="F9",e[e.F10=121]="F10",e[e.F11=122]="F11",e[e.F12=123]="F12",e[e.NumLock=144]="NumLock",e[e.ScrollLock=145]="ScrollLock",e[e.Semicolon=186]="Semicolon",e[e.Equals=187]="Equals",e[e.Comma=188]="Comma",e[e.Dash=189]="Dash",e[e.Period=190]="Period",e[e.ForwardSlash=191]="ForwardSlash",e[e.GraveAccent=192]="GraveAccent",e[e.OpenBracket=219]="OpenBracket",e[e.BackSlash=220]="BackSlash",e[e.CloseBracket=221]="CloseBracket",e[e.SingleQuote=222]="SingleQuote"})(de||(de={}));function Nn(e){const t=typeof e;return e!=null&&(t==="object"||t==="function")}function y(...e){return e.filter(Boolean).join(" ")}function X(e,t){return`${e}${t.charAt(0).toUpperCase()}${t.slice(1)}`}function Ot(e){const t=Object.entries(e).filter(([n,o])=>o!=null);return t.length?Object.fromEntries(t):void 0}function be(e,t,n,o){if(!o)return{};let a;return Nn(o)?a=Object.fromEntries(Object.entries(o).map(([i,s])=>[i,`var(--p-${n}-${s})`])):a={[Tn[0]]:`var(--p-${n}-${o})`},Object.fromEntries(Object.entries(a).map(([i,s])=>[`--pc-${e}-${t}-${i}`,s]))}function it(e,t,n){return n?Nn(n)?Object.fromEntries(Object.entries(n).map(([o,a])=>[`--pc-${e}-${t}-${o}`,a])):{[`--pc-${e}-${t}-${Tn[0]}`]:n}:{}}var eo={themeContainer:"Polaris-ThemeProvider--themeContainer"};const to=["light","dark-experimental"],no=e=>to.includes(e);function ro(e){const{as:t="div",children:n,className:o,theme:a=In}=e;return r.createElement(Rr.Provider,{value:a},r.createElement(Wr.Provider,{value:$r(a)},r.createElement(t,{"data-portal-id":e["data-portal-id"],className:y(Hr(a),eo.themeContainer,o)},n)))}const oo=u.createContext(!1);function kn(e,t,n,o){const a=u.useRef(t),i=u.useRef(o);Ue(()=>{a.current=t},[t]),Ue(()=>{i.current=o},[o]),u.useEffect(()=>{if(!(typeof e=="string"&&n!==null))return;let s;if(typeof n>"u")s=window;else if("current"in n){if(n.current===null)return;s=n.current}else s=n;const l=i.current,c=d=>a.current(d);return s.addEventListener(e,c,l),()=>{s.removeEventListener(e,c,l)}},[e,n])}var Bn=function(t){return r.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),r.createElement("path",{d:"M10 6a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5a.75.75 0 0 1 .75-.75Z"}),r.createElement("path",{d:"M11 13a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"}),r.createElement("path",{fillRule:"evenodd",d:"M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Zm-1.5 0a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0Z"}))};Bn.displayName="AlertCircleIcon";var _n=function(t){return r.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),r.createElement("path",{fillRule:"evenodd",d:"M16.5 10a.75.75 0 0 1-.75.75h-9.69l2.72 2.72a.75.75 0 0 1-1.06 1.06l-4-4a.75.75 0 0 1 0-1.06l4-4a.75.75 0 1 1 1.06 1.06l-2.72 2.72h9.69a.75.75 0 0 1 .75.75Z"}))};_n.displayName="ArrowLeftIcon";var Rt=function(t){return r.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),r.createElement("path",{fillRule:"evenodd",d:"M5.72 8.47a.75.75 0 0 1 1.06 0l3.47 3.47 3.47-3.47a.75.75 0 1 1 1.06 1.06l-4 4a.75.75 0 0 1-1.06 0l-4-4a.75.75 0 0 1 0-1.06Z"}))};Rt.displayName="ChevronDownIcon";var Mn=function(t){return r.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),r.createElement("path",{fillRule:"evenodd",d:"M11.764 5.204a.75.75 0 0 1 .032 1.06l-3.516 3.736 3.516 3.736a.75.75 0 1 1-1.092 1.028l-4-4.25a.75.75 0 0 1 0-1.028l4-4.25a.75.75 0 0 1 1.06-.032Z"}))};Mn.displayName="ChevronLeftIcon";var Fn=function(t){return r.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),r.createElement("path",{fillRule:"evenodd",d:"M7.72 14.53a.75.75 0 0 1 0-1.06l3.47-3.47-3.47-3.47a.75.75 0 0 1 1.06-1.06l4 4a.75.75 0 0 1 0 1.06l-4 4a.75.75 0 0 1-1.06 0Z"}))};Fn.displayName="ChevronRightIcon";var Wt=function(t){return r.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),r.createElement("path",{fillRule:"evenodd",d:"M14.53 12.28a.75.75 0 0 1-1.06 0l-3.47-3.47-3.47 3.47a.75.75 0 0 1-1.06-1.06l4-4a.75.75 0 0 1 1.06 0l4 4a.75.75 0 0 1 0 1.06Z"}))};Wt.displayName="ChevronUpIcon";var Ln=function(t){return r.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),r.createElement("path",{d:"M6 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"}),r.createElement("path",{d:"M11.5 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"}),r.createElement("path",{d:"M17 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"}))};Ln.displayName="MenuHorizontalIcon";var On=function(t){return r.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),r.createElement("path",{fillRule:"evenodd",d:"M12.323 13.383a5.5 5.5 0 1 1 1.06-1.06l2.897 2.897a.75.75 0 1 1-1.06 1.06l-2.897-2.897Zm.677-4.383a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z"}))};On.displayName="SearchIcon";var Rn=function(t){return r.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),r.createElement("path",{d:"M10.884 4.323a1.25 1.25 0 0 0-1.768 0l-2.646 2.647a.75.75 0 0 0 1.06 1.06l2.47-2.47 2.47 2.47a.75.75 0 1 0 1.06-1.06l-2.646-2.647Z"}),r.createElement("path",{d:"m13.53 13.03-2.646 2.647a1.25 1.25 0 0 1-1.768 0l-2.646-2.647a.75.75 0 0 1 1.06-1.06l2.47 2.47 2.47-2.47a.75.75 0 0 1 1.06 1.06Z"}))};Rn.displayName="SelectIcon";var Wn=function(t){return r.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),r.createElement("path",{d:"M13.03 6.97a.75.75 0 0 1 0 1.06l-1.97 1.97 1.97 1.97a.75.75 0 1 1-1.06 1.06l-1.97-1.97-1.97 1.97a.75.75 0 0 1-1.06-1.06l1.97-1.97-1.97-1.97a.75.75 0 0 1 1.06-1.06l1.97 1.97 1.97-1.97a.75.75 0 0 1 1.06 0Z"}),r.createElement("path",{fillRule:"evenodd",d:"M10 17a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm0-1.5a5.5 5.5 0 1 0 0-11 5.5 5.5 0 0 0 0 11Z"}))};Wn.displayName="XCircleIcon";function ao(e){const{top:t,left:n,bottom:o,right:a}=e.getBoundingClientRect();return t>=0&&a<=window.innerWidth&&o<=window.innerHeight&&n>=0}const Bt='a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not([aria-disabled="true"]):not([tabindex="-1"]):not(:disabled),*[tabindex]',ut='a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not([aria-disabled="true"]):not([tabindex="-1"]):not(:disabled),*[tabindex]:not([tabindex="-1"])',io='a[role="menuitem"],frame[role="menuitem"],iframe[role="menuitem"],input[role="menuitem"]:not([type=hidden]):not(:disabled),select[role="menuitem"]:not(:disabled),textarea[role="menuitem"]:not(:disabled),button[role="menuitem"]:not(:disabled),*[tabindex]:not([tabindex="-1"])',pt=({currentTarget:e})=>e.blur();function so(e,t){const n=[...document.querySelectorAll(Bt)],o=n.indexOf(e)+1,a=n.slice(o);for(const i of a)if(ao(i)&&(!t||t&&t(i)))return i;return null}function $n(e,t=!0){return!t&&mt(e,Bt)?e:e.querySelector(Bt)}function $e(e){const t="a,button,frame,iframe,input:not([type=hidden]),select,textarea,*[tabindex]";return mt(e,t)?e:e.querySelector(t)}function Ga(e,t=!0){var n;(n=$n(e,t))==null||n.focus()}function on(e,t){const n=so(e,t);return n&&n instanceof HTMLElement?(n.focus(),!0):!1}function Hn(e,t=!0){return!t&&mt(e,ut)?e:e.querySelector(ut)}function Ua(e,t=!0){const n=Hn(e,t);return n?(n.focus(),!0):!1}function lo(e,t=!0){if(!t&&mt(e,ut))return e;const n=e.querySelectorAll(ut);return n[n.length-1]}function Va(e,t=!0){const n=lo(e,t);return n?(n.focus(),!0):!1}function co(e,t){const n=Dn(e),o=zn(n,t);o===-1?n[0].focus():n[(o-1+n.length)%n.length].focus()}function uo(e,t){const n=Dn(e),o=zn(n,t);o===-1?n[0].focus():n[(o+1)%n.length].focus()}function Dn(e){return e.querySelectorAll(io)}function zn(e,t){let n=0;for(const o of e){if(o===t)break;n++}return n===e.length?-1:n}function mt(e,t){if(e.matches)return e.matches(t);const n=(e.ownerDocument||document).querySelectorAll(t);let o=n.length;for(;--o>=0&&n.item(o)!==e;)return o>-1}var U={Button:"Polaris-Button",disabled:"Polaris-Button--disabled",pressed:"Polaris-Button--pressed",variantPrimary:"Polaris-Button--variantPrimary",variantSecondary:"Polaris-Button--variantSecondary",variantTertiary:"Polaris-Button--variantTertiary",variantPlain:"Polaris-Button--variantPlain",removeUnderline:"Polaris-Button--removeUnderline",variantMonochromePlain:"Polaris-Button--variantMonochromePlain",toneSuccess:"Polaris-Button--toneSuccess",toneCritical:"Polaris-Button--toneCritical",sizeMicro:"Polaris-Button--sizeMicro",sizeSlim:"Polaris-Button--sizeSlim",sizeMedium:"Polaris-Button--sizeMedium",sizeLarge:"Polaris-Button--sizeLarge",textAlignCenter:"Polaris-Button--textAlignCenter",textAlignStart:"Polaris-Button--textAlignStart",textAlignLeft:"Polaris-Button--textAlignLeft",textAlignEnd:"Polaris-Button--textAlignEnd",textAlignRight:"Polaris-Button--textAlignRight",fullWidth:"Polaris-Button--fullWidth",iconOnly:"Polaris-Button--iconOnly",iconWithText:"Polaris-Button--iconWithText",disclosure:"Polaris-Button--disclosure",loading:"Polaris-Button--loading",pressable:"Polaris-Button--pressable",hidden:"Polaris-Button--hidden",Icon:"Polaris-Button__Icon",Spinner:"Polaris-Button__Spinner"},He={Icon:"Polaris-Icon",toneInherit:"Polaris-Icon--toneInherit",toneBase:"Polaris-Icon--toneBase",toneSubdued:"Polaris-Icon--toneSubdued",toneCaution:"Polaris-Icon--toneCaution",toneWarning:"Polaris-Icon--toneWarning",toneCritical:"Polaris-Icon--toneCritical",toneInteractive:"Polaris-Icon--toneInteractive",toneInfo:"Polaris-Icon--toneInfo",toneSuccess:"Polaris-Icon--toneSuccess",tonePrimary:"Polaris-Icon--tonePrimary",toneEmphasis:"Polaris-Icon--toneEmphasis",toneMagic:"Polaris-Icon--toneMagic",toneTextCaution:"Polaris-Icon--toneTextCaution",toneTextWarning:"Polaris-Icon--toneTextWarning",toneTextCritical:"Polaris-Icon--toneTextCritical",toneTextInfo:"Polaris-Icon--toneTextInfo",toneTextPrimary:"Polaris-Icon--toneTextPrimary",toneTextSuccess:"Polaris-Icon--toneTextSuccess",toneTextMagic:"Polaris-Icon--toneTextMagic",Svg:"Polaris-Icon__Svg",Img:"Polaris-Icon__Img",Placeholder:"Polaris-Icon__Placeholder"},pe={root:"Polaris-Text--root",block:"Polaris-Text--block",truncate:"Polaris-Text--truncate",visuallyHidden:"Polaris-Text--visuallyHidden",start:"Polaris-Text--start",center:"Polaris-Text--center",end:"Polaris-Text--end",justify:"Polaris-Text--justify",base:"Polaris-Text--base",inherit:"Polaris-Text--inherit",disabled:"Polaris-Text--disabled",success:"Polaris-Text--success",critical:"Polaris-Text--critical",caution:"Polaris-Text--caution",subdued:"Polaris-Text--subdued",magic:"Polaris-Text--magic","magic-subdued":"Polaris-Text__magic--subdued","text-inverse":"Polaris-Text__text--inverse","text-inverse-secondary":"Polaris-Text--textInverseSecondary",headingXs:"Polaris-Text--headingXs",headingSm:"Polaris-Text--headingSm",headingMd:"Polaris-Text--headingMd",headingLg:"Polaris-Text--headingLg",headingXl:"Polaris-Text--headingXl",heading2xl:"Polaris-Text--heading2xl",heading3xl:"Polaris-Text--heading3xl",bodyXs:"Polaris-Text--bodyXs",bodySm:"Polaris-Text--bodySm",bodyMd:"Polaris-Text--bodyMd",bodyLg:"Polaris-Text--bodyLg",regular:"Polaris-Text--regular",medium:"Polaris-Text--medium",semibold:"Polaris-Text--semibold",bold:"Polaris-Text--bold",break:"Polaris-Text--break",numeric:"Polaris-Text--numeric","line-through":"Polaris-Text__line--through"};const an={heading3xl:"heading2xl"},W=({alignment:e,as:t,breakWord:n,children:o,tone:a,fontWeight:i,id:s,numeric:l=!1,truncate:c=!1,variant:d,visuallyHidden:p=!1,textDecorationLine:h})=>{d&&Object.prototype.hasOwnProperty.call(an,d)&&console.warn(`Deprecation: <Text variant="${d}" />. The value "${d}" will be removed in a future major version of Polaris. Use "${an[d]}" instead.`);const f=t||(p?"span":"p"),m=y(pe.root,d&&pe[d],i&&pe[i],(e||c)&&pe.block,e&&pe[e],n&&pe.break,a&&pe[a],l&&pe.numeric,c&&pe.truncate,p&&pe.visuallyHidden,h&&pe[h]);return r.createElement(f,Object.assign({className:m},s&&{id:s}),o)};function me({source:e,tone:t,accessibilityLabel:n}){let o;typeof e=="function"?o="function":e==="placeholder"?o="placeholder":o="external",t&&o==="external"&&console.warn("Recoloring external SVGs is not supported. Set the intended color on your SVG instead.");const a=y(He.Icon,t&&He[X("tone",t)]),{mdDown:i}=Mt(),s=e,l={function:r.createElement(s,Object.assign({className:He.Svg,focusable:"false","aria-hidden":"true"},i?{viewBox:"1 1 18 18"}:{})),placeholder:r.createElement("div",{className:He.Placeholder}),external:r.createElement("img",{className:He.Img,src:`data:image/svg+xml;utf8,${e}`,alt:"","aria-hidden":"true"})};return r.createElement("span",{className:a},n&&r.createElement(W,{as:"span",visuallyHidden:!0},n),l[o])}var sn={Spinner:"Polaris-Spinner",sizeSmall:"Polaris-Spinner--sizeSmall",sizeLarge:"Polaris-Spinner--sizeLarge"};function jn({size:e="large",accessibilityLabel:t,hasFocusableParent:n}){const o=Ft(),a=y(sn.Spinner,e&&sn[X("size",e)]),i=e==="large"?r.createElement("svg",{viewBox:"0 0 44 44",xmlns:"http://www.w3.org/2000/svg"},r.createElement("path",{d:"M15.542 1.487A21.507 21.507 0 00.5 22c0 11.874 9.626 21.5 21.5 21.5 9.847 0 18.364-6.675 20.809-16.072a1.5 1.5 0 00-2.904-.756C37.803 34.755 30.473 40.5 22 40.5 11.783 40.5 3.5 32.217 3.5 22c0-8.137 5.3-15.247 12.942-17.65a1.5 1.5 0 10-.9-2.863z"})):r.createElement("svg",{viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},r.createElement("path",{d:"M7.229 1.173a9.25 9.25 0 1011.655 11.412 1.25 1.25 0 10-2.4-.698 6.75 6.75 0 11-8.506-8.329 1.25 1.25 0 10-.75-2.385z"})),s={...!n&&{role:"status"}},l=(o||!n)&&r.createElement(W,{as:"span",visuallyHidden:!0},t);return r.createElement(r.Fragment,null,r.createElement("span",{className:a},i),r.createElement("span",s,l))}function po(e,t){const n=u.useCallback(o=>{e&&(o.preventDefault(),o.stopPropagation())},[e]);return e?n:t}function mo(){return u.useContext(Dr)}const Gn=u.memo(u.forwardRef(function(t,n){const o=mo();if(o)return r.createElement(o,Object.assign({},rn.props,t,{ref:n}));const{external:a,url:i,target:s,...l}=t;let c;a?c="_blank":c=s??void 0;const d=c==="_blank"?"noopener noreferrer":void 0;return r.createElement("a",Object.assign({target:c},l,{href:i,rel:d},rn.props,{ref:n}))}));function fo({id:e,children:t,className:n,url:o,external:a,target:i,download:s,submit:l,disabled:c,loading:d,pressed:p,accessibilityLabel:h,role:f,ariaControls:m,ariaExpanded:b,ariaDescribedBy:P,ariaChecked:E,onClick:T,onFocus:S,onBlur:w,onKeyDown:A,onKeyPress:N,onKeyUp:C,onMouseEnter:g,onTouchStart:v,...I}){let M;const H={id:e,className:n,"aria-label":h},L={...H,role:f,onClick:T,onFocus:S,onBlur:w,onMouseUp:pt,onMouseEnter:g,onTouchStart:v},R=po(c,T);return o?M=c?r.createElement("a",H,t):r.createElement(Gn,Object.assign({},L,{url:o,external:a,target:i,download:s},I),t):M=r.createElement("button",Object.assign({},L,{"aria-disabled":c,type:l?"submit":"button","aria-busy":d?!0:void 0,"aria-controls":m,"aria-expanded":b,"aria-describedby":P,"aria-checked":E,"aria-pressed":p,onKeyDown:A,onKeyUp:C,onKeyPress:N,onClick:R,tabIndex:c?-1:void 0},I),t),M}class ho extends Error{constructor(t=""){super(`${t&&`${t} `}Your application must be wrapped in an <AppProvider> component. See https://polaris.shopify.com/components/app-provider for implementation instructions.`),this.name="MissingAppProviderError"}}function fe(){const e=u.useContext(zr);if(!e)throw new ho("No i18n was provided.");return e}function Ne({id:e,children:t,url:n,disabled:o,external:a,download:i,target:s,submit:l,loading:c,pressed:d,accessibilityLabel:p,role:h,ariaControls:f,ariaExpanded:m,ariaDescribedBy:b,ariaChecked:P,onClick:E,onFocus:T,onBlur:S,onKeyDown:w,onKeyPress:A,onKeyUp:N,onMouseEnter:C,onTouchStart:g,onPointerDown:v,icon:I,disclosure:M,removeUnderline:H,size:L="medium",textAlign:R="center",fullWidth:k,dataPrimaryLink:F,tone:$,variant:O="secondary"}){const Q=fe(),Y=o||c,{mdUp:oe}=Mt(),G=y(U.Button,U.pressable,U[X("variant",O)],U[X("size",L)],U[X("textAlign",R)],k&&U.fullWidth,M&&U.disclosure,I&&t&&U.iconWithText,I&&t==null&&U.iconOnly,Y&&U.disabled,c&&U.loading,d&&!o&&!n&&U.pressed,H&&U.removeUnderline,$&&U[X("tone",$)]),J=M?r.createElement("span",{className:c?U.hidden:U.Icon},r.createElement(me,{source:c?"placeholder":bo(M,Wt,Rt)})):null,ae=go(I)?r.createElement(me,{source:c?"placeholder":I}):I,K=ae?r.createElement("span",{className:c?U.hidden:U.Icon},ae):null,ce=["plain","monochromePlain"].includes(O);let he="medium";ce?he="regular":O==="primary"&&(he=oe?"medium":"semibold");let Z="bodySm";(L==="large"||ce&&L!=="micro")&&(Z="bodyMd");const Ee=t?r.createElement(W,{as:"span",variant:Z,fontWeight:he,key:o?"text-disabled":"text"},t):null,ie=c?r.createElement("span",{className:U.Spinner},r.createElement(jn,{size:"small",accessibilityLabel:Q.translate("Polaris.Button.spinnerAccessibilityLabel")})):null,ne={id:e,className:G,accessibilityLabel:p,ariaDescribedBy:b,role:h,onClick:E,onFocus:T,onBlur:S,onMouseUp:pt,onMouseEnter:C,onTouchStart:g,"data-primary-link":F},re={url:n,external:a,download:i,target:s},ue={submit:l,disabled:Y,loading:c,ariaControls:f,ariaExpanded:m,ariaChecked:P,pressed:d,onKeyDown:w,onKeyUp:N,onKeyPress:A,onPointerDown:v};return r.createElement(fo,Object.assign({},ne,re,ue),ie,K,Ee,J)}function go(e){return typeof e=="string"||typeof e=="object"&&e.body||typeof e=="function"}function bo(e,t,n){return e==="select"?Rn:e==="up"?t:n}function Za(e,t={}){return Array.isArray(e)?e.map((n,o)=>dt(n,t,o)):dt(e,t)}function dt({content:e,onAction:t,plain:n,destructive:o,...a},i,s){const l=n?"plain":void 0,c=o?"primary":void 0,d=!(i!=null&&i.tone)&&o?"critical":i==null?void 0:i.tone;return r.createElement(Ne,Object.assign({key:s,onClick:t,tone:d,variant:l||c},a,i),e)}var vo={ShadowBevel:"Polaris-ShadowBevel"};function Po(e){const{as:t="div",bevel:n=!0,borderRadius:o,boxShadow:a,children:i,zIndex:s="0"}=e,l=t;return r.createElement(l,{className:vo.ShadowBevel,style:{"--pc-shadow-bevel-z-index":s,...it("shadow-bevel","content",It(n,c=>c?'""':"none")),...it("shadow-bevel","box-shadow",It(n,c=>c?`var(--p-shadow-${a})`:"none")),...it("shadow-bevel","border-radius",It(n,c=>c?`var(--p-border-radius-${o})`:"var(--p-border-radius-0)"))}},i)}function It(e,t){return typeof e=="boolean"?t(e):Object.fromEntries(Object.entries(e).map(([n,o])=>[n,t(o)]))}var et={listReset:"Polaris-Box--listReset",Box:"Polaris-Box",visuallyHidden:"Polaris-Box--visuallyHidden",printHidden:"Polaris-Box--printHidden"};const z=u.forwardRef(({as:e="div",background:t,borderColor:n,borderStyle:o,borderWidth:a,borderBlockStartWidth:i,borderBlockEndWidth:s,borderInlineStartWidth:l,borderInlineEndWidth:c,borderRadius:d,borderEndStartRadius:p,borderEndEndRadius:h,borderStartStartRadius:f,borderStartEndRadius:m,children:b,color:P,id:E,minHeight:T,minWidth:S,maxWidth:w,overflowX:A,overflowY:N,outlineColor:C,outlineStyle:g,outlineWidth:v,padding:I,paddingBlock:M,paddingBlockStart:H,paddingBlockEnd:L,paddingInline:R,paddingInlineStart:k,paddingInlineEnd:F,role:$,shadow:O,tabIndex:Q,width:Y,printHidden:oe,visuallyHidden:G,position:J,insetBlockStart:ae,insetBlockEnd:K,insetInlineStart:ce,insetInlineEnd:he,zIndex:Z,opacity:Ee,...ie},ne)=>{const re=o||(n||a||i||s||l||c?"solid":void 0),ue=g||(C||v?"solid":void 0),Be={"--pc-box-color":P?`var(--p-color-${P})`:void 0,"--pc-box-background":t?`var(--p-color-${t})`:void 0,"--pc-box-border-color":n?n==="transparent"?"transparent":`var(--p-color-${n})`:void 0,"--pc-box-border-style":re,"--pc-box-border-radius":d?`var(--p-border-radius-${d})`:void 0,"--pc-box-border-end-start-radius":p?`var(--p-border-radius-${p})`:void 0,"--pc-box-border-end-end-radius":h?`var(--p-border-radius-${h})`:void 0,"--pc-box-border-start-start-radius":f?`var(--p-border-radius-${f})`:void 0,"--pc-box-border-start-end-radius":m?`var(--p-border-radius-${m})`:void 0,"--pc-box-border-width":a?`var(--p-border-width-${a})`:void 0,"--pc-box-border-block-start-width":i?`var(--p-border-width-${i})`:void 0,"--pc-box-border-block-end-width":s?`var(--p-border-width-${s})`:void 0,"--pc-box-border-inline-start-width":l?`var(--p-border-width-${l})`:void 0,"--pc-box-border-inline-end-width":c?`var(--p-border-width-${c})`:void 0,"--pc-box-min-height":T,"--pc-box-min-width":S,"--pc-box-max-width":w,"--pc-box-outline-color":C?`var(--p-color-${C})`:void 0,"--pc-box-outline-style":ue,"--pc-box-outline-width":v?`var(--p-border-width-${v})`:void 0,"--pc-box-overflow-x":A,"--pc-box-overflow-y":N,...be("box","padding-block-start","space",H||M||I),...be("box","padding-block-end","space",L||M||I),...be("box","padding-inline-start","space",k||R||I),...be("box","padding-inline-end","space",F||R||I),"--pc-box-shadow":O?`var(--p-shadow-${O})`:void 0,"--pc-box-width":Y,position:J,"--pc-box-inset-block-start":ae?`var(--p-space-${ae})`:void 0,"--pc-box-inset-block-end":K?`var(--p-space-${K})`:void 0,"--pc-box-inset-inline-start":ce?`var(--p-space-${ce})`:void 0,"--pc-box-inset-inline-end":he?`var(--p-space-${he})`:void 0,zIndex:Z,opacity:Ee},Qe=y(et.Box,G&&et.visuallyHidden,oe&&et.printHidden,e==="ul"&&et.listReset);return r.createElement(e,{className:Qe,id:E,ref:ne,style:Ot(Be),role:$,tabIndex:Q,...ie},b)});z.displayName="Box";const qa=({children:e,background:t="bg-surface",padding:n={xs:"400"},roundedAbove:o="sm"})=>{const a=Mt(),i="300",s=!!a[`${o}Up`];return r.createElement(oo.Provider,{value:!0},r.createElement(Po,{boxShadow:"100",borderRadius:s?i:"0",zIndex:"32"},r.createElement(z,{background:t,padding:n,overflowX:"clip",overflowY:"clip",minHeight:"100%"},e)))};var Eo={InlineStack:"Polaris-InlineStack"};const Ve=function({as:t="div",align:n,direction:o="row",blockAlign:a,gap:i,wrap:s=!0,children:l}){const c={"--pc-inline-stack-align":n,"--pc-inline-stack-block-align":a,"--pc-inline-stack-wrap":s?"wrap":"nowrap",...be("inline-stack","gap","space",i),...it("inline-stack","flex-direction",o)};return r.createElement(t,{className:Eo.InlineStack,style:c},l)};var At={BlockStack:"Polaris-BlockStack",listReset:"Polaris-BlockStack--listReset",fieldsetReset:"Polaris-BlockStack--fieldsetReset"};const xo=({as:e="div",children:t,align:n,inlineAlign:o,gap:a,id:i,reverseOrder:s=!1,...l})=>{const c=y(At.BlockStack,(e==="ul"||e==="ol")&&At.listReset,e==="fieldset"&&At.fieldsetReset),d={"--pc-block-stack-align":n?`${n}`:null,"--pc-block-stack-inline-align":o?`${o}`:null,"--pc-block-stack-order":s?"column-reverse":"column",...be("block-stack","gap","space",a)};return r.createElement(e,{className:c,id:i,style:Ot(d),...l},t)},Un=u.createContext(!1);function So({children:e,filterActions:t}){return r.createElement(Un.Provider,{value:t},e)}var se={Item:"Polaris-ActionList__Item",default:"Polaris-ActionList--default",active:"Polaris-ActionList--active",destructive:"Polaris-ActionList--destructive",disabled:"Polaris-ActionList--disabled",Prefix:"Polaris-ActionList__Prefix",Suffix:"Polaris-ActionList__Suffix",indented:"Polaris-ActionList--indented",menu:"Polaris-ActionList--menu",Text:"Polaris-ActionList__Text"};const Co=u.createContext(!1);var Ae={Badge:"Polaris-Badge",toneSuccess:"Polaris-Badge--toneSuccess","toneSuccess-strong":"Polaris-Badge__toneSuccess--strong",toneInfo:"Polaris-Badge--toneInfo","toneInfo-strong":"Polaris-Badge__toneInfo--strong",toneAttention:"Polaris-Badge--toneAttention","toneAttention-strong":"Polaris-Badge__toneAttention--strong",toneWarning:"Polaris-Badge--toneWarning","toneWarning-strong":"Polaris-Badge__toneWarning--strong",toneCritical:"Polaris-Badge--toneCritical","toneCritical-strong":"Polaris-Badge__toneCritical--strong",toneNew:"Polaris-Badge--toneNew",toneMagic:"Polaris-Badge--toneMagic","toneRead-only":"Polaris-Badge__toneRead--only",toneEnabled:"Polaris-Badge--toneEnabled",sizeLarge:"Polaris-Badge--sizeLarge",withinFilter:"Polaris-Badge--withinFilter",Icon:"Polaris-Badge__Icon",PipContainer:"Polaris-Badge__PipContainer"};let te;(function(e){e.Info="info",e.Success="success",e.Warning="warning",e.Critical="critical",e.Attention="attention",e.New="new",e.Magic="magic",e.InfoStrong="info-strong",e.SuccessStrong="success-strong",e.WarningStrong="warning-strong",e.CriticalStrong="critical-strong",e.AttentionStrong="attention-strong",e.ReadOnly="read-only",e.Enabled="enabled"})(te||(te={}));let ze;(function(e){e.Incomplete="incomplete",e.PartiallyComplete="partiallyComplete",e.Complete="complete"})(ze||(ze={}));function Vn(e,t,n){let o="",a="";if(!t&&!n)return"";switch(t){case ze.Incomplete:o=e.translate("Polaris.Badge.PROGRESS_LABELS.incomplete");break;case ze.PartiallyComplete:o=e.translate("Polaris.Badge.PROGRESS_LABELS.partiallyComplete");break;case ze.Complete:o=e.translate("Polaris.Badge.PROGRESS_LABELS.complete");break}switch(n){case te.Info:case te.InfoStrong:a=e.translate("Polaris.Badge.TONE_LABELS.info");break;case te.Success:case te.SuccessStrong:a=e.translate("Polaris.Badge.TONE_LABELS.success");break;case te.Warning:case te.WarningStrong:a=e.translate("Polaris.Badge.TONE_LABELS.warning");break;case te.Critical:case te.CriticalStrong:a=e.translate("Polaris.Badge.TONE_LABELS.critical");break;case te.Attention:case te.AttentionStrong:a=e.translate("Polaris.Badge.TONE_LABELS.attention");break;case te.New:a=e.translate("Polaris.Badge.TONE_LABELS.new");break;case te.ReadOnly:a=e.translate("Polaris.Badge.TONE_LABELS.readOnly");break;case te.Enabled:a=e.translate("Polaris.Badge.TONE_LABELS.enabled");break}return!n&&t?o:n&&!t?a:e.translate("Polaris.Badge.progressAndTone",{progressLabel:o,toneLabel:a})}var yt={Pip:"Polaris-Badge-Pip",toneInfo:"Polaris-Badge-Pip--toneInfo",toneSuccess:"Polaris-Badge-Pip--toneSuccess",toneNew:"Polaris-Badge-Pip--toneNew",toneAttention:"Polaris-Badge-Pip--toneAttention",toneWarning:"Polaris-Badge-Pip--toneWarning",toneCritical:"Polaris-Badge-Pip--toneCritical",progressIncomplete:"Polaris-Badge-Pip--progressIncomplete",progressPartiallyComplete:"Polaris-Badge-Pip--progressPartiallyComplete",progressComplete:"Polaris-Badge-Pip--progressComplete"};function To({tone:e,progress:t="complete",accessibilityLabelOverride:n}){const o=fe(),a=y(yt.Pip,e&&yt[X("tone",e)],t&&yt[X("progress",t)]),i=n||Vn(o,t,e);return r.createElement("span",{className:a},r.createElement(W,{as:"span",visuallyHidden:!0},i))}const ln="medium",Io={complete:()=>r.createElement("svg",{viewBox:"0 0 20 20"},r.createElement("path",{d:"M6 10c0-.93 0-1.395.102-1.776a3 3 0 0 1 2.121-2.122C8.605 6 9.07 6 10 6c.93 0 1.395 0 1.776.102a3 3 0 0 1 2.122 2.122C14 8.605 14 9.07 14 10s0 1.395-.102 1.777a3 3 0 0 1-2.122 2.12C11.395 14 10.93 14 10 14s-1.395 0-1.777-.102a3 3 0 0 1-2.12-2.121C6 11.395 6 10.93 6 10Z"})),partiallyComplete:()=>r.createElement("svg",{viewBox:"0 0 20 20"},r.createElement("path",{fillRule:"evenodd",d:"m8.888 6.014-.017-.018-.02.02c-.253.013-.45.038-.628.086a3 3 0 0 0-2.12 2.122C6 8.605 6 9.07 6 10s0 1.395.102 1.777a3 3 0 0 0 2.121 2.12C8.605 14 9.07 14 10 14c.93 0 1.395 0 1.776-.102a3 3 0 0 0 2.122-2.121C14 11.395 14 10.93 14 10c0-.93 0-1.395-.102-1.776a3 3 0 0 0-2.122-2.122C11.395 6 10.93 6 10 6c-.475 0-.829 0-1.112.014ZM8.446 7.34a1.75 1.75 0 0 0-1.041.94l4.314 4.315c.443-.2.786-.576.941-1.042L8.446 7.34Zm4.304 2.536L10.124 7.25c.908.001 1.154.013 1.329.06a1.75 1.75 0 0 1 1.237 1.237c.047.175.059.42.06 1.329ZM8.547 12.69c.182.05.442.06 1.453.06h.106L7.25 9.894V10c0 1.01.01 1.27.06 1.453a1.75 1.75 0 0 0 1.237 1.237Z"})),incomplete:()=>r.createElement("svg",{viewBox:"0 0 20 20"},r.createElement("path",{fillRule:"evenodd",d:"M8.547 12.69c.183.05.443.06 1.453.06s1.27-.01 1.453-.06a1.75 1.75 0 0 0 1.237-1.237c.05-.182.06-.443.06-1.453s-.01-1.27-.06-1.453a1.75 1.75 0 0 0-1.237-1.237c-.182-.05-.443-.06-1.453-.06s-1.27.01-1.453.06A1.75 1.75 0 0 0 7.31 8.547c-.05.183-.06.443-.06 1.453s.01 1.27.06 1.453a1.75 1.75 0 0 0 1.237 1.237ZM6.102 8.224C6 8.605 6 9.07 6 10s0 1.395.102 1.777a3 3 0 0 0 2.122 2.12C8.605 14 9.07 14 10 14s1.395 0 1.777-.102a3 3 0 0 0 2.12-2.121C14 11.395 14 10.93 14 10c0-.93 0-1.395-.102-1.776a3 3 0 0 0-2.121-2.122C11.395 6 10.93 6 10 6c-.93 0-1.395 0-1.776.102a3 3 0 0 0-2.122 2.122Z"}))};function Zn({children:e,tone:t,progress:n,icon:o,size:a=ln,toneAndProgressLabelOverride:i}){const s=fe(),l=u.useContext(Co),c=y(Ae.Badge,t&&Ae[X("tone",t)],a&&a!==ln&&Ae[X("size",a)],l&&Ae.withinFilter),d=i||Vn(s,n,t);let p=!!d&&r.createElement(W,{as:"span",visuallyHidden:!0},d);return n&&!o&&(p=r.createElement("span",{className:Ae.Icon},r.createElement(me,{accessibilityLabel:d,source:Io[n]}))),r.createElement("span",{className:c},p,o&&r.createElement("span",{className:Ae.Icon},r.createElement(me,{source:o})),e&&r.createElement(W,{as:"span",variant:"bodySm",fontWeight:t==="new"?"medium":void 0},e))}Zn.Pip=To;function Ze(e){const[t,n]=u.useState(e);return{value:t,toggle:u.useCallback(()=>n(o=>!o),[]),setTrue:u.useCallback(()=>n(!0),[]),setFalse:u.useCallback(()=>n(!1),[])}}var cn={TooltipContainer:"Polaris-Tooltip__TooltipContainer",HasUnderline:"Polaris-Tooltip__HasUnderline"};function Ao(){const e=u.useContext(jr);if(!e)throw new Error("No ephemeral presence manager was provided. Your application must be wrapped in an <AppProvider> component. See https://polaris.shopify.com/components/app-provider for implementation instructions.");return e}function yo(){const e=u.useContext(An);if(!e)throw new Error("No portals manager was provided. Your application must be wrapped in an <AppProvider> component. See https://polaris.shopify.com/components/app-provider for implementation instructions.");return e}function qn({children:e,idPrefix:t="",onPortalCreated:n=wo}){const o=Gr(),{container:a}=yo(),i=u.useId(),s=t!==""?`${t}-${i}`:i;return u.useEffect(()=>{n()},[n]),a?Or.createPortal(r.createElement(ro,{theme:no(o)?o:In,"data-portal-id":s},e),a):null}function wo(){}var xe={TooltipOverlay:"Polaris-Tooltip-TooltipOverlay",Tail:"Polaris-Tooltip-TooltipOverlay__Tail",positionedAbove:"Polaris-Tooltip-TooltipOverlay--positionedAbove",measuring:"Polaris-Tooltip-TooltipOverlay--measuring",measured:"Polaris-Tooltip-TooltipOverlay--measured",instant:"Polaris-Tooltip-TooltipOverlay--instant",Content:"Polaris-Tooltip-TooltipOverlay__Content",default:"Polaris-Tooltip-TooltipOverlay--default",wide:"Polaris-Tooltip-TooltipOverlay--wide"};function No(e,t,n,o,a,i,s,l=0){const c=e.top,d=c+e.height,p=e.top-l,h=a.height-e.top-e.height,f=t.height,m=n.activator+n.container,b=n.container,P=e.top-Math.max(o.top,0),E=a.top+Math.min(a.height,o.top+o.height)-(e.top+e.height),T=P>=b,S=E>=b,w=Math.min(p,f),A=Math.min(h,f),N=Math.min(p+e.height,f),C=Math.min(h+e.height,f),g=s?0:a.top,v={height:w-m,top:c+g-w,positioning:"above"},I={height:A-m,top:d+g,positioning:"below"},M={height:C-m,top:c+g,positioning:"cover"},H={height:N-m,top:c+g-w+e.height+m,positioning:"cover"};return i==="above"?(T||P>=E&&!S)&&(p>f||p>h)?v:I:i==="below"?(S||E>=P&&!T)&&(h>f||h>p)?I:v:i==="cover"?(S||E>=P&&!T)&&(h+e.height>f||h>p)?M:H:T&&S?p>h?v:I:P>b?v:I}function ko(e,t,n,o,a){const i=n.width-t.width;if(a==="left")return Math.min(i,Math.max(0,e.left-o.horizontal));if(a==="right"){const s=n.width-(e.left+e.width);return Math.min(i,Math.max(0,s-o.horizontal))}return Math.min(i,Math.max(0,e.center.x-t.width/2))}function Bo(e,t){const{center:n}=e;return n.y<t.top||n.y>t.top+t.height}function _o(e,t=Xn()){const n=Math.max(e.top,0),o=Math.max(e.left,0),a=Math.min(e.top+e.height,t.height),i=Math.min(e.left+e.width,t.width);return new Lt({top:n,left:o,height:a-n,width:i-o})}function Xn(){return new Lt({top:window.scrollY,left:window.scrollX,height:window.innerHeight,width:document.body.clientWidth})}var wt={PositionedOverlay:"Polaris-PositionedOverlay",fixed:"Polaris-PositionedOverlay--fixed",preventInteraction:"Polaris-PositionedOverlay--preventInteraction"};const un=Symbol("unique_identifier");function Mo(e){const t=u.useRef(un);return t.current===un&&(t.current=e()),t}function Fo(e){const t=Ft(),n=u.useRef(!1);if(t&&!n.current)return n.current=!0,e()}const Qn=u.createContext(void 0);var Ce={Scrollable:"Polaris-Scrollable",hasTopShadow:"Polaris-Scrollable--hasTopShadow",hasBottomShadow:"Polaris-Scrollable--hasBottomShadow",horizontal:"Polaris-Scrollable--horizontal",vertical:"Polaris-Scrollable--vertical",scrollbarWidthThin:"Polaris-Scrollable--scrollbarWidthThin",scrollbarWidthNone:"Polaris-Scrollable--scrollbarWidthNone",scrollbarWidthAuto:"Polaris-Scrollable--scrollbarWidthAuto",scrollbarGutterStable:"Polaris-Scrollable--scrollbarGutterStable","scrollbarGutterStableboth-edges":"Polaris-Scrollable__scrollbarGutterStableboth--edges"};function Lo(){const e=u.useRef(null),t=u.useContext(Qn);u.useEffect(()=>{!t||!e.current||t(e.current.offsetTop)},[t]);const n=u.useId();return r.createElement("a",{id:n,ref:e})}const dn=100,Yn=2,Jn=u.forwardRef(({children:e,className:t,horizontal:n=!0,vertical:o=!0,shadow:a,hint:i,focusable:s,scrollbarWidth:l="thin",scrollbarGutter:c,onScrolledToBottom:d,...p},h)=>{const[f,m]=u.useState(!1),[b,P]=u.useState(!1),E=Mo(()=>new Zr),T=u.useRef(null),S=u.useCallback((C,g={})=>{var M;const v=g.behavior||"smooth",I=Kn()?"auto":v;(M=T.current)==null||M.scrollTo({top:C,behavior:I})},[]),w=u.useRef();u.useImperativeHandle(h||w,()=>({scrollTo:S}));const A=u.useCallback(()=>{const C=T.current;C&&requestAnimationFrame(()=>{const{scrollTop:g,clientHeight:v,scrollHeight:I}=C,M=I>v,H=g>0,L=g+v>=I-Yn;m(H),P(!L),M&&L&&d&&d()})},[d]);Fo(()=>{A(),i&&requestAnimationFrame(()=>Oo(T.current))}),u.useEffect(()=>{var v;const C=T.current;if(!C)return;const g=Ur(A,50,{trailing:!0});return(v=E.current)==null||v.setContainer(C),C.addEventListener("scroll",A),globalThis.addEventListener("resize",g),()=>{C.removeEventListener("scroll",A),globalThis.removeEventListener("resize",g)}},[E,A]);const N=y(t,Ce.Scrollable,o&&Ce.vertical,n&&Ce.horizontal,a&&f&&Ce.hasTopShadow,a&&b&&Ce.hasBottomShadow,l&&Ce[X("scrollbarWidth",l)],c&&Ce[X("scrollbarGutter",c.replace(" ",""))]);return r.createElement(Qn.Provider,{value:S},r.createElement(Vr.Provider,{value:E.current},r.createElement("div",Object.assign({className:N},yn.props,p,{ref:T,tabIndex:s?0:void 0}),e)))});Jn.displayName="Scrollable";function Kn(){try{return window.matchMedia("(prefers-reduced-motion: reduce)").matches}catch{return!1}}function Oo(e){if(!e||Kn())return;const t=e.scrollHeight-e.clientHeight,n=Math.min(dn,t)-Yn,o=()=>{requestAnimationFrame(()=>{e.scrollTop>=n&&(e.removeEventListener("scroll",o),e.scrollTo({top:0,behavior:"smooth"}))})};e.addEventListener("scroll",o),e.scrollTo({top:dn,behavior:"smooth"})}const Ro=e=>{const t=e.closest(yn.selector);return t instanceof HTMLElement?t:document},ke=Jn;ke.ScrollTo=Lo;ke.forNode=Ro;const pn={childList:!0,subtree:!0,characterData:!0,attributeFilter:["style"]};class er extends u.PureComponent{constructor(t){super(t),this.state={measuring:!0,activatorRect:We(this.props.activator),right:void 0,left:void 0,top:0,height:0,width:null,positioning:"below",zIndex:null,outsideScrollableContainer:!1,lockPosition:!1,chevronOffset:0},this.overlay=null,this.scrollableContainers=[],this.overlayDetails=()=>{const{measuring:n,left:o,right:a,positioning:i,height:s,activatorRect:l,chevronOffset:c}=this.state;return{measuring:n,left:o,right:a,desiredHeight:s,positioning:i,activatorRect:l,chevronOffset:c}},this.setOverlay=n=>{this.overlay=n},this.setScrollableContainers=()=>{const n=[];let o=ke.forNode(this.props.activator);if(o)for(n.push(o);o!=null&&o.parentElement;)o=ke.forNode(o.parentElement),n.push(o);this.scrollableContainers=n},this.registerScrollHandlers=()=>{this.scrollableContainers.forEach(n=>{n.addEventListener("scroll",this.handleMeasurement)})},this.unregisterScrollHandlers=()=>{this.scrollableContainers.forEach(n=>{n.removeEventListener("scroll",this.handleMeasurement)})},this.handleMeasurement=()=>{const{lockPosition:n,top:o}=this.state;this.observer.disconnect(),this.setState(({left:a,top:i,right:s})=>({left:a,right:s,top:i,height:0,positioning:"below",measuring:!0}),()=>{if(this.overlay==null||this.firstScrollableContainer==null)return;const{activator:a,preferredPosition:i="below",preferredAlignment:s="center",onScrollOut:l,fullWidth:c,fixed:d,preferInputActivator:p=!0}=this.props,h=p&&a.querySelector("input")||a,f=We(h),m=We(this.overlay),b=Ho(this.firstScrollableContainer)?document.body:this.firstScrollableContainer,P=We(b),E=c||i==="cover"?new Lt({...m,width:f.width}):m;b===document.body&&(P.height=document.body.scrollHeight);let T=0;const S=b.querySelector(`${qr.selector}`);S&&(T=S.clientHeight);const w=this.overlay.firstElementChild&&this.overlay.firstChild instanceof HTMLElement?Wo(this.overlay.firstElementChild):{activator:0,container:0,horizontal:0},A=Xn(),N=$o(a),C=N==null?N:N+1,g=No(f,E,w,P,A,i,d,T),v=ko(f,E,A,w,s),I=f.center.x-v+w.horizontal*2;this.setState({measuring:!1,activatorRect:We(a),left:s!=="right"?v:void 0,right:s==="right"?v:void 0,top:n?o:g.top,lockPosition:!!d,height:g.height||0,width:c||i==="cover"?E.width:null,positioning:g.positioning,outsideScrollableContainer:l!=null&&Bo(f,_o(P)),zIndex:C,chevronOffset:I},()=>{this.overlay&&(this.observer.observe(this.overlay,pn),this.observer.observe(a,pn))})})},this.observer=new MutationObserver(this.handleMeasurement)}componentDidMount(){this.setScrollableContainers(),this.scrollableContainers.length&&!this.props.fixed&&this.registerScrollHandlers(),this.handleMeasurement()}componentWillUnmount(){this.observer.disconnect(),this.scrollableContainers.length&&!this.props.fixed&&this.unregisterScrollHandlers()}componentDidUpdate(){const{outsideScrollableContainer:t,top:n}=this.state,{onScrollOut:o,active:a}=this.props;a&&o!=null&&n!==0&&t&&o()}render(){const{left:t,right:n,top:o,zIndex:a,width:i}=this.state,{render:s,fixed:l,preventInteraction:c,classNames:d,zIndexOverride:p}=this.props,h={top:o==null||isNaN(o)?void 0:o,left:t==null||isNaN(t)?void 0:t,right:n==null||isNaN(n)?void 0:n,width:i==null||isNaN(i)?void 0:i,zIndex:p||a||void 0},f=y(wt.PositionedOverlay,l&&wt.fixed,c&&wt.preventInteraction,d);return r.createElement("div",{className:f,style:h,ref:this.setOverlay},r.createElement(ct,{event:"resize",handler:this.handleMeasurement}),s(this.overlayDetails()))}get firstScrollableContainer(){return this.scrollableContainers[0]??null}forceUpdatePosition(){requestAnimationFrame(this.handleMeasurement)}}function Wo(e){const t=window.getComputedStyle(e);return{activator:parseFloat(t.marginTop||"0"),container:parseFloat(t.marginBottom||"0"),horizontal:parseFloat(t.marginLeft||"0")}}function $o(e){const t=e.closest(wn.selector)||document.body,n=t===document.body?"auto":parseInt(window.getComputedStyle(t).zIndex||"0",10);return n==="auto"||isNaN(n)?null:n}function Ho(e){return e===document}const Do=r.createElement(r.Fragment,null,r.createElement("path",{d:"M18.829 8.171 11.862.921A3 3 0 0 0 7.619.838L0 8.171h1.442l6.87-6.612a2 2 0 0 1 2.83.055l6.3 6.557h1.387Z",fill:"var(--p-color-tooltip-tail-up-border-experimental)"}),r.createElement("path",{d:"M17.442 10.171h-16v-2l6.87-6.612a2 2 0 0 1 2.83.055l6.3 6.557v2Z",fill:"var(--p-color-bg-surface)"})),zo=r.createElement(r.Fragment,null,r.createElement("path",{d:"m0 2 6.967 7.25a3 3 0 0 0 4.243.083L18.829 2h-1.442l-6.87 6.612a2 2 0 0 1-2.83-.055L1.387 2H0Z",fill:"var(--p-color-tooltip-tail-down-border-experimental)"}),r.createElement("path",{d:"M1.387 0h16v2l-6.87 6.612a2 2 0 0 1-2.83-.055L1.387 2V0Z",fill:"var(--p-color-bg-surface)"}));function jo({active:e,activator:t,preferredPosition:n="above",preventInteraction:o,id:a,children:i,accessibilityLabel:s,width:l,padding:c,borderRadius:d,zIndexOverride:p,instant:h}){const f=fe();return e?r.createElement(er,{active:e,activator:t,preferredPosition:n,preventInteraction:o,render:b,zIndexOverride:p}):null;function b(P){const{measuring:E,desiredHeight:T,positioning:S,chevronOffset:w}=P,A=y(xe.TooltipOverlay,E&&xe.measuring,!E&&xe.measured,h&&xe.instant,S==="above"&&xe.positionedAbove),N=y(xe.Content,l&&xe[l]),C=E?void 0:{minHeight:T},g={"--pc-tooltip-chevron-x-pos":`${w}px`,"--pc-tooltip-border-radius":d?`var(--p-border-radius-${d})`:void 0,"--pc-tooltip-padding":c&&c==="default"?"var(--p-space-100) var(--p-space-200)":`var(--p-space-${c})`};return r.createElement("div",Object.assign({style:g,className:A},wn.props),r.createElement("svg",{className:xe.Tail,width:"19",height:"11",fill:"none"},S==="above"?zo:Do),r.createElement("div",{id:a,role:"tooltip",className:N,style:{...C,...g},"aria-label":s?f.translate("Polaris.TooltipOverlay.accessibilityLabel",{label:s}):void 0},i))}}const Go=150;function qe({children:e,content:t,dismissOnMouseOut:n,active:o,hoverDelay:a,preferredPosition:i="above",activatorWrapper:s="span",accessibilityLabel:l,width:c="default",padding:d="default",borderRadius:p,zIndexOverride:h,hasUnderline:f,persistOnClick:m,onOpen:b,onClose:P}){const E=p||"200",T=s,{value:S,setTrue:w,setFalse:A}=Ze(!!o),{value:N,toggle:C}=Ze(!!o&&!!m),[g,v]=u.useState(null),{presenceList:I,addPresence:M,removePresence:H}=Ao(),L=u.useId(),R=u.useRef(null),k=u.useRef(!1),[F,$]=u.useState(!o),O=u.useRef(null),Q=u.useRef(null),Y=u.useCallback(()=>{o!==!1&&w()},[o,w]);u.useEffect(()=>{const ne=(R.current?$n(R.current):null)||R.current;ne&&(ne.tabIndex=0,ne.setAttribute("aria-describedby",L),ne.setAttribute("data-polaris-tooltip-activator","true"))},[L,e]),u.useEffect(()=>()=>{O.current&&clearTimeout(O.current),Q.current&&clearTimeout(Q.current)},[]);const oe=u.useCallback(()=>{$(!I.tooltip&&!S),b==null||b(),M("tooltip")},[M,I.tooltip,b,S]),G=u.useCallback(()=>{P==null||P(),$(!1),Q.current=setTimeout(()=>{H("tooltip")},Go)},[H,P]),J=u.useCallback(ie=>{ie.key==="Escape"&&(G==null||G(),A(),m&&C())},[A,G,m,C]);u.useEffect(()=>{o===!1&&S&&(G(),A())},[o,S,G,A]);const ae=g?r.createElement(qn,{idPrefix:"tooltip"},r.createElement(jo,{id:L,preferredPosition:i,activator:g,active:S,accessibilityLabel:l,onClose:Uo,preventInteraction:n,width:c,padding:d,borderRadius:E,zIndexOverride:h,instant:!F},r.createElement(W,{as:"span",variant:"bodyMd"},t))):null,K=y(s==="div"&&cn.TooltipContainer,f&&cn.HasUnderline);return r.createElement(T,{onFocus:()=>{oe(),Y()},onBlur:()=>{G(),A(),m&&C()},onMouseLeave:Z,onMouseOver:Ee,onMouseDown:m?C:void 0,ref:ce,onKeyUp:J,className:K},e,ae);function ce(ie){const ne=R;if(ie==null){ne.current=null,v(null);return}ie.firstElementChild instanceof HTMLElement&&v(ie.firstElementChild),ne.current=ie}function he(){k.current=!0,a&&!I.tooltip?O.current=setTimeout(()=>{oe(),Y()},a):(oe(),Y())}function Z(){O.current&&(clearTimeout(O.current),O.current=null),k.current=!1,G(),N||A()}function Ee(){!k.current&&he()}}function Uo(){}function tr({id:e,badge:t,content:n,accessibilityLabel:o,helpText:a,url:i,onAction:s,onMouseEnter:l,icon:c,image:d,prefix:p,suffix:h,disabled:f,external:m,destructive:b,ellipsis:P,truncate:E,active:T,role:S,variant:w="default"}){const A=y(se.Item,f&&se.disabled,b&&se.destructive,T&&se.active,w==="default"&&se.default,w==="indented"&&se.indented,w==="menu"&&se.menu);let N=null;p?N=r.createElement("span",{className:se.Prefix},p):c?N=r.createElement("span",{className:se.Prefix},r.createElement(me,{source:c})):d&&(N=r.createElement("span",{role:"presentation",className:se.Prefix,style:{backgroundImage:`url(${d}`}}));let C=n||"";E&&n?C=r.createElement(Vo,null,n):P&&(C=`${n}…`);const g=a?r.createElement(r.Fragment,null,r.createElement(z,null,C),r.createElement(W,{as:"span",variant:"bodySm",tone:T||f?void 0:"subdued"},a)):r.createElement(W,{as:"span",variant:"bodyMd",fontWeight:T?"semibold":"regular"},C),v=t&&r.createElement("span",{className:se.Suffix},r.createElement(Zn,{tone:t.tone},t.content)),I=h&&r.createElement(z,null,r.createElement("span",{className:se.Suffix},h)),M=r.createElement("span",{className:se.Text},r.createElement(W,{as:"span",variant:"bodyMd",fontWeight:T?"semibold":"regular"},g)),H=r.createElement(Ve,{blockAlign:"center",gap:"150",wrap:!1},N,M,v,I),L=r.createElement(z,{width:"100%"},H),R=T?r.createElement(ke.ScrollTo,null):null,k=i?r.createElement(Gn,{id:e,url:f?null:i,className:A,external:m,"aria-label":o,onClick:f?null:s,role:S},L):r.createElement("button",{id:e,type:"button",className:A,disabled:f,"aria-label":o,onClick:s,onMouseUp:pt,role:S,onMouseEnter:l},L);return r.createElement(r.Fragment,null,R,k)}const Vo=({children:e})=>{const t=Xr(),n=u.useRef(null),[o,a]=u.useState(!1);return Ue(()=>{n.current&&a(n.current.scrollWidth>n.current.offsetWidth)},[e]),o?r.createElement(qe,{zIndexOverride:Number(t.zIndex["z-index-11"]),preferredPosition:"above",hoverDelay:1e3,content:e,dismissOnMouseOut:!0},r.createElement(W,{as:"span",truncate:!0},e)):r.createElement(W,{as:"span",truncate:!0},r.createElement(z,{width:"100%",ref:n},e))};function Zo({section:e,hasMultipleSections:t,isFirst:n,actionRole:o,onActionAnyItem:a}){const i=p=>()=>{p&&p(),a&&a()},s=e.items.map(({content:p,helpText:h,onAction:f,...m},b)=>{const P=r.createElement(tr,Object.assign({content:p,helpText:h,role:o,onAction:i(f)},m));return r.createElement(z,{as:"li",key:`${p}-${b}`,role:o==="menuitem"?"presentation":void 0},r.createElement(Ve,{wrap:!1},P))});let l=null;e.title&&(l=typeof e.title=="string"?r.createElement(z,{paddingBlockStart:"300",paddingBlockEnd:"100",paddingInlineStart:"300",paddingInlineEnd:"300"},r.createElement(W,{as:"p",variant:"headingSm"},e.title)):r.createElement(z,{padding:"200",paddingInlineEnd:"150"},e.title));let c;switch(o){case"option":c="presentation";break;case"menuitem":c=t?"presentation":"menu";break;default:c=void 0;break}const d=r.createElement(r.Fragment,null,l,r.createElement(z,Object.assign({as:"div",padding:"150"},t&&{paddingBlockStart:"0"},{tabIndex:t?void 0:-1}),r.createElement(xo,Object.assign({gap:"050",as:"ul"},c&&{role:c}),s)));return t?r.createElement(z,Object.assign({as:"li",role:"presentation",borderColor:"border-secondary"},!n&&{borderBlockStartWidth:"025"},!e.title&&{paddingBlockStart:"150"}),d):d}function Xe({keyCode:e,handler:t,keyEvent:n="keyup",options:o,useCapture:a}){const i=u.useRef({handler:t,keyCode:e});Ue(()=>{i.current={handler:t,keyCode:e}},[t,e]);const s=u.useCallback(l=>{const{handler:c,keyCode:d}=i.current;l.keyCode===d&&c(l)},[]);return u.useEffect(()=>(document.addEventListener(n,s,a||o),()=>{document.removeEventListener(n,s,a||o)}),[n,s,a,o]),null}var B={TextField:"Polaris-TextField",ClearButton:"Polaris-TextField__ClearButton",Loading:"Polaris-TextField__Loading",disabled:"Polaris-TextField--disabled",error:"Polaris-TextField--error",readOnly:"Polaris-TextField--readOnly",Input:"Polaris-TextField__Input",Backdrop:"Polaris-TextField__Backdrop",multiline:"Polaris-TextField--multiline",hasValue:"Polaris-TextField--hasValue",focus:"Polaris-TextField--focus",VerticalContent:"Polaris-TextField__VerticalContent",InputAndSuffixWrapper:"Polaris-TextField__InputAndSuffixWrapper",toneMagic:"Polaris-TextField--toneMagic",Prefix:"Polaris-TextField__Prefix",Suffix:"Polaris-TextField__Suffix",AutoSizeWrapper:"Polaris-TextField__AutoSizeWrapper",AutoSizeWrapperWithSuffix:"Polaris-TextField__AutoSizeWrapperWithSuffix",suggestion:"Polaris-TextField--suggestion",borderless:"Polaris-TextField--borderless",slim:"Polaris-TextField--slim","Input-hasClearButton":"Polaris-TextField__Input--hasClearButton","Input-suffixed":"Polaris-TextField__Input--suffixed","Input-alignRight":"Polaris-TextField__Input--alignRight","Input-alignLeft":"Polaris-TextField__Input--alignLeft","Input-alignCenter":"Polaris-TextField__Input--alignCenter","Input-autoSize":"Polaris-TextField__Input--autoSize",PrefixIcon:"Polaris-TextField__PrefixIcon",CharacterCount:"Polaris-TextField__CharacterCount",AlignFieldBottom:"Polaris-TextField__AlignFieldBottom",Spinner:"Polaris-TextField__Spinner",SpinnerIcon:"Polaris-TextField__SpinnerIcon",Resizer:"Polaris-TextField__Resizer",DummyInput:"Polaris-TextField__DummyInput",Segment:"Polaris-TextField__Segment",monospaced:"Polaris-TextField--monospaced"};const qo=r.forwardRef(function({onChange:t,onClick:n,onMouseDown:o,onMouseUp:a,onBlur:i},s){function l(d){return()=>t(d)}function c(d){return p=>{p.button===0&&(o==null||o(d))}}return r.createElement("div",{className:B.Spinner,onClick:n,"aria-hidden":!0,ref:s},r.createElement("div",{role:"button",className:B.Segment,tabIndex:-1,onClick:l(1),onMouseDown:c(l(1)),onMouseUp:a,onBlur:i},r.createElement("div",{className:B.SpinnerIcon},r.createElement(me,{source:Wt}))),r.createElement("div",{role:"button",className:B.Segment,tabIndex:-1,onClick:l(-1),onMouseDown:c(l(-1)),onMouseUp:a,onBlur:i},r.createElement("div",{className:B.SpinnerIcon},r.createElement(me,{source:Rt}))))});var Te={hidden:"Polaris-Labelled--hidden",LabelWrapper:"Polaris-Labelled__LabelWrapper",disabled:"Polaris-Labelled--disabled",HelpText:"Polaris-Labelled__HelpText",readOnly:"Polaris-Labelled--readOnly",Error:"Polaris-Labelled__Error",Action:"Polaris-Labelled__Action"},mn={InlineError:"Polaris-InlineError",Icon:"Polaris-InlineError__Icon"};function Xo({message:e,fieldID:t}){return e?r.createElement("div",{id:Qo(t),className:mn.InlineError},r.createElement("div",{className:mn.Icon},r.createElement(me,{source:Bn})),r.createElement(W,{as:"span",variant:"bodyMd"},e)):null}function Qo(e){return`${e}Error`}var tt={Label:"Polaris-Label",hidden:"Polaris-Label--hidden",Text:"Polaris-Label__Text",RequiredIndicator:"Polaris-Label__RequiredIndicator"};function nr(e){return`${e}Label`}function Yo({children:e,id:t,hidden:n,requiredIndicator:o}){const a=y(tt.Label,n&&tt.hidden);return r.createElement("div",{className:a},r.createElement("label",{id:nr(t),htmlFor:t,className:y(tt.Text,o&&tt.RequiredIndicator)},r.createElement(W,{as:"span",variant:"bodyMd"},e)))}function Jo({id:e,label:t,error:n,action:o,helpText:a,children:i,labelHidden:s,requiredIndicator:l,disabled:c,readOnly:d,...p}){const h=y(s&&Te.hidden,c&&Te.disabled,d&&Te.readOnly),f=o?r.createElement("div",{className:Te.Action},dt(o,{variant:"plain"})):null,m=a?r.createElement("div",{className:Te.HelpText,id:rr(e),"aria-disabled":c},r.createElement(W,{as:"span",tone:"subdued",variant:"bodyMd",breakWord:!0},a)):null,b=n&&typeof n!="boolean"&&r.createElement("div",{className:Te.Error},r.createElement(Xo,{message:n,fieldID:e})),P=t?r.createElement("div",{className:Te.LabelWrapper},r.createElement(Yo,Object.assign({id:e,requiredIndicator:l},p,{hidden:!1}),t),f):null;return r.createElement("div",{className:h},P,i,b,m)}function rr(e){return`${e}HelpText`}var De={Connected:"Polaris-Connected",Item:"Polaris-Connected__Item","Item-primary":"Polaris-Connected__Item--primary","Item-focused":"Polaris-Connected__Item--focused"};function Nt({children:e,position:t}){const{value:n,setTrue:o,setFalse:a}=Ze(!1),i=y(De.Item,n&&De["Item-focused"],t==="primary"?De["Item-primary"]:De["Item-connection"]);return r.createElement("div",{onBlur:a,onFocus:o,className:i},e)}function Ko({children:e,left:t,right:n}){const o=t?r.createElement(Nt,{position:"left"},t):null,a=n?r.createElement(Nt,{position:"right"},n):null;return r.createElement("div",{className:De.Connected},o,r.createElement(Nt,{position:"primary"},e),a)}function ea({contents:e,currentHeight:t=null,minimumLines:n,onHeightChange:o}){const a=u.useRef(null),i=u.useRef(null),s=u.useRef(),l=u.useRef(t);t!==l.current&&(l.current=t),u.useEffect(()=>()=>{s.current&&cancelAnimationFrame(s.current)},[]);const c=n?r.createElement("div",{ref:i,className:B.DummyInput,dangerouslySetInnerHTML:{__html:ra(n)}}):null,d=u.useCallback(()=>{s.current&&cancelAnimationFrame(s.current),s.current=requestAnimationFrame(()=>{if(!a.current||!i.current)return;const p=Math.max(a.current.offsetHeight,i.current.offsetHeight);p!==l.current&&o(p)})},[o]);return Ue(()=>{d()}),r.createElement("div",{"aria-hidden":!0,className:B.Resizer},r.createElement(ct,{event:"resize",handler:d}),r.createElement("div",{ref:a,className:B.DummyInput,dangerouslySetInnerHTML:{__html:oa(e)}}),c)}const or={"&":"&amp;","<":"&lt;",">":"&gt;","\n":"<br>","\r":""},ta=new RegExp(`[${Object.keys(or).join()}]`,"g");function na(e){return or[e]}function ra(e){let t="";for(let n=0;n<e;n++)t+="<br>";return t}function oa(e){return e?`${e.replace(ta,na)}<br>`:"<br>"}function aa({prefix:e,suffix:t,verticalContent:n,placeholder:o,value:a="",helpText:i,label:s,labelAction:l,labelHidden:c,disabled:d,clearButton:p,readOnly:h,autoFocus:f,focused:m,multiline:b,error:P,connectedRight:E,connectedLeft:T,type:S="text",name:w,id:A,role:N,step:C,largeStep:g,autoComplete:v,max:I,maxLength:M,maxHeight:H,min:L,minLength:R,pattern:k,inputMode:F,spellCheck:$,ariaOwns:O,ariaControls:Q,ariaExpanded:Y,ariaActiveDescendant:oe,ariaAutocomplete:G,showCharacterCount:J,align:ae,requiredIndicator:K,monospaced:ce,selectTextOnFocus:he,suggestion:Z,variant:Ee="inherit",size:ie="medium",onClearButtonClick:ne,onChange:re,onSpinnerChange:ue,onFocus:Be,onBlur:Qe,tone:Ht,autoSize:ft,loading:dr}){const ht=fe(),[gt,pr]=u.useState(null),[_e,bt]=u.useState(!!m),mr=Ft(),fr=u.useId(),D=A??fr,Dt=u.useRef(null),ve=u.useRef(null),zt=u.useRef(null),vt=u.useRef(null),Pt=u.useRef(null),Et=u.useRef(null),Ye=u.useRef(null),xt=u.useRef(),St=u.useRef(null),Ie=u.useCallback(()=>b?zt.current:ve.current,[b]);u.useEffect(()=>{const x=Ie();!x||m===void 0||(m?x.focus():x.blur())},[m,n,Ie]),u.useEffect(()=>{const x=ve.current;!x||!(S==="text"||S==="tel"||S==="search"||S==="url"||S==="password")||!Z||x.setSelectionRange(a.length,Z.length)},[_e,a,S,Z]);const Me=Z||a,jt=C??1,Gt=I??1/0,Ut=L??-1/0,hr=y(B.TextField,!!Me&&B.hasValue,d&&B.disabled,h&&B.readOnly,P&&B.error,Ht&&B[X("tone",Ht)],b&&B.multiline,_e&&!d&&B.focus,Ee!=="inherit"&&B[Ee],ie==="slim"&&B.slim),gr=S==="currency"?"text":S,Je=S==="number"||S==="integer",br=r.isValidElement(e)&&e.type===me,vr=e?r.createElement("div",{className:y(B.Prefix,br&&B.PrefixIcon),id:`${D}-Prefix`,ref:vt},r.createElement(W,{as:"span",variant:"bodyMd"},e)):null,Vt=t?r.createElement("div",{className:B.Suffix,id:`${D}-Suffix`,ref:Pt},r.createElement(W,{as:"span",variant:"bodyMd"},t)):null,Pr=dr?r.createElement("div",{className:B.Loading,id:`${D}-Loading`,ref:Et},r.createElement(jn,{size:"small"})):null;let Zt=null;if(J){const x=Me.length,_=M?ht.translate("Polaris.TextField.characterCountWithMaxLength",{count:x,limit:M}):ht.translate("Polaris.TextField.characterCount",{count:x}),q=y(B.CharacterCount,b&&B.AlignFieldBottom),ee=M?`${x}/${M}`:x;Zt=r.createElement("div",{id:`${D}-CharacterCounter`,className:q,"aria-label":_,"aria-live":_e?"polite":"off","aria-atomic":"true",onClick:Ke},r.createElement(W,{as:"span",variant:"bodyMd"},ee))}const Er=p&&Me!==""?r.createElement("button",{type:"button",className:B.ClearButton,onClick:_r,disabled:d},r.createElement(W,{as:"span",visuallyHidden:!0},ht.translate("Polaris.Common.clear")),r.createElement(me,{source:Wn,tone:"base"})):null,Fe=u.useCallback((x,_=jt)=>{if(re==null&&ue==null)return;const q=Lr=>(Lr.toString().split(".")[1]||[]).length,ee=a?parseFloat(a):0;if(isNaN(ee))return;const ge=S==="integer"?0:Math.max(q(ee),q(_)),Re=Math.min(Number(Gt),Math.max(ee+x*_,Number(Ut)));ue!=null?ue(String(Re.toFixed(ge)),D):re!=null&&re(String(Re.toFixed(ge)),D)},[D,Gt,Ut,re,ue,jt,S,a]),Ct=u.useCallback(()=>{clearTimeout(xt.current)},[]),xr=u.useCallback(x=>{let ee=200;const ge=()=>{ee>50&&(ee-=10),x(0),xt.current=window.setTimeout(ge,ee)};xt.current=window.setTimeout(ge,ee),document.addEventListener("mouseup",Ct,{once:!0})},[Ct]),Sr=Je&&C!==0&&!d&&!h?r.createElement(qo,{onClick:Ke,onChange:Fe,onMouseDown:xr,onMouseUp:Ct,ref:St,onBlur:Jt}):null,Cr=b&&gt?{height:gt,maxHeight:H}:null,Tr=u.useCallback(x=>{pr(x)},[]),Ir=b&&mr?r.createElement(ea,{contents:Me||o,currentHeight:gt,minimumLines:typeof b=="number"?b:1,onHeightChange:Tr}):null,Le=[];P&&Le.push(`${D}Error`),i&&Le.push(rr(D)),J&&Le.push(`${D}-CharacterCounter`);const Oe=[];e&&Oe.push(`${D}-Prefix`),t&&Oe.push(`${D}-Suffix`),n&&Oe.push(`${D}-VerticalContent`),Oe.unshift(nr(D));const Ar=y(B.Input,ae&&B[X("Input-align",ae)],t&&B["Input-suffixed"],p&&B["Input-hasClearButton"],ce&&B.monospaced,Z&&B.suggestion,ft&&B["Input-autoSize"]),qt=x=>{if(bt(!0),he&&!Z){const _=Ie();_==null||_.select()}Be&&Be(x)};kn("wheel",yr,ve);function yr(x){document.activeElement===x.target&&Je&&x.stopPropagation()}const Xt=u.createElement(b?"textarea":"input",{name:w,id:D,disabled:d,readOnly:h,role:N,autoFocus:f,value:Me,placeholder:o,style:Cr,autoComplete:v,className:Ar,ref:b?zt:ve,min:L,max:I,step:C,minLength:R,maxLength:M,spellCheck:$,pattern:k,inputMode:F,type:gr,rows:ia(b),size:ft?1:void 0,"aria-describedby":Le.length?Le.join(" "):void 0,"aria-labelledby":Oe.join(" "),"aria-invalid":!!P,"aria-owns":O,"aria-activedescendant":oe,"aria-autocomplete":G,"aria-controls":Q,"aria-expanded":Y,"aria-required":K,...sa(b),onFocus:qt,onBlur:Jt,onClick:Ke,onKeyPress:Mr,onKeyDown:Fr,onChange:Z?void 0:Yt,onInput:Z?Yt:void 0,"data-1p-ignore":v==="off"||void 0,"data-lpignore":v==="off"||void 0,"data-form-type":v==="off"?"other":void 0}),wr=n?r.createElement("div",{className:B.VerticalContent,id:`${D}-VerticalContent`,ref:Ye,onClick:Ke},n,Xt):null,Qt=n?wr:Xt,Nr=r.createElement("div",{className:y(B.Backdrop,T&&B["Backdrop-connectedLeft"],E&&B["Backdrop-connectedRight"])}),kr=ft?r.createElement("div",{className:B.InputAndSuffixWrapper},r.createElement("div",{className:y(B.AutoSizeWrapper,t&&B.AutoSizeWrapperWithSuffix),"data-auto-size-value":a||o},Qt),Vt):r.createElement(r.Fragment,null,Qt,Vt);return r.createElement(Jo,{label:s,id:D,error:P,action:l,labelHidden:c,helpText:i,requiredIndicator:K,disabled:d,readOnly:h},r.createElement(Ko,{left:T,right:E},r.createElement("div",{className:hr,onClick:Br,ref:Dt},vr,kr,Zt,Pr,Er,Sr,Nr,Ir)));function Yt(x){re&&re(x.currentTarget.value,D)}function Br(x){var ee,ge,Re;const{target:_}=x,q=(ee=ve==null?void 0:ve.current)==null?void 0:ee.getAttribute("role");if(_===ve.current&&q==="combobox"){(ge=ve.current)==null||ge.focus(),qt(x);return}Kt(_)||nn(_)||Tt(_)||en(_)||tn(_)||_e||(Re=Ie())==null||Re.focus()}function Ke(x){var _;!en(x.target)&&!Tt(x.target)&&x.stopPropagation(),!(Kt(x.target)||nn(x.target)||Tt(x.target)||tn(x.target)||_e)&&(bt(!0),(_=Ie())==null||_.focus())}function _r(){ne&&ne(D)}function Mr(x){const{key:_,which:q}=x,ee=/[\d.,eE+-]$/,ge=/[\deE+-]$/;!Je||q===de.Enter||S==="number"&&ee.test(_)||S==="integer"&&ge.test(_)||x.preventDefault()}function Fr(x){if(!Je)return;const{key:_,which:q}=x;S==="integer"&&(_==="ArrowUp"||q===de.UpArrow)&&(Fe(1),x.preventDefault()),S==="integer"&&(_==="ArrowDown"||q===de.DownArrow)&&(Fe(-1),x.preventDefault()),(q===de.Home||_==="Home")&&L!==void 0&&(ue!=null?ue(String(L),D):re!=null&&re(String(L),D)),(q===de.End||_==="End")&&I!==void 0&&(ue!=null?ue(String(I),D):re!=null&&re(String(I),D)),(q===de.PageUp||_==="PageUp")&&g!==void 0&&Fe(1,g),(q===de.PageDown||_==="PageDown")&&g!==void 0&&Fe(-1,g)}function Jt(x){var _;bt(!1),!((_=Dt.current)!=null&&_.contains(x==null?void 0:x.relatedTarget))&&Qe&&Qe(x)}function Tt(x){const _=Ie();return x instanceof HTMLElement&&_&&(_.contains(x)||_.contains(document.activeElement))}function Kt(x){return x instanceof Element&&(vt.current&&vt.current.contains(x)||Pt.current&&Pt.current.contains(x))}function en(x){return x instanceof Element&&St.current&&St.current.contains(x)}function tn(x){return x instanceof Element&&Et.current&&Et.current.contains(x)}function nn(x){return x instanceof Element&&Ye.current&&(Ye.current.contains(x)||Ye.current.contains(document.activeElement))}}function ia(e){if(e)return typeof e=="number"?e:1}function sa(e){if(e)return e||typeof e=="number"&&e>0?{"aria-multiline":!0}:void 0}const la=8;function $t({items:e,sections:t=[],actionRole:n,allowFiltering:o,onActionAnyItem:a}){const i=fe(),s=u.useContext(Un);let l=[];const c=u.useRef(null),[d,p]=u.useState("");e?l=[{items:e},...t]:t&&(l=t);const h=l==null?void 0:l.some(g=>g.items.some(v=>typeof v.content=="string")),f=l.length>1,m=f&&n==="menuitem"?"menu":void 0,b=f&&n==="menuitem"?-1:void 0,P=l==null?void 0:l.map(g=>({...g,items:g.items.filter(({content:v})=>typeof v=="string"?v==null?void 0:v.toLowerCase().includes(d.toLowerCase()):v)})),E=P.map((g,v)=>g.items.length>0?r.createElement(Zo,{key:typeof g.title=="string"?g.title:v,section:g,hasMultipleSections:f,actionRole:n,onActionAnyItem:a,isFirst:v===0}):null),T=g=>{g.preventDefault(),c.current&&g.target&&c.current.contains(g.target)&&co(c.current,g.target)},S=g=>{g.preventDefault(),c.current&&g.target&&c.current.contains(g.target)&&uo(c.current,g.target)},w=n==="menuitem"?r.createElement(r.Fragment,null,r.createElement(Xe,{keyEvent:"keydown",keyCode:de.DownArrow,handler:S}),r.createElement(Xe,{keyEvent:"keydown",keyCode:de.UpArrow,handler:T})):null,A=u.useMemo(()=>(P==null?void 0:P.reduce((v,I)=>v+I.items.length,0))||0,[P]),C=((l==null?void 0:l.reduce((g,v)=>g+v.items.length,0))||0)>=la;return r.createElement(r.Fragment,null,(o||s)&&C&&h&&r.createElement(z,{padding:"200",paddingBlockEnd:A>0?"0":"200"},r.createElement(aa,{clearButton:!0,labelHidden:!0,label:i.translate("Polaris.ActionList.SearchField.placeholder"),placeholder:i.translate("Polaris.ActionList.SearchField.placeholder"),autoComplete:"off",value:d,onChange:g=>p(g),prefix:r.createElement(me,{source:On}),onClearButtonClick:()=>p("")})),r.createElement(z,{as:f?"ul":"div",ref:c,role:m,tabIndex:b},w,E))}$t.Item=tr;var fn={ActionMenu:"Polaris-ActionMenu"},ca={RollupActivator:"Polaris-ActionMenu-RollupActions__RollupActivator"};function ua(e,{id:t,active:n=!1,ariaHaspopup:o,activatorDisabled:a=!1}){a||(e.tabIndex=e.tabIndex||0),e.setAttribute("aria-controls",t),e.setAttribute("aria-owns",t),e.setAttribute("aria-expanded",String(n)),e.setAttribute("data-state",n?"open":"closed"),o!=null&&e.setAttribute("aria-haspopup",String(o))}function ar(e,t,n){return e==null?null:ir(e,t)?e:r.createElement(t,n,e)}const da=fa;function ir(e,t){var s;if(e==null||!u.isValidElement(e)||typeof e.type=="string")return!1;const{type:n}=e,a=((s=e.props)==null?void 0:s.__type__)||n;return(Array.isArray(t)?t:[t]).some(l=>typeof a!="string"&&da(l,a))}function pa(e,t=()=>!0){return u.Children.toArray(e).filter(n=>u.isValidElement(n)&&t(n))}function ma({condition:e,wrapper:t,children:n}){return e?t(n):n}function kt({condition:e,children:t}){return e?t:null}function fa(e,t){const n=e.name,o=t.displayName;return e===t||!!n&&n===o}var j={Popover:"Polaris-Popover",PopoverOverlay:"Polaris-Popover__PopoverOverlay","PopoverOverlay-noAnimation":"Polaris-Popover__PopoverOverlay--noAnimation","PopoverOverlay-entering":"Polaris-Popover__PopoverOverlay--entering","PopoverOverlay-open":"Polaris-Popover__PopoverOverlay--open",measuring:"Polaris-Popover--measuring","PopoverOverlay-exiting":"Polaris-Popover__PopoverOverlay--exiting",fullWidth:"Polaris-Popover--fullWidth",Content:"Polaris-Popover__Content",positionedAbove:"Polaris-Popover--positionedAbove",positionedCover:"Polaris-Popover--positionedCover",ContentContainer:"Polaris-Popover__ContentContainer","Content-fullHeight":"Polaris-Popover__Content--fullHeight","Content-fluidContent":"Polaris-Popover__Content--fluidContent",Pane:"Polaris-Popover__Pane","Pane-fixed":"Polaris-Popover__Pane--fixed","Pane-subdued":"Polaris-Popover__Pane--subdued","Pane-captureOverscroll":"Polaris-Popover__Pane--captureOverscroll",Section:"Polaris-Popover__Section",FocusTracker:"Polaris-Popover__FocusTracker","PopoverOverlay-hideOnPrint":"Polaris-Popover__PopoverOverlay--hideOnPrint"};function sr({children:e}){return r.createElement("div",{className:j.Section},r.createElement(z,{paddingInlineStart:"300",paddingInlineEnd:"300",paddingBlockStart:"200",paddingBlockEnd:"150"},e))}function _t({captureOverscroll:e=!1,fixed:t,sectioned:n,children:o,height:a,subdued:i,onScrolledToBottom:s}){const l=y(j.Pane,t&&j["Pane-fixed"],i&&j["Pane-subdued"],e&&j["Pane-captureOverscroll"]),c=n?ar(o,sr,{}):o,d=a?{height:a,maxHeight:a,minHeight:a}:void 0;return t?r.createElement("div",{style:d,className:l},c):r.createElement(ke,{shadow:!0,className:l,style:d,onScrolledToBottom:s,scrollbarWidth:"thin"},c)}let Pe;(function(e){e[e.Click=0]="Click",e[e.EscapeKeypress=1]="EscapeKeypress",e[e.FocusOut=2]="FocusOut",e[e.ScrollOut=3]="ScrollOut"})(Pe||(Pe={}));var le;(function(e){e.Entering="entering",e.Entered="entered",e.Exiting="exiting",e.Exited="exited"})(le||(le={}));class lr extends u.PureComponent{constructor(t){super(t),this.state={transitionStatus:this.props.active?le.Entering:le.Exited},this.contentNode=u.createRef(),this.renderPopover=n=>{const{measuring:o,desiredHeight:a,positioning:i}=n,{id:s,children:l,sectioned:c,fullWidth:d,fullHeight:p,fluidContent:h,hideOnPrint:f,autofocusTarget:m,captureOverscroll:b}=this.props,P=i==="cover",E=y(j.Popover,o&&j.measuring,(d||P)&&j.fullWidth,f&&j["PopoverOverlay-hideOnPrint"],i&&j[X("positioned",i)]),T=o?void 0:{height:a},S=y(j.Content,p&&j["Content-fullHeight"],h&&j["Content-fluidContent"]);return r.createElement("div",Object.assign({className:E},Qr.props),r.createElement(ct,{event:"click",handler:this.handleClick}),r.createElement(ct,{event:"touchstart",handler:this.handleClick}),r.createElement(Xe,{keyCode:de.Escape,handler:this.handleEscape}),r.createElement("div",{className:j.FocusTracker,tabIndex:0,onFocus:this.handleFocusFirstItem}),r.createElement("div",{className:j.ContentContainer},r.createElement("div",{id:s,tabIndex:m==="none"?void 0:-1,className:S,style:T,ref:this.contentNode},ha(l,{captureOverscroll:b,sectioned:c}))),r.createElement("div",{className:j.FocusTracker,tabIndex:0,onFocus:this.handleFocusLastItem}))},this.handleClick=n=>{const o=n.target,{contentNode:a,props:{activator:i,onClose:s,preventCloseOnChildOverlayClick:l}}=this,c=n.composedPath(),d=l?ga(c,this.context.container):gn(c,a),p=hn(i,o);d||p||this.state.transitionStatus!==le.Entered||s(Pe.Click)},this.handleScrollOut=()=>{this.props.onClose(Pe.ScrollOut)},this.handleEscape=n=>{const o=n.target,{contentNode:a,props:{activator:i}}=this,s=n.composedPath(),l=gn(s,a),c=hn(i,o);(l||c)&&this.props.onClose(Pe.EscapeKeypress)},this.handleFocusFirstItem=()=>{this.props.onClose(Pe.FocusOut)},this.handleFocusLastItem=()=>{this.props.onClose(Pe.FocusOut)},this.overlayRef=u.createRef()}forceUpdatePosition(){var t;(t=this.overlayRef.current)==null||t.forceUpdatePosition()}changeTransitionStatus(t,n){this.setState({transitionStatus:t},n),this.contentNode.current&&this.contentNode.current.getBoundingClientRect()}componentDidMount(){this.props.active&&(this.focusContent(),this.changeTransitionStatus(le.Entered))}componentDidUpdate(t){this.props.active&&!t.active&&(this.focusContent(),this.changeTransitionStatus(le.Entering,()=>{this.clearTransitionTimeout(),this.enteringTimer=window.setTimeout(()=>{this.setState({transitionStatus:le.Entered})},parseInt(Yr.motion["motion-duration-100"],10))})),!this.props.active&&t.active&&(this.clearTransitionTimeout(),this.setState({transitionStatus:le.Exited}))}componentWillUnmount(){this.clearTransitionTimeout()}render(){const{active:t,activator:n,fullWidth:o,preferredPosition:a="below",preferredAlignment:i="center",preferInputActivator:s=!0,fixed:l,zIndexOverride:c}=this.props,{transitionStatus:d}=this.state;if(d===le.Exited&&!t)return null;const p=y(j.PopoverOverlay,d===le.Entering&&j["PopoverOverlay-entering"],d===le.Entered&&j["PopoverOverlay-open"],d===le.Exiting&&j["PopoverOverlay-exiting"],a==="cover"&&j["PopoverOverlay-noAnimation"]);return r.createElement(er,{ref:this.overlayRef,fullWidth:o,active:t,activator:n,preferInputActivator:s,preferredPosition:a,preferredAlignment:i,render:this.renderPopover.bind(this),fixed:l,onScrollOut:this.handleScrollOut,classNames:p,zIndexOverride:c})}clearTransitionTimeout(){this.enteringTimer&&window.clearTimeout(this.enteringTimer)}focusContent(){const{autofocusTarget:t="container"}=this.props;t==="none"||this.contentNode==null||requestAnimationFrame(()=>{if(this.contentNode.current==null)return;const n=Hn(this.contentNode.current);n&&t==="first-node"?n.focus({preventScroll:!0}):this.contentNode.current.focus({preventScroll:!0})})}}lr.contextType=An;function ha(e,t){const n=u.Children.toArray(e);return ir(n[0],_t)?n:ar(n,_t,t)}function hn(e,t){if(e===t)return!0;let n=t.parentNode;for(;n!=null;){if(n===e)return!0;n=n.parentNode}return!1}function gn(e,t){return t.current!=null&&e.includes(t.current)}function ga(e,t){return e.some(n=>n instanceof Node&&(t==null?void 0:t.contains(n)))}const ba=u.forwardRef(function({activatorWrapper:t="div",children:n,onClose:o,activator:a,preventFocusOnClose:i,active:s,fixed:l,ariaHaspopup:c,preferInputActivator:d=!0,zIndexOverride:p,...h},f){const[m,b]=u.useState(),P=u.useRef(null),E=u.useRef(null),T=t,S=u.useId();function w(){var g;(g=P.current)==null||g.forceUpdatePosition()}u.useImperativeHandle(f,()=>({forceUpdatePosition:w}));const A=u.useCallback(()=>{if(E.current==null)return;const v=$e(E.current)||E.current,I="disabled"in v&&!!v.disabled;ua(v,{id:S,active:s,ariaHaspopup:c,activatorDisabled:I})},[S,s,c]),N=g=>{if(o(g),!(E.current==null||i)){if(g===Pe.FocusOut&&m){const v=$e(m)||$e(E.current)||E.current;on(v,bn)||v.focus()}else if(g===Pe.EscapeKeypress&&m){const v=$e(m)||$e(E.current)||E.current;v?v.focus():on(v,bn)}}};u.useEffect(()=>{(!m&&E.current||m&&E.current&&!E.current.contains(m))&&b(E.current.firstElementChild),A()},[m,A]),u.useEffect(()=>{m&&E.current&&b(E.current.firstElementChild),A()},[m,A]);const C=m?r.createElement(qn,{idPrefix:"popover"},r.createElement(lr,Object.assign({ref:P,id:S,activator:m,preferInputActivator:d,onClose:N,active:s,fixed:l,zIndexOverride:p},h),n)):null;return r.createElement(T,{ref:E},u.Children.only(a),C)});function bn(e){let t=e.parentElement;for(;t;){if(t.matches(Jr.selector))return!1;t=t.parentElement}return!0}const cr=Object.assign(ba,{Pane:_t,Section:sr});function va({accessibilityLabel:e,items:t=[],sections:n=[]}){const o=fe(),{value:a,toggle:i}=Ze(!1);if(t.length===0&&n.length===0)return null;const s=r.createElement("div",{className:ca.RollupActivator},r.createElement(Ne,{icon:Ln,accessibilityLabel:e||o.translate("Polaris.ActionMenu.RollupActions.rollupButton"),onClick:i}));return r.createElement(cr,{active:a,activator:s,preferredAlignment:"right",onClose:i,hideOnPrint:!0},r.createElement($t,{items:t,sections:n,onActionAnyItem:i}))}var st={ActionsLayoutOuter:"Polaris-ActionMenu-Actions__ActionsLayoutOuter",ActionsLayout:"Polaris-ActionMenu-Actions__ActionsLayout","ActionsLayout--measuring":"Polaris-ActionMenu-Actions--actionsLayoutMeasuring",ActionsLayoutMeasurer:"Polaris-ActionMenu-Actions__ActionsLayoutMeasurer"};function vn(e=[],t=[],n,o,a){const i=o.reduce((f,m)=>f+m,0),s=e.map((f,m)=>m),l=t.map((f,m)=>m),c=[],d=[],p=[],h=[];if(a>i)c.push(...s),p.push(...l);else{let f=0;s.forEach(m=>{const b=o[m];if(f+b>=a-n){d.push(m);return}c.push(m),f+=b}),l.forEach(m=>{const b=o[m+e.length];if(f+b>=a-n){h.push(m);return}p.push(m),f+=b})}return{visibleActions:c,hiddenActions:d,visibleGroups:p,hiddenGroups:h}}var Pa={Details:"Polaris-ActionMenu-MenuGroup__Details"},Pn={SecondaryAction:"Polaris-ActionMenu-SecondaryAction",critical:"Polaris-ActionMenu-SecondaryAction--critical"};function je({children:e,tone:t,helpText:n,onAction:o,destructive:a,...i}){const s=r.createElement(Ne,Object.assign({onClick:o,tone:a?"critical":void 0},i),e),l=n?r.createElement(qe,{preferredPosition:"below",content:n},s):s;return r.createElement("div",{className:y(Pn.SecondaryAction,t==="critical"&&Pn.critical)},l)}function En({accessibilityLabel:e,active:t,actions:n,details:o,title:a,icon:i,disabled:s,onClick:l,onClose:c,onOpen:d,sections:p}){const h=u.useCallback(()=>{c(a)},[c,a]),f=u.useCallback(()=>{d(a)},[d,a]),m=u.useCallback(()=>{l?l(f):f()},[l,f]),b=r.createElement(je,{disclosure:!0,disabled:s,icon:i,accessibilityLabel:e,onClick:m},a);return r.createElement(cr,{active:!!t,activator:b,preferredAlignment:"left",onClose:h,hideOnPrint:!0},r.createElement($t,{items:n,sections:p,onActionAnyItem:h}),o&&r.createElement("div",{className:Pa.Details},o))}const Ea=8;function xa({actions:e=[],groups:t=[],handleMeasurement:n}){const o=fe(),a=u.useRef(null),i={title:o.translate("Polaris.ActionMenu.Actions.moreActions")},s=r.createElement(je,{disclosure:!0},i.title),l=u.useCallback(()=>{if(!a.current)return;const p=a.current.offsetWidth,h=a.current.children,m=Array.from(h).map(P=>Math.ceil(P.getBoundingClientRect().width)+Ea),b=m.pop()||0;n({containerWidth:p,disclosureWidth:b,hiddenActionsWidths:m})},[n]);u.useEffect(()=>{l()},[l,e,t]);const c=e.map(p=>{const{content:h,onAction:f,...m}=p;return r.createElement(je,Object.assign({key:h,onClick:f},m),h)}),d=t.map(p=>{const{title:h,icon:f}=p;return r.createElement(je,{key:h,disclosure:!0,icon:f},h)});return kn("resize",l),r.createElement("div",{className:st.ActionsLayoutMeasurer,ref:a},c,d,s)}function Sa({actions:e,groups:t,onActionRollup:n}){const o=fe(),a=u.useRef(null),[i,s]=u.useState(void 0),[l,c]=u.useReducer((k,F)=>({...k,...F}),{disclosureWidth:0,containerWidth:1/0,actionsWidths:[],visibleActions:[],hiddenActions:[],visibleGroups:[],hiddenGroups:[],hasMeasured:!1}),{visibleActions:d,hiddenActions:p,visibleGroups:h,hiddenGroups:f,containerWidth:m,disclosureWidth:b,actionsWidths:P,hasMeasured:E}=l,T={title:o.translate("Polaris.ActionMenu.Actions.moreActions"),actions:[]},S=u.useCallback(k=>s(i?void 0:k),[i]),w=u.useCallback(()=>s(void 0),[]);u.useEffect(()=>{if(m===0)return;const{visibleActions:k,visibleGroups:F,hiddenActions:$,hiddenGroups:O}=vn(e,t,b,P,m);c({visibleActions:k,visibleGroups:F,hiddenActions:$,hiddenGroups:O,hasMeasured:m!==1/0})},[m,b,e,t,P,c]);const A=u.useMemo(()=>e??[],[e]),N=u.useMemo(()=>t??[],[t]),C=A.filter((k,F)=>!!d.includes(F)).map(k=>{const{content:F,onAction:$,...O}=k;return r.createElement(je,Object.assign({key:F,onClick:$},O),F)}),v=(f.length>0||p.length>0?[...N,T]:[...N]).filter((k,F)=>{const $=N.length===0,O=h.includes(F),Q=k===T;return $?p.length>0:Q?!0:O}),I=p.map(k=>A[k]).filter(k=>k!=null),M=f.map(k=>N[k]).filter(k=>k!=null),H=v.map(k=>{const{title:F,actions:$,...O}=k,Q=k===T,Y=[...I,...M],[oe,G]=Y.reduce(([J,ae],K)=>(Ca(K)?ae.push({title:K.title,items:K.actions.map(ce=>({...ce,disabled:K.disabled||ce.disabled}))}):J.push(K),[J,ae]),[[],[]]);return Q?r.createElement(En,Object.assign({key:F,title:F,active:F===i,actions:[...oe,...$],sections:G},O,{onOpen:S,onClose:w})):r.createElement(En,Object.assign({key:F,title:F,active:F===i,actions:$},O,{onOpen:S,onClose:w}))}),L=u.useCallback(k=>{const{hiddenActionsWidths:F,containerWidth:$,disclosureWidth:O}=k,{visibleActions:Q,hiddenActions:Y,visibleGroups:oe,hiddenGroups:G}=vn(A,N,O,F,$);if(n){const J=Y.length>0||G.length>0;a.current!==J&&(n(J),a.current=J)}c({visibleActions:Q,hiddenActions:Y,visibleGroups:oe,hiddenGroups:G,actionsWidths:F,containerWidth:$,disclosureWidth:O,hasMeasured:!0})},[A,N,n]),R=r.createElement(xa,{actions:e,groups:t,handleMeasurement:L});return r.createElement("div",{className:st.ActionsLayoutOuter},R,r.createElement("div",{className:y(st.ActionsLayout,!E&&st["ActionsLayout--measuring"])},C,H))}function Ca(e){return"title"in e}function Ta({actions:e=[],groups:t=[],rollup:n,rollupActionsLabel:o,onActionRollup:a}){if(e.length===0&&t.length===0)return null;const i=y(fn.ActionMenu,n&&fn.rollup),s=t.map(l=>Aa(l));return r.createElement("div",{className:i},n?r.createElement(va,{accessibilityLabel:o,items:e,sections:s}):r.createElement(Sa,{actions:e,groups:t,onActionRollup:a}))}function Ia(e=[]){return e.length===0?!1:e.some(t=>t.actions.length>0)}function Aa({title:e,actions:t,disabled:n}){return{title:e,items:t.map(o=>({...o,disabled:n||o.disabled}))}}var Se={ButtonGroup:"Polaris-ButtonGroup",Item:"Polaris-ButtonGroup__Item","Item-plain":"Polaris-ButtonGroup__Item--plain",variantSegmented:"Polaris-ButtonGroup--variantSegmented","Item-focused":"Polaris-ButtonGroup__Item--focused",fullWidth:"Polaris-ButtonGroup--fullWidth",extraTight:"Polaris-ButtonGroup--extraTight",tight:"Polaris-ButtonGroup--tight",loose:"Polaris-ButtonGroup--loose",noWrap:"Polaris-ButtonGroup--noWrap"};function ya({button:e}){const{value:t,setTrue:n,setFalse:o}=Ze(!1),a=y(Se.Item,t&&Se["Item-focused"],e.props.variant==="plain"&&Se["Item-plain"]);return r.createElement("div",{className:a,onFocus:n,onBlur:o},e)}function wa({children:e,gap:t,variant:n,fullWidth:o,connectedTop:a,noWrap:i}){const s=y(Se.ButtonGroup,t&&Se[t],n&&Se[X("variant",n)],o&&Se.fullWidth,i&&Se.noWrap),l=pa(e).map((c,d)=>r.createElement(ya,{button:c,key:d}));return r.createElement("div",{className:s,"data-buttongroup-variant":n,"data-buttongroup-connected-top":a,"data-buttongroup-full-width":o,"data-buttongroup-no-wrap":i},l)}var Na={Bleed:"Polaris-Bleed"};const ka=({marginInline:e,marginBlock:t,marginBlockStart:n,marginBlockEnd:o,marginInlineStart:a,marginInlineEnd:i,children:s})=>{const l=m=>{const b=["marginInlineStart","marginInlineEnd"],P=["marginBlockStart","marginBlockEnd"],E={marginBlockStart:n,marginBlockEnd:o,marginInlineStart:a,marginInlineEnd:i,marginInline:e,marginBlock:t};if(E[m])return E[m];if(b.includes(m)&&e)return E.marginInline;if(P.includes(m)&&t)return E.marginBlock},c=l("marginBlockStart"),d=l("marginBlockEnd"),p=l("marginInlineStart"),h=l("marginInlineEnd"),f={...be("bleed","margin-block-start","space",c),...be("bleed","margin-block-end","space",d),...be("bleed","margin-inline-start","space",p),...be("bleed","margin-inline-end","space",h)};return r.createElement("div",{className:Na.Bleed,style:Ot(f)},s)};function Ba({backAction:e}){const{content:t}=e;return r.createElement(Ne,{key:t,url:"url"in e?e.url:void 0,onClick:"onAction"in e?e.onAction:void 0,onPointerDown:pt,icon:_n,accessibilityLabel:e.accessibilityLabel??t})}var we;(function(e){e.Input="INPUT",e.Textarea="TEXTAREA",e.Select="SELECT",e.ContentEditable="contenteditable"})(we||(we={}));function _a(){if(document==null||document.activeElement==null)return!1;const{tagName:e}=document.activeElement;return e===we.Input||e===we.Textarea||e===we.Select||document.activeElement.hasAttribute(we.ContentEditable)}var nt={Pagination:"Polaris-Pagination",table:"Polaris-Pagination--table",TablePaginationActions:"Polaris-Pagination__TablePaginationActions"};function Ma({hasNext:e,hasPrevious:t,nextURL:n,previousURL:o,onNext:a,onPrevious:i,nextTooltip:s,previousTooltip:l,nextKeys:c,previousKeys:d,accessibilityLabel:p,accessibilityLabels:h,label:f,type:m="page"}){const b=fe(),P=u.createRef(),E=p||b.translate("Polaris.Pagination.pagination"),T=(h==null?void 0:h.previous)||b.translate("Polaris.Pagination.previous"),S=(h==null?void 0:h.next)||b.translate("Polaris.Pagination.next"),w=r.createElement(Ne,{icon:Mn,accessibilityLabel:T,url:o,onClick:i,disabled:!t,id:"previousURL"}),A=l&&t?r.createElement(qe,{activatorWrapper:"span",content:l,preferredPosition:"below"},w):w,N=r.createElement(Ne,{icon:Fn,accessibilityLabel:S,url:n,onClick:a,disabled:!e,id:"nextURL"}),C=s&&e?r.createElement(qe,{activatorWrapper:"span",content:s,preferredPosition:"below"},N):N,g=i||Sn,v=d&&(o||i)&&t&&d.map(R=>r.createElement(Xe,{key:R,keyCode:R,handler:rt(o?xn("previousURL",P):g)})),I=a||Sn,M=c&&(n||a)&&e&&c.map(R=>r.createElement(Xe,{key:R,keyCode:R,handler:rt(n?xn("nextURL",P):I)}));if(m==="table"){const R=f?r.createElement(z,{padding:"300",paddingBlockStart:"0",paddingBlockEnd:"0"},r.createElement(W,{as:"span",variant:"bodySm",fontWeight:"medium"},f)):null;return r.createElement("nav",{"aria-label":E,ref:P,className:y(nt.Pagination,nt.table)},v,M,r.createElement(z,{background:"bg-surface-secondary",paddingBlockStart:"150",paddingBlockEnd:"150",paddingInlineStart:"300",paddingInlineEnd:"200"},r.createElement(Ve,{align:"center",blockAlign:"center"},r.createElement("div",{className:nt.TablePaginationActions,"data-buttongroup-variant":"segmented"},r.createElement("div",null,A),R,r.createElement("div",null,C)))))}const H=e&&t?r.createElement("span",null,f):r.createElement(W,{tone:"subdued",as:"span"},f),L=f?r.createElement(z,{padding:"300",paddingBlockStart:"0",paddingBlockEnd:"0"},r.createElement("div",{"aria-live":"polite"},H)):null;return r.createElement("nav",{"aria-label":E,ref:P,className:nt.Pagination},v,M,r.createElement(wa,{variant:"segmented"},A,L,C))}function xn(e,t){return()=>{if(t.current==null)return;const n=t.current.querySelector(`#${e}`);n&&n.click()}}function rt(e){return()=>{_a()||e()}}function Sn(){}function ur(){const e=u.useContext(Kr);if(!e)throw new Error("No mediaQuery was provided. Your application must be wrapped in an <AppProvider> component. See https://polaris.shopify.com/components/app-provider for implementation instructions.");return e}function Ge(e){return!u.isValidElement(e)&&e!==void 0}function lt(e){return u.isValidElement(e)&&e!==void 0}var ot={Page:"Polaris-Page",fullWidth:"Polaris-Page--fullWidth",narrowWidth:"Polaris-Page--narrowWidth",Content:"Polaris-Page__Content"},V={TitleWrapper:"Polaris-Page-Header__TitleWrapper",TitleWrapperExpand:"Polaris-Page-Header__TitleWrapperExpand",BreadcrumbWrapper:"Polaris-Page-Header__BreadcrumbWrapper",PaginationWrapper:"Polaris-Page-Header__PaginationWrapper",PrimaryActionWrapper:"Polaris-Page-Header__PrimaryActionWrapper",Row:"Polaris-Page-Header__Row",mobileView:"Polaris-Page-Header--mobileView",RightAlign:"Polaris-Page-Header__RightAlign",noBreadcrumbs:"Polaris-Page-Header--noBreadcrumbs",AdditionalMetaData:"Polaris-Page-Header__AdditionalMetaData",Actions:"Polaris-Page-Header__Actions",longTitle:"Polaris-Page-Header--longTitle",mediumTitle:"Polaris-Page-Header--mediumTitle",isSingleRow:"Polaris-Page-Header--isSingleRow"},ye={Title:"Polaris-Header-Title",TitleWithSubtitle:"Polaris-Header-Title__TitleWithSubtitle",TitleWrapper:"Polaris-Header-Title__TitleWrapper",SubTitle:"Polaris-Header-Title__SubTitle",SubtitleCompact:"Polaris-Header-Title__SubtitleCompact",SubtitleMaxWidth:"Polaris-Header-Title__SubtitleMaxWidth"};function Fa({title:e,subtitle:t,titleMetadata:n,compactTitle:o,hasSubtitleMaxWidth:a}){const i=y(ye.Title,t&&ye.TitleWithSubtitle),s=e?r.createElement("h1",{className:i},r.createElement(W,{as:"span",variant:"headingLg",fontWeight:"bold"},e)):null,l=n?r.createElement(ka,{marginBlock:"100"},n):null,c=r.createElement("div",{className:ye.TitleWrapper},s,l),d=t?r.createElement("div",{className:y(ye.SubTitle,o&&ye.SubtitleCompact,a&&ye.SubtitleMaxWidth)},r.createElement(W,{as:"p",variant:"bodySm",tone:"subdued"},t)):null;return r.createElement(r.Fragment,null,c,d)}const La=20,Oa=8,Cn=34;function Ra({title:e,subtitle:t,pageReadyAccessibilityLabel:n,titleMetadata:o,additionalMetadata:a,titleHidden:i=!1,primaryAction:s,pagination:l,filterActions:c,backAction:d,secondaryActions:p=[],actionGroups:h=[],compactTitle:f=!1,onActionRollup:m}){const b=fe(),{isNavigationCollapsed:P}=ur(),E=!s&&!l&&(Ge(p)&&!p.length||lt(p))&&!h.length,T=h.length>0||Ge(p)&&p.length>0||lt(p),S=d?r.createElement("div",{className:V.BreadcrumbWrapper},r.createElement(z,{maxWidth:"100%",paddingInlineEnd:"100",printHidden:!0},r.createElement(Ba,{backAction:d}))):null,w=l&&!P?r.createElement("div",{className:V.PaginationWrapper},r.createElement(z,{printHidden:!0},r.createElement(Ma,Object.assign({},l,{hasPrevious:l.hasPrevious,hasNext:l.hasNext})))):null,A=r.createElement("div",{className:y(V.TitleWrapper,!T&&V.TitleWrapperExpand)},r.createElement(Fa,{title:e,subtitle:t,titleMetadata:o,compactTitle:f,hasSubtitleMaxWidth:T})),N=n||e,C=N?r.createElement("div",{role:"status"},r.createElement(W,{visuallyHidden:!0,as:"p"},b.translate("Polaris.Page.Header.pageReadyAccessibilityLabel",{title:N}))):void 0,g=s?r.createElement(Wa,{primaryAction:s}):null;let v=null;Ge(p)&&(p.length>0||Ia(h))?v=r.createElement(Ta,{actions:p,groups:h,rollup:P,rollupActionsLabel:e?b.translate("Polaris.Page.Header.rollupActionsLabel",{title:e}):void 0,onActionRollup:m}):lt(p)&&(v=r.createElement(r.Fragment,null,p));const I=S||w?r.createElement(z,{printHidden:!0,paddingBlockEnd:"100",paddingInlineEnd:v&&P?"1000":void 0},r.createElement(Ve,{gap:"400",align:"space-between",blockAlign:"center"},S,w)):null,M=a?r.createElement("div",{className:V.AdditionalMetaData},r.createElement(W,{tone:"subdued",as:"span",variant:"bodySm"},a)):null,H=y(E&&V.isSingleRow,I&&V.hasNavigation,v&&V.hasActionMenu,P&&V.mobileView,!d&&V.noBreadcrumbs,e&&e.length<Cn&&V.mediumTitle,e&&e.length>Cn&&V.longTitle),{slot1:L,slot2:R,slot3:k,slot4:F,slot5:$}=Ha({actionMenuMarkup:v,additionalMetadataMarkup:M,breadcrumbMarkup:S,isNavigationCollapsed:P,pageTitleMarkup:A,paginationMarkup:w,primaryActionMarkup:g,title:e});return r.createElement(z,{position:"relative",paddingBlockStart:{xs:"400",md:"600"},paddingBlockEnd:{xs:"400",md:"600"},paddingInlineStart:{xs:"400",sm:"0"},paddingInlineEnd:{xs:"400",sm:"0"},visuallyHidden:i},C,r.createElement("div",{className:H},r.createElement(So,{filterActions:!!c},r.createElement(kt,{condition:[L,R,k,F].some(at)},r.createElement("div",{className:V.Row},L,R,r.createElement(kt,{condition:[k,F].some(at)},r.createElement("div",{className:V.RightAlign},r.createElement(ma,{condition:[k,F].every(at),wrapper:O=>r.createElement("div",{className:V.Actions},O)},k,F))))),r.createElement(kt,{condition:[$].some(at)},r.createElement("div",{className:V.Row},r.createElement(Ve,{gap:"400"},$))))))}function Wa({primaryAction:e}){const{isNavigationCollapsed:t}=ur();let n;if(Ge(e)){const{primary:o,helpText:a}=e,i=o===void 0?!0:o,s=dt($a(t,e),{variant:i?"primary":void 0});n=a?r.createElement(qe,{content:a},s):s}else n=e;return r.createElement("div",{className:V.PrimaryActionWrapper},r.createElement(z,{printHidden:!0},n))}function $a(e,t){let{content:n,accessibilityLabel:o}=t;const{icon:a}=t;return a==null?{...t,icon:void 0}:(e&&(o=o||n,n=void 0),{...t,content:n,accessibilityLabel:o,icon:a})}function at(e){return e!=null}function Ha({actionMenuMarkup:e,additionalMetadataMarkup:t,breadcrumbMarkup:n,isNavigationCollapsed:o,pageTitleMarkup:a,paginationMarkup:i,primaryActionMarkup:s,title:l}){const c={mobileCompact:{slots:{slot1:null,slot2:a,slot3:e,slot4:s,slot5:t},condition:o&&n==null&&l!=null&&l.length<=Oa},mobileDefault:{slots:{slot1:n,slot2:a,slot3:e,slot4:s,slot5:t},condition:o},desktopCompact:{slots:{slot1:n,slot2:a,slot3:e,slot4:s,slot5:t},condition:!o&&i==null&&e==null&&l!=null&&l.length<=La},desktopDefault:{slots:{slot1:n,slot2:a,slot3:r.createElement(r.Fragment,null,e,s),slot4:i,slot5:t},condition:!o}};return(Object.values(c).find(p=>p.condition)||c.desktopDefault).slots}function Xa({children:e,fullWidth:t,narrowWidth:n,...o}){const a=y(ot.Page,t&&ot.fullWidth,n&&ot.narrowWidth),i=o.title!=null&&o.title!==""||o.subtitle!=null&&o.subtitle!==""||o.primaryAction!=null||o.secondaryActions!=null&&(Ge(o.secondaryActions)&&o.secondaryActions.length>0||lt(o.secondaryActions))||o.actionGroups!=null&&o.actionGroups.length>0||o.backAction!=null,s=y(!i&&ot.Content),l=i?r.createElement(Ra,Object.assign({filterActions:!0},o)):null;return r.createElement("div",{className:a},l,r.createElement("div",{className:s},e))}export{$t as A,xo as B,qa as C,Ua as D,Va as E,Za as F,qn as G,ke as H,Ve as I,jn as J,Xe as K,Ma as L,ho as M,Zn as N,Rn as O,Xa as P,Jo as Q,rr as R,Ln as S,W as T,Gn as U,oo as W,z as a,aa as b,y as c,Ne as d,be as e,Xo as f,it as g,me as h,ir as i,Qo as j,wa as k,kn as l,Fo as m,qe as n,Ze as o,cr as p,fo as q,pa as r,Ot as s,Ga as t,fe as u,X as v,ar as w,de as x,Hn as y,lo as z};
