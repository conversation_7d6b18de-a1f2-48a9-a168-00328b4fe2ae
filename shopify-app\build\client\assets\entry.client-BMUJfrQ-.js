import{j as R}from"./jsx-runtime-DlxonYWr.js";import{r as i,a as p}from"./index-y-Yv8VR0.js";import{E as h,i as y,f as E,g as M,m as g,s as S,h as P,j as F,k as b,l as D,n as O,o as k,R as L,p as T,q as z,r as j}from"./components-7DQbiWt_.js";/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function H(s){if(!s)return null;let u=Object.entries(s),o={};for(let[t,e]of u)if(e&&e.__type==="RouteErrorResponse")o[t]=new h(e.status,e.statusText,e.data,e.internal===!0);else if(e&&e.__type==="Error"){if(e.__subType){let a=window[e.__subType];if(typeof a=="function")try{let n=new a(e.message);n.stack=e.stack,o[t]=n}catch{}}if(o[t]==null){let a=new Error(e.message);a.stack=e.stack,o[t]=a}}else o[t]=e;return o}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */let l,r,f=!1,C;new Promise(s=>{C=s}).catch(()=>{});function I(s){if(!r){if(window.__remixContext.future.v3_singleFetch){if(!l){let _=window.__remixContext.stream;y(_,"No stream found for single fetch decoding"),window.__remixContext.stream=void 0,l=E(_,window).then(d=>{window.__remixContext.state=d.value,l.value=!0}).catch(d=>{l.error=d})}if(l.error)throw l.error;if(!l.value)throw l}let a=M(window.__remixManifest.routes,window.__remixRouteModules,window.__remixContext.state,window.__remixContext.future,window.__remixContext.isSpaMode),n;if(!window.__remixContext.isSpaMode){n={...window.__remixContext.state,loaderData:{...window.__remixContext.state.loaderData}};let _=g(a,window.location,window.__remixContext.basename);if(_)for(let d of _){let w=d.route.id,c=window.__remixRouteModules[w],m=window.__remixManifest.routes[w];c&&S(m,c,window.__remixContext.isSpaMode)&&(c.HydrateFallback||!m.hasLoader)?n.loaderData[w]=void 0:m&&!m.hasLoader&&(n.loaderData[w]=null)}n&&n.errors&&(n.errors=H(n.errors))}r=P({routes:a,history:D(),basename:window.__remixContext.basename,future:{v7_normalizeFormMethod:!0,v7_fetcherPersist:window.__remixContext.future.v3_fetcherPersist,v7_partialHydration:!0,v7_prependBasename:!0,v7_relativeSplatPath:window.__remixContext.future.v3_relativeSplatPath,v7_skipActionErrorRevalidation:window.__remixContext.future.v3_singleFetch===!0},hydrationData:n,mapRouteProperties:j,dataStrategy:window.__remixContext.future.v3_singleFetch&&!window.__remixContext.isSpaMode?b(window.__remixManifest,window.__remixRouteModules,()=>r):void 0,patchRoutesOnNavigation:F(window.__remixManifest,window.__remixRouteModules,window.__remixContext.future,window.__remixContext.isSpaMode,window.__remixContext.basename)}),r.state.initialized&&(f=!0,r.initialize()),r.createRoutesForHMR=O,window.__remixRouter=r,C&&C(r)}let[u,o]=i.useState(window.__remixContext.criticalCss);window.__remixClearCriticalCss=()=>o(void 0);let[t,e]=i.useState(r.state.location);return i.useLayoutEffect(()=>{f||(f=!0,r.initialize())},[]),i.useLayoutEffect(()=>r.subscribe(a=>{a.location!==t&&e(a.location)}),[t]),k(r,window.__remixManifest,window.__remixRouteModules,window.__remixContext.future,window.__remixContext.isSpaMode),i.createElement(i.Fragment,null,i.createElement(L.Provider,{value:{manifest:window.__remixManifest,routeModules:window.__remixRouteModules,future:window.__remixContext.future,criticalCss:u,isSpaMode:window.__remixContext.isSpaMode}},i.createElement(T,{location:t},i.createElement(z,{router:r,fallbackElement:null,future:{v7_startTransition:!0}}))),window.__remixContext.future.v3_singleFetch?i.createElement(i.Fragment,null):null)}var x={},v;function N(){if(v)return x;v=1;var s=p();{var u=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;x.createRoot=function(o,t){u.usingClientEntryPoint=!0;try{return s.createRoot(o,t)}finally{u.usingClientEntryPoint=!1}},x.hydrateRoot=function(o,t,e){u.usingClientEntryPoint=!0;try{return s.hydrateRoot(o,t,e)}finally{u.usingClientEntryPoint=!1}}}return x}var B=N();i.startTransition(()=>{B.hydrateRoot(document,R.jsx(i.StrictMode,{children:R.jsx(I,{})}))});
