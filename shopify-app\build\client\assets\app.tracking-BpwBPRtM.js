import{j as r}from"./jsx-runtime-DlxonYWr.js";import{u as e}from"./components-7DQbiWt_.js";import"./index-y-Yv8VR0.js";function l(){const{trackingData:t}=e();return r.jsxs("main",{style:{padding:20},children:[r.jsx("h2",{children:"Order Tracking"}),r.jsx("ul",{children:t.map(({id:s,orderNumber:a,trackingId:i,status:n})=>r.jsxs("li",{style:{marginBottom:10},children:[r.jsxs("strong",{children:["Order ",a]}),": Tracking ID ",i," - Status: ",n]},s))})]})}export{l as default};
