(()=>{var IR=Object.create;var jd=Object.defineProperty,$R=Object.defineProperties,eT=Object.getOwnPropertyDescriptor,tT=Object.getOwnPropertyDescriptors,nT=Object.getOwnPropertyNames,us=Object.getOwnPropertySymbols,aT=Object.getPrototypeOf,Fd=Object.prototype.hasOwnProperty,Yh=Object.prototype.propertyIsEnumerable;var wh=(v,c,y)=>c in v?jd(v,c,{enumerable:!0,configurable:!0,writable:!0,value:y}):v[c]=y,Ja=(v,c)=>{for(var y in c||(c={}))Fd.call(c,y)&&wh(v,y,c[y]);if(us)for(var y of us(c))Yh.call(c,y)&&wh(v,y,c[y]);return v},qh=(v,c)=>$R(v,tT(c));var ls=(v,c)=>{var y={};for(var T in v)Fd.call(v,T)&&c.indexOf(T)<0&&(y[T]=v[T]);if(v!=null&&us)for(var T of us(v))c.indexOf(T)<0&&Yh.call(v,T)&&(y[T]=v[T]);return y};var jr=(v,c)=>()=>(c||v((c={exports:{}}).exports,c),c.exports);var rT=(v,c,y,T)=>{if(c&&typeof c=="object"||typeof c=="function")for(let g of nT(c))!Fd.call(v,g)&&g!==y&&jd(v,g,{get:()=>c[g],enumerable:!(T=eT(c,g))||T.enumerable});return v};var Kn=(v,c,y)=>(y=v!=null?IR(aT(v)):{},rT(c||!v||!v.__esModule?jd(y,"default",{value:v,enumerable:!0}):y,v));var os=(v,c,y)=>new Promise((T,g)=>{var M=A=>{try{B(y.next(A))}catch(h){g(h)}},q=A=>{try{B(y.throw(A))}catch(h){g(h)}},B=A=>A.done?T(A.value):Promise.resolve(A.value).then(M,q);B((y=y.apply(v,c)).next())});var Qh=jr((Le,ss)=>{"use strict";(function(){"use strict";typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error);var v="18.3.1",c=Symbol.for("react.element"),y=Symbol.for("react.portal"),T=Symbol.for("react.fragment"),g=Symbol.for("react.strict_mode"),M=Symbol.for("react.profiler"),q=Symbol.for("react.provider"),B=Symbol.for("react.context"),A=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),F=Symbol.for("react.suspense_list"),Q=Symbol.for("react.memo"),he=Symbol.for("react.lazy"),at=Symbol.for("react.offscreen"),Ke=Symbol.iterator,Ye="@@iterator";function Xe(l){if(l===null||typeof l!="object")return null;var p=Ke&&l[Ke]||l[Ye];return typeof p=="function"?p:null}var Re={current:null},xe={transition:null},ft={current:null,isBatchingLegacy:!1,didScheduleLegacyUpdate:!1},gt={current:null},zn={},Ne=null;function ye(l){Ne=l}zn.setExtraStackFrame=function(l){Ne=l},zn.getCurrentStack=null,zn.getStackAddendum=function(){var l="";Ne&&(l+=Ne);var p=zn.getCurrentStack;return p&&(l+=p()||""),l};var yn=!1,se=!1,Ue=!1,$=!1,ge=!1,Te={ReactCurrentDispatcher:Re,ReactCurrentBatchConfig:xe,ReactCurrentOwner:gt};Te.ReactDebugCurrentFrame=zn,Te.ReactCurrentActQueue=ft;function dt(l){{for(var p=arguments.length,R=new Array(p>1?p-1:0),x=1;x<p;x++)R[x-1]=arguments[x];Rt("warn",l,R)}}function ue(l){{for(var p=arguments.length,R=new Array(p>1?p-1:0),x=1;x<p;x++)R[x-1]=arguments[x];Rt("error",l,R)}}function Rt(l,p,R){{var x=Te.ReactDebugCurrentFrame,j=x.getStackAddendum();j!==""&&(p+="%s",R=R.concat([j]));var ie=R.map(function(Z){return String(Z)});ie.unshift("Warning: "+p),Function.prototype.apply.call(console[l],console,ie)}}var Me={};function Tt(l,p){{var R=l.constructor,x=R&&(R.displayName||R.name)||"ReactClass",j=x+"."+p;if(Me[j])return;ue("Can't call %s on a component that is not yet mounted. This is a no-op, but it might indicate a bug in your application. Instead, assign to `this.state` directly or define a `state = {};` class property with the desired state in the %s component.",p,x),Me[j]=!0}}var Fe={isMounted:function(l){return!1},enqueueForceUpdate:function(l,p,R){Tt(l,"forceUpdate")},enqueueReplaceState:function(l,p,R,x){Tt(l,"replaceState")},enqueueSetState:function(l,p,R,x){Tt(l,"setState")}},st=Object.assign,Ae={};Object.freeze(Ae);function Ct(l,p,R){this.props=l,this.context=p,this.refs=Ae,this.updater=R||Fe}Ct.prototype.isReactComponent={},Ct.prototype.setState=function(l,p){if(typeof l!="object"&&typeof l!="function"&&l!=null)throw new Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,l,p,"setState")},Ct.prototype.forceUpdate=function(l){this.updater.enqueueForceUpdate(this,l,"forceUpdate")};{var Zt={isMounted:["isMounted","Instead, make sure to clean up subscriptions and pending requests in componentWillUnmount to prevent memory leaks."],replaceState:["replaceState","Refactor your code to use setState instead (see https://github.com/facebook/react/issues/3236)."]},Xn=function(l,p){Object.defineProperty(Ct.prototype,l,{get:function(){dt("%s(...) is deprecated in plain JavaScript React classes. %s",p[0],p[1])}})};for(var St in Zt)Zt.hasOwnProperty(St)&&Xn(St,Zt[St])}function gn(){}gn.prototype=Ct.prototype;function rt(l,p,R){this.props=l,this.context=p,this.refs=Ae,this.updater=R||Fe}var kt=rt.prototype=new gn;kt.constructor=rt,st(kt,Ct.prototype),kt.isPureReactComponent=!0;function ia(){var l={current:null};return Object.seal(l),l}var Jn=Array.isArray;function Dn(l){return Jn(l)}function _n(l){{var p=typeof Symbol=="function"&&Symbol.toStringTag,R=p&&l[Symbol.toStringTag]||l.constructor.name||"Object";return R}}function Zn(l){try{return Hn(l),!1}catch(p){return!0}}function Hn(l){return""+l}function Bt(l){if(Zn(l))return ue("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",_n(l)),Hn(l)}function Sn(l,p,R){var x=l.displayName;if(x)return x;var j=p.displayName||p.name||"";return j!==""?R+"("+j+")":R}function Ln(l){return l.displayName||"Context"}function bt(l){if(l==null)return null;if(typeof l.tag=="number"&&ue("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof l=="function")return l.displayName||l.name||null;if(typeof l=="string")return l;switch(l){case T:return"Fragment";case y:return"Portal";case M:return"Profiler";case g:return"StrictMode";case h:return"Suspense";case F:return"SuspenseList"}if(typeof l=="object")switch(l.$$typeof){case B:var p=l;return Ln(p)+".Consumer";case q:var R=l;return Ln(R._context)+".Provider";case A:return Sn(l,l.render,"ForwardRef");case Q:var x=l.displayName||null;return x!==null?x:bt(l.type)||"Memo";case he:{var j=l,ie=j._payload,Z=j._init;try{return bt(Z(ie))}catch(me){return null}}}return null}var Vt=Object.prototype.hasOwnProperty,ua={key:!0,ref:!0,__self:!0,__source:!0},kn,it,jn;jn={};function Ia(l){if(Vt.call(l,"ref")){var p=Object.getOwnPropertyDescriptor(l,"ref").get;if(p&&p.isReactWarning)return!1}return l.ref!==void 0}function xa(l){if(Vt.call(l,"key")){var p=Object.getOwnPropertyDescriptor(l,"key").get;if(p&&p.isReactWarning)return!1}return l.key!==void 0}function Da(l,p){var R=function(){kn||(kn=!0,ue("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",p))};R.isReactWarning=!0,Object.defineProperty(l,"key",{get:R,configurable:!0})}function Fn(l,p){var R=function(){it||(it=!0,ue("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",p))};R.isReactWarning=!0,Object.defineProperty(l,"ref",{get:R,configurable:!0})}function vr(l){if(typeof l.ref=="string"&&gt.current&&l.__self&&gt.current.stateNode!==l.__self){var p=bt(gt.current.type);jn[p]||(ue('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',p,l.ref),jn[p]=!0)}}var la=function(l,p,R,x,j,ie,Z){var me={$$typeof:c,type:l,key:p,ref:R,props:Z,_owner:ie};return me._store={},Object.defineProperty(me._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(me,"_self",{configurable:!1,enumerable:!1,writable:!1,value:x}),Object.defineProperty(me,"_source",{configurable:!1,enumerable:!1,writable:!1,value:j}),Object.freeze&&(Object.freeze(me.props),Object.freeze(me)),me};function pr(l,p,R){var x,j={},ie=null,Z=null,me=null,Oe=null;if(p!=null){Ia(p)&&(Z=p.ref,vr(p)),xa(p)&&(Bt(p.key),ie=""+p.key),me=p.__self===void 0?null:p.__self,Oe=p.__source===void 0?null:p.__source;for(x in p)Vt.call(p,x)&&!ua.hasOwnProperty(x)&&(j[x]=p[x])}var Ze=arguments.length-2;if(Ze===1)j.children=R;else if(Ze>1){for(var et=Array(Ze),lt=0;lt<Ze;lt++)et[lt]=arguments[lt+2];Object.freeze&&Object.freeze(et),j.children=et}if(l&&l.defaultProps){var ct=l.defaultProps;for(x in ct)j[x]===void 0&&(j[x]=ct[x])}if(ie||Z){var ht=typeof l=="function"?l.displayName||l.name||"Unknown":l;ie&&Da(j,ht),Z&&Fn(j,ht)}return la(l,ie,Z,me,Oe,gt.current,j)}function w(l,p){var R=la(l.type,p,l.ref,l._self,l._source,l._owner,l.props);return R}function ne(l,p,R){if(l==null)throw new Error("React.cloneElement(...): The argument must be a React element, but you passed "+l+".");var x,j=st({},l.props),ie=l.key,Z=l.ref,me=l._self,Oe=l._source,Ze=l._owner;if(p!=null){Ia(p)&&(Z=p.ref,Ze=gt.current),xa(p)&&(Bt(p.key),ie=""+p.key);var et;l.type&&l.type.defaultProps&&(et=l.type.defaultProps);for(x in p)Vt.call(p,x)&&!ua.hasOwnProperty(x)&&(p[x]===void 0&&et!==void 0?j[x]=et[x]:j[x]=p[x])}var lt=arguments.length-2;if(lt===1)j.children=R;else if(lt>1){for(var ct=Array(lt),ht=0;ht<lt;ht++)ct[ht]=arguments[ht+2];j.children=ct}return la(l.type,ie,Z,me,Oe,Ze,j)}function pe(l){return typeof l=="object"&&l!==null&&l.$$typeof===c}var ee=".",ut=":";function wt(l){var p=/[=:]/g,R={"=":"=0",":":"=2"},x=l.replace(p,function(j){return R[j]});return"$"+x}var W=!1,X=/\/+/g;function $e(l){return l.replace(X,"$&/")}function qe(l,p){return typeof l=="object"&&l!==null&&l.key!=null?(Bt(l.key),wt(""+l.key)):p.toString(36)}function ce(l,p,R,x,j){var ie=typeof l;(ie==="undefined"||ie==="boolean")&&(l=null);var Z=!1;if(l===null)Z=!0;else switch(ie){case"string":case"number":Z=!0;break;case"object":switch(l.$$typeof){case c:case y:Z=!0}}if(Z){var me=l,Oe=j(me),Ze=x===""?ee+qe(me,0):x;if(Dn(Oe)){var et="";Ze!=null&&(et=$e(Ze)+"/"),ce(Oe,p,et,"",function(Ls){return Ls})}else Oe!=null&&(pe(Oe)&&(Oe.key&&(!me||me.key!==Oe.key)&&Bt(Oe.key),Oe=w(Oe,R+(Oe.key&&(!me||me.key!==Oe.key)?$e(""+Oe.key)+"/":"")+Ze)),p.push(Oe));return 1}var lt,ct,ht=0,Dt=x===""?ee:x+ut;if(Dn(l))for(var Er=0;Er<l.length;Er++)lt=l[Er],ct=Dt+qe(lt,Er),ht+=ce(lt,p,R,ct,j);else{var ou=Xe(l);if(typeof ou=="function"){var xl=l;ou===xl.entries&&(W||dt("Using Maps as children is not supported. Use an array of keyed ReactElements instead."),W=!0);for(var mi=ou.call(xl),Dl,Hs=0;!(Dl=mi.next()).done;)lt=Dl.value,ct=Dt+qe(lt,Hs++),ht+=ce(lt,p,R,ct,j)}else if(ie==="object"){var _l=String(l);throw new Error("Objects are not valid as a React child (found: "+(_l==="[object Object]"?"object with keys {"+Object.keys(l).join(", ")+"}":_l)+"). If you meant to render a collection of children, use an array instead.")}}return ht}function Wt(l,p,R){if(l==null)return l;var x=[],j=0;return ce(l,x,"","",function(ie){return p.call(R,ie,j++)}),x}function oa(l){var p=0;return Wt(l,function(){p++}),p}function mr(l,p,R){Wt(l,function(){p.apply(this,arguments)},R)}function ze(l){return Wt(l,function(p){return p})||[]}function On(l){if(!pe(l))throw new Error("React.Children.only expected to receive a single React element child.");return l}function Wn(l){var p={$$typeof:B,_currentValue:l,_currentValue2:l,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null};p.Provider={$$typeof:q,_context:p};var R=!1,x=!1,j=!1;{var ie={$$typeof:B,_context:p};Object.defineProperties(ie,{Provider:{get:function(){return x||(x=!0,ue("Rendering <Context.Consumer.Provider> is not supported and will be removed in a future major release. Did you mean to render <Context.Provider> instead?")),p.Provider},set:function(Z){p.Provider=Z}},_currentValue:{get:function(){return p._currentValue},set:function(Z){p._currentValue=Z}},_currentValue2:{get:function(){return p._currentValue2},set:function(Z){p._currentValue2=Z}},_threadCount:{get:function(){return p._threadCount},set:function(Z){p._threadCount=Z}},Consumer:{get:function(){return R||(R=!0,ue("Rendering <Context.Consumer.Consumer> is not supported and will be removed in a future major release. Did you mean to render <Context.Consumer> instead?")),p.Consumer}},displayName:{get:function(){return p.displayName},set:function(Z){j||(dt("Setting `displayName` on Context.Consumer has no effect. You should set it directly on the context with Context.displayName = '%s'.",Z),j=!0)}}}),p.Consumer=ie}return p._currentRenderer=null,p._currentRenderer2=null,p}var sn=-1,Yt=0,Nn=1,sa=2;function $a(l){if(l._status===sn){var p=l._result,R=p();if(R.then(function(ie){if(l._status===Yt||l._status===sn){var Z=l;Z._status=Nn,Z._result=ie}},function(ie){if(l._status===Yt||l._status===sn){var Z=l;Z._status=sa,Z._result=ie}}),l._status===sn){var x=l;x._status=Yt,x._result=R}}if(l._status===Nn){var j=l._result;return j===void 0&&ue(`lazy: Expected the result of a dynamic import() call. Instead received: %s

Your code should look like: 
  const MyComponent = lazy(() => import('./MyComponent'))

Did you accidentally put curly braces around the import?`,j),"default"in j||ue(`lazy: Expected the result of a dynamic import() call. Instead received: %s

Your code should look like: 
  const MyComponent = lazy(() => import('./MyComponent'))`,j),j.default}else throw l._result}function si(l){var p={_status:sn,_result:l},R={$$typeof:he,_payload:p,_init:$a};{var x,j;Object.defineProperties(R,{defaultProps:{configurable:!0,get:function(){return x},set:function(ie){ue("React.lazy(...): It is not supported to assign `defaultProps` to a lazy component import. Either specify them where the component is defined, or create a wrapping component around it."),x=ie,Object.defineProperty(R,"defaultProps",{enumerable:!0})}},propTypes:{configurable:!0,get:function(){return j},set:function(ie){ue("React.lazy(...): It is not supported to assign `propTypes` to a lazy component import. Either specify them where the component is defined, or create a wrapping component around it."),j=ie,Object.defineProperty(R,"propTypes",{enumerable:!0})}}})}return R}function Wi(l){l!=null&&l.$$typeof===Q?ue("forwardRef requires a render function but received a `memo` component. Instead of forwardRef(memo(...)), use memo(forwardRef(...))."):typeof l!="function"?ue("forwardRef requires a render function but was given %s.",l===null?"null":typeof l):l.length!==0&&l.length!==2&&ue("forwardRef render functions accept exactly two parameters: props and ref. %s",l.length===1?"Did you forget to use the ref parameter?":"Any additional parameter will be undefined."),l!=null&&(l.defaultProps!=null||l.propTypes!=null)&&ue("forwardRef render functions do not support propTypes or defaultProps. Did you accidentally pass a React component?");var p={$$typeof:A,render:l};{var R;Object.defineProperty(p,"displayName",{enumerable:!1,configurable:!0,get:function(){return R},set:function(x){R=x,!l.name&&!l.displayName&&(l.displayName=x)}})}return p}var _a;_a=Symbol.for("react.module.reference");function xt(l){return!!(typeof l=="string"||typeof l=="function"||l===T||l===M||ge||l===g||l===h||l===F||$||l===at||yn||se||Ue||typeof l=="object"&&l!==null&&(l.$$typeof===he||l.$$typeof===Q||l.$$typeof===q||l.$$typeof===B||l.$$typeof===A||l.$$typeof===_a||l.getModuleId!==void 0))}function Vr(l,p){xt(l)||ue("memo: The first argument must be a component. Instead received: %s",l===null?"null":typeof l);var R={$$typeof:Q,type:l,compare:p===void 0?null:p};{var x;Object.defineProperty(R,"displayName",{enumerable:!1,configurable:!0,get:function(){return x},set:function(j){x=j,!l.name&&!l.displayName&&(l.displayName=j)}})}return R}function d(){var l=Re.current;return l===null&&ue(`Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.`),l}function H(l){var p=d();if(l._context!==void 0){var R=l._context;R.Consumer===l?ue("Calling useContext(Context.Consumer) is not supported, may cause bugs, and will be removed in a future major release. Did you mean to call useContext(Context) instead?"):R.Provider===l&&ue("Calling useContext(Context.Provider) is not supported. Did you mean to call useContext(Context) instead?")}return p.useContext(l)}function V(l){var p=d();return p.useState(l)}function re(l,p,R){var x=d();return x.useReducer(l,p,R)}function be(l){var p=d();return p.useRef(l)}function Ve(l,p){var R=d();return R.useEffect(l,p)}function De(l,p){var R=d();return R.useInsertionEffect(l,p)}function Se(l,p){var R=d();return R.useLayoutEffect(l,p)}function vt(l,p){var R=d();return R.useCallback(l,p)}function Je(l,p){var R=d();return R.useMemo(l,p)}function Qe(l,p,R){var x=d();return x.useImperativeHandle(l,p,R)}function cn(l,p){{var R=d();return R.useDebugValue(l,p)}}function Bn(){var l=d();return l.useTransition()}function ca(l){var p=d();return p.useDeferredValue(l)}function qt(){var l=d();return l.useId()}function hr(l,p,R){var x=d();return x.useSyncExternalStore(l,p,R)}var Oa=0,yr,Ii,dl,$i,vl,fn,ci;function pl(){}pl.__reactDisabledLog=!0;function Es(){{if(Oa===0){yr=console.log,Ii=console.info,dl=console.warn,$i=console.error,vl=console.group,fn=console.groupCollapsed,ci=console.groupEnd;var l={configurable:!0,enumerable:!0,value:pl,writable:!0};Object.defineProperties(console,{info:l,log:l,warn:l,error:l,group:l,groupCollapsed:l,groupEnd:l})}Oa++}}function Rs(){{if(Oa--,Oa===0){var l={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:st({},l,{value:yr}),info:st({},l,{value:Ii}),warn:st({},l,{value:dl}),error:st({},l,{value:$i}),group:st({},l,{value:vl}),groupCollapsed:st({},l,{value:fn}),groupEnd:st({},l,{value:ci})})}Oa<0&&ue("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var eu=Te.ReactCurrentDispatcher,tu;function gr(l,p,R){{if(tu===void 0)try{throw Error()}catch(j){var x=j.stack.trim().match(/\n( *(at )?)/);tu=x&&x[1]||""}return`
`+tu+l}}var fi=!1,wr;{var nu=typeof WeakMap=="function"?WeakMap:Map;wr=new nu}function au(l,p){if(!l||fi)return"";{var R=wr.get(l);if(R!==void 0)return R}var x;fi=!0;var j=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var ie;ie=eu.current,eu.current=null,Es();try{if(p){var Z=function(){throw Error()};if(Object.defineProperty(Z.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Z,[])}catch(Dt){x=Dt}Reflect.construct(l,[],Z)}else{try{Z.call()}catch(Dt){x=Dt}l.call(Z.prototype)}}else{try{throw Error()}catch(Dt){x=Dt}l()}}catch(Dt){if(Dt&&x&&typeof Dt.stack=="string"){for(var me=Dt.stack.split(`
`),Oe=x.stack.split(`
`),Ze=me.length-1,et=Oe.length-1;Ze>=1&&et>=0&&me[Ze]!==Oe[et];)et--;for(;Ze>=1&&et>=0;Ze--,et--)if(me[Ze]!==Oe[et]){if(Ze!==1||et!==1)do if(Ze--,et--,et<0||me[Ze]!==Oe[et]){var lt=`
`+me[Ze].replace(" at new "," at ");return l.displayName&&lt.includes("<anonymous>")&&(lt=lt.replace("<anonymous>",l.displayName)),typeof l=="function"&&wr.set(l,lt),lt}while(Ze>=1&&et>=0);break}}}finally{fi=!1,eu.current=ie,Rs(),Error.prepareStackTrace=j}var ct=l?l.displayName||l.name:"",ht=ct?gr(ct):"";return typeof l=="function"&&wr.set(l,ht),ht}function ml(l,p,R){return au(l,!1)}function Ts(l){var p=l.prototype;return!!(p&&p.isReactComponent)}function Sr(l,p,R){if(l==null)return"";if(typeof l=="function")return au(l,Ts(l));if(typeof l=="string")return gr(l);switch(l){case h:return gr("Suspense");case F:return gr("SuspenseList")}if(typeof l=="object")switch(l.$$typeof){case A:return ml(l.render);case Q:return Sr(l.type,p,R);case he:{var x=l,j=x._payload,ie=x._init;try{return Sr(ie(j),p,R)}catch(Z){}}}return""}var Yr={},hl=Te.ReactDebugCurrentFrame;function Qt(l){if(l){var p=l._owner,R=Sr(l.type,l._source,p?p.type:null);hl.setExtraStackFrame(R)}else hl.setExtraStackFrame(null)}function di(l,p,R,x,j){{var ie=Function.call.bind(Vt);for(var Z in l)if(ie(l,Z)){var me=void 0;try{if(typeof l[Z]!="function"){var Oe=Error((x||"React class")+": "+R+" type `"+Z+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof l[Z]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw Oe.name="Invariant Violation",Oe}me=l[Z](p,Z,x,R,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(Ze){me=Ze}me&&!(me instanceof Error)&&(Qt(j),ue("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",x||"React class",R,Z,typeof me),Qt(null)),me instanceof Error&&!(me.message in Yr)&&(Yr[me.message]=!0,Qt(j),ue("Failed %s type: %s",R,me.message),Qt(null))}}}function Mt(l){if(l){var p=l._owner,R=Sr(l.type,l._source,p?p.type:null);ye(R)}else ye(null)}var ru;ru=!1;function Cs(){if(gt.current){var l=bt(gt.current.type);if(l)return`

Check the render method of \``+l+"`."}return""}function tv(l){if(l!==void 0){var p=l.fileName.replace(/^.*[\\\/]/,""),R=l.lineNumber;return`

Check your code at `+p+":"+R+"."}return""}function xs(l){return l!=null?tv(l.__source):""}var Ds={};function nv(l){var p=Cs();if(!p){var R=typeof l=="string"?l:l.displayName||l.name;R&&(p=`

Check the top-level render call using <`+R+">.")}return p}function yl(l,p){if(!(!l._store||l._store.validated||l.key!=null)){l._store.validated=!0;var R=nv(p);if(!Ds[R]){Ds[R]=!0;var x="";l&&l._owner&&l._owner!==gt.current&&(x=" It was passed a child from "+bt(l._owner.type)+"."),Mt(l),ue('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',R,x),Mt(null)}}}function gl(l,p){if(typeof l=="object"){if(Dn(l))for(var R=0;R<l.length;R++){var x=l[R];pe(x)&&yl(x,p)}else if(pe(l))l._store&&(l._store.validated=!0);else if(l){var j=Xe(l);if(typeof j=="function"&&j!==l.entries)for(var ie=j.call(l),Z;!(Z=ie.next()).done;)pe(Z.value)&&yl(Z.value,p)}}}function Sl(l){{var p=l.type;if(p==null||typeof p=="string")return;var R;if(typeof p=="function")R=p.propTypes;else if(typeof p=="object"&&(p.$$typeof===A||p.$$typeof===Q))R=p.propTypes;else return;if(R){var x=bt(p);di(R,l.props,"prop",x,l)}else if(p.PropTypes!==void 0&&!ru){ru=!0;var j=bt(p);ue("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",j||"Unknown")}typeof p.getDefaultProps=="function"&&!p.getDefaultProps.isReactClassApproved&&ue("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function _s(l){{for(var p=Object.keys(l.props),R=0;R<p.length;R++){var x=p[R];if(x!=="children"&&x!=="key"){Mt(l),ue("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",x),Mt(null);break}}l.ref!==null&&(Mt(l),ue("Invalid attribute `ref` supplied to `React.Fragment`."),Mt(null))}}function er(l,p,R){var x=xt(l);if(!x){var j="";(l===void 0||typeof l=="object"&&l!==null&&Object.keys(l).length===0)&&(j+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var ie=xs(p);ie?j+=ie:j+=Cs();var Z;l===null?Z="null":Dn(l)?Z="array":l!==void 0&&l.$$typeof===c?(Z="<"+(bt(l.type)||"Unknown")+" />",j=" Did you accidentally export a JSX literal instead of a component?"):Z=typeof l,ue("React.createElement: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",Z,j)}var me=pr.apply(this,arguments);if(me==null)return me;if(x)for(var Oe=2;Oe<arguments.length;Oe++)gl(arguments[Oe],l);return l===T?_s(me):Sl(me),me}var bl=!1;function Os(l){var p=er.bind(null,l);return p.type=l,bl||(bl=!0,dt("React.createFactory() is deprecated and will be removed in a future major release. Consider using JSX or use React.createElement() directly instead.")),Object.defineProperty(p,"type",{enumerable:!1,get:function(){return dt("Factory.type is deprecated. Access the class directly before passing it to createFactory."),Object.defineProperty(this,"type",{value:l}),l}}),p}function Ns(l,p,R){for(var x=ne.apply(this,arguments),j=2;j<arguments.length;j++)gl(arguments[j],x.type);return Sl(x),x}function qr(l,p){var R=xe.transition;xe.transition={};var x=xe.transition;xe.transition._updatedFibers=new Set;try{l()}finally{if(xe.transition=R,R===null&&x._updatedFibers){var j=x._updatedFibers.size;j>10&&dt("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table."),x._updatedFibers.clear()}}}var El=!1,vi=null;function Us(l){if(vi===null)try{var p=("require"+Math.random()).slice(0,7),R=ss&&ss[p];vi=R.call(ss,"timers").setImmediate}catch(x){vi=function(j){El===!1&&(El=!0,typeof MessageChannel=="undefined"&&ue("This browser does not have a MessageChannel implementation, so enqueuing tasks via await act(async () => ...) will fail. Please file an issue at https://github.com/facebook/react/issues if you encounter this warning."));var ie=new MessageChannel;ie.port1.onmessage=j,ie.port2.postMessage(void 0)}}return vi(l)}var br=0,Rl=!1;function Tl(l){{var p=br;br++,ft.current===null&&(ft.current=[]);var R=ft.isBatchingLegacy,x;try{if(ft.isBatchingLegacy=!0,x=l(),!R&&ft.didScheduleLegacyUpdate){var j=ft.current;j!==null&&(ft.didScheduleLegacyUpdate=!1,lu(j))}}catch(ct){throw pi(p),ct}finally{ft.isBatchingLegacy=R}if(x!==null&&typeof x=="object"&&typeof x.then=="function"){var ie=x,Z=!1,me={then:function(ct,ht){Z=!0,ie.then(function(Dt){pi(p),br===0?iu(Dt,ct,ht):ct(Dt)},function(Dt){pi(p),ht(Dt)})}};return!Rl&&typeof Promise!="undefined"&&Promise.resolve().then(function(){}).then(function(){Z||(Rl=!0,ue("You called act(async () => ...) without await. This could lead to unexpected testing behaviour, interleaving multiple act calls and mixing their scopes. You should - await act(async () => ...);"))}),me}else{var Oe=x;if(pi(p),br===0){var Ze=ft.current;Ze!==null&&(lu(Ze),ft.current=null);var et={then:function(ct,ht){ft.current===null?(ft.current=[],iu(Oe,ct,ht)):ct(Oe)}};return et}else{var lt={then:function(ct,ht){ct(Oe)}};return lt}}}}function pi(l){l!==br-1&&ue("You seem to have overlapping act() calls, this is not supported. Be sure to await previous act() calls before making a new one. "),br=l}function iu(l,p,R){{var x=ft.current;if(x!==null)try{lu(x),Us(function(){x.length===0?(ft.current=null,p(l)):iu(l,p,R)})}catch(j){R(j)}else p(l)}}var uu=!1;function lu(l){if(!uu){uu=!0;var p=0;try{for(;p<l.length;p++){var R=l[p];do R=R(!0);while(R!==null)}l.length=0}catch(x){throw l=l.slice(p+1),x}finally{uu=!1}}}var Ms=er,As=Ns,Cl=Os,zs={map:Wt,forEach:mr,count:oa,toArray:ze,only:On};Le.Children=zs,Le.Component=Ct,Le.Fragment=T,Le.Profiler=M,Le.PureComponent=rt,Le.StrictMode=g,Le.Suspense=h,Le.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Te,Le.act=Tl,Le.cloneElement=As,Le.createContext=Wn,Le.createElement=Ms,Le.createFactory=Cl,Le.createRef=ia,Le.forwardRef=Wi,Le.isValidElement=pe,Le.lazy=si,Le.memo=Vr,Le.startTransition=qr,Le.unstable_act=Tl,Le.useCallback=vt,Le.useContext=H,Le.useDebugValue=cn,Le.useDeferredValue=ca,Le.useEffect=Ve,Le.useId=qt,Le.useImperativeHandle=Qe,Le.useInsertionEffect=De,Le.useLayoutEffect=Se,Le.useMemo=Je,Le.useReducer=re,Le.useRef=be,Le.useState=V,Le.useSyncExternalStore=hr,Le.useTransition=Bn,Le.version=v,typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error)})()});var Za=jr((TT,Ph)=>{"use strict";Ph.exports=Qh()});var ty=jr(nt=>{"use strict";(function(){"use strict";typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error);var v=!1,c=!1,y=5;function T(w,ne){var pe=w.length;w.push(ne),q(w,ne,pe)}function g(w){return w.length===0?null:w[0]}function M(w){if(w.length===0)return null;var ne=w[0],pe=w.pop();return pe!==ne&&(w[0]=pe,B(w,pe,0)),ne}function q(w,ne,pe){for(var ee=pe;ee>0;){var ut=ee-1>>>1,wt=w[ut];if(A(wt,ne)>0)w[ut]=ne,w[ee]=wt,ee=ut;else return}}function B(w,ne,pe){for(var ee=pe,ut=w.length,wt=ut>>>1;ee<wt;){var W=(ee+1)*2-1,X=w[W],$e=W+1,qe=w[$e];if(A(X,ne)<0)$e<ut&&A(qe,X)<0?(w[ee]=qe,w[$e]=ne,ee=$e):(w[ee]=X,w[W]=ne,ee=W);else if($e<ut&&A(qe,ne)<0)w[ee]=qe,w[$e]=ne,ee=$e;else return}}function A(w,ne){var pe=w.sortIndex-ne.sortIndex;return pe!==0?pe:w.id-ne.id}var h=1,F=2,Q=3,he=4,at=5;function Ke(w,ne){}var Ye=typeof performance=="object"&&typeof performance.now=="function";if(Ye){var Xe=performance;nt.unstable_now=function(){return Xe.now()}}else{var Re=Date,xe=Re.now();nt.unstable_now=function(){return Re.now()-xe}}var ft=1073741823,gt=-1,zn=250,Ne=5e3,ye=1e4,yn=ft,se=[],Ue=[],$=1,ge=null,Te=Q,dt=!1,ue=!1,Rt=!1,Me=typeof setTimeout=="function"?setTimeout:null,Tt=typeof clearTimeout=="function"?clearTimeout:null,Fe=typeof setImmediate!="undefined"?setImmediate:null,st=typeof navigator!="undefined"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0?navigator.scheduling.isInputPending.bind(navigator.scheduling):null;function Ae(w){for(var ne=g(Ue);ne!==null;){if(ne.callback===null)M(Ue);else if(ne.startTime<=w)M(Ue),ne.sortIndex=ne.expirationTime,T(se,ne);else return;ne=g(Ue)}}function Ct(w){if(Rt=!1,Ae(w),!ue)if(g(se)!==null)ue=!0,Da(Zt);else{var ne=g(Ue);ne!==null&&Fn(Ct,ne.startTime-w)}}function Zt(w,ne){ue=!1,Rt&&(Rt=!1,vr()),dt=!0;var pe=Te;try{if(c)try{return Xn(w,ne)}catch(ut){if(ge!==null){var ee=nt.unstable_now();ge.isQueued=!1}throw ut}else return Xn(w,ne)}finally{ge=null,Te=pe,dt=!1}}function Xn(w,ne){var pe=ne;for(Ae(pe),ge=g(se);ge!==null&&!v&&!(ge.expirationTime>pe&&(!w||Vt()));){var ee=ge.callback;if(typeof ee=="function"){ge.callback=null,Te=ge.priorityLevel;var ut=ge.expirationTime<=pe,wt=ee(ut);pe=nt.unstable_now(),typeof wt=="function"?ge.callback=wt:ge===g(se)&&M(se),Ae(pe)}else M(se);ge=g(se)}if(ge!==null)return!0;var W=g(Ue);return W!==null&&Fn(Ct,W.startTime-pe),!1}function St(w,ne){switch(w){case h:case F:case Q:case he:case at:break;default:w=Q}var pe=Te;Te=w;try{return ne()}finally{Te=pe}}function gn(w){var ne;switch(Te){case h:case F:case Q:ne=Q;break;default:ne=Te;break}var pe=Te;Te=ne;try{return w()}finally{Te=pe}}function rt(w){var ne=Te;return function(){var pe=Te;Te=ne;try{return w.apply(this,arguments)}finally{Te=pe}}}function kt(w,ne,pe){var ee=nt.unstable_now(),ut;if(typeof pe=="object"&&pe!==null){var wt=pe.delay;typeof wt=="number"&&wt>0?ut=ee+wt:ut=ee}else ut=ee;var W;switch(w){case h:W=gt;break;case F:W=zn;break;case at:W=yn;break;case he:W=ye;break;case Q:default:W=Ne;break}var X=ut+W,$e={id:$++,callback:ne,priorityLevel:w,startTime:ut,expirationTime:X,sortIndex:-1};return ut>ee?($e.sortIndex=ut,T(Ue,$e),g(se)===null&&$e===g(Ue)&&(Rt?vr():Rt=!0,Fn(Ct,ut-ee))):($e.sortIndex=X,T(se,$e),!ue&&!dt&&(ue=!0,Da(Zt))),$e}function ia(){}function Jn(){!ue&&!dt&&(ue=!0,Da(Zt))}function Dn(){return g(se)}function _n(w){w.callback=null}function Zn(){return Te}var Hn=!1,Bt=null,Sn=-1,Ln=y,bt=-1;function Vt(){var w=nt.unstable_now()-bt;return!(w<Ln)}function ua(){}function kn(w){if(w<0||w>125){console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported");return}w>0?Ln=Math.floor(1e3/w):Ln=y}var it=function(){if(Bt!==null){var w=nt.unstable_now();bt=w;var ne=!0,pe=!0;try{pe=Bt(ne,w)}finally{pe?jn():(Hn=!1,Bt=null)}}else Hn=!1},jn;if(typeof Fe=="function")jn=function(){Fe(it)};else if(typeof MessageChannel!="undefined"){var Ia=new MessageChannel,xa=Ia.port2;Ia.port1.onmessage=it,jn=function(){xa.postMessage(null)}}else jn=function(){Me(it,0)};function Da(w){Bt=w,Hn||(Hn=!0,jn())}function Fn(w,ne){Sn=Me(function(){w(nt.unstable_now())},ne)}function vr(){Tt(Sn),Sn=-1}var la=ua,pr=null;nt.unstable_IdlePriority=at,nt.unstable_ImmediatePriority=h,nt.unstable_LowPriority=he,nt.unstable_NormalPriority=Q,nt.unstable_Profiling=pr,nt.unstable_UserBlockingPriority=F,nt.unstable_cancelCallback=_n,nt.unstable_continueExecution=Jn,nt.unstable_forceFrameRate=kn,nt.unstable_getCurrentPriorityLevel=Zn,nt.unstable_getFirstCallbackNode=Dn,nt.unstable_next=gn,nt.unstable_pauseExecution=ia,nt.unstable_requestPaint=la,nt.unstable_runWithPriority=St,nt.unstable_scheduleCallback=kt,nt.unstable_shouldYield=Vt,nt.unstable_wrapCallback=rt,typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error)})()});var ay=jr((sC,ny)=>{"use strict";ny.exports=ty()});var iy=jr((cC,ry)=>{"use strict";ry.exports=function(c){var y={},T=Za(),g=ay(),M=T.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,q=!1;function B(e){q=e}function A(e){if(!q){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];F("warn",e,n)}}function h(e){if(!q){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];F("error",e,n)}}function F(e,t,n){{var a=M.ReactDebugCurrentFrame,r=a.getStackAddendum();r!==""&&(t+="%s",n=n.concat([r]));var i=n.map(function(u){return String(u)});i.unshift("Warning: "+t),Function.prototype.apply.call(console[e],console,i)}}var Q=Object.assign;function he(e){return e._reactInternals}function at(e,t){e._reactInternals=t}var Ke=!1,Ye=!1,Xe=!1,Re=!1,xe=!1,ft=!0,gt=!0,zn=!0,Ne=0,ye=1,yn=2,se=3,Ue=4,$=5,ge=6,Te=7,dt=8,ue=9,Rt=10,Me=11,Tt=12,Fe=13,st=14,Ae=15,Ct=16,Zt=17,Xn=18,St=19,gn=21,rt=22,kt=23,ia=24,Jn=25,Dn=Symbol.for("react.element"),_n=Symbol.for("react.portal"),Zn=Symbol.for("react.fragment"),Hn=Symbol.for("react.strict_mode"),Bt=Symbol.for("react.profiler"),Sn=Symbol.for("react.provider"),Ln=Symbol.for("react.context"),bt=Symbol.for("react.forward_ref"),Vt=Symbol.for("react.suspense"),ua=Symbol.for("react.suspense_list"),kn=Symbol.for("react.memo"),it=Symbol.for("react.lazy"),jn=Symbol.for("react.scope"),Ia=Symbol.for("react.debug_trace_mode"),xa=Symbol.for("react.offscreen"),Da=Symbol.for("react.legacy_hidden"),Fn=Symbol.for("react.cache"),vr=Symbol.for("react.tracing_marker"),la=Symbol.iterator,pr="@@iterator";function w(e){if(e===null||typeof e!="object")return null;var t=la&&e[la]||e[pr];return typeof t=="function"?t:null}function ne(e,t,n){var a=e.displayName;if(a)return a;var r=t.displayName||t.name||"";return r!==""?n+"("+r+")":n}function pe(e){return e.displayName||"Context"}function ee(e){if(e==null)return null;if(typeof e.tag=="number"&&h("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Zn:return"Fragment";case _n:return"Portal";case Bt:return"Profiler";case Hn:return"StrictMode";case Vt:return"Suspense";case ua:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Ln:var t=e;return pe(t)+".Consumer";case Sn:var n=e;return pe(n._context)+".Provider";case bt:return ne(e,e.render,"ForwardRef");case kn:var a=e.displayName||null;return a!==null?a:ee(e.type)||"Memo";case it:{var r=e,i=r._payload,u=r._init;try{return ee(u(i))}catch(o){return null}}}return null}function ut(e,t,n){var a=t.displayName||t.name||"";return e.displayName||(a!==""?n+"("+a+")":n)}function wt(e){return e.displayName||"Context"}function W(e){var t=e.tag,n=e.type;switch(t){case ia:return"Cache";case ue:var a=n;return wt(a)+".Consumer";case Rt:var r=n;return wt(r._context)+".Provider";case Xn:return"DehydratedFragment";case Me:return ut(n,n.render,"ForwardRef");case Te:return"Fragment";case $:return n;case Ue:return"Portal";case se:return"Root";case ge:return"Text";case Ct:return ee(n);case dt:return n===Hn?"StrictMode":"Mode";case rt:return"Offscreen";case Tt:return"Profiler";case gn:return"Scope";case Fe:return"Suspense";case St:return"SuspenseList";case Jn:return"TracingMarker";case ye:case Ne:case Zt:case yn:case st:case Ae:if(typeof n=="function")return n.displayName||n.name||null;if(typeof n=="string")return n;break}return null}var X=0,$e=1,qe=2,ce=4,Wt=16,oa=32,mr=64,ze=128,On=256,Wn=512,sn=1024,Yt=2048,Nn=4096,sa=8192,$a=16384,si=Yt|ce|mr|Wn|sn|$a,Wi=32767,_a=32768,xt=65536,Vr=131072,d=1048576,H=2097152,V=4194304,re=8388608,be=16777216,Ve=33554432,De=ce|sn|0,Se=qe|ce|Wt|oa|Wn|Nn|sa,vt=ce|mr|Wn|sa,Je=Yt|Wt,Qe=V|re|H,cn=M.ReactCurrentOwner;function Bn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{var a=t;do t=a,(t.flags&(qe|Nn))!==X&&(n=t.return),a=t.return;while(a)}return t.tag===se?n:null}function ca(e){return Bn(e)===e}function qt(e){{var t=cn.current;if(t!==null&&t.tag===ye){var n=t,a=n.stateNode;a._warnedAboutRefsInRender||h("%s is accessing isMounted inside its render() function. render() should be a pure function of props and state. It should never access something that requires stale data from the previous render, such as refs. Move this logic to componentDidMount and componentDidUpdate instead.",W(n)||"A component"),a._warnedAboutRefsInRender=!0}}var r=he(e);return r?Bn(r)===r:!1}function hr(e){if(Bn(e)!==e)throw new Error("Unable to find node on an unmounted component.")}function Oa(e){var t=e.alternate;if(!t){var n=Bn(e);if(n===null)throw new Error("Unable to find node on an unmounted component.");return n!==e?null:e}for(var a=e,r=t;;){var i=a.return;if(i===null)break;var u=i.alternate;if(u===null){var o=i.return;if(o!==null){a=r=o;continue}break}if(i.child===u.child){for(var s=i.child;s;){if(s===a)return hr(i),e;if(s===r)return hr(i),t;s=s.sibling}throw new Error("Unable to find node on an unmounted component.")}if(a.return!==r.return)a=i,r=u;else{for(var f=!1,m=i.child;m;){if(m===a){f=!0,a=i,r=u;break}if(m===r){f=!0,r=i,a=u;break}m=m.sibling}if(!f){for(m=u.child;m;){if(m===a){f=!0,a=u,r=i;break}if(m===r){f=!0,r=u,a=i;break}m=m.sibling}if(!f)throw new Error("Child was not found in either parent set. This indicates a bug in React related to the return pointer. Please file an issue.")}}if(a.alternate!==r)throw new Error("Return fibers should always be each others' alternates. This error is likely caused by a bug in React. Please file an issue.")}if(a.tag!==se)throw new Error("Unable to find node on an unmounted component.");return a.stateNode.current===a?e:t}function yr(e){var t=Oa(e);return t!==null?Ii(t):null}function Ii(e){if(e.tag===$||e.tag===ge)return e;for(var t=e.child;t!==null;){var n=Ii(t);if(n!==null)return n;t=t.sibling}return null}function dl(e){var t=Oa(e);return t!==null?$i(t):null}function $i(e){if(e.tag===$||e.tag===ge)return e;for(var t=e.child;t!==null;){if(t.tag!==Ue){var n=$i(t);if(n!==null)return n}t=t.sibling}return null}var vl=Array.isArray;function fn(e){return vl(e)}var ci=c.getPublicInstance,pl=c.getRootHostContext,Es=c.getChildHostContext,Rs=c.prepareForCommit,eu=c.resetAfterCommit,tu=c.createInstance,gr=c.appendInitialChild,fi=c.finalizeInitialChildren,wr=c.prepareUpdate,nu=c.shouldSetTextContent,au=c.createTextInstance,ml=c.scheduleTimeout,Ts=c.cancelTimeout,Sr=c.noTimeout,Yr=c.isPrimaryRenderer,hl=c.warnsIfNotActing,Qt=c.supportsMutation,di=c.supportsPersistence,Mt=c.supportsHydration,ru=c.getInstanceFromNode,Cs=c.beforeActiveInstanceBlur,tv=c.afterActiveInstanceBlur,xs=c.preparePortalMount,Ds=c.prepareScopeUpdate,nv=c.getInstanceFromScope,yl=c.getCurrentEventPriority,gl=c.detachDeletedInstance,Sl=c.supportsMicrotasks,_s=c.scheduleMicrotask,er=c.supportsTestSelectors,bl=c.findFiberRoot,Os=c.getBoundingRect,Ns=c.getTextContent,qr=c.isHiddenSubtree,El=c.matchAccessibilityRole,vi=c.setFocusIfFocusable,Us=c.setupIntersectionObserver,br=c.appendChild,Rl=c.appendChildToContainer,Tl=c.commitTextUpdate,pi=c.commitMount,iu=c.commitUpdate,uu=c.insertBefore,lu=c.insertInContainerBefore,Ms=c.removeChild,As=c.removeChildFromContainer,Cl=c.resetTextContent,zs=c.hideInstance,l=c.hideTextInstance,p=c.unhideInstance,R=c.unhideTextInstance,x=c.clearContainer,j=c.cloneInstance,ie=c.createContainerChildSet,Z=c.appendChildToContainerChildSet,me=c.finalizeContainerChildren,Oe=c.replaceContainerChildren,Ze=c.cloneHiddenInstance,et=c.cloneHiddenTextInstance,lt=c.canHydrateInstance,ct=c.canHydrateTextInstance,ht=c.canHydrateSuspenseInstance,Dt=c.isSuspenseInstancePending,Er=c.isSuspenseInstanceFallback,ou=c.getSuspenseInstanceFallbackErrorDetails,xl=c.registerSuspenseInstanceRetry,mi=c.getNextHydratableSibling,Dl=c.getFirstHydratableChild,Hs=c.getFirstHydratableChildWithinContainer,_l=c.getFirstHydratableChildWithinSuspenseInstance,Ls=c.hydrateInstance,xy=c.hydrateTextInstance,Dy=c.hydrateSuspenseInstance,_y=c.getNextHydratableInstanceAfterSuspenseInstance,Oy=c.commitHydratedContainer,Ny=c.commitHydratedSuspenseInstance,Uy=c.clearSuspenseBoundary,My=c.clearSuspenseBoundaryFromContainer,Ay=c.shouldDeleteUnhydratedTailInstances,zy=c.didNotMatchHydratedContainerTextInstance,Hy=c.didNotMatchHydratedTextInstance,Ly=c.didNotHydrateInstanceWithinContainer,jy=c.didNotHydrateInstanceWithinSuspenseInstance,Fy=c.didNotHydrateInstance,By=c.didNotFindHydratableInstanceWithinContainer,Vy=c.didNotFindHydratableTextInstanceWithinContainer,wy=c.didNotFindHydratableSuspenseInstanceWithinContainer,Yy=c.didNotFindHydratableInstanceWithinSuspenseInstance,qy=c.didNotFindHydratableTextInstanceWithinSuspenseInstance,Qy=c.didNotFindHydratableSuspenseInstanceWithinSuspenseInstance,Py=c.didNotFindHydratableInstance,Gy=c.didNotFindHydratableTextInstance,Ky=c.didNotFindHydratableSuspenseInstance,Xy=c.errorHydratingContainer,su=0,av,rv,iv,uv,lv,ov,sv;function cv(){}cv.__reactDisabledLog=!0;function Jy(){{if(su===0){av=console.log,rv=console.info,iv=console.warn,uv=console.error,lv=console.group,ov=console.groupCollapsed,sv=console.groupEnd;var e={configurable:!0,enumerable:!0,value:cv,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}su++}}function Zy(){{if(su--,su===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:Q({},e,{value:av}),info:Q({},e,{value:rv}),warn:Q({},e,{value:iv}),error:Q({},e,{value:uv}),group:Q({},e,{value:lv}),groupCollapsed:Q({},e,{value:ov}),groupEnd:Q({},e,{value:sv})})}su<0&&h("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var js=M.ReactCurrentDispatcher,Fs;function Rr(e,t,n){{if(Fs===void 0)try{throw Error()}catch(r){var a=r.stack.trim().match(/\n( *(at )?)/);Fs=a&&a[1]||""}return`
`+Fs+e}}var Bs=!1,Ol;{var ky=typeof WeakMap=="function"?WeakMap:Map;Ol=new ky}function Vs(e,t){if(!e||Bs)return"";{var n=Ol.get(e);if(n!==void 0)return n}var a;Bs=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var i;i=js.current,js.current=null,Jy();try{if(t){var u=function(){throw Error()};if(Object.defineProperty(u.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(u,[])}catch(U){a=U}Reflect.construct(e,[],u)}else{try{u.call()}catch(U){a=U}e.call(u.prototype)}}else{try{throw Error()}catch(U){a=U}e()}}catch(U){if(U&&a&&typeof U.stack=="string"){for(var o=U.stack.split(`
`),s=a.stack.split(`
`),f=o.length-1,m=s.length-1;f>=1&&m>=0&&o[f]!==s[m];)m--;for(;f>=1&&m>=0;f--,m--)if(o[f]!==s[m]){if(f!==1||m!==1)do if(f--,m--,m<0||o[f]!==s[m]){var S=`
`+o[f].replace(" at new "," at ");return e.displayName&&S.includes("<anonymous>")&&(S=S.replace("<anonymous>",e.displayName)),typeof e=="function"&&Ol.set(e,S),S}while(f>=1&&m>=0);break}}}finally{Bs=!1,js.current=i,Zy(),Error.prepareStackTrace=r}var C=e?e.displayName||e.name:"",O=C?Rr(C):"";return typeof e=="function"&&Ol.set(e,O),O}function Wy(e,t,n){return Vs(e,!0)}function ws(e,t,n){return Vs(e,!1)}function Iy(e){var t=e.prototype;return!!(t&&t.isReactComponent)}function Ys(e,t,n){if(e==null)return"";if(typeof e=="function")return Vs(e,Iy(e));if(typeof e=="string")return Rr(e);switch(e){case Vt:return Rr("Suspense");case ua:return Rr("SuspenseList")}if(typeof e=="object")switch(e.$$typeof){case bt:return ws(e.render);case kn:return Ys(e.type,t,n);case it:{var a=e,r=a._payload,i=a._init;try{return Ys(i(r),t,n)}catch(u){}}}return""}var fv=Object.prototype.hasOwnProperty,dv={},vv=M.ReactDebugCurrentFrame;function Nl(e){if(e){var t=e._owner,n=Ys(e.type,e._source,t?t.type:null);vv.setExtraStackFrame(n)}else vv.setExtraStackFrame(null)}function fa(e,t,n,a,r){{var i=Function.call.bind(fv);for(var u in e)if(i(e,u)){var o=void 0;try{if(typeof e[u]!="function"){var s=Error((a||"React class")+": "+n+" type `"+u+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[u]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw s.name="Invariant Violation",s}o=e[u](t,u,a,n,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(f){o=f}o&&!(o instanceof Error)&&(Nl(r),h("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",a||"React class",n,u,typeof o),Nl(null)),o instanceof Error&&!(o.message in dv)&&(dv[o.message]=!0,Nl(r),h("Failed %s type: %s",n,o.message),Nl(null))}}}var qs=[],Ul;Ul=[];var tr=-1;function Tr(e){return{current:e}}function dn(e,t){if(tr<0){h("Unexpected pop.");return}t!==Ul[tr]&&h("Unexpected Fiber popped."),e.current=qs[tr],qs[tr]=null,Ul[tr]=null,tr--}function It(e,t,n){tr++,qs[tr]=e.current,Ul[tr]=n,e.current=t}var Qs;Qs={};var Vn={};Object.freeze(Vn);var nr=Tr(Vn),Na=Tr(!1),Ps=Vn;function hi(e,t,n){return n&&Ua(t)?Ps:nr.current}function pv(e,t,n){{var a=e.stateNode;a.__reactInternalMemoizedUnmaskedChildContext=t,a.__reactInternalMemoizedMaskedChildContext=n}}function yi(e,t){{var n=e.type,a=n.contextTypes;if(!a)return Vn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={};for(var u in a)i[u]=t[u];{var o=W(e)||"Unknown";fa(a,i,"context",o)}return r&&pv(e,t,i),i}}function Ml(){return Na.current}function Ua(e){{var t=e.childContextTypes;return t!=null}}function Al(e){dn(Na,e),dn(nr,e)}function Gs(e){dn(Na,e),dn(nr,e)}function mv(e,t,n){{if(nr.current!==Vn)throw new Error("Unexpected context found on stack. This error is likely caused by a bug in React. Please file an issue.");It(nr,t,e),It(Na,n,e)}}function hv(e,t,n){{var a=e.stateNode,r=t.childContextTypes;if(typeof a.getChildContext!="function"){{var i=W(e)||"Unknown";Qs[i]||(Qs[i]=!0,h("%s.childContextTypes is specified but there is no getChildContext() method on the instance. You can either define getChildContext() on %s or remove childContextTypes from it.",i,i))}return n}var u=a.getChildContext();for(var o in u)if(!(o in r))throw new Error((W(e)||"Unknown")+'.getChildContext(): key "'+o+'" is not defined in childContextTypes.');{var s=W(e)||"Unknown";fa(r,u,"child context",s)}return Q({},n,u)}}function zl(e){{var t=e.stateNode,n=t&&t.__reactInternalMemoizedMergedChildContext||Vn;return Ps=nr.current,It(nr,n,e),It(Na,Na.current,e),!0}}function yv(e,t,n){{var a=e.stateNode;if(!a)throw new Error("Expected to have an instance by this point. This error is likely caused by a bug in React. Please file an issue.");if(n){var r=hv(e,t,Ps);a.__reactInternalMemoizedMergedChildContext=r,dn(Na,e),dn(nr,e),It(nr,r,e),It(Na,n,e)}else dn(Na,e),It(Na,n,e)}}function $y(e){{if(!ca(e)||e.tag!==ye)throw new Error("Expected subtree parent to be a mounted class component. This error is likely caused by a bug in React. Please file an issue.");var t=e;do{switch(t.tag){case se:return t.stateNode.context;case ye:{var n=t.type;if(Ua(n))return t.stateNode.__reactInternalMemoizedMergedChildContext;break}}t=t.return}while(t!==null);throw new Error("Found unexpected detached subtree parent. This error is likely caused by a bug in React. Please file an issue.")}}var gi=0,gv=1,le=0,Be=1,ke=2,_t=8,Ma=16,Sv=Math.clz32?Math.clz32:ng,eg=Math.log,tg=Math.LN2;function ng(e){var t=e>>>0;return t===0?32:31-(eg(t)/tg|0)|0}var Ks=31,z=0,Pt=0,fe=1,Si=2,ar=4,Qr=8,Aa=16,cu=32,bi=4194240,fu=64,Xs=128,Js=256,Zs=512,ks=1024,Ws=2048,Is=4096,$s=8192,ec=16384,tc=32768,nc=65536,ac=131072,rc=262144,ic=524288,uc=1048576,lc=2097152,Hl=130023424,Ei=4194304,oc=8388608,sc=16777216,cc=33554432,fc=67108864,bv=Ei,du=134217728,Ev=268435455,vu=268435456,Pr=536870912,wn=1073741824;function ag(e){{if(e&fe)return"Sync";if(e&Si)return"InputContinuousHydration";if(e&ar)return"InputContinuous";if(e&Qr)return"DefaultHydration";if(e&Aa)return"Default";if(e&cu)return"TransitionHydration";if(e&bi)return"Transition";if(e&Hl)return"Retry";if(e&du)return"SelectiveHydration";if(e&vu)return"IdleHydration";if(e&Pr)return"Idle";if(e&wn)return"Offscreen"}}var ot=-1,Ll=fu,jl=Ei;function pu(e){switch(Gr(e)){case fe:return fe;case Si:return Si;case ar:return ar;case Qr:return Qr;case Aa:return Aa;case cu:return cu;case fu:case Xs:case Js:case Zs:case ks:case Ws:case Is:case $s:case ec:case tc:case nc:case ac:case rc:case ic:case uc:case lc:return e&bi;case Ei:case oc:case sc:case cc:case fc:return e&Hl;case du:return du;case vu:return vu;case Pr:return Pr;case wn:return wn;default:return h("Should have found matching lanes. This is a bug in React."),e}}function Fl(e,t){var n=e.pendingLanes;if(n===z)return z;var a=z,r=e.suspendedLanes,i=e.pingedLanes,u=n&Ev;if(u!==z){var o=u&~r;if(o!==z)a=pu(o);else{var s=u&i;s!==z&&(a=pu(s))}}else{var f=n&~r;f!==z?a=pu(f):i!==z&&(a=pu(i))}if(a===z)return z;if(t!==z&&t!==a&&(t&r)===z){var m=Gr(a),S=Gr(t);if(m>=S||m===Aa&&(S&bi)!==z)return t}(a&ar)!==z&&(a|=n&Aa);var C=e.entangledLanes;if(C!==z)for(var O=e.entanglements,U=a&C;U>0;){var N=Kr(U),I=1<<N;a|=O[N],U&=~I}return a}function rg(e,t){for(var n=e.eventTimes,a=ot;t>0;){var r=Kr(t),i=1<<r,u=n[r];u>a&&(a=u),t&=~i}return a}function ig(e,t){switch(e){case fe:case Si:case ar:return t+250;case Qr:case Aa:case cu:case fu:case Xs:case Js:case Zs:case ks:case Ws:case Is:case $s:case ec:case tc:case nc:case ac:case rc:case ic:case uc:case lc:return t+5e3;case Ei:case oc:case sc:case cc:case fc:return ot;case du:case vu:case Pr:case wn:return ot;default:return h("Should have found matching lanes. This is a bug in React."),ot}}function ug(e,t){for(var n=e.pendingLanes,a=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,u=n;u>0;){var o=Kr(u),s=1<<o,f=i[o];f===ot?((s&a)===z||(s&r)!==z)&&(i[o]=ig(s,t)):f<=t&&(e.expiredLanes|=s),u&=~s}}function lg(e){return pu(e.pendingLanes)}function dc(e){var t=e.pendingLanes&~wn;return t!==z?t:t&wn?wn:z}function og(e){return(e&fe)!==z}function vc(e){return(e&Ev)!==z}function Rv(e){return(e&Hl)===e}function sg(e){var t=fe|ar|Aa;return(e&t)===z}function cg(e){return(e&bi)===e}function Bl(e,t){var n=Si|ar|Qr|Aa;return(t&n)!==z}function fg(e,t){return(t&e.expiredLanes)!==z}function Tv(e){return(e&bi)!==z}function Cv(){var e=Ll;return Ll<<=1,(Ll&bi)===z&&(Ll=fu),e}function dg(){var e=jl;return jl<<=1,(jl&Hl)===z&&(jl=Ei),e}function Gr(e){return e&-e}function mu(e){return Gr(e)}function Kr(e){return 31-Sv(e)}function pc(e){return Kr(e)}function Yn(e,t){return(e&t)!==z}function Ri(e,t){return(e&t)===t}function Ee(e,t){return e|t}function Vl(e,t){return e&~t}function xv(e,t){return e&t}function ST(e){return e}function vg(e,t){return e!==Pt&&e<t?e:t}function mc(e){for(var t=[],n=0;n<Ks;n++)t.push(e);return t}function hu(e,t,n){e.pendingLanes|=t,t!==Pr&&(e.suspendedLanes=z,e.pingedLanes=z);var a=e.eventTimes,r=pc(t);a[r]=n}function pg(e,t){e.suspendedLanes|=t,e.pingedLanes&=~t;for(var n=e.expirationTimes,a=t;a>0;){var r=Kr(a),i=1<<r;n[r]=ot,a&=~i}}function Dv(e,t,n){e.pingedLanes|=e.suspendedLanes&t}function mg(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=z,e.pingedLanes=z,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t;for(var a=e.entanglements,r=e.eventTimes,i=e.expirationTimes,u=n;u>0;){var o=Kr(u),s=1<<o;a[o]=z,r[o]=ot,i[o]=ot,u&=~s}}function hc(e,t){for(var n=e.entangledLanes|=t,a=e.entanglements,r=n;r;){var i=Kr(r),u=1<<i;u&t|a[i]&t&&(a[i]|=t),r&=~u}}function hg(e,t){var n=Gr(t),a;switch(n){case ar:a=Si;break;case Aa:a=Qr;break;case fu:case Xs:case Js:case Zs:case ks:case Ws:case Is:case $s:case ec:case tc:case nc:case ac:case rc:case ic:case uc:case lc:case Ei:case oc:case sc:case cc:case fc:a=cu;break;case Pr:a=vu;break;default:a=Pt;break}return(a&(e.suspendedLanes|t))!==Pt?Pt:a}function _v(e,t,n){if(va)for(var a=e.pendingUpdatersLaneMap;n>0;){var r=pc(n),i=1<<r,u=a[r];u.add(t),n&=~i}}function Ov(e,t){if(va)for(var n=e.pendingUpdatersLaneMap,a=e.memoizedUpdaters;t>0;){var r=pc(t),i=1<<r,u=n[r];u.size>0&&(u.forEach(function(o){var s=o.alternate;(s===null||!a.has(s))&&a.add(o)}),u.clear()),t&=~i}}function Nv(e,t){return null}var za=fe,yu=ar,gu=Aa,yc=Pr,Su=Pt;function da(){return Su}function Gt(e){Su=e}function yg(e,t){var n=Su;try{return Su=e,t()}finally{Su=n}}function gg(e,t){return e!==0&&e<t?e:t}function Sg(e,t){return e===0||e>t?e:t}function Uv(e,t){return e!==0&&e<t}function Mv(e){var t=Gr(e);return Uv(za,t)?Uv(yu,t)?vc(t)?gu:yc:yu:za}var Av=g.unstable_scheduleCallback,bg=g.unstable_cancelCallback,Eg=g.unstable_shouldYield,Rg=g.unstable_requestPaint,Kt=g.unstable_now,wl=g.unstable_ImmediatePriority,zv=g.unstable_UserBlockingPriority,Ti=g.unstable_NormalPriority,Hv=g.unstable_IdlePriority,Tg=g.unstable_yieldValue,Cg=g.unstable_setDisableYieldValue,Xr=null,$t=null,P=null,Ha=!1,va=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined";function xg(e){if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined")return!1;var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(t.isDisabled)return!0;if(!t.supportsFiber)return h("The installed version of React DevTools is too old and will not work with the current version of React. Please update React DevTools. https://reactjs.org/link/react-devtools"),!0;try{ft&&(e=Q({},e,{getLaneLabelMap:Mg,injectProfilingHooks:Ug})),Xr=t.inject(e),$t=t}catch(n){h("React instrumentation encountered an error: %s.",n)}return!!t.checkDCE}function Dg(e,t){if($t&&typeof $t.onScheduleFiberRoot=="function")try{$t.onScheduleFiberRoot(Xr,e,t)}catch(n){Ha||(Ha=!0,h("React instrumentation encountered an error: %s",n))}}function _g(e,t){if($t&&typeof $t.onCommitFiberRoot=="function")try{var n=(e.current.flags&ze)===ze;if(gt){var a;switch(t){case za:a=wl;break;case yu:a=zv;break;case gu:a=Ti;break;case yc:a=Hv;break;default:a=Ti;break}$t.onCommitFiberRoot(Xr,e,a,n)}else $t.onCommitFiberRoot(Xr,e,void 0,n)}catch(r){Ha||(Ha=!0,h("React instrumentation encountered an error: %s",r))}}function Og(e){if($t&&typeof $t.onPostCommitFiberRoot=="function")try{$t.onPostCommitFiberRoot(Xr,e)}catch(t){Ha||(Ha=!0,h("React instrumentation encountered an error: %s",t))}}function Ng(e){if($t&&typeof $t.onCommitFiberUnmount=="function")try{$t.onCommitFiberUnmount(Xr,e)}catch(t){Ha||(Ha=!0,h("React instrumentation encountered an error: %s",t))}}function Xt(e){if(typeof Tg=="function"&&(Cg(e),B(e)),$t&&typeof $t.setStrictMode=="function")try{$t.setStrictMode(Xr,e)}catch(t){Ha||(Ha=!0,h("React instrumentation encountered an error: %s",t))}}function Ug(e){P=e}function Mg(){{for(var e=new Map,t=1,n=0;n<Ks;n++){var a=ag(t);e.set(t,a),t*=2}return e}}function Ag(e){P!==null&&typeof P.markCommitStarted=="function"&&P.markCommitStarted(e)}function Lv(){P!==null&&typeof P.markCommitStopped=="function"&&P.markCommitStopped()}function bu(e){P!==null&&typeof P.markComponentRenderStarted=="function"&&P.markComponentRenderStarted(e)}function Ci(){P!==null&&typeof P.markComponentRenderStopped=="function"&&P.markComponentRenderStopped()}function zg(e){P!==null&&typeof P.markComponentPassiveEffectMountStarted=="function"&&P.markComponentPassiveEffectMountStarted(e)}function Hg(){P!==null&&typeof P.markComponentPassiveEffectMountStopped=="function"&&P.markComponentPassiveEffectMountStopped()}function Lg(e){P!==null&&typeof P.markComponentPassiveEffectUnmountStarted=="function"&&P.markComponentPassiveEffectUnmountStarted(e)}function jg(){P!==null&&typeof P.markComponentPassiveEffectUnmountStopped=="function"&&P.markComponentPassiveEffectUnmountStopped()}function Fg(e){P!==null&&typeof P.markComponentLayoutEffectMountStarted=="function"&&P.markComponentLayoutEffectMountStarted(e)}function Bg(){P!==null&&typeof P.markComponentLayoutEffectMountStopped=="function"&&P.markComponentLayoutEffectMountStopped()}function jv(e){P!==null&&typeof P.markComponentLayoutEffectUnmountStarted=="function"&&P.markComponentLayoutEffectUnmountStarted(e)}function Fv(){P!==null&&typeof P.markComponentLayoutEffectUnmountStopped=="function"&&P.markComponentLayoutEffectUnmountStopped()}function Vg(e,t,n){P!==null&&typeof P.markComponentErrored=="function"&&P.markComponentErrored(e,t,n)}function wg(e,t,n){P!==null&&typeof P.markComponentSuspended=="function"&&P.markComponentSuspended(e,t,n)}function Yg(e){P!==null&&typeof P.markLayoutEffectsStarted=="function"&&P.markLayoutEffectsStarted(e)}function qg(){P!==null&&typeof P.markLayoutEffectsStopped=="function"&&P.markLayoutEffectsStopped()}function Qg(e){P!==null&&typeof P.markPassiveEffectsStarted=="function"&&P.markPassiveEffectsStarted(e)}function Pg(){P!==null&&typeof P.markPassiveEffectsStopped=="function"&&P.markPassiveEffectsStopped()}function Bv(e){P!==null&&typeof P.markRenderStarted=="function"&&P.markRenderStarted(e)}function Gg(){P!==null&&typeof P.markRenderYielded=="function"&&P.markRenderYielded()}function Vv(){P!==null&&typeof P.markRenderStopped=="function"&&P.markRenderStopped()}function Kg(e){P!==null&&typeof P.markRenderScheduled=="function"&&P.markRenderScheduled(e)}function Xg(e,t){P!==null&&typeof P.markForceUpdateScheduled=="function"&&P.markForceUpdateScheduled(e,t)}function gc(e,t){P!==null&&typeof P.markStateUpdateScheduled=="function"&&P.markStateUpdateScheduled(e,t)}function Jg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var qn=typeof Object.is=="function"?Object.is:Jg,rr=null,Sc=!1,bc=!1;function wv(e){rr===null?rr=[e]:rr.push(e)}function Zg(e){Sc=!0,wv(e)}function Yv(){Sc&&La()}function La(){if(!bc&&rr!==null){bc=!0;var e=0,t=da();try{var n=!0,a=rr;for(Gt(za);e<a.length;e++){var r=a[e];do r=r(n);while(r!==null)}rr=null,Sc=!1}catch(i){throw rr!==null&&(rr=rr.slice(e+1)),Av(wl,La),i}finally{Gt(t),bc=!1}}return null}function qv(e){var t=e.current.memoizedState;return t.isDehydrated}var xi=[],Di=0,Yl=null,ql=0,In=[],$n=0,Jr=null,ir=1,ur="";function kg(e){return kr(),(e.flags&d)!==X}function Wg(e){return kr(),ql}function Ig(){var e=ur,t=ir,n=t&~$g(t);return n.toString(32)+e}function Zr(e,t){kr(),xi[Di++]=ql,xi[Di++]=Yl,Yl=e,ql=t}function Qv(e,t,n){kr(),In[$n++]=ir,In[$n++]=ur,In[$n++]=Jr,Jr=e;var a=ir,r=ur,i=Ql(a)-1,u=a&~(1<<i),o=n+1,s=Ql(t)+i;if(s>30){var f=i-i%5,m=(1<<f)-1,S=(u&m).toString(32),C=u>>f,O=i-f,U=Ql(t)+O,N=o<<O,I=N|C,oe=S+r;ir=1<<U|I,ur=oe}else{var te=o<<i,Ge=te|u,je=r;ir=1<<s|Ge,ur=je}}function Ec(e){kr();var t=e.return;if(t!==null){var n=1,a=0;Zr(e,n),Qv(e,n,a)}}function Ql(e){return 32-Sv(e)}function $g(e){return 1<<Ql(e)-1}function Rc(e){for(;e===Yl;)Yl=xi[--Di],xi[Di]=null,ql=xi[--Di],xi[Di]=null;for(;e===Jr;)Jr=In[--$n],In[$n]=null,ur=In[--$n],In[$n]=null,ir=In[--$n],In[$n]=null}function eS(){return kr(),Jr!==null?{id:ir,overflow:ur}:null}function tS(e,t){kr(),In[$n++]=ir,In[$n++]=ur,In[$n++]=Jr,ir=t.id,ur=t.overflow,Jr=e}function kr(){tn()||h("Expected to be hydrating. This is a bug in React. Please file an issue.")}var en=null,ea=null,pa=!1,Cr=!1,xr=null;function nS(){pa&&h("We should not be hydrating here. This is a bug in React. Please file a bug.")}function Pv(){Cr=!0}function aS(){return Cr}function rS(e){if(!Mt)return!1;var t=e.stateNode.containerInfo;return ea=Hs(t),en=e,pa=!0,xr=null,Cr=!1,!0}function iS(e,t,n){return Mt?(ea=_l(t),en=e,pa=!0,xr=null,Cr=!1,n!==null&&tS(e,n),!0):!1}function Gv(e,t){switch(e.tag){case se:{Ly(e.stateNode.containerInfo,t);break}case $:{var n=(e.mode&Be)!==le;Fy(e.type,e.memoizedProps,e.stateNode,t,n);break}case Fe:{var a=e.memoizedState;a.dehydrated!==null&&jy(a.dehydrated,t);break}}}function Kv(e,t){Gv(e,t);var n=MR();n.stateNode=t,n.return=e;var a=e.deletions;a===null?(e.deletions=[n],e.flags|=Wt):a.push(n)}function Tc(e,t){{if(Cr)return;switch(e.tag){case se:{var n=e.stateNode.containerInfo;switch(t.tag){case $:var a=t.type,r=t.pendingProps;By(n,a,r);break;case ge:var i=t.pendingProps;Vy(n,i);break;case Fe:wy(n);break}break}case $:{var u=e.type,o=e.memoizedProps,s=e.stateNode;switch(t.tag){case $:{var f=t.type,m=t.pendingProps,S=(e.mode&Be)!==le;Py(u,o,s,f,m,S);break}case ge:{var C=t.pendingProps,O=(e.mode&Be)!==le;Gy(u,o,s,C,O);break}case Fe:{Ky(u,o,s);break}}break}case Fe:{var U=e.memoizedState,N=U.dehydrated;if(N!==null)switch(t.tag){case $:var I=t.type,oe=t.pendingProps;Yy(N,I,oe);break;case ge:var te=t.pendingProps;qy(N,te);break;case Fe:Qy(N);break}break}default:return}}}function Xv(e,t){t.flags=t.flags&~Nn|qe,Tc(e,t)}function Jv(e,t){switch(e.tag){case $:{var n=e.type,a=e.pendingProps,r=lt(t,n,a);return r!==null?(e.stateNode=r,en=e,ea=Dl(r),!0):!1}case ge:{var i=e.pendingProps,u=ct(t,i);return u!==null?(e.stateNode=u,en=e,ea=null,!0):!1}case Fe:{var o=ht(t);if(o!==null){var s={dehydrated:o,treeContext:eS(),retryLane:wn};e.memoizedState=s;var f=AR(o);return f.return=e,e.child=f,en=e,ea=null,!0}return!1}default:return!1}}function Cc(e){return(e.mode&Be)!==le&&(e.flags&ze)===X}function xc(e){throw new Error("Hydration failed because the initial UI does not match what was rendered on the server.")}function Dc(e){if(pa){var t=ea;if(!t){Cc(e)&&(Tc(en,e),xc()),Xv(en,e),pa=!1,en=e;return}var n=t;if(!Jv(e,t)){Cc(e)&&(Tc(en,e),xc()),t=mi(n);var a=en;if(!t||!Jv(e,t)){Xv(en,e),pa=!1,en=e;return}Kv(a,n)}}}function uS(e,t,n){if(!Mt)throw new Error("Expected prepareToHydrateHostInstance() to never be called. This error is likely caused by a bug in React. Please file an issue.");var a=e.stateNode,r=!Cr,i=Ls(a,e.type,e.memoizedProps,t,n,e,r);return e.updateQueue=i,i!==null}function lS(e){if(!Mt)throw new Error("Expected prepareToHydrateHostTextInstance() to never be called. This error is likely caused by a bug in React. Please file an issue.");var t=e.stateNode,n=e.memoizedProps,a=!Cr,r=xy(t,n,e,a);if(r){var i=en;if(i!==null)switch(i.tag){case se:{var u=i.stateNode.containerInfo,o=(i.mode&Be)!==le;zy(u,t,n,o);break}case $:{var s=i.type,f=i.memoizedProps,m=i.stateNode,S=(i.mode&Be)!==le;Hy(s,f,m,t,n,S);break}}}return r}function oS(e){if(!Mt)throw new Error("Expected prepareToHydrateHostSuspenseInstance() to never be called. This error is likely caused by a bug in React. Please file an issue.");var t=e.memoizedState,n=t!==null?t.dehydrated:null;if(!n)throw new Error("Expected to have a hydrated suspense instance. This error is likely caused by a bug in React. Please file an issue.");Dy(n,e)}function sS(e){if(!Mt)throw new Error("Expected skipPastDehydratedSuspenseInstance() to never be called. This error is likely caused by a bug in React. Please file an issue.");var t=e.memoizedState,n=t!==null?t.dehydrated:null;if(!n)throw new Error("Expected to have a hydrated suspense instance. This error is likely caused by a bug in React. Please file an issue.");return _y(n)}function Zv(e){for(var t=e.return;t!==null&&t.tag!==$&&t.tag!==se&&t.tag!==Fe;)t=t.return;en=t}function Pl(e){if(!Mt||e!==en)return!1;if(!pa)return Zv(e),pa=!0,!1;if(e.tag!==se&&(e.tag!==$||Ay(e.type)&&!nu(e.type,e.memoizedProps))){var t=ea;if(t)if(Cc(e))kv(e),xc();else for(;t;)Kv(e,t),t=mi(t)}return Zv(e),e.tag===Fe?ea=sS(e):ea=en?mi(e.stateNode):null,!0}function cS(){return pa&&ea!==null}function kv(e){for(var t=ea;t;)Gv(e,t),t=mi(t)}function _i(){Mt&&(en=null,ea=null,pa=!1,Cr=!1)}function Wv(){xr!==null&&(Wm(xr),xr=null)}function tn(){return pa}function _c(e){xr===null?xr=[e]:xr.push(e)}var fS=M.ReactCurrentBatchConfig,dS=null;function vS(){return fS.transition}function Gl(e,t){if(qn(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),a=Object.keys(t);if(n.length!==a.length)return!1;for(var r=0;r<n.length;r++){var i=n[r];if(!fv.call(t,i)||!qn(e[i],t[i]))return!1}return!0}function pS(e){var t=e._debugOwner?e._debugOwner.type:null,n=e._debugSource;switch(e.tag){case $:return Rr(e.type);case Ct:return Rr("Lazy");case Fe:return Rr("Suspense");case St:return Rr("SuspenseList");case Ne:case yn:case Ae:return ws(e.type);case Me:return ws(e.type.render);case ye:return Wy(e.type);default:return""}}function Iv(e){try{var t="",n=e;do t+=pS(n),n=n.return;while(n);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}var $v=M.ReactDebugCurrentFrame,Qn=null,Eu=!1;function mS(){{if(Qn===null)return null;var e=Qn._debugOwner;if(e!==null&&typeof e!="undefined")return W(e)}return null}function hS(){return Qn===null?"":Iv(Qn)}function bn(){$v.getCurrentStack=null,Qn=null,Eu=!1}function Et(e){$v.getCurrentStack=e===null?null:hS,Qn=e,Eu=!1}function yS(){return Qn}function ja(e){Eu=e}var ma={recordUnsafeLifecycleWarnings:function(e,t){},flushPendingUnsafeLifecycleWarnings:function(){},recordLegacyContextWarning:function(e,t){},flushLegacyContextWarning:function(){},discardPendingWarnings:function(){}};{var gS=function(e){for(var t=null,n=e;n!==null;)n.mode&_t&&(t=n),n=n.return;return t},Wr=function(e){var t=[];return e.forEach(function(n){t.push(n)}),t.sort().join(", ")},Ru=[],Tu=[],Cu=[],xu=[],Du=[],_u=[],Ir=new Set;ma.recordUnsafeLifecycleWarnings=function(e,t){Ir.has(e.type)||(typeof t.componentWillMount=="function"&&t.componentWillMount.__suppressDeprecationWarning!==!0&&Ru.push(e),e.mode&_t&&typeof t.UNSAFE_componentWillMount=="function"&&Tu.push(e),typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps.__suppressDeprecationWarning!==!0&&Cu.push(e),e.mode&_t&&typeof t.UNSAFE_componentWillReceiveProps=="function"&&xu.push(e),typeof t.componentWillUpdate=="function"&&t.componentWillUpdate.__suppressDeprecationWarning!==!0&&Du.push(e),e.mode&_t&&typeof t.UNSAFE_componentWillUpdate=="function"&&_u.push(e))},ma.flushPendingUnsafeLifecycleWarnings=function(){var e=new Set;Ru.length>0&&(Ru.forEach(function(C){e.add(W(C)||"Component"),Ir.add(C.type)}),Ru=[]);var t=new Set;Tu.length>0&&(Tu.forEach(function(C){t.add(W(C)||"Component"),Ir.add(C.type)}),Tu=[]);var n=new Set;Cu.length>0&&(Cu.forEach(function(C){n.add(W(C)||"Component"),Ir.add(C.type)}),Cu=[]);var a=new Set;xu.length>0&&(xu.forEach(function(C){a.add(W(C)||"Component"),Ir.add(C.type)}),xu=[]);var r=new Set;Du.length>0&&(Du.forEach(function(C){r.add(W(C)||"Component"),Ir.add(C.type)}),Du=[]);var i=new Set;if(_u.length>0&&(_u.forEach(function(C){i.add(W(C)||"Component"),Ir.add(C.type)}),_u=[]),t.size>0){var u=Wr(t);h(`Using UNSAFE_componentWillMount in strict mode is not recommended and may indicate bugs in your code. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move code with side effects to componentDidMount, and set initial state in the constructor.

Please update the following components: %s`,u)}if(a.size>0){var o=Wr(a);h(`Using UNSAFE_componentWillReceiveProps in strict mode is not recommended and may indicate bugs in your code. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://reactjs.org/link/derived-state

Please update the following components: %s`,o)}if(i.size>0){var s=Wr(i);h(`Using UNSAFE_componentWillUpdate in strict mode is not recommended and may indicate bugs in your code. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.

Please update the following components: %s`,s)}if(e.size>0){var f=Wr(e);A(`componentWillMount has been renamed, and is not recommended for use. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move code with side effects to componentDidMount, and set initial state in the constructor.
* Rename componentWillMount to UNSAFE_componentWillMount to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run \`npx react-codemod rename-unsafe-lifecycles\` in your project source folder.

Please update the following components: %s`,f)}if(n.size>0){var m=Wr(n);A(`componentWillReceiveProps has been renamed, and is not recommended for use. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://reactjs.org/link/derived-state
* Rename componentWillReceiveProps to UNSAFE_componentWillReceiveProps to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run \`npx react-codemod rename-unsafe-lifecycles\` in your project source folder.

Please update the following components: %s`,m)}if(r.size>0){var S=Wr(r);A(`componentWillUpdate has been renamed, and is not recommended for use. See https://reactjs.org/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* Rename componentWillUpdate to UNSAFE_componentWillUpdate to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run \`npx react-codemod rename-unsafe-lifecycles\` in your project source folder.

Please update the following components: %s`,S)}};var Kl=new Map,ep=new Set;ma.recordLegacyContextWarning=function(e,t){var n=gS(e);if(n===null){h("Expected to find a StrictMode component in a strict mode tree. This error is likely caused by a bug in React. Please file an issue.");return}if(!ep.has(e.type)){var a=Kl.get(n);(e.type.contextTypes!=null||e.type.childContextTypes!=null||t!==null&&typeof t.getChildContext=="function")&&(a===void 0&&(a=[],Kl.set(n,a)),a.push(e))}},ma.flushLegacyContextWarning=function(){Kl.forEach(function(e,t){if(e.length!==0){var n=e[0],a=new Set;e.forEach(function(i){a.add(W(i)||"Component"),ep.add(i.type)});var r=Wr(a);try{Et(n),h(`Legacy context API has been detected within a strict-mode tree.

The old API will be supported in all 16.x releases, but applications using it should migrate to the new version.

Please update the following components: %s

Learn more about this warning here: https://reactjs.org/link/legacy-context`,r)}finally{bn()}}})},ma.discardPendingWarnings=function(){Ru=[],Tu=[],Cu=[],xu=[],Du=[],_u=[],Kl=new Map}}function tp(e){{var t=typeof Symbol=="function"&&Symbol.toStringTag,n=t&&e[Symbol.toStringTag]||e.constructor.name||"Object";return n}}function np(e){try{return Oc(e),!1}catch(t){return!0}}function Oc(e){return""+e}function SS(e){if(np(e))return h("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",tp(e)),Oc(e)}function bS(e,t){if(np(e))return h("The provided `%s` prop is an unsupported type %s. This value must be coerced to a string before before using it here.",t,tp(e)),Oc(e)}function ha(e,t){if(e&&e.defaultProps){var n=Q({},t),a=e.defaultProps;for(var r in a)n[r]===void 0&&(n[r]=a[r]);return n}return t}var Xl=Tr(null),Ou;Ou={};var Jl=null,Oi=null,Nc=null,Zl=!1;function kl(){Jl=null,Oi=null,Nc=null,Zl=!1}function ap(){Zl=!0}function rp(){Zl=!1}function ip(e,t,n){Yr?(It(Xl,t._currentValue,e),t._currentValue=n,t._currentRenderer!==void 0&&t._currentRenderer!==null&&t._currentRenderer!==Ou&&h("Detected multiple renderers concurrently rendering the same context provider. This is currently unsupported."),t._currentRenderer=Ou):(It(Xl,t._currentValue2,e),t._currentValue2=n,t._currentRenderer2!==void 0&&t._currentRenderer2!==null&&t._currentRenderer2!==Ou&&h("Detected multiple renderers concurrently rendering the same context provider. This is currently unsupported."),t._currentRenderer2=Ou)}function Uc(e,t){var n=Xl.current;dn(Xl,t),Yr?e._currentValue=n:e._currentValue2=n}function Mc(e,t,n){for(var a=e;a!==null;){var r=a.alternate;if(Ri(a.childLanes,t)?r!==null&&!Ri(r.childLanes,t)&&(r.childLanes=Ee(r.childLanes,t)):(a.childLanes=Ee(a.childLanes,t),r!==null&&(r.childLanes=Ee(r.childLanes,t))),a===n)break;a=a.return}a!==n&&h("Expected to find the propagation root when scheduling context work. This error is likely caused by a bug in React. Please file an issue.")}function ES(e,t,n){RS(e,t,n)}function RS(e,t,n){var a=e.child;for(a!==null&&(a.return=e);a!==null;){var r=void 0,i=a.dependencies;if(i!==null){r=a.child;for(var u=i.firstContext;u!==null;){if(u.context===t){if(a.tag===ye){var o=mu(n),s=lr(ot,o);s.tag=Il;var f=a.updateQueue;if(f!==null){var m=f.shared,S=m.pending;S===null?s.next=s:(s.next=S.next,S.next=s),m.pending=s}}a.lanes=Ee(a.lanes,n);var C=a.alternate;C!==null&&(C.lanes=Ee(C.lanes,n)),Mc(a.return,n,e),i.lanes=Ee(i.lanes,n);break}u=u.next}}else if(a.tag===Rt)r=a.type===e.type?null:a.child;else if(a.tag===Xn){var O=a.return;if(O===null)throw new Error("We just came from a parent so we must have had a parent. This is a bug in React.");O.lanes=Ee(O.lanes,n);var U=O.alternate;U!==null&&(U.lanes=Ee(U.lanes,n)),Mc(O,n,e),r=a.sibling}else r=a.child;if(r!==null)r.return=a;else for(r=a;r!==null;){if(r===e){r=null;break}var N=r.sibling;if(N!==null){N.return=r.return,r=N;break}r=r.return}a=r}}function Ni(e,t){Jl=e,Oi=null,Nc=null;var n=e.dependencies;if(n!==null){var a=n.firstContext;a!==null&&(Yn(n.lanes,t)&&Qu(),n.firstContext=null)}}function Ot(e){Zl&&h("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().");var t=Yr?e._currentValue:e._currentValue2;if(Nc!==e){var n={context:e,memoizedValue:t,next:null};if(Oi===null){if(Jl===null)throw new Error("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().");Oi=n,Jl.dependencies={lanes:z,firstContext:n}}else Oi=Oi.next=n}return t}var $r=null;function Ac(e){$r===null?$r=[e]:$r.push(e)}function TS(){if($r!==null){for(var e=0;e<$r.length;e++){var t=$r[e],n=t.interleaved;if(n!==null){t.interleaved=null;var a=n.next,r=t.pending;if(r!==null){var i=r.next;r.next=a,n.next=i}t.pending=n}}$r=null}}function up(e,t,n,a){var r=t.interleaved;return r===null?(n.next=n,Ac(t)):(n.next=r.next,r.next=n),t.interleaved=n,Wl(e,a)}function CS(e,t,n,a){var r=t.interleaved;r===null?(n.next=n,Ac(t)):(n.next=r.next,r.next=n),t.interleaved=n}function xS(e,t,n,a){var r=t.interleaved;return r===null?(n.next=n,Ac(t)):(n.next=r.next,r.next=n),t.interleaved=n,Wl(e,a)}function En(e,t){return Wl(e,t)}var DS=Wl;function Wl(e,t){e.lanes=Ee(e.lanes,t);var n=e.alternate;n!==null&&(n.lanes=Ee(n.lanes,t)),n===null&&(e.flags&(qe|Nn))!==X&&oh(e);for(var a=e,r=e.return;r!==null;)r.childLanes=Ee(r.childLanes,t),n=r.alternate,n!==null?n.childLanes=Ee(n.childLanes,t):(r.flags&(qe|Nn))!==X&&oh(e),a=r,r=r.return;if(a.tag===se){var i=a.stateNode;return i}else return null}var lp=0,op=1,Il=2,zc=3,$l=!1,Hc,eo;Hc=!1,eo=null;function Lc(e){var t={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:z},effects:null};e.updateQueue=t}function sp(e,t){var n=t.updateQueue,a=e.updateQueue;if(n===a){var r={baseState:a.baseState,firstBaseUpdate:a.firstBaseUpdate,lastBaseUpdate:a.lastBaseUpdate,shared:a.shared,effects:a.effects};t.updateQueue=r}}function lr(e,t){var n={eventTime:e,lane:t,tag:lp,payload:null,callback:null,next:null};return n}function Dr(e,t,n){var a=e.updateQueue;if(a===null)return null;var r=a.shared;if(eo===r&&!Hc&&(h("An update (setState, replaceState, or forceUpdate) was scheduled from inside an update function. Update functions should be pure, with zero side-effects. Consider using componentDidUpdate or a callback."),Hc=!0),VE()){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,DS(e,n)}else return xS(e,r,t,n)}function to(e,t,n){var a=t.updateQueue;if(a!==null){var r=a.shared;if(Tv(n)){var i=r.lanes;i=xv(i,e.pendingLanes);var u=Ee(i,n);r.lanes=u,hc(e,u)}}}function jc(e,t){var n=e.updateQueue,a=e.alternate;if(a!==null){var r=a.updateQueue;if(n===r){var i=null,u=null,o=n.firstBaseUpdate;if(o!==null){var s=o;do{var f={eventTime:s.eventTime,lane:s.lane,tag:s.tag,payload:s.payload,callback:s.callback,next:null};u===null?i=u=f:(u.next=f,u=f),s=s.next}while(s!==null);u===null?i=u=t:(u.next=t,u=t)}else i=u=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:u,shared:r.shared,effects:r.effects},e.updateQueue=n;return}}var m=n.lastBaseUpdate;m===null?n.firstBaseUpdate=t:m.next=t,n.lastBaseUpdate=t}function _S(e,t,n,a,r,i){switch(n.tag){case op:{var u=n.payload;if(typeof u=="function"){ap();var o=u.call(i,a,r);{if(e.mode&_t){Xt(!0);try{u.call(i,a,r)}finally{Xt(!1)}}rp()}return o}return u}case zc:e.flags=e.flags&~xt|ze;case lp:{var s=n.payload,f;if(typeof s=="function"){ap(),f=s.call(i,a,r);{if(e.mode&_t){Xt(!0);try{s.call(i,a,r)}finally{Xt(!1)}}rp()}}else f=s;return f==null?a:Q({},a,f)}case Il:return $l=!0,a}return a}function no(e,t,n,a){var r=e.updateQueue;$l=!1,eo=r.shared;var i=r.firstBaseUpdate,u=r.lastBaseUpdate,o=r.shared.pending;if(o!==null){r.shared.pending=null;var s=o,f=s.next;s.next=null,u===null?i=f:u.next=f,u=s;var m=e.alternate;if(m!==null){var S=m.updateQueue,C=S.lastBaseUpdate;C!==u&&(C===null?S.firstBaseUpdate=f:C.next=f,S.lastBaseUpdate=s)}}if(i!==null){var O=r.baseState,U=z,N=null,I=null,oe=null,te=i;do{var Ge=te.lane,je=te.eventTime;if(Ri(a,Ge)){if(oe!==null){var D={eventTime:je,lane:Pt,tag:te.tag,payload:te.payload,callback:te.callback,next:null};oe=oe.next=D}O=_S(e,r,te,O,t,n);var b=te.callback;if(b!==null&&te.lane!==Pt){e.flags|=mr;var L=r.effects;L===null?r.effects=[te]:L.push(te)}}else{var E={eventTime:je,lane:Ge,tag:te.tag,payload:te.payload,callback:te.callback,next:null};oe===null?(I=oe=E,N=O):oe=oe.next=E,U=Ee(U,Ge)}if(te=te.next,te===null){if(o=r.shared.pending,o===null)break;var k=o,K=k.next;k.next=null,te=K,r.lastBaseUpdate=k,r.shared.pending=null}}while(!0);oe===null&&(N=O),r.baseState=N,r.firstBaseUpdate=I,r.lastBaseUpdate=oe;var He=r.shared.interleaved;if(He!==null){var de=He;do U=Ee(U,de.lane),de=de.next;while(de!==He)}else i===null&&(r.shared.lanes=z);al(U),e.lanes=U,e.memoizedState=O}eo=null}function OS(e,t){if(typeof e!="function")throw new Error("Invalid argument passed as callback. Expected a function. Instead "+("received: "+e));e.call(t)}function cp(){$l=!1}function ao(){return $l}function fp(e,t,n){var a=t.effects;if(t.effects=null,a!==null)for(var r=0;r<a.length;r++){var i=a[r],u=i.callback;u!==null&&(i.callback=null,OS(u,n))}}var Fc={},dp=new T.Component().refs,Bc,Vc,wc,Yc,qc,vp,ro,Qc,Pc,Gc;{Bc=new Set,Vc=new Set,wc=new Set,Yc=new Set,Qc=new Set,qc=new Set,Pc=new Set,Gc=new Set;var pp=new Set;ro=function(e,t){if(!(e===null||typeof e=="function")){var n=t+"_"+e;pp.has(n)||(pp.add(n),h("%s(...): Expected the last optional `callback` argument to be a function. Instead received: %s.",t,e))}},vp=function(e,t){if(t===void 0){var n=ee(e)||"Component";qc.has(n)||(qc.add(n),h("%s.getDerivedStateFromProps(): A valid state object (or null) must be returned. You have returned undefined.",n))}},Object.defineProperty(Fc,"_processChildContext",{enumerable:!1,value:function(){throw new Error("_processChildContext is not available in React 16+. This likely means you have multiple copies of React and are attempting to nest a React 15 tree inside a React 16 tree using unstable_renderSubtreeIntoContainer, which isn't supported. Try to make sure you have only one copy of React (and ideally, switch to ReactDOM.createPortal).")}}),Object.freeze(Fc)}function Kc(e,t,n,a){var r=e.memoizedState,i=n(a,r);{if(e.mode&_t){Xt(!0);try{i=n(a,r)}finally{Xt(!1)}}vp(t,i)}var u=i==null?r:Q({},r,i);if(e.memoizedState=u,e.lanes===z){var o=e.updateQueue;o.baseState=u}}var Xc={isMounted:qt,enqueueSetState:function(e,t,n){var a=he(e),r=pn(),i=zr(a),u=lr(r,i);u.payload=t,n!=null&&(ro(n,"setState"),u.callback=n);var o=Dr(a,u,i);o!==null&&(Ut(o,a,i,r),to(o,a,i)),gc(a,i)},enqueueReplaceState:function(e,t,n){var a=he(e),r=pn(),i=zr(a),u=lr(r,i);u.tag=op,u.payload=t,n!=null&&(ro(n,"replaceState"),u.callback=n);var o=Dr(a,u,i);o!==null&&(Ut(o,a,i,r),to(o,a,i)),gc(a,i)},enqueueForceUpdate:function(e,t){var n=he(e),a=pn(),r=zr(n),i=lr(a,r);i.tag=Il,t!=null&&(ro(t,"forceUpdate"),i.callback=t);var u=Dr(n,i,r);u!==null&&(Ut(u,n,r,a),to(u,n,r)),Xg(n,r)}};function mp(e,t,n,a,r,i,u){var o=e.stateNode;if(typeof o.shouldComponentUpdate=="function"){var s=o.shouldComponentUpdate(a,i,u);{if(e.mode&_t){Xt(!0);try{s=o.shouldComponentUpdate(a,i,u)}finally{Xt(!1)}}s===void 0&&h("%s.shouldComponentUpdate(): Returned undefined instead of a boolean value. Make sure to return true or false.",ee(t)||"Component")}return s}return t.prototype&&t.prototype.isPureReactComponent?!Gl(n,a)||!Gl(r,i):!0}function NS(e,t,n){var a=e.stateNode;{var r=ee(t)||"Component",i=a.render;i||(t.prototype&&typeof t.prototype.render=="function"?h("%s(...): No `render` method found on the returned component instance: did you accidentally return an object from the constructor?",r):h("%s(...): No `render` method found on the returned component instance: you may have forgotten to define `render`.",r)),a.getInitialState&&!a.getInitialState.isReactClassApproved&&!a.state&&h("getInitialState was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Did you mean to define a state property instead?",r),a.getDefaultProps&&!a.getDefaultProps.isReactClassApproved&&h("getDefaultProps was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Use a static property to define defaultProps instead.",r),a.propTypes&&h("propTypes was defined as an instance property on %s. Use a static property to define propTypes instead.",r),a.contextType&&h("contextType was defined as an instance property on %s. Use a static property to define contextType instead.",r),a.contextTypes&&h("contextTypes was defined as an instance property on %s. Use a static property to define contextTypes instead.",r),t.contextType&&t.contextTypes&&!Pc.has(t)&&(Pc.add(t),h("%s declares both contextTypes and contextType static properties. The legacy contextTypes property will be ignored.",r)),typeof a.componentShouldUpdate=="function"&&h("%s has a method called componentShouldUpdate(). Did you mean shouldComponentUpdate()? The name is phrased as a question because the function is expected to return a value.",r),t.prototype&&t.prototype.isPureReactComponent&&typeof a.shouldComponentUpdate!="undefined"&&h("%s has a method called shouldComponentUpdate(). shouldComponentUpdate should not be used when extending React.PureComponent. Please extend React.Component if shouldComponentUpdate is used.",ee(t)||"A pure component"),typeof a.componentDidUnmount=="function"&&h("%s has a method called componentDidUnmount(). But there is no such lifecycle method. Did you mean componentWillUnmount()?",r),typeof a.componentDidReceiveProps=="function"&&h("%s has a method called componentDidReceiveProps(). But there is no such lifecycle method. If you meant to update the state in response to changing props, use componentWillReceiveProps(). If you meant to fetch data or run side-effects or mutations after React has updated the UI, use componentDidUpdate().",r),typeof a.componentWillRecieveProps=="function"&&h("%s has a method called componentWillRecieveProps(). Did you mean componentWillReceiveProps()?",r),typeof a.UNSAFE_componentWillRecieveProps=="function"&&h("%s has a method called UNSAFE_componentWillRecieveProps(). Did you mean UNSAFE_componentWillReceiveProps()?",r);var u=a.props!==n;a.props!==void 0&&u&&h("%s(...): When calling super() in `%s`, make sure to pass up the same props that your component's constructor was passed.",r,r),a.defaultProps&&h("Setting defaultProps as an instance property on %s is not supported and will be ignored. Instead, define defaultProps as a static property on %s.",r,r),typeof a.getSnapshotBeforeUpdate=="function"&&typeof a.componentDidUpdate!="function"&&!wc.has(t)&&(wc.add(t),h("%s: getSnapshotBeforeUpdate() should be used with componentDidUpdate(). This component defines getSnapshotBeforeUpdate() only.",ee(t))),typeof a.getDerivedStateFromProps=="function"&&h("%s: getDerivedStateFromProps() is defined as an instance method and will be ignored. Instead, declare it as a static method.",r),typeof a.getDerivedStateFromError=="function"&&h("%s: getDerivedStateFromError() is defined as an instance method and will be ignored. Instead, declare it as a static method.",r),typeof t.getSnapshotBeforeUpdate=="function"&&h("%s: getSnapshotBeforeUpdate() is defined as a static method and will be ignored. Instead, declare it as an instance method.",r);var o=a.state;o&&(typeof o!="object"||fn(o))&&h("%s.state: must be set to an object or null",r),typeof a.getChildContext=="function"&&typeof t.childContextTypes!="object"&&h("%s.getChildContext(): childContextTypes must be defined in order to use getChildContext().",r)}}function hp(e,t){t.updater=Xc,e.stateNode=t,at(t,e),t._reactInternalInstance=Fc}function yp(e,t,n){var a=!1,r=Vn,i=Vn,u=t.contextType;if("contextType"in t){var o=u===null||u!==void 0&&u.$$typeof===Ln&&u._context===void 0;if(!o&&!Gc.has(t)){Gc.add(t);var s="";u===void 0?s=" However, it is set to undefined. This can be caused by a typo or by mixing up named and default imports. This can also happen due to a circular dependency, so try moving the createContext() call to a separate file.":typeof u!="object"?s=" However, it is set to a "+typeof u+".":u.$$typeof===Sn?s=" Did you accidentally pass the Context.Provider instead?":u._context!==void 0?s=" Did you accidentally pass the Context.Consumer instead?":s=" However, it is set to an object with keys {"+Object.keys(u).join(", ")+"}.",h("%s defines an invalid contextType. contextType should point to the Context object returned by React.createContext().%s",ee(t)||"Component",s)}}if(typeof u=="object"&&u!==null)i=Ot(u);else{r=hi(e,t,!0);var f=t.contextTypes;a=f!=null,i=a?yi(e,r):Vn}var m=new t(n,i);if(e.mode&_t){Xt(!0);try{m=new t(n,i)}finally{Xt(!1)}}var S=e.memoizedState=m.state!==null&&m.state!==void 0?m.state:null;hp(e,m);{if(typeof t.getDerivedStateFromProps=="function"&&S===null){var C=ee(t)||"Component";Vc.has(C)||(Vc.add(C),h("`%s` uses `getDerivedStateFromProps` but its initial state is %s. This is not recommended. Instead, define the initial state by assigning an object to `this.state` in the constructor of `%s`. This ensures that `getDerivedStateFromProps` arguments have a consistent shape.",C,m.state===null?"null":"undefined",C))}if(typeof t.getDerivedStateFromProps=="function"||typeof m.getSnapshotBeforeUpdate=="function"){var O=null,U=null,N=null;if(typeof m.componentWillMount=="function"&&m.componentWillMount.__suppressDeprecationWarning!==!0?O="componentWillMount":typeof m.UNSAFE_componentWillMount=="function"&&(O="UNSAFE_componentWillMount"),typeof m.componentWillReceiveProps=="function"&&m.componentWillReceiveProps.__suppressDeprecationWarning!==!0?U="componentWillReceiveProps":typeof m.UNSAFE_componentWillReceiveProps=="function"&&(U="UNSAFE_componentWillReceiveProps"),typeof m.componentWillUpdate=="function"&&m.componentWillUpdate.__suppressDeprecationWarning!==!0?N="componentWillUpdate":typeof m.UNSAFE_componentWillUpdate=="function"&&(N="UNSAFE_componentWillUpdate"),O!==null||U!==null||N!==null){var I=ee(t)||"Component",oe=typeof t.getDerivedStateFromProps=="function"?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";Yc.has(I)||(Yc.add(I),h(`Unsafe legacy lifecycles will not be called for components using new component APIs.

%s uses %s but also contains the following legacy lifecycles:%s%s%s

The above lifecycles should be removed. Learn more about this warning here:
https://reactjs.org/link/unsafe-component-lifecycles`,I,oe,O!==null?`
  `+O:"",U!==null?`
  `+U:"",N!==null?`
  `+N:""))}}}return a&&pv(e,r,i),m}function US(e,t){var n=t.state;typeof t.componentWillMount=="function"&&t.componentWillMount(),typeof t.UNSAFE_componentWillMount=="function"&&t.UNSAFE_componentWillMount(),n!==t.state&&(h("%s.componentWillMount(): Assigning directly to this.state is deprecated (except inside a component's constructor). Use setState instead.",W(e)||"Component"),Xc.enqueueReplaceState(t,t.state,null))}function gp(e,t,n,a){var r=t.state;if(typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,a),t.state!==r){{var i=W(e)||"Component";Bc.has(i)||(Bc.add(i),h("%s.componentWillReceiveProps(): Assigning directly to this.state is deprecated (except inside a component's constructor). Use setState instead.",i))}Xc.enqueueReplaceState(t,t.state,null)}}function Jc(e,t,n,a){NS(e,t,n);var r=e.stateNode;r.props=n,r.state=e.memoizedState,r.refs=dp,Lc(e);var i=t.contextType;if(typeof i=="object"&&i!==null)r.context=Ot(i);else{var u=hi(e,t,!0);r.context=yi(e,u)}{if(r.state===n){var o=ee(t)||"Component";Qc.has(o)||(Qc.add(o),h("%s: It is not recommended to assign props directly to state because updates to props won't be reflected in state. In most cases, it is better to use props directly.",o))}e.mode&_t&&ma.recordLegacyContextWarning(e,r),ma.recordUnsafeLifecycleWarnings(e,r)}r.state=e.memoizedState;var s=t.getDerivedStateFromProps;if(typeof s=="function"&&(Kc(e,t,s,n),r.state=e.memoizedState),typeof t.getDerivedStateFromProps!="function"&&typeof r.getSnapshotBeforeUpdate!="function"&&(typeof r.UNSAFE_componentWillMount=="function"||typeof r.componentWillMount=="function")&&(US(e,r),no(e,n,r,a),r.state=e.memoizedState),typeof r.componentDidMount=="function"){var f=ce;f|=V,(e.mode&Ma)!==le&&(f|=be),e.flags|=f}}function MS(e,t,n,a){var r=e.stateNode,i=e.memoizedProps;r.props=i;var u=r.context,o=t.contextType,s=Vn;if(typeof o=="object"&&o!==null)s=Ot(o);else{var f=hi(e,t,!0);s=yi(e,f)}var m=t.getDerivedStateFromProps,S=typeof m=="function"||typeof r.getSnapshotBeforeUpdate=="function";!S&&(typeof r.UNSAFE_componentWillReceiveProps=="function"||typeof r.componentWillReceiveProps=="function")&&(i!==n||u!==s)&&gp(e,r,n,s),cp();var C=e.memoizedState,O=r.state=C;if(no(e,n,r,a),O=e.memoizedState,i===n&&C===O&&!Ml()&&!ao()){if(typeof r.componentDidMount=="function"){var U=ce;U|=V,(e.mode&Ma)!==le&&(U|=be),e.flags|=U}return!1}typeof m=="function"&&(Kc(e,t,m,n),O=e.memoizedState);var N=ao()||mp(e,t,i,n,C,O,s);if(N){if(!S&&(typeof r.UNSAFE_componentWillMount=="function"||typeof r.componentWillMount=="function")&&(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"){var I=ce;I|=V,(e.mode&Ma)!==le&&(I|=be),e.flags|=I}}else{if(typeof r.componentDidMount=="function"){var oe=ce;oe|=V,(e.mode&Ma)!==le&&(oe|=be),e.flags|=oe}e.memoizedProps=n,e.memoizedState=O}return r.props=n,r.state=O,r.context=s,N}function AS(e,t,n,a,r){var i=t.stateNode;sp(e,t);var u=t.memoizedProps,o=t.type===t.elementType?u:ha(t.type,u);i.props=o;var s=t.pendingProps,f=i.context,m=n.contextType,S=Vn;if(typeof m=="object"&&m!==null)S=Ot(m);else{var C=hi(t,n,!0);S=yi(t,C)}var O=n.getDerivedStateFromProps,U=typeof O=="function"||typeof i.getSnapshotBeforeUpdate=="function";!U&&(typeof i.UNSAFE_componentWillReceiveProps=="function"||typeof i.componentWillReceiveProps=="function")&&(u!==s||f!==S)&&gp(t,i,a,S),cp();var N=t.memoizedState,I=i.state=N;if(no(t,a,i,r),I=t.memoizedState,u===s&&N===I&&!Ml()&&!ao()&&!Ye)return typeof i.componentDidUpdate=="function"&&(u!==e.memoizedProps||N!==e.memoizedState)&&(t.flags|=ce),typeof i.getSnapshotBeforeUpdate=="function"&&(u!==e.memoizedProps||N!==e.memoizedState)&&(t.flags|=sn),!1;typeof O=="function"&&(Kc(t,n,O,a),I=t.memoizedState);var oe=ao()||mp(t,n,o,a,N,I,S)||Ye;return oe?(!U&&(typeof i.UNSAFE_componentWillUpdate=="function"||typeof i.componentWillUpdate=="function")&&(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(a,I,S),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(a,I,S)),typeof i.componentDidUpdate=="function"&&(t.flags|=ce),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=sn)):(typeof i.componentDidUpdate=="function"&&(u!==e.memoizedProps||N!==e.memoizedState)&&(t.flags|=ce),typeof i.getSnapshotBeforeUpdate=="function"&&(u!==e.memoizedProps||N!==e.memoizedState)&&(t.flags|=sn),t.memoizedProps=a,t.memoizedState=I),i.props=a,i.state=I,i.context=S,oe}var Zc,kc,Wc,Ic,$c,Sp=function(e,t){};Zc=!1,kc=!1,Wc={},Ic={},$c={},Sp=function(e,t){if(!(e===null||typeof e!="object")&&!(!e._store||e._store.validated||e.key!=null)){if(typeof e._store!="object")throw new Error("React Component in warnForMissingKey should have a _store. This error is likely caused by a bug in React. Please file an issue.");e._store.validated=!0;var n=W(t)||"Component";Ic[n]||(Ic[n]=!0,h('Each child in a list should have a unique "key" prop. See https://reactjs.org/link/warning-keys for more information.'))}};function Nu(e,t,n){var a=n.ref;if(a!==null&&typeof a!="function"&&typeof a!="object"){if((e.mode&_t||xe)&&!(n._owner&&n._self&&n._owner.stateNode!==n._self)){var r=W(e)||"Component";Wc[r]||(h('A string ref, "%s", has been found within a strict mode tree. String refs are a source of potential bugs and should be avoided. We recommend using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',a),Wc[r]=!0)}if(n._owner){var i=n._owner,u;if(i){var o=i;if(o.tag!==ye)throw new Error("Function components cannot have string refs. We recommend using useRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref");u=o.stateNode}if(!u)throw new Error("Missing owner for string ref "+a+". This error is likely caused by a bug in React. Please file an issue.");var s=u;bS(a,"ref");var f=""+a;if(t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===f)return t.ref;var m=function(S){var C=s.refs;C===dp&&(C=s.refs={}),S===null?delete C[f]:C[f]=S};return m._stringRef=f,m}else{if(typeof a!="string")throw new Error("Expected ref to be a function, a string, an object returned by React.createRef(), or null.");if(!n._owner)throw new Error("Element ref was specified as a string ("+a+`) but no owner was set. This could happen for one of the following reasons:
1. You may be adding a ref to a function component
2. You may be adding a ref to a component that was not created inside a component's render method
3. You have multiple copies of React loaded
See https://reactjs.org/link/refs-must-have-owner for more information.`)}}return a}function io(e,t){var n=Object.prototype.toString.call(t);throw new Error("Objects are not valid as a React child (found: "+(n==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":n)+"). If you meant to render a collection of children, use an array instead.")}function uo(e){{var t=W(e)||"Component";if($c[t])return;$c[t]=!0,h("Functions are not valid as a React child. This may happen if you return a Component instead of <Component /> from render. Or maybe you meant to call this function rather than return it.")}}function bp(e){var t=e._payload,n=e._init;return n(t)}function Ep(e){function t(E,D){if(e){var b=E.deletions;b===null?(E.deletions=[D],E.flags|=Wt):b.push(D)}}function n(E,D){if(!e)return null;for(var b=D;b!==null;)t(E,b),b=b.sibling;return null}function a(E,D){for(var b=new Map,L=D;L!==null;)L.key!==null?b.set(L.key,L):b.set(L.index,L),L=L.sibling;return b}function r(E,D){var b=li(E,D);return b.index=0,b.sibling=null,b}function i(E,D,b){if(E.index=b,!e)return E.flags|=d,D;var L=E.alternate;if(L!==null){var k=L.index;return k<D?(E.flags|=qe,D):k}else return E.flags|=qe,D}function u(E){return e&&E.alternate===null&&(E.flags|=qe),E}function o(E,D,b,L){if(D===null||D.tag!==ge){var k=Md(b,E.mode,L);return k.return=E,k}else{var K=r(D,b);return K.return=E,K}}function s(E,D,b,L){var k=b.type;if(k===Zn)return m(E,D,b.props.children,L,b.key);if(D!==null&&(D.elementType===k||dh(D,b)||typeof k=="object"&&k!==null&&k.$$typeof===it&&bp(k)===D.type)){var K=r(D,b.props);return K.ref=Nu(E,D,b),K.return=E,K._debugSource=b._source,K._debugOwner=b._owner,K}var He=Ud(b,E.mode,L);return He.ref=Nu(E,D,b),He.return=E,He}function f(E,D,b,L){if(D===null||D.tag!==Ue||D.stateNode.containerInfo!==b.containerInfo||D.stateNode.implementation!==b.implementation){var k=Ad(b,E.mode,L);return k.return=E,k}else{var K=r(D,b.children||[]);return K.return=E,K}}function m(E,D,b,L,k){if(D===null||D.tag!==Te){var K=Lr(b,E.mode,L,k);return K.return=E,K}else{var He=r(D,b);return He.return=E,He}}function S(E,D,b){if(typeof D=="string"&&D!==""||typeof D=="number"){var L=Md(""+D,E.mode,b);return L.return=E,L}if(typeof D=="object"&&D!==null){switch(D.$$typeof){case Dn:{var k=Ud(D,E.mode,b);return k.ref=Nu(E,null,D),k.return=E,k}case _n:{var K=Ad(D,E.mode,b);return K.return=E,K}case it:{var He=D._payload,de=D._init;return S(E,de(He),b)}}if(fn(D)||w(D)){var we=Lr(D,E.mode,b,null);return we.return=E,we}io(E,D)}return typeof D=="function"&&uo(E),null}function C(E,D,b,L){var k=D!==null?D.key:null;if(typeof b=="string"&&b!==""||typeof b=="number")return k!==null?null:o(E,D,""+b,L);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case Dn:return b.key===k?s(E,D,b,L):null;case _n:return b.key===k?f(E,D,b,L):null;case it:{var K=b._payload,He=b._init;return C(E,D,He(K),L)}}if(fn(b)||w(b))return k!==null?null:m(E,D,b,L,null);io(E,b)}return typeof b=="function"&&uo(E),null}function O(E,D,b,L,k){if(typeof L=="string"&&L!==""||typeof L=="number"){var K=E.get(b)||null;return o(D,K,""+L,k)}if(typeof L=="object"&&L!==null){switch(L.$$typeof){case Dn:{var He=E.get(L.key===null?b:L.key)||null;return s(D,He,L,k)}case _n:{var de=E.get(L.key===null?b:L.key)||null;return f(D,de,L,k)}case it:var we=L._payload,_e=L._init;return O(E,D,b,_e(we),k)}if(fn(L)||w(L)){var tt=E.get(b)||null;return m(D,tt,L,k,null)}io(D,L)}return typeof L=="function"&&uo(D),null}function U(E,D,b){{if(typeof E!="object"||E===null)return D;switch(E.$$typeof){case Dn:case _n:Sp(E,b);var L=E.key;if(typeof L!="string")break;if(D===null){D=new Set,D.add(L);break}if(!D.has(L)){D.add(L);break}h("Encountered two children with the same key, `%s`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted \u2014 the behavior is unsupported and could change in a future version.",L);break;case it:var k=E._payload,K=E._init;U(K(k),D,b);break}}return D}function N(E,D,b,L){for(var k=null,K=0;K<b.length;K++){var He=b[K];k=U(He,k,E)}for(var de=null,we=null,_e=D,tt=0,ae=0,yt=null;_e!==null&&ae<b.length;ae++){_e.index>ae?(yt=_e,_e=null):yt=_e.sibling;var mn=C(E,_e,b[ae],L);if(mn===null){_e===null&&(_e=yt);break}e&&_e&&mn.alternate===null&&t(E,_e),tt=i(mn,tt,ae),we===null?de=mn:we.sibling=mn,we=mn,_e=yt}if(ae===b.length){if(n(E,_e),tn()){var on=ae;Zr(E,on)}return de}if(_e===null){for(;ae<b.length;ae++){var Gn=S(E,b[ae],L);Gn!==null&&(tt=i(Gn,tt,ae),we===null?de=Gn:we.sibling=Gn,we=Gn)}if(tn()){var Cn=ae;Zr(E,Cn)}return de}for(var xn=a(E,_e);ae<b.length;ae++){var hn=O(xn,E,ae,b[ae],L);hn!==null&&(e&&hn.alternate!==null&&xn.delete(hn.key===null?ae:hn.key),tt=i(hn,tt,ae),we===null?de=hn:we.sibling=hn,we=hn)}if(e&&xn.forEach(function(Ki){return t(E,Ki)}),tn()){var fr=ae;Zr(E,fr)}return de}function I(E,D,b,L){var k=w(b);if(typeof k!="function")throw new Error("An object is not an iterable. This error is likely caused by a bug in React. Please file an issue.");{typeof Symbol=="function"&&b[Symbol.toStringTag]==="Generator"&&(kc||h("Using Generators as children is unsupported and will likely yield unexpected results because enumerating a generator mutates it. You may convert it to an array with `Array.from()` or the `[...spread]` operator before rendering. Keep in mind you might need to polyfill these features for older browsers."),kc=!0),b.entries===k&&(Zc||h("Using Maps as children is not supported. Use an array of keyed ReactElements instead."),Zc=!0);var K=k.call(b);if(K)for(var He=null,de=K.next();!de.done;de=K.next()){var we=de.value;He=U(we,He,E)}}var _e=k.call(b);if(_e==null)throw new Error("An iterable object provided no iterator.");for(var tt=null,ae=null,yt=D,mn=0,on=0,Gn=null,Cn=_e.next();yt!==null&&!Cn.done;on++,Cn=_e.next()){yt.index>on?(Gn=yt,yt=null):Gn=yt.sibling;var xn=C(E,yt,Cn.value,L);if(xn===null){yt===null&&(yt=Gn);break}e&&yt&&xn.alternate===null&&t(E,yt),mn=i(xn,mn,on),ae===null?tt=xn:ae.sibling=xn,ae=xn,yt=Gn}if(Cn.done){if(n(E,yt),tn()){var hn=on;Zr(E,hn)}return tt}if(yt===null){for(;!Cn.done;on++,Cn=_e.next()){var fr=S(E,Cn.value,L);fr!==null&&(mn=i(fr,mn,on),ae===null?tt=fr:ae.sibling=fr,ae=fr)}if(tn()){var Ki=on;Zr(E,Ki)}return tt}for(var ul=a(E,yt);!Cn.done;on++,Cn=_e.next()){var Xa=O(ul,E,on,Cn.value,L);Xa!==null&&(e&&Xa.alternate!==null&&ul.delete(Xa.key===null?on:Xa.key),mn=i(Xa,mn,on),ae===null?tt=Xa:ae.sibling=Xa,ae=Xa)}if(e&&ul.forEach(function(WR){return t(E,WR)}),tn()){var kR=on;Zr(E,kR)}return tt}function oe(E,D,b,L){if(D!==null&&D.tag===ge){n(E,D.sibling);var k=r(D,b);return k.return=E,k}n(E,D);var K=Md(b,E.mode,L);return K.return=E,K}function te(E,D,b,L){for(var k=b.key,K=D;K!==null;){if(K.key===k){var He=b.type;if(He===Zn){if(K.tag===Te){n(E,K.sibling);var de=r(K,b.props.children);return de.return=E,de._debugSource=b._source,de._debugOwner=b._owner,de}}else if(K.elementType===He||dh(K,b)||typeof He=="object"&&He!==null&&He.$$typeof===it&&bp(He)===K.type){n(E,K.sibling);var we=r(K,b.props);return we.ref=Nu(E,K,b),we.return=E,we._debugSource=b._source,we._debugOwner=b._owner,we}n(E,K);break}else t(E,K);K=K.sibling}if(b.type===Zn){var _e=Lr(b.props.children,E.mode,L,b.key);return _e.return=E,_e}else{var tt=Ud(b,E.mode,L);return tt.ref=Nu(E,D,b),tt.return=E,tt}}function Ge(E,D,b,L){for(var k=b.key,K=D;K!==null;){if(K.key===k)if(K.tag===Ue&&K.stateNode.containerInfo===b.containerInfo&&K.stateNode.implementation===b.implementation){n(E,K.sibling);var He=r(K,b.children||[]);return He.return=E,He}else{n(E,K);break}else t(E,K);K=K.sibling}var de=Ad(b,E.mode,L);return de.return=E,de}function je(E,D,b,L){var k=typeof b=="object"&&b!==null&&b.type===Zn&&b.key===null;if(k&&(b=b.props.children),typeof b=="object"&&b!==null){switch(b.$$typeof){case Dn:return u(te(E,D,b,L));case _n:return u(Ge(E,D,b,L));case it:var K=b._payload,He=b._init;return je(E,D,He(K),L)}if(fn(b))return N(E,D,b,L);if(w(b))return I(E,D,b,L);io(E,b)}return typeof b=="string"&&b!==""||typeof b=="number"?u(oe(E,D,""+b,L)):(typeof b=="function"&&uo(E),n(E,D))}return je}var Ui=Ep(!0),Rp=Ep(!1);function zS(e,t){if(e!==null&&t.child!==e.child)throw new Error("Resuming work not yet implemented.");if(t.child!==null){var n=t.child,a=li(n,n.pendingProps);for(t.child=a,a.return=t;n.sibling!==null;)n=n.sibling,a=a.sibling=li(n,n.pendingProps),a.return=t;a.sibling=null}}function HS(e,t){for(var n=e.child;n!==null;)DR(n,t),n=n.sibling}var Uu={},_r=Tr(Uu),Mu=Tr(Uu),lo=Tr(Uu);function oo(e){if(e===Uu)throw new Error("Expected host context to exist. This error is likely caused by a bug in React. Please file an issue.");return e}function ef(){var e=oo(lo.current);return e}function tf(e,t){It(lo,t,e),It(Mu,e,e),It(_r,Uu,e);var n=pl(t);dn(_r,e),It(_r,n,e)}function Mi(e){dn(_r,e),dn(Mu,e),dn(lo,e)}function Au(){var e=oo(_r.current);return e}function Tp(e){var t=oo(lo.current),n=oo(_r.current),a=Es(n,e.type,t);n!==a&&(It(Mu,e,e),It(_r,a,e))}function nf(e){Mu.current===e&&(dn(_r,e),dn(Mu,e))}var LS=0,Cp=1,xp=1,zu=2,ya=Tr(LS);function af(e,t){return(e&t)!==0}function Ai(e){return e&Cp}function rf(e,t){return e&Cp|t}function jS(e,t){return e|t}function Or(e,t){It(ya,t,e)}function zi(e){dn(ya,e)}function FS(e,t){var n=e.memoizedState;if(n!==null)return n.dehydrated!==null;var a=e.memoizedProps;return!0}function so(e){for(var t=e;t!==null;){if(t.tag===Fe){var n=t.memoizedState;if(n!==null){var a=n.dehydrated;if(a===null||Dt(a)||Er(a))return t}}else if(t.tag===St&&t.memoizedProps.revealOrder!==void 0){var r=(t.flags&ze)!==X;if(r)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)return null;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Un=0,At=1,Fa=2,zt=4,nn=8,uf=[];function lf(){for(var e=0;e<uf.length;e++){var t=uf[e];Yr?t._workInProgressVersionPrimary=null:t._workInProgressVersionSecondary=null}uf.length=0}function BS(e,t){var n=t._getVersion,a=n(t._source);e.mutableSourceEagerHydrationData==null?e.mutableSourceEagerHydrationData=[t,a]:e.mutableSourceEagerHydrationData.push(t,a)}var G=M.ReactCurrentDispatcher,Hu=M.ReactCurrentBatchConfig,of,Hi;of=new Set;var ei=z,We=null,Ht=null,Lt=null,co=!1,Lu=!1,ju=0,VS=0,wS=25,_=null,ta=null,Nr=-1,sf=!1;function Pe(){{var e=_;ta===null?ta=[e]:ta.push(e)}}function Y(){{var e=_;ta!==null&&(Nr++,ta[Nr]!==e&&YS(e))}}function Li(e){e!=null&&!fn(e)&&h("%s received a final argument that is not an array (instead, received `%s`). When specified, the final argument must be an array.",_,typeof e)}function YS(e){{var t=W(We);if(!of.has(t)&&(of.add(t),ta!==null)){for(var n="",a=30,r=0;r<=Nr;r++){for(var i=ta[r],u=r===Nr?e:i,o=r+1+". "+i;o.length<a;)o+=" ";o+=u+`
`,n+=o}h(`React has detected a change in the order of Hooks called by %s. This will lead to bugs and errors if not fixed. For more information, read the Rules of Hooks: https://reactjs.org/link/rules-of-hooks

   Previous render            Next render
   ------------------------------------------------------
%s   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
`,t,n)}}}function vn(){throw new Error(`Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:
1. You might have mismatching versions of React and the renderer (such as React DOM)
2. You might be breaking the Rules of Hooks
3. You might have more than one copy of React in the same app
See https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.`)}function cf(e,t){if(sf)return!1;if(t===null)return h("%s received a final argument during this render, but not during the previous render. Even though the final argument is optional, its type cannot change between renders.",_),!1;e.length!==t.length&&h(`The final argument passed to %s changed size between renders. The order and size of this array must remain constant.

Previous: %s
Incoming: %s`,_,"["+t.join(", ")+"]","["+e.join(", ")+"]");for(var n=0;n<t.length&&n<e.length;n++)if(!qn(e[n],t[n]))return!1;return!0}function ji(e,t,n,a,r,i){ei=i,We=t,ta=e!==null?e._debugHookTypes:null,Nr=-1,sf=e!==null&&e.type!==t.type,t.memoizedState=null,t.updateQueue=null,t.lanes=z,e!==null&&e.memoizedState!==null?G.current=Xp:ta!==null?G.current=Kp:G.current=Gp;var u=n(a,r);if(Lu){var o=0;do{if(Lu=!1,ju=0,o>=wS)throw new Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");o+=1,sf=!1,Ht=null,Lt=null,t.updateQueue=null,Nr=-1,G.current=Jp,u=n(a,r)}while(Lu)}G.current=Co,t._debugHookTypes=ta;var s=Ht!==null&&Ht.next!==null;if(ei=z,We=null,Ht=null,Lt=null,_=null,ta=null,Nr=-1,e!==null&&(e.flags&Qe)!==(t.flags&Qe)&&(e.mode&Be)!==le&&h("Internal React error: Expected static flag was missing. Please notify the React team."),co=!1,s)throw new Error("Rendered fewer hooks than expected. This may be caused by an accidental early return statement.");return u}function Fi(){var e=ju!==0;return ju=0,e}function Dp(e,t,n){t.updateQueue=e.updateQueue,(t.mode&Ma)!==le?t.flags&=~(Ve|be|Yt|ce):t.flags&=~(Yt|ce),e.lanes=Vl(e.lanes,n)}function _p(){if(G.current=Co,co){for(var e=We.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}co=!1}ei=z,We=null,Ht=null,Lt=null,ta=null,Nr=-1,_=null,wp=!1,Lu=!1,ju=0}function Ba(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Lt===null?We.memoizedState=Lt=e:Lt=Lt.next=e,Lt}function na(){var e;if(Ht===null){var t=We.alternate;t!==null?e=t.memoizedState:e=null}else e=Ht.next;var n;if(Lt===null?n=We.memoizedState:n=Lt.next,n!==null)Lt=n,n=Lt.next,Ht=e;else{if(e===null)throw new Error("Rendered more hooks than during the previous render.");Ht=e;var a={memoizedState:Ht.memoizedState,baseState:Ht.baseState,baseQueue:Ht.baseQueue,queue:Ht.queue,next:null};Lt===null?We.memoizedState=Lt=a:Lt=Lt.next=a}return Lt}function Op(){return{lastEffect:null,stores:null}}function ff(e,t){return typeof t=="function"?t(e):t}function df(e,t,n){var a=Ba(),r;n!==void 0?r=n(t):r=t,a.memoizedState=a.baseState=r;var i={pending:null,interleaved:null,lanes:z,dispatch:null,lastRenderedReducer:e,lastRenderedState:r};a.queue=i;var u=i.dispatch=GS.bind(null,We,i);return[a.memoizedState,u]}function vf(e,t,n){var a=na(),r=a.queue;if(r===null)throw new Error("Should have a queue. This is likely a bug in React. Please file an issue.");r.lastRenderedReducer=e;var i=Ht,u=i.baseQueue,o=r.pending;if(o!==null){if(u!==null){var s=u.next,f=o.next;u.next=f,o.next=s}i.baseQueue!==u&&h("Internal error: Expected work-in-progress queue to be a clone. This is a bug in React."),i.baseQueue=u=o,r.pending=null}if(u!==null){var m=u.next,S=i.baseState,C=null,O=null,U=null,N=m;do{var I=N.lane;if(Ri(ei,I)){if(U!==null){var te={lane:Pt,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null};U=U.next=te}if(N.hasEagerState)S=N.eagerState;else{var Ge=N.action;S=e(S,Ge)}}else{var oe={lane:I,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null};U===null?(O=U=oe,C=S):U=U.next=oe,We.lanes=Ee(We.lanes,I),al(I)}N=N.next}while(N!==null&&N!==m);U===null?C=S:U.next=O,qn(S,a.memoizedState)||Qu(),a.memoizedState=S,a.baseState=C,a.baseQueue=U,r.lastRenderedState=S}var je=r.interleaved;if(je!==null){var E=je;do{var D=E.lane;We.lanes=Ee(We.lanes,D),al(D),E=E.next}while(E!==je)}else u===null&&(r.lanes=z);var b=r.dispatch;return[a.memoizedState,b]}function pf(e,t,n){var a=na(),r=a.queue;if(r===null)throw new Error("Should have a queue. This is likely a bug in React. Please file an issue.");r.lastRenderedReducer=e;var i=r.dispatch,u=r.pending,o=a.memoizedState;if(u!==null){r.pending=null;var s=u.next,f=s;do{var m=f.action;o=e(o,m),f=f.next}while(f!==s);qn(o,a.memoizedState)||Qu(),a.memoizedState=o,a.baseQueue===null&&(a.baseState=o),r.lastRenderedState=o}return[o,i]}function bT(e,t,n){}function ET(e,t,n){}function mf(e,t,n){var a=We,r=Ba(),i,u=tn();if(u){if(n===void 0)throw new Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");i=n(),Hi||i!==n()&&(h("The result of getServerSnapshot should be cached to avoid an infinite loop"),Hi=!0)}else{if(i=t(),!Hi){var o=t();qn(i,o)||(h("The result of getSnapshot should be cached to avoid an infinite loop"),Hi=!0)}var s=Io();if(s===null)throw new Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");Bl(s,ei)||Np(a,t,i)}r.memoizedState=i;var f={value:i,getSnapshot:t};return r.queue=f,ho(Mp.bind(null,a,f,e),[e]),a.flags|=Yt,Fu(At|nn,Up.bind(null,a,f,i,t),void 0,null),i}function fo(e,t,n){var a=We,r=na(),i=t();if(!Hi){var u=t();qn(i,u)||(h("The result of getSnapshot should be cached to avoid an infinite loop"),Hi=!0)}var o=r.memoizedState,s=!qn(o,i);s&&(r.memoizedState=i,Qu());var f=r.queue;if(Vu(Mp.bind(null,a,f,e),[e]),f.getSnapshot!==t||s||Lt!==null&&Lt.memoizedState.tag&At){a.flags|=Yt,Fu(At|nn,Up.bind(null,a,f,i,t),void 0,null);var m=Io();if(m===null)throw new Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");Bl(m,ei)||Np(a,t,i)}return i}function Np(e,t,n){e.flags|=$a;var a={getSnapshot:t,value:n},r=We.updateQueue;if(r===null)r=Op(),We.updateQueue=r,r.stores=[a];else{var i=r.stores;i===null?r.stores=[a]:i.push(a)}}function Up(e,t,n,a){t.value=n,t.getSnapshot=a,Ap(t)&&zp(e)}function Mp(e,t,n){var a=function(){Ap(t)&&zp(e)};return n(a)}function Ap(e){var t=e.getSnapshot,n=e.value;try{var a=t();return!qn(n,a)}catch(r){return!0}}function zp(e){var t=En(e,fe);t!==null&&Ut(t,e,fe,ot)}function vo(e){var t=Ba();typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e;var n={pending:null,interleaved:null,lanes:z,dispatch:null,lastRenderedReducer:ff,lastRenderedState:e};t.queue=n;var a=n.dispatch=KS.bind(null,We,n);return[t.memoizedState,a]}function hf(e){return vf(ff)}function yf(e){return pf(ff)}function Fu(e,t,n,a){var r={tag:e,create:t,destroy:n,deps:a,next:null},i=We.updateQueue;if(i===null)i=Op(),We.updateQueue=i,i.lastEffect=r.next=r;else{var u=i.lastEffect;if(u===null)i.lastEffect=r.next=r;else{var o=u.next;u.next=r,r.next=o,i.lastEffect=r}}return r}function gf(e){var t=Ba();{var n={current:e};return t.memoizedState=n,n}}function po(e){var t=na();return t.memoizedState}function Bu(e,t,n,a){var r=Ba(),i=a===void 0?null:a;We.flags|=e,r.memoizedState=Fu(At|t,n,void 0,i)}function mo(e,t,n,a){var r=na(),i=a===void 0?null:a,u=void 0;if(Ht!==null){var o=Ht.memoizedState;if(u=o.destroy,i!==null){var s=o.deps;if(cf(i,s)){r.memoizedState=Fu(t,n,u,i);return}}}We.flags|=e,r.memoizedState=Fu(At|t,n,u,i)}function ho(e,t){return(We.mode&Ma)!==le?Bu(Ve|Yt|re,nn,e,t):Bu(Yt|re,nn,e,t)}function Vu(e,t){return mo(Yt,nn,e,t)}function Sf(e,t){return Bu(ce,Fa,e,t)}function yo(e,t){return mo(ce,Fa,e,t)}function bf(e,t){var n=ce;return n|=V,(We.mode&Ma)!==le&&(n|=be),Bu(n,zt,e,t)}function go(e,t){return mo(ce,zt,e,t)}function Hp(e,t){if(typeof t=="function"){var n=t,a=e();return n(a),function(){n(null)}}else if(t!=null){var r=t;r.hasOwnProperty("current")||h("Expected useImperativeHandle() first argument to either be a ref callback or React.createRef() object. Instead received: %s.","an object with keys {"+Object.keys(r).join(", ")+"}");var i=e();return r.current=i,function(){r.current=null}}}function Ef(e,t,n){typeof t!="function"&&h("Expected useImperativeHandle() second argument to be a function that creates a handle. Instead received: %s.",t!==null?typeof t:"null");var a=n!=null?n.concat([e]):null,r=ce;return r|=V,(We.mode&Ma)!==le&&(r|=be),Bu(r,zt,Hp.bind(null,t,e),a)}function So(e,t,n){typeof t!="function"&&h("Expected useImperativeHandle() second argument to be a function that creates a handle. Instead received: %s.",t!==null?typeof t:"null");var a=n!=null?n.concat([e]):null;return mo(ce,zt,Hp.bind(null,t,e),a)}function qS(e,t){}var bo=qS;function Rf(e,t){var n=Ba(),a=t===void 0?null:t;return n.memoizedState=[e,a],e}function Eo(e,t){var n=na(),a=t===void 0?null:t,r=n.memoizedState;if(r!==null&&a!==null){var i=r[1];if(cf(a,i))return r[0]}return n.memoizedState=[e,a],e}function Tf(e,t){var n=Ba(),a=t===void 0?null:t,r=e();return n.memoizedState=[r,a],r}function Ro(e,t){var n=na(),a=t===void 0?null:t,r=n.memoizedState;if(r!==null&&a!==null){var i=r[1];if(cf(a,i))return r[0]}var u=e();return n.memoizedState=[u,a],u}function Cf(e){var t=Ba();return t.memoizedState=e,e}function Lp(e){var t=na(),n=Ht,a=n.memoizedState;return Fp(t,a,e)}function jp(e){var t=na();if(Ht===null)return t.memoizedState=e,e;var n=Ht.memoizedState;return Fp(t,n,e)}function Fp(e,t,n){var a=!sg(ei);if(a){if(!qn(n,t)){var r=Cv();We.lanes=Ee(We.lanes,r),al(r),e.baseState=!0}return t}else return e.baseState&&(e.baseState=!1,Qu()),e.memoizedState=n,n}function QS(e,t,n){var a=da();Gt(gg(a,yu)),e(!0);var r=Hu.transition;Hu.transition={};var i=Hu.transition;Hu.transition._updatedFibers=new Set;try{e(!1),t()}finally{if(Gt(a),Hu.transition=r,r===null&&i._updatedFibers){var u=i._updatedFibers.size;u>10&&A("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table."),i._updatedFibers.clear()}}}function xf(){var e=vo(!1),t=e[0],n=e[1],a=QS.bind(null,n),r=Ba();return r.memoizedState=a,[t,a]}function Bp(){var e=hf(),t=e[0],n=na(),a=n.memoizedState;return[t,a]}function Vp(){var e=yf(),t=e[0],n=na(),a=n.memoizedState;return[t,a]}var wp=!1;function PS(){return wp}function Df(){var e=Ba(),t=Io(),n=t.identifierPrefix,a;if(tn()){var r=Ig();a=":"+n+"R"+r;var i=ju++;i>0&&(a+="H"+i.toString(32)),a+=":"}else{var u=VS++;a=":"+n+"r"+u.toString(32)+":"}return e.memoizedState=a,a}function To(){var e=na(),t=e.memoizedState;return t}function GS(e,t,n){typeof arguments[3]=="function"&&h("State updates from the useState() and useReducer() Hooks don't support the second callback argument. To execute a side effect after rendering, declare it in the component body with useEffect().");var a=zr(e),r={lane:a,action:n,hasEagerState:!1,eagerState:null,next:null};if(Yp(e))qp(t,r);else{var i=up(e,t,r,a);if(i!==null){var u=pn();Ut(i,e,a,u),Qp(i,t,a)}}Pp(e,a)}function KS(e,t,n){typeof arguments[3]=="function"&&h("State updates from the useState() and useReducer() Hooks don't support the second callback argument. To execute a side effect after rendering, declare it in the component body with useEffect().");var a=zr(e),r={lane:a,action:n,hasEagerState:!1,eagerState:null,next:null};if(Yp(e))qp(t,r);else{var i=e.alternate;if(e.lanes===z&&(i===null||i.lanes===z)){var u=t.lastRenderedReducer;if(u!==null){var o;o=G.current,G.current=ga;try{var s=t.lastRenderedState,f=u(s,n);if(r.hasEagerState=!0,r.eagerState=f,qn(f,s)){CS(e,t,r,a);return}}catch(C){}finally{G.current=o}}}var m=up(e,t,r,a);if(m!==null){var S=pn();Ut(m,e,a,S),Qp(m,t,a)}}Pp(e,a)}function Yp(e){var t=e.alternate;return e===We||t!==null&&t===We}function qp(e,t){Lu=co=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Qp(e,t,n){if(Tv(n)){var a=t.lanes;a=xv(a,e.pendingLanes);var r=Ee(a,n);t.lanes=r,hc(e,r)}}function Pp(e,t,n){gc(e,t)}var Co={readContext:Ot,useCallback:vn,useContext:vn,useEffect:vn,useImperativeHandle:vn,useInsertionEffect:vn,useLayoutEffect:vn,useMemo:vn,useReducer:vn,useRef:vn,useState:vn,useDebugValue:vn,useDeferredValue:vn,useTransition:vn,useMutableSource:vn,useSyncExternalStore:vn,useId:vn,unstable_isNewReconciler:Ke},Gp=null,Kp=null,Xp=null,Jp=null,Va=null,ga=null,xo=null;{var _f=function(){h("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().")},ve=function(){h("Do not call Hooks inside useEffect(...), useMemo(...), or other built-in Hooks. You can only call Hooks at the top level of your React function. For more information, see https://reactjs.org/link/rules-of-hooks")};Gp={readContext:function(e){return Ot(e)},useCallback:function(e,t){return _="useCallback",Pe(),Li(t),Rf(e,t)},useContext:function(e){return _="useContext",Pe(),Ot(e)},useEffect:function(e,t){return _="useEffect",Pe(),Li(t),ho(e,t)},useImperativeHandle:function(e,t,n){return _="useImperativeHandle",Pe(),Li(n),Ef(e,t,n)},useInsertionEffect:function(e,t){return _="useInsertionEffect",Pe(),Li(t),Sf(e,t)},useLayoutEffect:function(e,t){return _="useLayoutEffect",Pe(),Li(t),bf(e,t)},useMemo:function(e,t){_="useMemo",Pe(),Li(t);var n=G.current;G.current=Va;try{return Tf(e,t)}finally{G.current=n}},useReducer:function(e,t,n){_="useReducer",Pe();var a=G.current;G.current=Va;try{return df(e,t,n)}finally{G.current=a}},useRef:function(e){return _="useRef",Pe(),gf(e)},useState:function(e){_="useState",Pe();var t=G.current;G.current=Va;try{return vo(e)}finally{G.current=t}},useDebugValue:function(e,t){return _="useDebugValue",Pe(),void 0},useDeferredValue:function(e){return _="useDeferredValue",Pe(),Cf(e)},useTransition:function(){return _="useTransition",Pe(),xf()},useMutableSource:function(e,t,n){return _="useMutableSource",Pe(),void 0},useSyncExternalStore:function(e,t,n){return _="useSyncExternalStore",Pe(),mf(e,t,n)},useId:function(){return _="useId",Pe(),Df()},unstable_isNewReconciler:Ke},Kp={readContext:function(e){return Ot(e)},useCallback:function(e,t){return _="useCallback",Y(),Rf(e,t)},useContext:function(e){return _="useContext",Y(),Ot(e)},useEffect:function(e,t){return _="useEffect",Y(),ho(e,t)},useImperativeHandle:function(e,t,n){return _="useImperativeHandle",Y(),Ef(e,t,n)},useInsertionEffect:function(e,t){return _="useInsertionEffect",Y(),Sf(e,t)},useLayoutEffect:function(e,t){return _="useLayoutEffect",Y(),bf(e,t)},useMemo:function(e,t){_="useMemo",Y();var n=G.current;G.current=Va;try{return Tf(e,t)}finally{G.current=n}},useReducer:function(e,t,n){_="useReducer",Y();var a=G.current;G.current=Va;try{return df(e,t,n)}finally{G.current=a}},useRef:function(e){return _="useRef",Y(),gf(e)},useState:function(e){_="useState",Y();var t=G.current;G.current=Va;try{return vo(e)}finally{G.current=t}},useDebugValue:function(e,t){return _="useDebugValue",Y(),void 0},useDeferredValue:function(e){return _="useDeferredValue",Y(),Cf(e)},useTransition:function(){return _="useTransition",Y(),xf()},useMutableSource:function(e,t,n){return _="useMutableSource",Y(),void 0},useSyncExternalStore:function(e,t,n){return _="useSyncExternalStore",Y(),mf(e,t,n)},useId:function(){return _="useId",Y(),Df()},unstable_isNewReconciler:Ke},Xp={readContext:function(e){return Ot(e)},useCallback:function(e,t){return _="useCallback",Y(),Eo(e,t)},useContext:function(e){return _="useContext",Y(),Ot(e)},useEffect:function(e,t){return _="useEffect",Y(),Vu(e,t)},useImperativeHandle:function(e,t,n){return _="useImperativeHandle",Y(),So(e,t,n)},useInsertionEffect:function(e,t){return _="useInsertionEffect",Y(),yo(e,t)},useLayoutEffect:function(e,t){return _="useLayoutEffect",Y(),go(e,t)},useMemo:function(e,t){_="useMemo",Y();var n=G.current;G.current=ga;try{return Ro(e,t)}finally{G.current=n}},useReducer:function(e,t,n){_="useReducer",Y();var a=G.current;G.current=ga;try{return vf(e,t,n)}finally{G.current=a}},useRef:function(e){return _="useRef",Y(),po()},useState:function(e){_="useState",Y();var t=G.current;G.current=ga;try{return hf(e)}finally{G.current=t}},useDebugValue:function(e,t){return _="useDebugValue",Y(),bo()},useDeferredValue:function(e){return _="useDeferredValue",Y(),Lp(e)},useTransition:function(){return _="useTransition",Y(),Bp()},useMutableSource:function(e,t,n){return _="useMutableSource",Y(),void 0},useSyncExternalStore:function(e,t,n){return _="useSyncExternalStore",Y(),fo(e,t)},useId:function(){return _="useId",Y(),To()},unstable_isNewReconciler:Ke},Jp={readContext:function(e){return Ot(e)},useCallback:function(e,t){return _="useCallback",Y(),Eo(e,t)},useContext:function(e){return _="useContext",Y(),Ot(e)},useEffect:function(e,t){return _="useEffect",Y(),Vu(e,t)},useImperativeHandle:function(e,t,n){return _="useImperativeHandle",Y(),So(e,t,n)},useInsertionEffect:function(e,t){return _="useInsertionEffect",Y(),yo(e,t)},useLayoutEffect:function(e,t){return _="useLayoutEffect",Y(),go(e,t)},useMemo:function(e,t){_="useMemo",Y();var n=G.current;G.current=xo;try{return Ro(e,t)}finally{G.current=n}},useReducer:function(e,t,n){_="useReducer",Y();var a=G.current;G.current=xo;try{return pf(e,t,n)}finally{G.current=a}},useRef:function(e){return _="useRef",Y(),po()},useState:function(e){_="useState",Y();var t=G.current;G.current=xo;try{return yf(e)}finally{G.current=t}},useDebugValue:function(e,t){return _="useDebugValue",Y(),bo()},useDeferredValue:function(e){return _="useDeferredValue",Y(),jp(e)},useTransition:function(){return _="useTransition",Y(),Vp()},useMutableSource:function(e,t,n){return _="useMutableSource",Y(),void 0},useSyncExternalStore:function(e,t,n){return _="useSyncExternalStore",Y(),fo(e,t)},useId:function(){return _="useId",Y(),To()},unstable_isNewReconciler:Ke},Va={readContext:function(e){return _f(),Ot(e)},useCallback:function(e,t){return _="useCallback",ve(),Pe(),Rf(e,t)},useContext:function(e){return _="useContext",ve(),Pe(),Ot(e)},useEffect:function(e,t){return _="useEffect",ve(),Pe(),ho(e,t)},useImperativeHandle:function(e,t,n){return _="useImperativeHandle",ve(),Pe(),Ef(e,t,n)},useInsertionEffect:function(e,t){return _="useInsertionEffect",ve(),Pe(),Sf(e,t)},useLayoutEffect:function(e,t){return _="useLayoutEffect",ve(),Pe(),bf(e,t)},useMemo:function(e,t){_="useMemo",ve(),Pe();var n=G.current;G.current=Va;try{return Tf(e,t)}finally{G.current=n}},useReducer:function(e,t,n){_="useReducer",ve(),Pe();var a=G.current;G.current=Va;try{return df(e,t,n)}finally{G.current=a}},useRef:function(e){return _="useRef",ve(),Pe(),gf(e)},useState:function(e){_="useState",ve(),Pe();var t=G.current;G.current=Va;try{return vo(e)}finally{G.current=t}},useDebugValue:function(e,t){return _="useDebugValue",ve(),Pe(),void 0},useDeferredValue:function(e){return _="useDeferredValue",ve(),Pe(),Cf(e)},useTransition:function(){return _="useTransition",ve(),Pe(),xf()},useMutableSource:function(e,t,n){return _="useMutableSource",ve(),Pe(),void 0},useSyncExternalStore:function(e,t,n){return _="useSyncExternalStore",ve(),Pe(),mf(e,t,n)},useId:function(){return _="useId",ve(),Pe(),Df()},unstable_isNewReconciler:Ke},ga={readContext:function(e){return _f(),Ot(e)},useCallback:function(e,t){return _="useCallback",ve(),Y(),Eo(e,t)},useContext:function(e){return _="useContext",ve(),Y(),Ot(e)},useEffect:function(e,t){return _="useEffect",ve(),Y(),Vu(e,t)},useImperativeHandle:function(e,t,n){return _="useImperativeHandle",ve(),Y(),So(e,t,n)},useInsertionEffect:function(e,t){return _="useInsertionEffect",ve(),Y(),yo(e,t)},useLayoutEffect:function(e,t){return _="useLayoutEffect",ve(),Y(),go(e,t)},useMemo:function(e,t){_="useMemo",ve(),Y();var n=G.current;G.current=ga;try{return Ro(e,t)}finally{G.current=n}},useReducer:function(e,t,n){_="useReducer",ve(),Y();var a=G.current;G.current=ga;try{return vf(e,t,n)}finally{G.current=a}},useRef:function(e){return _="useRef",ve(),Y(),po()},useState:function(e){_="useState",ve(),Y();var t=G.current;G.current=ga;try{return hf(e)}finally{G.current=t}},useDebugValue:function(e,t){return _="useDebugValue",ve(),Y(),bo()},useDeferredValue:function(e){return _="useDeferredValue",ve(),Y(),Lp(e)},useTransition:function(){return _="useTransition",ve(),Y(),Bp()},useMutableSource:function(e,t,n){return _="useMutableSource",ve(),Y(),void 0},useSyncExternalStore:function(e,t,n){return _="useSyncExternalStore",ve(),Y(),fo(e,t)},useId:function(){return _="useId",ve(),Y(),To()},unstable_isNewReconciler:Ke},xo={readContext:function(e){return _f(),Ot(e)},useCallback:function(e,t){return _="useCallback",ve(),Y(),Eo(e,t)},useContext:function(e){return _="useContext",ve(),Y(),Ot(e)},useEffect:function(e,t){return _="useEffect",ve(),Y(),Vu(e,t)},useImperativeHandle:function(e,t,n){return _="useImperativeHandle",ve(),Y(),So(e,t,n)},useInsertionEffect:function(e,t){return _="useInsertionEffect",ve(),Y(),yo(e,t)},useLayoutEffect:function(e,t){return _="useLayoutEffect",ve(),Y(),go(e,t)},useMemo:function(e,t){_="useMemo",ve(),Y();var n=G.current;G.current=ga;try{return Ro(e,t)}finally{G.current=n}},useReducer:function(e,t,n){_="useReducer",ve(),Y();var a=G.current;G.current=ga;try{return pf(e,t,n)}finally{G.current=a}},useRef:function(e){return _="useRef",ve(),Y(),po()},useState:function(e){_="useState",ve(),Y();var t=G.current;G.current=ga;try{return yf(e)}finally{G.current=t}},useDebugValue:function(e,t){return _="useDebugValue",ve(),Y(),bo()},useDeferredValue:function(e){return _="useDeferredValue",ve(),Y(),jp(e)},useTransition:function(){return _="useTransition",ve(),Y(),Vp()},useMutableSource:function(e,t,n){return _="useMutableSource",ve(),Y(),void 0},useSyncExternalStore:function(e,t,n){return _="useSyncExternalStore",ve(),Y(),fo(e,t)},useId:function(){return _="useId",ve(),Y(),To()},unstable_isNewReconciler:Ke}}var Ur=g.unstable_now,Zp=0,Do=-1,wu=-1,_o=-1,Of=!1,Oo=!1;function kp(){return Of}function XS(){Oo=!0}function JS(){Of=!1,Oo=!1}function ZS(){Of=Oo,Oo=!1}function Wp(){return Zp}function Ip(){Zp=Ur()}function Nf(e){wu=Ur(),e.actualStartTime<0&&(e.actualStartTime=Ur())}function $p(e){wu=-1}function No(e,t){if(wu>=0){var n=Ur()-wu;e.actualDuration+=n,t&&(e.selfBaseDuration=n),wu=-1}}function wa(e){if(Do>=0){var t=Ur()-Do;Do=-1;for(var n=e.return;n!==null;){switch(n.tag){case se:var a=n.stateNode;a.effectDuration+=t;return;case Tt:var r=n.stateNode;r.effectDuration+=t;return}n=n.return}}}function Uf(e){if(_o>=0){var t=Ur()-_o;_o=-1;for(var n=e.return;n!==null;){switch(n.tag){case se:var a=n.stateNode;a!==null&&(a.passiveEffectDuration+=t);return;case Tt:var r=n.stateNode;r!==null&&(r.passiveEffectDuration+=t);return}n=n.return}}}function Ya(){Do=Ur()}function Mf(){_o=Ur()}function Af(e){for(var t=e.child;t;)e.actualDuration+=t.actualDuration,t=t.sibling}function ti(e,t){return{value:e,source:t,stack:Iv(t),digest:null}}function zf(e,t,n){return{value:e,source:null,stack:n!=null?n:null,digest:t!=null?t:null}}function kS(e,t){return!0}function Hf(e,t){try{var n=kS(e,t);if(n===!1)return;var a=t.value,r=t.source,i=t.stack,u=i!==null?i:"";if(a!=null&&a._suppressLogging){if(e.tag===ye)return;console.error(a)}var o=r?W(r):null,s=o?"The above error occurred in the <"+o+"> component:":"The above error occurred in one of your React components:",f;if(e.tag===se)f=`Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.`;else{var m=W(e)||"Anonymous";f="React will try to recreate this component tree from scratch "+("using the error boundary you provided, "+m+".")}var S=s+`
`+u+`

`+(""+f);console.error(S)}catch(C){setTimeout(function(){throw C})}}var WS=typeof WeakMap=="function"?WeakMap:Map;function em(e,t,n){var a=lr(ot,n);a.tag=zc,a.payload={element:null};var r=t.value;return a.callback=function(){uR(r),Hf(e,t)},a}function Lf(e,t,n){var a=lr(ot,n);a.tag=zc;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;a.payload=function(){return r(i)},a.callback=function(){vh(e),Hf(e,t)}}var u=e.stateNode;return u!==null&&typeof u.componentDidCatch=="function"&&(a.callback=function(){vh(e),Hf(e,t),typeof r!="function"&&rR(this);var s=t.value,f=t.stack;this.componentDidCatch(s,{componentStack:f!==null?f:""}),typeof r!="function"&&(Yn(e.lanes,fe)||h("%s: Error boundaries should implement getDerivedStateFromError(). In that method, return a state update to display an error message or fallback UI.",W(e)||"Unknown"))}),a}function tm(e,t,n){var a=e.pingCache,r;if(a===null?(a=e.pingCache=new WS,r=new Set,a.set(t,r)):(r=a.get(t),r===void 0&&(r=new Set,a.set(t,r))),!r.has(n)){r.add(n);var i=lR.bind(null,e,t,n);va&&rl(e,n),t.then(i,i)}}function IS(e,t,n,a){var r=e.updateQueue;if(r===null){var i=new Set;i.add(n),e.updateQueue=i}else r.add(n)}function $S(e,t){var n=e.tag;if((e.mode&Be)===le&&(n===Ne||n===Me||n===Ae)){var a=e.alternate;a?(e.updateQueue=a.updateQueue,e.memoizedState=a.memoizedState,e.lanes=a.lanes):(e.updateQueue=null,e.memoizedState=null)}}function nm(e){var t=e;do{if(t.tag===Fe&&FS(t))return t;t=t.return}while(t!==null);return null}function am(e,t,n,a,r){if((e.mode&Be)===le){if(e===t)e.flags|=xt;else{if(e.flags|=ze,n.flags|=Vr,n.flags&=~(si|_a),n.tag===ye){var i=n.alternate;if(i===null)n.tag=Zt;else{var u=lr(ot,fe);u.tag=Il,Dr(n,u,fe)}}n.lanes=Ee(n.lanes,fe)}return e}return e.flags|=xt,e.lanes=r,e}function eb(e,t,n,a,r){if(n.flags|=_a,va&&rl(e,r),a!==null&&typeof a=="object"&&typeof a.then=="function"){var i=a;$S(n),tn()&&n.mode&Be&&Pv();var u=nm(t);if(u!==null){u.flags&=~On,am(u,t,n,e,r),u.mode&Be&&tm(e,i,r),IS(u,e,i);return}else{if(!og(r)){tm(e,i,r),Sd();return}var o=new Error("A component suspended while responding to synchronous input. This will cause the UI to be replaced with a loading indicator. To fix, updates that suspend should be wrapped with startTransition.");a=o}}else if(tn()&&n.mode&Be){Pv();var s=nm(t);if(s!==null){(s.flags&xt)===X&&(s.flags|=On),am(s,t,n,e,r),_c(ti(a,n));return}}a=ti(a,n),kE(a);var f=t;do{switch(f.tag){case se:{var m=a;f.flags|=xt;var S=mu(r);f.lanes=Ee(f.lanes,S);var C=em(f,m,S);jc(f,C);return}case ye:var O=a,U=f.type,N=f.stateNode;if((f.flags&ze)===X&&(typeof U.getDerivedStateFromError=="function"||N!==null&&typeof N.componentDidCatch=="function"&&!rh(N))){f.flags|=xt;var I=mu(r);f.lanes=Ee(f.lanes,I);var oe=Lf(f,O,I);jc(f,oe);return}break}f=f.return}while(f!==null)}function tb(){return null}var Yu=M.ReactCurrentOwner,Sa=!1,jf,qu,Ff,Bf,Vf,ni,wf,Uo;jf={},qu={},Ff={},Bf={},Vf={},ni=!1,wf={},Uo={};function Rn(e,t,n,a){e===null?t.child=Rp(t,null,n,a):t.child=Ui(t,e.child,n,a)}function nb(e,t,n,a){t.child=Ui(t,e.child,null,a),t.child=Ui(t,null,n,a)}function rm(e,t,n,a,r){if(t.type!==t.elementType){var i=n.propTypes;i&&fa(i,a,"prop",ee(n))}var u=n.render,o=t.ref,s,f;Ni(t,r),bu(t);{if(Yu.current=t,ja(!0),s=ji(e,t,u,a,o,r),f=Fi(),t.mode&_t){Xt(!0);try{s=ji(e,t,u,a,o,r),f=Fi()}finally{Xt(!1)}}ja(!1)}return Ci(),e!==null&&!Sa?(Dp(e,t,r),or(e,t,r)):(tn()&&f&&Ec(t),t.flags|=$e,Rn(e,t,s,r),t.child)}function im(e,t,n,a,r){if(e===null){var i=n.type;if(CR(i)&&n.compare===null&&n.defaultProps===void 0){var u=i;return u=Gi(i),t.tag=Ae,t.type=u,Qf(t,i),um(e,t,u,a,r)}{var o=i.propTypes;o&&fa(o,a,"prop",ee(i))}var s=Nd(n.type,null,a,t,t.mode,r);return s.ref=t.ref,s.return=t,t.child=s,s}{var f=n.type,m=f.propTypes;m&&fa(m,a,"prop",ee(f))}var S=e.child,C=Zf(e,r);if(!C){var O=S.memoizedProps,U=n.compare;if(U=U!==null?U:Gl,U(O,a)&&e.ref===t.ref)return or(e,t,r)}t.flags|=$e;var N=li(S,a);return N.ref=t.ref,N.return=t,t.child=N,N}function um(e,t,n,a,r){if(t.type!==t.elementType){var i=t.elementType;if(i.$$typeof===it){var u=i,o=u._payload,s=u._init;try{i=s(o)}catch(S){i=null}var f=i&&i.propTypes;f&&fa(f,a,"prop",ee(i))}}if(e!==null){var m=e.memoizedProps;if(Gl(m,a)&&e.ref===t.ref&&t.type===e.type)if(Sa=!1,t.pendingProps=a=m,Zf(e,r))(e.flags&Vr)!==X&&(Sa=!0);else return t.lanes=e.lanes,or(e,t,r)}return Yf(e,t,n,a,r)}function lm(e,t,n){var a=t.pendingProps,r=a.children,i=e!==null?e.memoizedState:null;if(a.mode==="hidden"||Xe)if((t.mode&Be)===le){var u={baseLanes:z,cachePool:null,transitions:null};t.memoizedState=u,es(t,n)}else if(Yn(n,wn)){var S={baseLanes:z,cachePool:null,transitions:null};t.memoizedState=S;var C=i!==null?i.baseLanes:n;es(t,C)}else{var o=null,s;if(i!==null){var f=i.baseLanes;s=Ee(f,n)}else s=n;t.lanes=t.childLanes=wn;var m={baseLanes:s,cachePool:o,transitions:null};return t.memoizedState=m,t.updateQueue=null,es(t,s),null}else{var O;i!==null?(O=Ee(i.baseLanes,n),t.memoizedState=null):O=n,es(t,O)}return Rn(e,t,r,n),t.child}function ab(e,t,n){var a=t.pendingProps;return Rn(e,t,a,n),t.child}function rb(e,t,n){var a=t.pendingProps.children;return Rn(e,t,a,n),t.child}function ib(e,t,n){{t.flags|=ce;{var a=t.stateNode;a.effectDuration=0,a.passiveEffectDuration=0}}var r=t.pendingProps,i=r.children;return Rn(e,t,i,n),t.child}function om(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=Wn,t.flags|=H)}function Yf(e,t,n,a,r){if(t.type!==t.elementType){var i=n.propTypes;i&&fa(i,a,"prop",ee(n))}var u;{var o=hi(t,n,!0);u=yi(t,o)}var s,f;Ni(t,r),bu(t);{if(Yu.current=t,ja(!0),s=ji(e,t,n,a,u,r),f=Fi(),t.mode&_t){Xt(!0);try{s=ji(e,t,n,a,u,r),f=Fi()}finally{Xt(!1)}}ja(!1)}return Ci(),e!==null&&!Sa?(Dp(e,t,r),or(e,t,r)):(tn()&&f&&Ec(t),t.flags|=$e,Rn(e,t,s,r),t.child)}function sm(e,t,n,a,r){{switch(Rh(t)){case!1:{var i=t.stateNode,u=t.type,o=new u(t.memoizedProps,i.context),s=o.state;i.updater.enqueueSetState(i,s,null);break}case!0:{t.flags|=ze,t.flags|=xt;var f=new Error("Simulated error coming from DevTools"),m=mu(r);t.lanes=Ee(t.lanes,m);var S=Lf(t,ti(f,t),m);jc(t,S);break}}if(t.type!==t.elementType){var C=n.propTypes;C&&fa(C,a,"prop",ee(n))}}var O;Ua(n)?(O=!0,zl(t)):O=!1,Ni(t,r);var U=t.stateNode,N;U===null?(Ao(e,t),yp(t,n,a),Jc(t,n,a,r),N=!0):e===null?N=MS(t,n,a,r):N=AS(e,t,n,a,r);var I=qf(e,t,n,N,O,r);{var oe=t.stateNode;N&&oe.props!==a&&(ni||h("It looks like %s is reassigning its own `this.props` while rendering. This is not supported and can lead to confusing bugs.",W(t)||"a component"),ni=!0)}return I}function qf(e,t,n,a,r,i){om(e,t);var u=(t.flags&ze)!==X;if(!a&&!u)return r&&yv(t,n,!1),or(e,t,i);var o=t.stateNode;Yu.current=t;var s;if(u&&typeof n.getDerivedStateFromError!="function")s=null,$p();else{bu(t);{if(ja(!0),s=o.render(),t.mode&_t){Xt(!0);try{o.render()}finally{Xt(!1)}}ja(!1)}Ci()}return t.flags|=$e,e!==null&&u?nb(e,t,s,i):Rn(e,t,s,i),t.memoizedState=o.state,r&&yv(t,n,!0),t.child}function cm(e){var t=e.stateNode;t.pendingContext?mv(e,t.pendingContext,t.pendingContext!==t.context):t.context&&mv(e,t.context,!1),tf(e,t.containerInfo)}function ub(e,t,n){if(cm(t),e===null)throw new Error("Should have a current fiber. This is a bug in React.");var a=t.pendingProps,r=t.memoizedState,i=r.element;sp(e,t),no(t,a,null,n);var u=t.memoizedState,o=t.stateNode,s=u.element;if(Mt&&r.isDehydrated){var f={element:s,isDehydrated:!1,cache:u.cache,pendingSuspenseBoundaries:u.pendingSuspenseBoundaries,transitions:u.transitions},m=t.updateQueue;if(m.baseState=f,t.memoizedState=f,t.flags&On){var S=ti(new Error("There was an error while hydrating. Because the error happened outside of a Suspense boundary, the entire root will switch to client rendering."),t);return fm(e,t,s,n,S)}else if(s!==i){var C=ti(new Error("This root received an early update, before anything was able hydrate. Switched the entire root to client rendering."),t);return fm(e,t,s,n,C)}else{rS(t);var O=Rp(t,null,s,n);t.child=O;for(var U=O;U;)U.flags=U.flags&~qe|Nn,U=U.sibling}}else{if(_i(),s===i)return or(e,t,n);Rn(e,t,s,n)}return t.child}function fm(e,t,n,a,r){return _i(),_c(r),t.flags|=On,Rn(e,t,n,a),t.child}function lb(e,t,n){Tp(t),e===null&&Dc(t);var a=t.type,r=t.pendingProps,i=e!==null?e.memoizedProps:null,u=r.children,o=nu(a,r);return o?u=null:i!==null&&nu(a,i)&&(t.flags|=oa),om(e,t),Rn(e,t,u,n),t.child}function ob(e,t){return e===null&&Dc(t),null}function sb(e,t,n,a){Ao(e,t);var r=t.pendingProps,i=n,u=i._payload,o=i._init,s=o(u);t.type=s;var f=t.tag=xR(s),m=ha(s,r),S;switch(f){case Ne:return Qf(t,s),t.type=s=Gi(s),S=Yf(null,t,s,m,a),S;case ye:return t.type=s=Td(s),S=sm(null,t,s,m,a),S;case Me:return t.type=s=Cd(s),S=rm(null,t,s,m,a),S;case st:{if(t.type!==t.elementType){var C=s.propTypes;C&&fa(C,m,"prop",ee(s))}return S=im(null,t,s,ha(s.type,m),a),S}}var O="";throw s!==null&&typeof s=="object"&&s.$$typeof===it&&(O=" Did you wrap a component in React.lazy() more than once?"),new Error("Element type is invalid. Received a promise that resolves to: "+s+". "+("Lazy element type must resolve to a class or function."+O))}function cb(e,t,n,a,r){Ao(e,t),t.tag=ye;var i;return Ua(n)?(i=!0,zl(t)):i=!1,Ni(t,r),yp(t,n,a),Jc(t,n,a,r),qf(null,t,n,!0,i,r)}function fb(e,t,n,a){Ao(e,t);var r=t.pendingProps,i;{var u=hi(t,n,!1);i=yi(t,u)}Ni(t,a);var o,s;bu(t);{if(n.prototype&&typeof n.prototype.render=="function"){var f=ee(n)||"Unknown";jf[f]||(h("The <%s /> component appears to have a render method, but doesn't extend React.Component. This is likely to cause errors. Change %s to extend React.Component instead.",f,f),jf[f]=!0)}t.mode&_t&&ma.recordLegacyContextWarning(t,null),ja(!0),Yu.current=t,o=ji(null,t,n,r,i,a),s=Fi(),ja(!1)}if(Ci(),t.flags|=$e,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0){var m=ee(n)||"Unknown";qu[m]||(h("The <%s /> component appears to be a function component that returns a class instance. Change %s to a class that extends React.Component instead. If you can't use a class try assigning the prototype on the function as a workaround. `%s.prototype = React.Component.prototype`. Don't use an arrow function since it cannot be called with `new` by React.",m,m,m),qu[m]=!0)}if(typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0){{var S=ee(n)||"Unknown";qu[S]||(h("The <%s /> component appears to be a function component that returns a class instance. Change %s to a class that extends React.Component instead. If you can't use a class try assigning the prototype on the function as a workaround. `%s.prototype = React.Component.prototype`. Don't use an arrow function since it cannot be called with `new` by React.",S,S,S),qu[S]=!0)}t.tag=ye,t.memoizedState=null,t.updateQueue=null;var C=!1;return Ua(n)?(C=!0,zl(t)):C=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Lc(t),hp(t,o),Jc(t,n,r,a),qf(null,t,n,!0,C,a)}else{if(t.tag=Ne,t.mode&_t){Xt(!0);try{o=ji(null,t,n,r,i,a),s=Fi()}finally{Xt(!1)}}return tn()&&s&&Ec(t),Rn(null,t,o,a),Qf(t,n),t.child}}function Qf(e,t){{if(t&&t.childContextTypes&&h("%s(...): childContextTypes cannot be defined on a function component.",t.displayName||t.name||"Component"),e.ref!==null){var n="",a=mS();a&&(n+=`

Check the render method of \``+a+"`.");var r=a||"",i=e._debugSource;i&&(r=i.fileName+":"+i.lineNumber),Vf[r]||(Vf[r]=!0,h("Function components cannot be given refs. Attempts to access this ref will fail. Did you mean to use React.forwardRef()?%s",n))}if(typeof t.getDerivedStateFromProps=="function"){var u=ee(t)||"Unknown";Bf[u]||(h("%s: Function components do not support getDerivedStateFromProps.",u),Bf[u]=!0)}if(typeof t.contextType=="object"&&t.contextType!==null){var o=ee(t)||"Unknown";Ff[o]||(h("%s: Function components do not support contextType.",o),Ff[o]=!0)}}}var Pf={dehydrated:null,treeContext:null,retryLane:Pt};function Gf(e){return{baseLanes:e,cachePool:tb(),transitions:null}}function db(e,t){var n=null;return{baseLanes:Ee(e.baseLanes,t),cachePool:n,transitions:e.transitions}}function vb(e,t,n,a){if(t!==null){var r=t.memoizedState;if(r===null)return!1}return af(e,zu)}function pb(e,t){return Vl(e.childLanes,t)}function dm(e,t,n){var a=t.pendingProps;Ch(t)&&(t.flags|=ze);var r=ya.current,i=!1,u=(t.flags&ze)!==X;if(u||vb(r,e)?(i=!0,t.flags&=~ze):(e===null||e.memoizedState!==null)&&(r=jS(r,xp)),r=Ai(r),Or(t,r),e===null){Dc(t);var o=t.memoizedState;if(o!==null){var s=o.dehydrated;if(s!==null)return Sb(t,s)}var f=a.children,m=a.fallback;if(i){var S=mb(t,f,m,n),C=t.child;return C.memoizedState=Gf(n),t.memoizedState=Pf,S}else return Kf(t,f)}else{var O=e.memoizedState;if(O!==null){var U=O.dehydrated;if(U!==null)return bb(e,t,u,a,U,O,n)}if(i){var N=a.fallback,I=a.children,oe=yb(e,t,I,N,n),te=t.child,Ge=e.child.memoizedState;return te.memoizedState=Ge===null?Gf(n):db(Ge,n),te.childLanes=pb(e,n),t.memoizedState=Pf,oe}else{var je=a.children,E=hb(e,t,je,n);return t.memoizedState=null,E}}}function Kf(e,t,n){var a=e.mode,r={mode:"visible",children:t},i=Xf(r,a);return i.return=e,e.child=i,i}function mb(e,t,n,a){var r=e.mode,i=e.child,u={mode:"hidden",children:t},o,s;return(r&Be)===le&&i!==null?(o=i,o.childLanes=z,o.pendingProps=u,e.mode&ke&&(o.actualDuration=0,o.actualStartTime=-1,o.selfBaseDuration=0,o.treeBaseDuration=0),s=Lr(n,r,a,null)):(o=Xf(u,r),s=Lr(n,r,a,null)),o.return=e,s.return=e,o.sibling=s,e.child=o,s}function Xf(e,t,n){return mh(e,t,z,null)}function vm(e,t){return li(e,t)}function hb(e,t,n,a){var r=e.child,i=r.sibling,u=vm(r,{mode:"visible",children:n});if((t.mode&Be)===le&&(u.lanes=a),u.return=t,u.sibling=null,i!==null){var o=t.deletions;o===null?(t.deletions=[i],t.flags|=Wt):o.push(i)}return t.child=u,u}function yb(e,t,n,a,r){var i=t.mode,u=e.child,o=u.sibling,s={mode:"hidden",children:n},f;if((i&Be)===le&&t.child!==u){var m=t.child;f=m,f.childLanes=z,f.pendingProps=s,t.mode&ke&&(f.actualDuration=0,f.actualStartTime=-1,f.selfBaseDuration=u.selfBaseDuration,f.treeBaseDuration=u.treeBaseDuration),t.deletions=null}else f=vm(u,s),f.subtreeFlags=u.subtreeFlags&Qe;var S;return o!==null?S=li(o,a):(S=Lr(a,i,r,null),S.flags|=qe),S.return=t,f.return=t,f.sibling=S,t.child=f,S}function Mo(e,t,n,a){a!==null&&_c(a),Ui(t,e.child,null,n);var r=t.pendingProps,i=r.children,u=Kf(t,i);return u.flags|=qe,t.memoizedState=null,u}function gb(e,t,n,a,r){var i=t.mode,u={mode:"visible",children:n},o=Xf(u,i),s=Lr(a,i,r,null);return s.flags|=qe,o.return=t,s.return=t,o.sibling=s,t.child=o,(t.mode&Be)!==le&&Ui(t,e.child,null,r),s}function Sb(e,t,n){return(e.mode&Be)===le?(h("Cannot hydrate Suspense in legacy mode. Switch from ReactDOM.hydrate(element, container) to ReactDOMClient.hydrateRoot(container, <App />).render(element) or remove the Suspense components from the server rendered components."),e.lanes=fe):Er(t)?e.lanes=Qr:e.lanes=wn,null}function bb(e,t,n,a,r,i,u){if(n)if(t.flags&On){t.flags&=~On;var E=zf(new Error("There was an error while hydrating this Suspense boundary. Switched to client rendering."));return Mo(e,t,u,E)}else{if(t.memoizedState!==null)return t.child=e.child,t.flags|=ze,null;var D=a.children,b=a.fallback,L=gb(e,t,D,b,u),k=t.child;return k.memoizedState=Gf(u),t.memoizedState=Pf,L}else{if(nS(),(t.mode&Be)===le)return Mo(e,t,u,null);if(Er(r)){var o,s,f;{var m=ou(r);o=m.digest,s=m.message,f=m.stack}var S;s?S=new Error(s):S=new Error("The server could not finish this Suspense boundary, likely due to an error during server rendering. Switched to client rendering.");var C=zf(S,o,f);return Mo(e,t,u,C)}var O=Yn(u,e.childLanes);if(Sa||O){var U=Io();if(U!==null){var N=hg(U,u);if(N!==Pt&&N!==i.retryLane){i.retryLane=N;var I=ot;En(e,N),Ut(U,e,N,I)}}Sd();var oe=zf(new Error("This Suspense boundary received an update before it finished hydrating. This caused the boundary to switch to client rendering. The usual way to fix this is to wrap the original update in startTransition."));return Mo(e,t,u,oe)}else if(Dt(r)){t.flags|=ze,t.child=e.child;var te=oR.bind(null,e);return xl(r,te),null}else{iS(t,r,i.treeContext);var Ge=a.children,je=Kf(t,Ge);return je.flags|=Nn,je}}}function pm(e,t,n){e.lanes=Ee(e.lanes,t);var a=e.alternate;a!==null&&(a.lanes=Ee(a.lanes,t)),Mc(e.return,t,n)}function Eb(e,t,n){for(var a=t;a!==null;){if(a.tag===Fe){var r=a.memoizedState;r!==null&&pm(a,n,e)}else if(a.tag===St)pm(a,n,e);else if(a.child!==null){a.child.return=a,a=a.child;continue}if(a===e)return;for(;a.sibling===null;){if(a.return===null||a.return===e)return;a=a.return}a.sibling.return=a.return,a=a.sibling}}function Rb(e){for(var t=e,n=null;t!==null;){var a=t.alternate;a!==null&&so(a)===null&&(n=t),t=t.sibling}return n}function Tb(e){if(e!==void 0&&e!=="forwards"&&e!=="backwards"&&e!=="together"&&!wf[e])if(wf[e]=!0,typeof e=="string")switch(e.toLowerCase()){case"together":case"forwards":case"backwards":{h('"%s" is not a valid value for revealOrder on <SuspenseList />. Use lowercase "%s" instead.',e,e.toLowerCase());break}case"forward":case"backward":{h('"%s" is not a valid value for revealOrder on <SuspenseList />. React uses the -s suffix in the spelling. Use "%ss" instead.',e,e.toLowerCase());break}default:h('"%s" is not a supported revealOrder on <SuspenseList />. Did you mean "together", "forwards" or "backwards"?',e);break}else h('%s is not a supported value for revealOrder on <SuspenseList />. Did you mean "together", "forwards" or "backwards"?',e)}function Cb(e,t){e!==void 0&&!Uo[e]&&(e!=="collapsed"&&e!=="hidden"?(Uo[e]=!0,h('"%s" is not a supported value for tail on <SuspenseList />. Did you mean "collapsed" or "hidden"?',e)):t!=="forwards"&&t!=="backwards"&&(Uo[e]=!0,h('<SuspenseList tail="%s" /> is only valid if revealOrder is "forwards" or "backwards". Did you mean to specify revealOrder="forwards"?',e)))}function mm(e,t){{var n=fn(e),a=!n&&typeof w(e)=="function";if(n||a){var r=n?"array":"iterable";return h("A nested %s was passed to row #%s in <SuspenseList />. Wrap it in an additional SuspenseList to configure its revealOrder: <SuspenseList revealOrder=...> ... <SuspenseList revealOrder=...>{%s}</SuspenseList> ... </SuspenseList>",r,t,r),!1}}return!0}function xb(e,t){if((t==="forwards"||t==="backwards")&&e!==void 0&&e!==null&&e!==!1)if(fn(e)){for(var n=0;n<e.length;n++)if(!mm(e[n],n))return}else{var a=w(e);if(typeof a=="function"){var r=a.call(e);if(r)for(var i=r.next(),u=0;!i.done;i=r.next()){if(!mm(i.value,u))return;u++}}else h('A single row was passed to a <SuspenseList revealOrder="%s" />. This is not useful since it needs multiple rows. Did you mean to pass multiple children or an array?',t)}}function Jf(e,t,n,a,r){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:r}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=a,i.tail=n,i.tailMode=r)}function hm(e,t,n){var a=t.pendingProps,r=a.revealOrder,i=a.tail,u=a.children;Tb(r),Cb(i,r),xb(u,r),Rn(e,t,u,n);var o=ya.current,s=af(o,zu);if(s)o=rf(o,zu),t.flags|=ze;else{var f=e!==null&&(e.flags&ze)!==X;f&&Eb(t,t.child,n),o=Ai(o)}if(Or(t,o),(t.mode&Be)===le)t.memoizedState=null;else switch(r){case"forwards":{var m=Rb(t.child),S;m===null?(S=t.child,t.child=null):(S=m.sibling,m.sibling=null),Jf(t,!1,S,m,i);break}case"backwards":{var C=null,O=t.child;for(t.child=null;O!==null;){var U=O.alternate;if(U!==null&&so(U)===null){t.child=O;break}var N=O.sibling;O.sibling=C,C=O,O=N}Jf(t,!0,C,null,i);break}case"together":{Jf(t,!1,null,null,void 0);break}default:t.memoizedState=null}return t.child}function Db(e,t,n){tf(t,t.stateNode.containerInfo);var a=t.pendingProps;return e===null?t.child=Ui(t,null,a,n):Rn(e,t,a,n),t.child}var ym=!1;function _b(e,t,n){var a=t.type,r=a._context,i=t.pendingProps,u=t.memoizedProps,o=i.value;{"value"in i||ym||(ym=!0,h("The `value` prop is required for the `<Context.Provider>`. Did you misspell it or forget to pass it?"));var s=t.type.propTypes;s&&fa(s,i,"prop","Context.Provider")}if(ip(t,r,o),u!==null){var f=u.value;if(qn(f,o)){if(u.children===i.children&&!Ml())return or(e,t,n)}else ES(t,r,n)}var m=i.children;return Rn(e,t,m,n),t.child}var gm=!1;function Ob(e,t,n){var a=t.type;a._context===void 0?a!==a.Consumer&&(gm||(gm=!0,h("Rendering <Context> directly is not supported and will be removed in a future major release. Did you mean to render <Context.Consumer> instead?"))):a=a._context;var r=t.pendingProps,i=r.children;typeof i!="function"&&h("A context consumer was rendered with multiple children, or a child that isn't a function. A context consumer expects a single child that is a function. If you did pass a function, make sure there is no trailing or leading whitespace around it."),Ni(t,n);var u=Ot(a);bu(t);var o;return Yu.current=t,ja(!0),o=i(u),ja(!1),Ci(),t.flags|=$e,Rn(e,t,o,n),t.child}function Qu(){Sa=!0}function Ao(e,t){(t.mode&Be)===le&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=qe)}function or(e,t,n){return e!==null&&(t.dependencies=e.dependencies),$p(),al(t.lanes),Yn(n,t.childLanes)?(zS(e,t),t.child):null}function Nb(e,t,n){{var a=t.return;if(a===null)throw new Error("Cannot swap the root fiber.");if(e.alternate=null,t.alternate=null,n.index=t.index,n.sibling=t.sibling,n.return=t.return,n.ref=t.ref,t===a.child)a.child=n;else{var r=a.child;if(r===null)throw new Error("Expected parent to have a child.");for(;r.sibling!==t;)if(r=r.sibling,r===null)throw new Error("Expected to find the previous sibling.");r.sibling=n}var i=a.deletions;return i===null?(a.deletions=[e],a.flags|=Wt):i.push(e),n.flags|=qe,n}}function Zf(e,t){var n=e.lanes;return!!Yn(n,t)}function Ub(e,t,n){switch(t.tag){case se:cm(t);var a=t.stateNode;_i();break;case $:Tp(t);break;case ye:{var r=t.type;Ua(r)&&zl(t);break}case Ue:tf(t,t.stateNode.containerInfo);break;case Rt:{var i=t.memoizedProps.value,u=t.type._context;ip(t,u,i);break}case Tt:{var o=Yn(n,t.childLanes);o&&(t.flags|=ce);{var s=t.stateNode;s.effectDuration=0,s.passiveEffectDuration=0}}break;case Fe:{var f=t.memoizedState;if(f!==null){if(f.dehydrated!==null)return Or(t,Ai(ya.current)),t.flags|=ze,null;var m=t.child,S=m.childLanes;if(Yn(n,S))return dm(e,t,n);Or(t,Ai(ya.current));var C=or(e,t,n);return C!==null?C.sibling:null}else Or(t,Ai(ya.current));break}case St:{var O=(e.flags&ze)!==X,U=Yn(n,t.childLanes);if(O){if(U)return hm(e,t,n);t.flags|=ze}var N=t.memoizedState;if(N!==null&&(N.rendering=null,N.tail=null,N.lastEffect=null),Or(t,ya.current),U)break;return null}case rt:case kt:return t.lanes=z,lm(e,t,n)}return or(e,t,n)}function Sm(e,t,n){if(t._debugNeedsRemount&&e!==null)return Nb(e,t,Nd(t.type,t.key,t.pendingProps,t._debugOwner||null,t.mode,t.lanes));if(e!==null){var a=e.memoizedProps,r=t.pendingProps;if(a!==r||Ml()||t.type!==e.type)Sa=!0;else{var i=Zf(e,n);if(!i&&(t.flags&ze)===X)return Sa=!1,Ub(e,t,n);(e.flags&Vr)!==X?Sa=!0:Sa=!1}}else if(Sa=!1,tn()&&kg(t)){var u=t.index,o=Wg();Qv(t,o,u)}switch(t.lanes=z,t.tag){case yn:return fb(e,t,t.type,n);case Ct:{var s=t.elementType;return sb(e,t,s,n)}case Ne:{var f=t.type,m=t.pendingProps,S=t.elementType===f?m:ha(f,m);return Yf(e,t,f,S,n)}case ye:{var C=t.type,O=t.pendingProps,U=t.elementType===C?O:ha(C,O);return sm(e,t,C,U,n)}case se:return ub(e,t,n);case $:return lb(e,t,n);case ge:return ob(e,t);case Fe:return dm(e,t,n);case Ue:return Db(e,t,n);case Me:{var N=t.type,I=t.pendingProps,oe=t.elementType===N?I:ha(N,I);return rm(e,t,N,oe,n)}case Te:return ab(e,t,n);case dt:return rb(e,t,n);case Tt:return ib(e,t,n);case Rt:return _b(e,t,n);case ue:return Ob(e,t,n);case st:{var te=t.type,Ge=t.pendingProps,je=ha(te,Ge);if(t.type!==t.elementType){var E=te.propTypes;E&&fa(E,je,"prop",ee(te))}return je=ha(te.type,je),im(e,t,te,je,n)}case Ae:return um(e,t,t.type,t.pendingProps,n);case Zt:{var D=t.type,b=t.pendingProps,L=t.elementType===D?b:ha(D,b);return cb(e,t,D,L,n)}case St:return hm(e,t,n);case gn:break;case rt:return lm(e,t,n)}throw new Error("Unknown unit of work tag ("+t.tag+"). This error is likely caused by a bug in React. Please file an issue.")}function qa(e){e.flags|=ce}function bm(e){e.flags|=Wn,e.flags|=H}function Em(e,t){var n=e!==null&&e.child===t.child;if(n)return!0;if((t.flags&Wt)!==X)return!1;for(var a=t.child;a!==null;){if((a.flags&Se)!==X||(a.subtreeFlags&Se)!==X)return!1;a=a.sibling}return!0}var Pu,Gu,zo,Ho;if(Qt)Pu=function(e,t,n,a){for(var r=t.child;r!==null;){if(r.tag===$||r.tag===ge)gr(e,r.stateNode);else if(r.tag!==Ue){if(r.child!==null){r.child.return=r,r=r.child;continue}}if(r===t)return;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}},Gu=function(e,t){},zo=function(e,t,n,a,r){var i=e.memoizedProps;if(i!==a){var u=t.stateNode,o=Au(),s=wr(u,n,i,a,r,o);t.updateQueue=s,s&&qa(t)}},Ho=function(e,t,n,a){n!==a&&qa(t)};else if(di){Pu=function(e,t,n,a){for(var r=t.child;r!==null;){if(r.tag===$){var i=r.stateNode;if(n&&a){var u=r.memoizedProps,o=r.type;i=Ze(i,o,u,r)}gr(e,i)}else if(r.tag===ge){var s=r.stateNode;if(n&&a){var f=r.memoizedProps;s=et(s,f,r)}gr(e,s)}else if(r.tag!==Ue){if(r.tag===rt&&r.memoizedState!==null){var m=r.child;m!==null&&(m.return=r),Pu(e,r,!0,!0)}else if(r.child!==null){r.child.return=r,r=r.child;continue}}if(r=r,r===t)return;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};var Rm=function(e,t,n,a){for(var r=t.child;r!==null;){if(r.tag===$){var i=r.stateNode;if(n&&a){var u=r.memoizedProps,o=r.type;i=Ze(i,o,u,r)}Z(e,i)}else if(r.tag===ge){var s=r.stateNode;if(n&&a){var f=r.memoizedProps;s=et(s,f,r)}Z(e,s)}else if(r.tag!==Ue){if(r.tag===rt&&r.memoizedState!==null){var m=r.child;m!==null&&(m.return=r),Rm(e,r,!0,!0)}else if(r.child!==null){r.child.return=r,r=r.child;continue}}if(r=r,r===t)return;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};Gu=function(e,t){var n=t.stateNode,a=Em(e,t);if(!a){var r=n.containerInfo,i=ie(r);Rm(i,t,!1,!1),n.pendingChildren=i,qa(t),me(r,i)}},zo=function(e,t,n,a,r){var i=e.stateNode,u=e.memoizedProps,o=Em(e,t);if(o&&u===a){t.stateNode=i;return}var s=t.stateNode,f=Au(),m=null;if(u!==a&&(m=wr(s,n,u,a,r,f)),o&&m===null){t.stateNode=i;return}var S=j(i,m,n,u,a,t,o,s);fi(S,n,a,r,f)&&qa(t),t.stateNode=S,o?qa(t):Pu(S,t,!1,!1)},Ho=function(e,t,n,a){if(n!==a){var r=ef(),i=Au();t.stateNode=au(a,r,i,t),qa(t)}else t.stateNode=e.stateNode}}else Gu=function(e,t){},zo=function(e,t,n,a,r){},Ho=function(e,t,n,a){};function Ku(e,t){if(!tn())switch(e.tailMode){case"hidden":{for(var n=e.tail,a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?e.tail=null:a.sibling=null;break}case"collapsed":{for(var r=e.tail,i=null;r!==null;)r.alternate!==null&&(i=r),r=r.sibling;i===null?!t&&e.tail!==null?e.tail.sibling=null:e.tail=null:i.sibling=null;break}}}function an(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=z,a=X;if(t){if((e.mode&ke)!==le){for(var s=e.selfBaseDuration,f=e.child;f!==null;)n=Ee(n,Ee(f.lanes,f.childLanes)),a|=f.subtreeFlags&Qe,a|=f.flags&Qe,s+=f.treeBaseDuration,f=f.sibling;e.treeBaseDuration=s}else for(var m=e.child;m!==null;)n=Ee(n,Ee(m.lanes,m.childLanes)),a|=m.subtreeFlags&Qe,a|=m.flags&Qe,m.return=e,m=m.sibling;e.subtreeFlags|=a}else{if((e.mode&ke)!==le){for(var r=e.actualDuration,i=e.selfBaseDuration,u=e.child;u!==null;)n=Ee(n,Ee(u.lanes,u.childLanes)),a|=u.subtreeFlags,a|=u.flags,r+=u.actualDuration,i+=u.treeBaseDuration,u=u.sibling;e.actualDuration=r,e.treeBaseDuration=i}else for(var o=e.child;o!==null;)n=Ee(n,Ee(o.lanes,o.childLanes)),a|=o.subtreeFlags,a|=o.flags,o.return=e,o=o.sibling;e.subtreeFlags|=a}return e.childLanes=n,t}function Mb(e,t,n){if(cS()&&(t.mode&Be)!==le&&(t.flags&ze)===X)return kv(t),_i(),t.flags|=On|_a|xt,!1;var a=Pl(t);if(n!==null&&n.dehydrated!==null)if(e===null){if(!a)throw new Error("A dehydrated suspense component was completed without a hydrated node. This is probably a bug in React.");if(oS(t),an(t),(t.mode&ke)!==le){var r=n!==null;if(r){var i=t.child;i!==null&&(t.treeBaseDuration-=i.treeBaseDuration)}}return!1}else{if(_i(),(t.flags&ze)===X&&(t.memoizedState=null),t.flags|=ce,an(t),(t.mode&ke)!==le){var u=n!==null;if(u){var o=t.child;o!==null&&(t.treeBaseDuration-=o.treeBaseDuration)}}return!1}else return Wv(),!0}function Tm(e,t,n){var a=t.pendingProps;switch(Rc(t),t.tag){case yn:case Ct:case Ae:case Ne:case Me:case Te:case dt:case Tt:case ue:case st:return an(t),null;case ye:{var r=t.type;return Ua(r)&&Al(t),an(t),null}case se:{var i=t.stateNode;if(Mi(t),Gs(t),lf(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),e===null||e.child===null){var u=Pl(t);if(u)qa(t);else if(e!==null){var o=e.memoizedState;(!o.isDehydrated||(t.flags&On)!==X)&&(t.flags|=sn,Wv())}}return Gu(e,t),an(t),null}case $:{nf(t);var s=ef(),f=t.type;if(e!==null&&t.stateNode!=null)zo(e,t,f,a,s),e.ref!==t.ref&&bm(t);else{if(!a){if(t.stateNode===null)throw new Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");return an(t),null}var m=Au(),S=Pl(t);if(S)uS(t,s,m)&&qa(t);else{var C=tu(f,a,s,m,t);Pu(C,t,!1,!1),t.stateNode=C,fi(C,f,a,s,m)&&qa(t)}t.ref!==null&&bm(t)}return an(t),null}case ge:{var O=a;if(e&&t.stateNode!=null){var U=e.memoizedProps;Ho(e,t,U,O)}else{if(typeof O!="string"&&t.stateNode===null)throw new Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");var N=ef(),I=Au(),oe=Pl(t);oe?lS(t)&&qa(t):t.stateNode=au(O,N,I,t)}return an(t),null}case Fe:{zi(t);var te=t.memoizedState;if(e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){var Ge=Mb(e,t,te);if(!Ge)return t.flags&xt?t:null}if((t.flags&ze)!==X)return t.lanes=n,(t.mode&ke)!==le&&Af(t),t;var je=te!==null,E=e!==null&&e.memoizedState!==null;if(je!==E&&je){var D=t.child;if(D.flags|=sa,(t.mode&Be)!==le){var b=e===null&&(t.memoizedProps.unstable_avoidThisFallback!==!0||!Re);b||af(ya.current,xp)?ZE():Sd()}}var L=t.updateQueue;if(L!==null&&(t.flags|=ce),an(t),(t.mode&ke)!==le&&je){var k=t.child;k!==null&&(t.treeBaseDuration-=k.treeBaseDuration)}return null}case Ue:return Mi(t),Gu(e,t),e===null&&xs(t.stateNode.containerInfo),an(t),null;case Rt:var K=t.type._context;return Uc(K,t),an(t),null;case Zt:{var He=t.type;return Ua(He)&&Al(t),an(t),null}case St:{zi(t);var de=t.memoizedState;if(de===null)return an(t),null;var we=(t.flags&ze)!==X,_e=de.rendering;if(_e===null)if(we)Ku(de,!1);else{var tt=WE()&&(e===null||(e.flags&ze)===X);if(!tt)for(var ae=t.child;ae!==null;){var yt=so(ae);if(yt!==null){we=!0,t.flags|=ze,Ku(de,!1);var mn=yt.updateQueue;return mn!==null&&(t.updateQueue=mn,t.flags|=ce),t.subtreeFlags=X,HS(t,n),Or(t,rf(ya.current,zu)),t.child}ae=ae.sibling}de.tail!==null&&Kt()>Jm()&&(t.flags|=ze,we=!0,Ku(de,!1),t.lanes=bv)}else{if(!we){var on=so(_e);if(on!==null){t.flags|=ze,we=!0;var Gn=on.updateQueue;if(Gn!==null&&(t.updateQueue=Gn,t.flags|=ce),Ku(de,!0),de.tail===null&&de.tailMode==="hidden"&&!_e.alternate&&!tn())return an(t),null}else Kt()*2-de.renderingStartTime>Jm()&&n!==wn&&(t.flags|=ze,we=!0,Ku(de,!1),t.lanes=bv)}if(de.isBackwards)_e.sibling=t.child,t.child=_e;else{var Cn=de.last;Cn!==null?Cn.sibling=_e:t.child=_e,de.last=_e}}if(de.tail!==null){var xn=de.tail;de.rendering=xn,de.tail=xn.sibling,de.renderingStartTime=Kt(),xn.sibling=null;var hn=ya.current;return we?hn=rf(hn,zu):hn=Ai(hn),Or(t,hn),xn}return an(t),null}case gn:break;case rt:case kt:{gd(t);var fr=t.memoizedState,Ki=fr!==null;if(e!==null){var ul=e.memoizedState,Xa=ul!==null;Xa!==Ki&&!Xe&&(t.flags|=sa)}return!Ki||(t.mode&Be)===le?an(t):Yn(Ga,wn)&&(an(t),Qt&&t.subtreeFlags&(qe|ce)&&(t.flags|=sa)),null}case ia:return null;case Jn:return null}throw new Error("Unknown unit of work tag ("+t.tag+"). This error is likely caused by a bug in React. Please file an issue.")}function Ab(e,t,n){switch(Rc(t),t.tag){case ye:{var a=t.type;Ua(a)&&Al(t);var r=t.flags;return r&xt?(t.flags=r&~xt|ze,(t.mode&ke)!==le&&Af(t),t):null}case se:{var i=t.stateNode;Mi(t),Gs(t),lf();var u=t.flags;return(u&xt)!==X&&(u&ze)===X?(t.flags=u&~xt|ze,t):null}case $:return nf(t),null;case Fe:{zi(t);var o=t.memoizedState;if(o!==null&&o.dehydrated!==null){if(t.alternate===null)throw new Error("Threw in newly mounted dehydrated component. This is likely a bug in React. Please file an issue.");_i()}var s=t.flags;return s&xt?(t.flags=s&~xt|ze,(t.mode&ke)!==le&&Af(t),t):null}case St:return zi(t),null;case Ue:return Mi(t),null;case Rt:var f=t.type._context;return Uc(f,t),null;case rt:case kt:return gd(t),null;case ia:return null;default:return null}}function Cm(e,t,n){switch(Rc(t),t.tag){case ye:{var a=t.type.childContextTypes;a!=null&&Al(t);break}case se:{var r=t.stateNode;Mi(t),Gs(t),lf();break}case $:{nf(t);break}case Ue:Mi(t);break;case Fe:zi(t);break;case St:zi(t);break;case Rt:var i=t.type._context;Uc(i,t);break;case rt:case kt:gd(t);break}}function xm(e,t,n,a,r,i,u,o,s){var f=Array.prototype.slice.call(arguments,3);try{t.apply(n,f)}catch(m){this.onError(m)}}var Dm=xm;if(typeof window!="undefined"&&typeof window.dispatchEvent=="function"&&typeof document!="undefined"&&typeof document.createEvent=="function"){var kf=document.createElement("react");Dm=function(t,n,a,r,i,u,o,s,f){if(typeof document=="undefined"||document===null)throw new Error("The `document` global was defined when React was initialized, but is not defined anymore. This can happen in a test environment if a component schedules an update from an asynchronous callback, but the test has already finished running. To solve this, you can either unmount the component at the end of your test (and ensure that any asynchronous operations get canceled in `componentWillUnmount`), or you can change the test itself to be asynchronous.");var m=document.createEvent("Event"),S=!1,C=!0,O=window.event,U=Object.getOwnPropertyDescriptor(window,"event");function N(){kf.removeEventListener(D,oe,!1),typeof window.event!="undefined"&&window.hasOwnProperty("event")&&(window.event=O)}var I=Array.prototype.slice.call(arguments,3);function oe(){S=!0,N(),n.apply(a,I),C=!1}var te,Ge=!1,je=!1;function E(b){if(te=b.error,Ge=!0,te===null&&b.colno===0&&b.lineno===0&&(je=!0),b.defaultPrevented&&te!=null&&typeof te=="object")try{te._suppressLogging=!0}catch(L){}}var D="react-"+(t||"invokeguardedcallback");if(window.addEventListener("error",E),kf.addEventListener(D,oe,!1),m.initEvent(D,!1,!1),kf.dispatchEvent(m),U&&Object.defineProperty(window,"event",U),S&&C&&(Ge?je&&(te=new Error("A cross-origin error was thrown. React doesn't have access to the actual error object in development. See https://reactjs.org/link/crossorigin-error for more information.")):te=new Error(`An error was thrown inside one of your components, but React doesn't know what it was. This is likely due to browser flakiness. React does its best to preserve the "Pause on exceptions" behavior of the DevTools, which requires some DEV-mode only tricks. It's possible that these don't work in your browser. Try triggering the error in production mode, or switching to a modern browser. If you suspect that this is actually an issue with React, please file an issue.`),this.onError(te)),window.removeEventListener("error",E),!S)return N(),xm.apply(this,arguments)}}var zb=Dm,Xu=!1,Lo=null,Hb={onError:function(e){Xu=!0,Lo=e}};function _m(e,t,n,a,r,i,u,o,s){Xu=!1,Lo=null,zb.apply(Hb,arguments)}function Lb(){return Xu}function Om(){if(Xu){var e=Lo;return Xu=!1,Lo=null,e}else throw new Error("clearCaughtError was called but no error was captured. This error is likely caused by a bug in React. Please file an issue.")}var Nm=null;Nm=new Set;var jo=!1,rn=!1,jb=typeof WeakSet=="function"?WeakSet:Set,J=null,Bi=null,Vi=null;function Fb(e){_m(null,function(){throw e}),Om()}var Bb=function(e,t){if(t.props=e.memoizedProps,t.state=e.memoizedState,e.mode&ke)try{Ya(),t.componentWillUnmount()}finally{wa(e)}else t.componentWillUnmount()};function Um(e,t){try{Mr(zt,e)}catch(n){Ie(e,t,n)}}function Wf(e,t,n){try{Bb(e,n)}catch(a){Ie(e,t,a)}}function Vb(e,t,n){try{n.componentDidMount()}catch(a){Ie(e,t,a)}}function Mm(e,t){try{Hm(e)}catch(n){Ie(e,t,n)}}function wi(e,t){var n=e.ref;if(n!==null)if(typeof n=="function"){var a;try{if(gt&&zn&&e.mode&ke)try{Ya(),a=n(null)}finally{wa(e)}else a=n(null)}catch(r){Ie(e,t,r)}typeof a=="function"&&h("Unexpected return value from a callback ref in %s. A callback ref should not return a function.",W(e))}else n.current=null}function Fo(e,t,n){try{n()}catch(a){Ie(e,t,a)}}var Am=null,zm=!1;function wb(e,t){Am=Rs(e.containerInfo),J=t,Yb();var n=zm;return zm=!1,Am=null,n}function Yb(){for(;J!==null;){var e=J,t=e.child;(e.subtreeFlags&De)!==X&&t!==null?(t.return=e,J=t):qb()}}function qb(){for(;J!==null;){var e=J;Et(e);try{Qb(e)}catch(n){Ie(e,e.return,n)}bn();var t=e.sibling;if(t!==null){t.return=e.return,J=t;return}J=e.return}}function Qb(e){var t=e.alternate,n=e.flags;if((n&sn)!==X){switch(Et(e),e.tag){case Ne:case Me:case Ae:break;case ye:{if(t!==null){var a=t.memoizedProps,r=t.memoizedState,i=e.stateNode;e.type===e.elementType&&!ni&&(i.props!==e.memoizedProps&&h("Expected %s props to match memoized props before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",W(e)||"instance"),i.state!==e.memoizedState&&h("Expected %s state to match memoized state before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",W(e)||"instance"));var u=i.getSnapshotBeforeUpdate(e.elementType===e.type?a:ha(e.type,a),r);{var o=Nm;u===void 0&&!o.has(e.type)&&(o.add(e.type),h("%s.getSnapshotBeforeUpdate(): A snapshot value (or null) must be returned. You have returned undefined.",W(e)))}i.__reactInternalSnapshotBeforeUpdate=u}break}case se:{if(Qt){var s=e.stateNode;x(s.containerInfo)}break}case $:case ge:case Ue:case Zt:break;default:throw new Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.")}bn()}}function ba(e,t,n){var a=t.updateQueue,r=a!==null?a.lastEffect:null;if(r!==null){var i=r.next,u=i;do{if((u.tag&e)===e){var o=u.destroy;u.destroy=void 0,o!==void 0&&((e&nn)!==Un?Lg(t):(e&zt)!==Un&&jv(t),(e&Fa)!==Un&&il(!0),Fo(t,n,o),(e&Fa)!==Un&&il(!1),(e&nn)!==Un?jg():(e&zt)!==Un&&Fv())}u=u.next}while(u!==i)}}function Mr(e,t){var n=t.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var r=a.next,i=r;do{if((i.tag&e)===e){(e&nn)!==Un?zg(t):(e&zt)!==Un&&Fg(t);var u=i.create;(e&Fa)!==Un&&il(!0),i.destroy=u(),(e&Fa)!==Un&&il(!1),(e&nn)!==Un?Hg():(e&zt)!==Un&&Bg();{var o=i.destroy;if(o!==void 0&&typeof o!="function"){var s=void 0;(i.tag&zt)!==X?s="useLayoutEffect":(i.tag&Fa)!==X?s="useInsertionEffect":s="useEffect";var f=void 0;o===null?f=" You returned null. If your effect does not require clean up, return undefined (or nothing).":typeof o.then=="function"?f=`

It looks like you wrote `+s+`(async () => ...) or returned a Promise. Instead, write the async function inside your effect and call it immediately:

`+s+`(() => {
  async function fetchData() {
    // You can await here
    const response = await MyAPI.getData(someId);
    // ...
  }
  fetchData();
}, [someId]); // Or [] if effect doesn't need props or state

Learn more about data fetching with Hooks: https://reactjs.org/link/hooks-data-fetching`:f=" You returned: "+o,h("%s must not return anything besides a function, which is used for clean-up.%s",s,f)}}}i=i.next}while(i!==r)}}function Pb(e,t){if((t.flags&ce)!==X)switch(t.tag){case Tt:{var n=t.stateNode.passiveEffectDuration,a=t.memoizedProps,r=a.id,i=a.onPostCommit,u=Wp(),o=t.alternate===null?"mount":"update";kp()&&(o="nested-update"),typeof i=="function"&&i(r,o,n,u);var s=t.return;e:for(;s!==null;){switch(s.tag){case se:var f=s.stateNode;f.passiveEffectDuration+=n;break e;case Tt:var m=s.stateNode;m.passiveEffectDuration+=n;break e}s=s.return}break}}}function Gb(e,t,n,a){if((n.flags&vt)!==X)switch(n.tag){case Ne:case Me:case Ae:{if(!rn)if(n.mode&ke)try{Ya(),Mr(zt|At,n)}finally{wa(n)}else Mr(zt|At,n);break}case ye:{var r=n.stateNode;if(n.flags&ce&&!rn)if(t===null)if(n.type===n.elementType&&!ni&&(r.props!==n.memoizedProps&&h("Expected %s props to match memoized props before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",W(n)||"instance"),r.state!==n.memoizedState&&h("Expected %s state to match memoized state before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",W(n)||"instance")),n.mode&ke)try{Ya(),r.componentDidMount()}finally{wa(n)}else r.componentDidMount();else{var i=n.elementType===n.type?t.memoizedProps:ha(n.type,t.memoizedProps),u=t.memoizedState;if(n.type===n.elementType&&!ni&&(r.props!==n.memoizedProps&&h("Expected %s props to match memoized props before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",W(n)||"instance"),r.state!==n.memoizedState&&h("Expected %s state to match memoized state before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",W(n)||"instance")),n.mode&ke)try{Ya(),r.componentDidUpdate(i,u,r.__reactInternalSnapshotBeforeUpdate)}finally{wa(n)}else r.componentDidUpdate(i,u,r.__reactInternalSnapshotBeforeUpdate)}var o=n.updateQueue;o!==null&&(n.type===n.elementType&&!ni&&(r.props!==n.memoizedProps&&h("Expected %s props to match memoized props before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.",W(n)||"instance"),r.state!==n.memoizedState&&h("Expected %s state to match memoized state before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.",W(n)||"instance")),fp(n,o,r));break}case se:{var s=n.updateQueue;if(s!==null){var f=null;if(n.child!==null)switch(n.child.tag){case $:f=ci(n.child.stateNode);break;case ye:f=n.child.stateNode;break}fp(n,s,f)}break}case $:{var m=n.stateNode;if(t===null&&n.flags&ce){var S=n.type,C=n.memoizedProps;pi(m,S,C,n)}break}case ge:break;case Ue:break;case Tt:{{var O=n.memoizedProps,U=O.onCommit,N=O.onRender,I=n.stateNode.effectDuration,oe=Wp(),te=t===null?"mount":"update";kp()&&(te="nested-update"),typeof N=="function"&&N(n.memoizedProps.id,te,n.actualDuration,n.treeBaseDuration,n.actualStartTime,oe);{typeof U=="function"&&U(n.memoizedProps.id,te,I,oe),nR(n);var Ge=n.return;e:for(;Ge!==null;){switch(Ge.tag){case se:var je=Ge.stateNode;je.effectDuration+=I;break e;case Tt:var E=Ge.stateNode;E.effectDuration+=I;break e}Ge=Ge.return}}}break}case Fe:{eE(e,n);break}case St:case Zt:case gn:case rt:case kt:case Jn:break;default:throw new Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.")}rn||n.flags&Wn&&Hm(n)}function Kb(e){switch(e.tag){case Ne:case Me:case Ae:{if(e.mode&ke)try{Ya(),Um(e,e.return)}finally{wa(e)}else Um(e,e.return);break}case ye:{var t=e.stateNode;typeof t.componentDidMount=="function"&&Vb(e,e.return,t),Mm(e,e.return);break}case $:{Mm(e,e.return);break}}}function Xb(e,t){var n=null;if(Qt)for(var a=e;;){if(a.tag===$){if(n===null){n=a;try{var r=a.stateNode;t?zs(r):p(a.stateNode,a.memoizedProps)}catch(u){Ie(e,e.return,u)}}}else if(a.tag===ge){if(n===null)try{var i=a.stateNode;t?l(i):R(i,a.memoizedProps)}catch(u){Ie(e,e.return,u)}}else if(!((a.tag===rt||a.tag===kt)&&a.memoizedState!==null&&a!==e)){if(a.child!==null){a.child.return=a,a=a.child;continue}}if(a===e)return;for(;a.sibling===null;){if(a.return===null||a.return===e)return;n===a&&(n=null),a=a.return}n===a&&(n=null),a.sibling.return=a.return,a=a.sibling}}function Hm(e){var t=e.ref;if(t!==null){var n=e.stateNode,a;switch(e.tag){case $:a=ci(n);break;default:a=n}if(typeof t=="function"){var r;if(e.mode&ke)try{Ya(),r=t(a)}finally{wa(e)}else r=t(a);typeof r=="function"&&h("Unexpected return value from a callback ref in %s. A callback ref should not return a function.",W(e))}else t.hasOwnProperty("current")||h("Unexpected ref object provided for %s. Use either a ref-setter function or React.createRef().",W(e)),t.current=a}}function Jb(e){var t=e.alternate;t!==null&&(t.return=null),e.return=null}function Lm(e){var t=e.alternate;t!==null&&(e.alternate=null,Lm(t));{if(e.child=null,e.deletions=null,e.sibling=null,e.tag===$){var n=e.stateNode;n!==null&&gl(n)}e.stateNode=null,e._debugOwner=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}}function Zb(e){if(di){var t=e.stateNode,n=t.containerInfo,a=ie(n);Oe(n,a)}}function kb(e){for(var t=e.return;t!==null;){if(jm(t))return t;t=t.return}throw new Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.")}function jm(e){return e.tag===$||e.tag===se||e.tag===Ue}function Fm(e){var t=e;e:for(;;){for(;t.sibling===null;){if(t.return===null||jm(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==$&&t.tag!==ge&&t.tag!==Xn;){if(t.flags&qe||t.child===null||t.tag===Ue)continue e;t.child.return=t,t=t.child}if(!(t.flags&qe))return t.stateNode}}function Wb(e){if(Qt){var t=kb(e);switch(t.tag){case $:{var n=t.stateNode;t.flags&oa&&(Cl(n),t.flags&=~oa);var a=Fm(e);$f(e,a,n);break}case se:case Ue:{var r=t.stateNode.containerInfo,i=Fm(e);If(e,i,r);break}default:throw new Error("Invalid host parent fiber. This error is likely caused by a bug in React. Please file an issue.")}}}function If(e,t,n){var a=e.tag,r=a===$||a===ge;if(r){var i=e.stateNode;t?lu(n,i,t):Rl(n,i)}else if(a!==Ue){var u=e.child;if(u!==null){If(u,t,n);for(var o=u.sibling;o!==null;)If(o,t,n),o=o.sibling}}}function $f(e,t,n){var a=e.tag,r=a===$||a===ge;if(r){var i=e.stateNode;t?uu(n,i,t):br(n,i)}else if(a!==Ue){var u=e.child;if(u!==null){$f(u,t,n);for(var o=u.sibling;o!==null;)$f(o,t,n),o=o.sibling}}}var un=null,Ea=!1;function Ib(e,t,n){if(Qt){var a=t;e:for(;a!==null;){switch(a.tag){case $:{un=a.stateNode,Ea=!1;break e}case se:{un=a.stateNode.containerInfo,Ea=!0;break e}case Ue:{un=a.stateNode.containerInfo,Ea=!0;break e}}a=a.return}if(un===null)throw new Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.");ed(e,t,n),un=null,Ea=!1}else ed(e,t,n);Jb(n)}function Qa(e,t,n){for(var a=n.child;a!==null;)ed(e,t,a),a=a.sibling}function ed(e,t,n){switch(Ng(n),n.tag){case $:rn||wi(n,t);case ge:{if(Qt){var a=un,r=Ea;un=null,Qa(e,t,n),un=a,Ea=r,un!==null&&(Ea?As(un,n.stateNode):Ms(un,n.stateNode))}else Qa(e,t,n);return}case Xn:{Qt&&un!==null&&(Ea?My(un,n.stateNode):Uy(un,n.stateNode));return}case Ue:{if(Qt){var i=un,u=Ea;un=n.stateNode.containerInfo,Ea=!0,Qa(e,t,n),un=i,Ea=u}else Zb(n),Qa(e,t,n);return}case Ne:case Me:case st:case Ae:{if(!rn){var o=n.updateQueue;if(o!==null){var s=o.lastEffect;if(s!==null){var f=s.next,m=f;do{var S=m,C=S.destroy,O=S.tag;C!==void 0&&((O&Fa)!==Un?Fo(n,t,C):(O&zt)!==Un&&(jv(n),n.mode&ke?(Ya(),Fo(n,t,C),wa(n)):Fo(n,t,C),Fv())),m=m.next}while(m!==f)}}}Qa(e,t,n);return}case ye:{if(!rn){wi(n,t);var U=n.stateNode;typeof U.componentWillUnmount=="function"&&Wf(n,t,U)}Qa(e,t,n);return}case gn:{Qa(e,t,n);return}case rt:{if(n.mode&Be){var N=rn;rn=N||n.memoizedState!==null,Qa(e,t,n),rn=N}else Qa(e,t,n);break}default:{Qa(e,t,n);return}}}function $b(e){var t=e.memoizedState}function eE(e,t){if(Mt){var n=t.memoizedState;if(n===null){var a=t.alternate;if(a!==null){var r=a.memoizedState;if(r!==null){var i=r.dehydrated;i!==null&&Ny(i)}}}}}function Bm(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new jb),t.forEach(function(a){var r=sR.bind(null,e,a);if(!n.has(a)){if(n.add(a),va)if(Bi!==null&&Vi!==null)rl(Vi,Bi);else throw Error("Expected finished root and lanes to be set. This is a bug in React.");a.then(r,r)}})}}function tE(e,t,n){Bi=n,Vi=e,Et(t),Vm(t,e),Et(t),Bi=null,Vi=null}function Ra(e,t,n){var a=t.deletions;if(a!==null)for(var r=0;r<a.length;r++){var i=a[r];try{Ib(e,t,i)}catch(s){Ie(i,t,s)}}var u=yS();if(t.subtreeFlags&Se)for(var o=t.child;o!==null;)Et(o),Vm(o,e),o=o.sibling;Et(u)}function Vm(e,t,n){var a=e.alternate,r=e.flags;switch(e.tag){case Ne:case Me:case st:case Ae:{if(Ra(t,e),Pa(e),r&ce){try{ba(Fa|At,e,e.return),Mr(Fa|At,e)}catch(ae){Ie(e,e.return,ae)}if(e.mode&ke){try{Ya(),ba(zt|At,e,e.return)}catch(ae){Ie(e,e.return,ae)}wa(e)}else try{ba(zt|At,e,e.return)}catch(ae){Ie(e,e.return,ae)}}return}case ye:{Ra(t,e),Pa(e),r&Wn&&a!==null&&wi(a,a.return);return}case $:{if(Ra(t,e),Pa(e),r&Wn&&a!==null&&wi(a,a.return),Qt){if(e.flags&oa){var i=e.stateNode;try{Cl(i)}catch(ae){Ie(e,e.return,ae)}}if(r&ce){var u=e.stateNode;if(u!=null){var o=e.memoizedProps,s=a!==null?a.memoizedProps:o,f=e.type,m=e.updateQueue;if(e.updateQueue=null,m!==null)try{iu(u,m,f,s,o,e)}catch(ae){Ie(e,e.return,ae)}}}}return}case ge:{if(Ra(t,e),Pa(e),r&ce&&Qt){if(e.stateNode===null)throw new Error("This should have a text node initialized. This error is likely caused by a bug in React. Please file an issue.");var S=e.stateNode,C=e.memoizedProps,O=a!==null?a.memoizedProps:C;try{Tl(S,O,C)}catch(ae){Ie(e,e.return,ae)}}return}case se:{if(Ra(t,e),Pa(e),r&ce){if(Qt&&Mt&&a!==null){var U=a.memoizedState;if(U.isDehydrated)try{Oy(t.containerInfo)}catch(ae){Ie(e,e.return,ae)}}if(di){var N=t.containerInfo,I=t.pendingChildren;try{Oe(N,I)}catch(ae){Ie(e,e.return,ae)}}}return}case Ue:{if(Ra(t,e),Pa(e),r&ce&&di){var oe=e.stateNode,te=oe.containerInfo,Ge=oe.pendingChildren;try{Oe(te,Ge)}catch(ae){Ie(e,e.return,ae)}}return}case Fe:{Ra(t,e),Pa(e);var je=e.child;if(je.flags&sa){var E=je.stateNode,D=je.memoizedState,b=D!==null;if(E.isHidden=b,b){var L=je.alternate!==null&&je.alternate.memoizedState!==null;L||JE()}}if(r&ce){try{$b(e)}catch(ae){Ie(e,e.return,ae)}Bm(e)}return}case rt:{var k=a!==null&&a.memoizedState!==null;if(e.mode&Be){var K=rn;rn=K||k,Ra(t,e),rn=K}else Ra(t,e);if(Pa(e),r&sa){var He=e.stateNode,de=e.memoizedState,we=de!==null,_e=e;if(He.isHidden=we,we&&!k&&(_e.mode&Be)!==le){J=_e;for(var tt=_e.child;tt!==null;)J=tt,aE(tt),tt=tt.sibling}Qt&&Xb(_e,we)}return}case St:{Ra(t,e),Pa(e),r&ce&&Bm(e);return}case gn:return;default:{Ra(t,e),Pa(e);return}}}function Pa(e){var t=e.flags;if(t&qe){try{Wb(e)}catch(n){Ie(e,e.return,n)}e.flags&=~qe}t&Nn&&(e.flags&=~Nn)}function nE(e,t,n){Bi=n,Vi=t,J=e,wm(e,t,n),Bi=null,Vi=null}function wm(e,t,n){for(var a=(e.mode&Be)!==le;J!==null;){var r=J,i=r.child;if(r.tag===rt&&a){var u=r.memoizedState!==null,o=u||jo;if(o){td(e,t,n);continue}else{var s=r.alternate,f=s!==null&&s.memoizedState!==null,m=f||rn,S=jo,C=rn;jo=o,rn=m,rn&&!C&&(J=r,rE(r));for(var O=i;O!==null;)J=O,wm(O,t,n),O=O.sibling;J=r,jo=S,rn=C,td(e,t,n);continue}}(r.subtreeFlags&vt)!==X&&i!==null?(i.return=r,J=i):td(e,t,n)}}function td(e,t,n){for(;J!==null;){var a=J;if((a.flags&vt)!==X){var r=a.alternate;Et(a);try{Gb(t,r,a,n)}catch(u){Ie(a,a.return,u)}bn()}if(a===e){J=null;return}var i=a.sibling;if(i!==null){i.return=a.return,J=i;return}J=a.return}}function aE(e){for(;J!==null;){var t=J,n=t.child;switch(t.tag){case Ne:case Me:case st:case Ae:{if(t.mode&ke)try{Ya(),ba(zt,t,t.return)}finally{wa(t)}else ba(zt,t,t.return);break}case ye:{wi(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&Wf(t,t.return,a);break}case $:{wi(t,t.return);break}case rt:{var r=t.memoizedState!==null;if(r){Ym(e);continue}break}}n!==null?(n.return=t,J=n):Ym(e)}}function Ym(e){for(;J!==null;){var t=J;if(t===e){J=null;return}var n=t.sibling;if(n!==null){n.return=t.return,J=n;return}J=t.return}}function rE(e){for(;J!==null;){var t=J,n=t.child;if(t.tag===rt){var a=t.memoizedState!==null;if(a){qm(e);continue}}n!==null?(n.return=t,J=n):qm(e)}}function qm(e){for(;J!==null;){var t=J;Et(t);try{Kb(t)}catch(a){Ie(t,t.return,a)}if(bn(),t===e){J=null;return}var n=t.sibling;if(n!==null){n.return=t.return,J=n;return}J=t.return}}function iE(e,t,n,a){J=t,uE(t,e,n,a)}function uE(e,t,n,a){for(;J!==null;){var r=J,i=r.child;(r.subtreeFlags&Je)!==X&&i!==null?(i.return=r,J=i):lE(e,t,n,a)}}function lE(e,t,n,a){for(;J!==null;){var r=J;if((r.flags&Yt)!==X){Et(r);try{oE(t,r,n,a)}catch(u){Ie(r,r.return,u)}bn()}if(r===e){J=null;return}var i=r.sibling;if(i!==null){i.return=r.return,J=i;return}J=r.return}}function oE(e,t,n,a){switch(t.tag){case Ne:case Me:case Ae:{if(t.mode&ke){Mf();try{Mr(nn|At,t)}finally{Uf(t)}}else Mr(nn|At,t);break}}}function sE(e){J=e,cE()}function cE(){for(;J!==null;){var e=J,t=e.child;if((J.flags&Wt)!==X){var n=e.deletions;if(n!==null){for(var a=0;a<n.length;a++){var r=n[a];J=r,vE(r,e)}{var i=e.alternate;if(i!==null){var u=i.child;if(u!==null){i.child=null;do{var o=u.sibling;u.sibling=null,u=o}while(u!==null)}}}J=e}}(e.subtreeFlags&Je)!==X&&t!==null?(t.return=e,J=t):fE()}}function fE(){for(;J!==null;){var e=J;(e.flags&Yt)!==X&&(Et(e),dE(e),bn());var t=e.sibling;if(t!==null){t.return=e.return,J=t;return}J=e.return}}function dE(e){switch(e.tag){case Ne:case Me:case Ae:{e.mode&ke?(Mf(),ba(nn|At,e,e.return),Uf(e)):ba(nn|At,e,e.return);break}}}function vE(e,t){for(;J!==null;){var n=J;Et(n),mE(n,t),bn();var a=n.child;a!==null?(a.return=n,J=a):pE(e)}}function pE(e){for(;J!==null;){var t=J,n=t.sibling,a=t.return;if(Lm(t),t===e){J=null;return}if(n!==null){n.return=a,J=n;return}J=a}}function mE(e,t){switch(e.tag){case Ne:case Me:case Ae:{e.mode&ke?(Mf(),ba(nn,e,t),Uf(e)):ba(nn,e,t);break}}}function hE(e){switch(e.tag){case Ne:case Me:case Ae:{try{Mr(zt|At,e)}catch(n){Ie(e,e.return,n)}break}case ye:{var t=e.stateNode;try{t.componentDidMount()}catch(n){Ie(e,e.return,n)}break}}}function yE(e){switch(e.tag){case Ne:case Me:case Ae:{try{Mr(nn|At,e)}catch(t){Ie(e,e.return,t)}break}}}function gE(e){switch(e.tag){case Ne:case Me:case Ae:{try{ba(zt|At,e,e.return)}catch(n){Ie(e,e.return,n)}break}case ye:{var t=e.stateNode;typeof t.componentWillUnmount=="function"&&Wf(e,e.return,t);break}}}function SE(e){switch(e.tag){case Ne:case Me:case Ae:try{ba(nn|At,e,e.return)}catch(t){Ie(e,e.return,t)}}}var Bo=0,Vo=1,wo=2,Yo=3,qo=4;if(typeof Symbol=="function"&&Symbol.for){var Ju=Symbol.for;Bo=Ju("selector.component"),Vo=Ju("selector.has_pseudo_class"),wo=Ju("selector.role"),Yo=Ju("selector.test_id"),qo=Ju("selector.text")}function bE(e){return{$$typeof:Bo,value:e}}function EE(e){return{$$typeof:Vo,value:e}}function RE(e){return{$$typeof:wo,value:e}}function TE(e){return{$$typeof:qo,value:e}}function CE(e){return{$$typeof:Yo,value:e}}function nd(e){var t=ru(e);if(t!=null){if(typeof t.memoizedProps["data-testname"]!="string")throw new Error("Invalid host root specified. Should be either a React container or a node with a testname attribute.");return t}else{var n=bl(e);if(n===null)throw new Error("Could not find React container within specified host subtree.");return n.stateNode.current}}function ad(e,t){switch(t.$$typeof){case Bo:if(e.type===t.value)return!0;break;case Vo:return xE(e,t.value);case wo:if(e.tag===$){var n=e.stateNode;if(El(n,t.value))return!0}break;case qo:if(e.tag===$||e.tag===ge){var a=Ns(e);if(a!==null&&a.indexOf(t.value)>=0)return!0}break;case Yo:if(e.tag===$){var r=e.memoizedProps["data-testname"];if(typeof r=="string"&&r.toLowerCase()===t.value.toLowerCase())return!0}break;default:throw new Error("Invalid selector type specified.")}return!1}function rd(e){switch(e.$$typeof){case Bo:var t=ee(e.value)||"Unknown";return"<"+t+">";case Vo:return":has("+(rd(e)||"")+")";case wo:return'[role="'+e.value+'"]';case qo:return'"'+e.value+'"';case Yo:return'[data-testname="'+e.value+'"]';default:throw new Error("Invalid selector type specified.")}}function Qm(e,t){for(var n=[],a=[e,0],r=0;r<a.length;){var i=a[r++],u=a[r++],o=t[u];if(!(i.tag===$&&qr(i))){for(;o!=null&&ad(i,o);)u++,o=t[u];if(u===t.length)n.push(i);else for(var s=i.child;s!==null;)a.push(s,u),s=s.sibling}}return n}function xE(e,t){for(var n=[e,0],a=0;a<n.length;){var r=n[a++],i=n[a++],u=t[i];if(!(r.tag===$&&qr(r))){for(;u!=null&&ad(r,u);)i++,u=t[i];if(i===t.length)return!0;for(var o=r.child;o!==null;)n.push(o,i),o=o.sibling}}return!1}function Qo(e,t){if(!er)throw new Error("Test selector API is not supported by this renderer.");for(var n=nd(e),a=Qm(n,t),r=[],i=Array.from(a),u=0;u<i.length;){var o=i[u++];if(o.tag===$){if(qr(o))continue;r.push(o.stateNode)}else for(var s=o.child;s!==null;)i.push(s),s=s.sibling}return r}function DE(e,t){if(!er)throw new Error("Test selector API is not supported by this renderer.");for(var n=nd(e),a=0,r=[],i=[n,0],u=0;u<i.length;){var o=i[u++],s=i[u++],f=t[s];if(!(o.tag===$&&qr(o))&&(ad(o,f)&&(r.push(rd(f)),s++,s>a&&(a=s)),s<t.length))for(var m=o.child;m!==null;)i.push(m,s),m=m.sibling}if(a<t.length){for(var S=[],C=a;C<t.length;C++)S.push(rd(t[C]));return`findAllNodes was able to match part of the selector:
`+("  "+r.join(" > ")+`

`)+`No matching component was found for:
`+("  "+S.join(" > "))}return null}function _E(e,t){if(!er)throw new Error("Test selector API is not supported by this renderer.");for(var n=Qo(e,t),a=[],r=0;r<n.length;r++)a.push(Os(n[r]));for(var i=a.length-1;i>0;i--)for(var u=a[i],o=u.x,s=o+u.width,f=u.y,m=f+u.height,S=i-1;S>=0;S--)if(i!==S){var C=a[S],O=C.x,U=O+C.width,N=C.y,I=N+C.height;if(o>=O&&f>=N&&s<=U&&m<=I){a.splice(i,1);break}else if(o===O&&u.width===C.width&&!(I<f)&&!(N>m)){N>f&&(C.height+=N-f,C.y=f),I<m&&(C.height=m-N),a.splice(i,1);break}else if(f===N&&u.height===C.height&&!(U<o)&&!(O>s)){O>o&&(C.width+=O-o,C.x=o),U<s&&(C.width=s-O),a.splice(i,1);break}}return a}function OE(e,t){if(!er)throw new Error("Test selector API is not supported by this renderer.");for(var n=nd(e),a=Qm(n,t),r=Array.from(a),i=0;i<r.length;){var u=r[i++];if(!qr(u)){if(u.tag===$){var o=u.stateNode;if(vi(o))return!0}for(var s=u.child;s!==null;)r.push(s),s=s.sibling}}return!1}var Po=[];function NE(){er&&Po.forEach(function(e){return e()})}function UE(e,t,n,a){if(!er)throw new Error("Test selector API is not supported by this renderer.");var r=Qo(e,t),i=Us(r,n,a),u=i.disconnect,o=i.observe,s=i.unobserve,f=function(){var m=Qo(e,t);r.forEach(function(S){m.indexOf(S)<0&&s(S)}),m.forEach(function(S){r.indexOf(S)<0&&o(S)})};return Po.push(f),{disconnect:function(){var m=Po.indexOf(f);m>=0&&Po.splice(m,1),u()}}}var ME=M.ReactCurrentActQueue;function AE(e){{var t=typeof IS_REACT_ACT_ENVIRONMENT!="undefined"?IS_REACT_ACT_ENVIRONMENT:void 0,n=typeof jest!="undefined";return hl&&n&&t!==!1}}function Pm(){{var e=typeof IS_REACT_ACT_ENVIRONMENT!="undefined"?IS_REACT_ACT_ENVIRONMENT:void 0;return!e&&ME.current!==null&&h("The current testing environment is not configured to support act(...)"),e}}var zE=Math.ceil,id=M.ReactCurrentDispatcher,ud=M.ReactCurrentOwner,pt=M.ReactCurrentBatchConfig,Ta=M.ReactCurrentActQueue,Nt=0,ld=1,ln=2,aa=4,sr=0,Zu=1,ai=2,Go=3,ku=4,Gm=5,od=6,Ce=Nt,Tn=null,mt=null,jt=z,Ga=z,sd=Tr(z),Ft=sr,Wu=null,cd=z,Ko=z,Iu=z,Xo=z,$u=null,Mn=null,fd=0,Km=500,Xm=1/0,HE=500,cr=null;function Yi(){Xm=Kt()+HE}function Jm(){return Xm}var Jo=!1,dd=null,qi=null,ri=!1,Ar=null,el=z,vd=[],pd=null,LE=50,tl=0,md=null,hd=!1,Zo=!1,jE=50,Qi=0,ko=null,nl=ot,Wo=z,Zm=!1;function Io(){return Tn}function pn(){return(Ce&(ln|aa))!==Nt?Kt():(nl!==ot||(nl=Kt()),nl)}function zr(e){var t=e.mode;if((t&Be)===le)return fe;if((Ce&ln)!==Nt&&jt!==z)return mu(jt);var n=vS()!==dS;if(n){if(pt.transition!==null){var a=pt.transition;a._updatedFibers||(a._updatedFibers=new Set),a._updatedFibers.add(e)}return Wo===Pt&&(Wo=Cv()),Wo}var r=da();if(r!==Pt)return r;var i=yl();return i}function FE(e){var t=e.mode;return(t&Be)===le?fe:dg()}function Ut(e,t,n,a){fR(),Zm&&h("useInsertionEffect must not schedule updates."),hd&&(Zo=!0),hu(e,n,a),(Ce&ln)!==z&&e===Tn?pR(t):(va&&_v(e,t,n),mR(t),e===Tn&&((Ce&ln)===Nt&&(Iu=Ee(Iu,n)),Ft===ku&&Hr(e,jt)),An(e,a),n===fe&&Ce===Nt&&(t.mode&Be)===le&&!Ta.isBatchingLegacy&&(Yi(),Yv()))}function BE(e,t,n){var a=e.current;a.lanes=t,hu(e,t,n),An(e,n)}function VE(e){return(Ce&ln)!==Nt}function An(e,t){var n=e.callbackNode;ug(e,t);var a=Fl(e,e===Tn?jt:z);if(a===z){n!==null&&ch(n),e.callbackNode=null,e.callbackPriority=Pt;return}var r=Gr(a),i=e.callbackPriority;if(i===r&&!(Ta.current!==null&&n!==Rd)){n==null&&i!==fe&&h("Expected scheduled callback to exist. This error is likely caused by a bug in React. Please file an issue.");return}n!=null&&ch(n);var u;if(r===fe)e.tag===gi?(Ta.isBatchingLegacy!==null&&(Ta.didScheduleLegacyUpdate=!0),Zg(Im.bind(null,e))):wv(Im.bind(null,e)),Sl?Ta.current!==null?Ta.current.push(La):_s(function(){(Ce&(ln|aa))===Nt&&La()}):rs(wl,La),u=null;else{var o;switch(Mv(a)){case za:o=wl;break;case yu:o=zv;break;case gu:o=Ti;break;case yc:o=Hv;break;default:o=Ti;break}u=rs(o,km.bind(null,e))}e.callbackPriority=r,e.callbackNode=u}function km(e,t){if(JS(),nl=ot,Wo=z,(Ce&(ln|aa))!==Nt)throw new Error("Should not already be working.");var n=e.callbackNode,a=Ka();if(a&&e.callbackNode!==n)return null;var r=Fl(e,e===Tn?jt:z);if(r===z)return null;var i=!Bl(e,r)&&!fg(e,r)&&!t,u=i?$E(e,r):ts(e,r);if(u!==sr){if(u===ai){var o=dc(e);o!==z&&(r=o,u=yd(e,o))}if(u===Zu){var s=Wu;throw ii(e,z),Hr(e,r),An(e,Kt()),s}if(u===od)Hr(e,r);else{var f=!Bl(e,r),m=e.current.alternate;if(f&&!YE(m)){if(u=ts(e,r),u===ai){var S=dc(e);S!==z&&(r=S,u=yd(e,S))}if(u===Zu){var C=Wu;throw ii(e,z),Hr(e,r),An(e,Kt()),C}}e.finishedWork=m,e.finishedLanes=r,wE(e,u,r)}}return An(e,Kt()),e.callbackNode===n?km.bind(null,e):null}function yd(e,t){var n=$u;if(qv(e)){var a=ii(e,t);a.flags|=On,Xy(e.containerInfo)}var r=ts(e,t);if(r!==ai){var i=Mn;Mn=n,i!==null&&Wm(i)}return r}function Wm(e){Mn===null?Mn=e:Mn.push.apply(Mn,e)}function wE(e,t,n){switch(t){case sr:case Zu:throw new Error("Root did not complete. This is a bug in React.");case ai:{ui(e,Mn,cr);break}case Go:{if(Hr(e,n),Rv(n)&&!fh()){var a=fd+Km-Kt();if(a>10){var r=Fl(e,z);if(r!==z)break;var i=e.suspendedLanes;if(!Ri(i,n)){var u=pn();Dv(e,i);break}e.timeoutHandle=ml(ui.bind(null,e,Mn,cr),a);break}}ui(e,Mn,cr);break}case ku:{if(Hr(e,n),cg(n))break;if(!fh()){var o=rg(e,n),s=o,f=Kt()-s,m=cR(f)-f;if(m>10){e.timeoutHandle=ml(ui.bind(null,e,Mn,cr),m);break}}ui(e,Mn,cr);break}case Gm:{ui(e,Mn,cr);break}default:throw new Error("Unknown root exit status.")}}function YE(e){for(var t=e;;){if(t.flags&$a){var n=t.updateQueue;if(n!==null){var a=n.stores;if(a!==null)for(var r=0;r<a.length;r++){var i=a[r],u=i.getSnapshot,o=i.value;try{if(!qn(u(),o))return!1}catch(f){return!1}}}}var s=t.child;if(t.subtreeFlags&$a&&s!==null){s.return=t,t=s;continue}if(t===e)return!0;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}return!0}function Hr(e,t){t=Vl(t,Xo),t=Vl(t,Iu),pg(e,t)}function Im(e){if(ZS(),(Ce&(ln|aa))!==Nt)throw new Error("Should not already be working.");Ka();var t=Fl(e,z);if(!Yn(t,fe))return An(e,Kt()),null;var n=ts(e,t);if(e.tag!==gi&&n===ai){var a=dc(e);a!==z&&(t=a,n=yd(e,a))}if(n===Zu){var r=Wu;throw ii(e,z),Hr(e,t),An(e,Kt()),r}if(n===od)throw new Error("Root did not complete. This is a bug in React.");var i=e.current.alternate;return e.finishedWork=i,e.finishedLanes=t,ui(e,Mn,cr),An(e,Kt()),null}function qE(e,t){t!==z&&(hc(e,Ee(t,fe)),An(e,Kt()),(Ce&(ln|aa))===Nt&&(Yi(),La()))}function QE(e){var t=da(),n=pt.transition;try{return pt.transition=null,Gt(gu),e()}finally{Gt(t),pt.transition=n}}function PE(e,t){var n=Ce;Ce|=ld;try{return e(t)}finally{Ce=n,Ce===Nt&&!Ta.isBatchingLegacy&&(Yi(),Yv())}}function GE(e,t,n,a,r){var i=da(),u=pt.transition;try{return pt.transition=null,Gt(za),e(t,n,a,r)}finally{Gt(i),pt.transition=u,Ce===Nt&&Yi()}}function $o(e){Ar!==null&&Ar.tag===gi&&(Ce&(ln|aa))===Nt&&Ka();var t=Ce;Ce|=ld;var n=pt.transition,a=da();try{return pt.transition=null,Gt(za),e?e():void 0}finally{Gt(a),pt.transition=n,Ce=t,(Ce&(ln|aa))===Nt&&La()}}function KE(){return(Ce&(ln|aa))!==Nt}function XE(e){var t=Ce;Ce|=ld;var n=pt.transition,a=da();try{pt.transition=null,Gt(za),e()}finally{Gt(a),pt.transition=n,Ce=t,Ce===Nt&&(Yi(),La())}}function es(e,t){It(sd,Ga,e),Ga=Ee(Ga,t),cd=Ee(cd,t)}function gd(e){Ga=sd.current,dn(sd,e)}function ii(e,t){e.finishedWork=null,e.finishedLanes=z;var n=e.timeoutHandle;if(n!==Sr&&(e.timeoutHandle=Sr,Ts(n)),mt!==null)for(var a=mt.return;a!==null;){var r=a.alternate;Cm(r,a),a=a.return}Tn=e;var i=li(e.current,null);return mt=i,jt=Ga=cd=t,Ft=sr,Wu=null,Ko=z,Iu=z,Xo=z,$u=null,Mn=null,TS(),ma.discardPendingWarnings(),i}function $m(e,t){do{var n=mt;try{if(kl(),_p(),bn(),ud.current=null,n===null||n.return===null){Ft=Zu,Wu=t,mt=null;return}if(gt&&n.mode&ke&&No(n,!0),ft)if(Ci(),t!==null&&typeof t=="object"&&typeof t.then=="function"){var a=t;wg(n,a,jt)}else Vg(n,t,jt);eb(e,n.return,n,t,jt),ah(n)}catch(r){t=r,mt===n&&n!==null?(n=n.return,mt=n):n=mt;continue}return}while(!0)}function eh(){var e=id.current;return id.current=Co,e===null?Co:e}function th(e){id.current=e}function JE(){fd=Kt()}function al(e){Ko=Ee(e,Ko)}function ZE(){Ft===sr&&(Ft=Go)}function Sd(){(Ft===sr||Ft===Go||Ft===ai)&&(Ft=ku),Tn!==null&&(vc(Ko)||vc(Iu))&&Hr(Tn,jt)}function kE(e){Ft!==ku&&(Ft=ai),$u===null?$u=[e]:$u.push(e)}function WE(){return Ft===sr}function ts(e,t){var n=Ce;Ce|=ln;var a=eh();if(Tn!==e||jt!==t){if(va){var r=e.memoizedUpdaters;r.size>0&&(rl(e,jt),r.clear()),Ov(e,t)}cr=Nv(),ii(e,t)}Bv(t);do try{IE();break}catch(i){$m(e,i)}while(!0);if(kl(),Ce=n,th(a),mt!==null)throw new Error("Cannot commit an incomplete root. This error is likely caused by a bug in React. Please file an issue.");return Vv(),Tn=null,jt=z,Ft}function IE(){for(;mt!==null;)nh(mt)}function $E(e,t){var n=Ce;Ce|=ln;var a=eh();if(Tn!==e||jt!==t){if(va){var r=e.memoizedUpdaters;r.size>0&&(rl(e,jt),r.clear()),Ov(e,t)}cr=Nv(),Yi(),ii(e,t)}Bv(t);do try{eR();break}catch(i){$m(e,i)}while(!0);return kl(),th(a),Ce=n,mt!==null?(Gg(),sr):(Vv(),Tn=null,jt=z,Ft)}function eR(){for(;mt!==null&&!Eg();)nh(mt)}function nh(e){var t=e.alternate;Et(e);var n;(e.mode&ke)!==le?(Nf(e),n=bd(t,e,Ga),No(e,!0)):n=bd(t,e,Ga),bn(),e.memoizedProps=e.pendingProps,n===null?ah(e):mt=n,ud.current=null}function ah(e){var t=e;do{var n=t.alternate,a=t.return;if((t.flags&_a)===X){Et(t);var r=void 0;if((t.mode&ke)===le?r=Tm(n,t,Ga):(Nf(t),r=Tm(n,t,Ga),No(t,!1)),bn(),r!==null){mt=r;return}}else{var i=Ab(n,t);if(i!==null){i.flags&=Wi,mt=i;return}if((t.mode&ke)!==le){No(t,!1);for(var u=t.actualDuration,o=t.child;o!==null;)u+=o.actualDuration,o=o.sibling;t.actualDuration=u}if(a!==null)a.flags|=_a,a.subtreeFlags=X,a.deletions=null;else{Ft=od,mt=null;return}}var s=t.sibling;if(s!==null){mt=s;return}t=a,mt=t}while(t!==null);Ft===sr&&(Ft=Gm)}function ui(e,t,n){var a=da(),r=pt.transition;try{pt.transition=null,Gt(za),tR(e,t,n,a)}finally{pt.transition=r,Gt(a)}return null}function tR(e,t,n,a){do Ka();while(Ar!==null);if(dR(),(Ce&(ln|aa))!==Nt)throw new Error("Should not already be working.");var r=e.finishedWork,i=e.finishedLanes;if(Ag(i),r===null)return Lv(),null;if(i===z&&h("root.finishedLanes should not be empty during a commit. This is a bug in React."),e.finishedWork=null,e.finishedLanes=z,r===e.current)throw new Error("Cannot commit the same tree as before. This error is likely caused by a bug in React. Please file an issue.");e.callbackNode=null,e.callbackPriority=Pt;var u=Ee(r.lanes,r.childLanes);mg(e,u),e===Tn&&(Tn=null,mt=null,jt=z),((r.subtreeFlags&Je)!==X||(r.flags&Je)!==X)&&(ri||(ri=!0,pd=n,rs(Ti,function(){return Ka(),null})));var o=(r.subtreeFlags&(De|Se|vt|Je))!==X,s=(r.flags&(De|Se|vt|Je))!==X;if(o||s){var f=pt.transition;pt.transition=null;var m=da();Gt(za);var S=Ce;Ce|=aa,ud.current=null;var C=wb(e,r);Ip(),tE(e,r,i),eu(e.containerInfo),e.current=r,Yg(i),nE(r,e,i),qg(),Rg(),Ce=S,Gt(m),pt.transition=f}else e.current=r,Ip();var O=ri;if(ri?(ri=!1,Ar=e,el=i):(Qi=0,ko=null),u=e.pendingLanes,u===z&&(qi=null),O||lh(e.current,!1),_g(r.stateNode,a),va&&e.memoizedUpdaters.clear(),NE(),An(e,Kt()),t!==null)for(var U=e.onRecoverableError,N=0;N<t.length;N++){var I=t[N],oe=I.stack,te=I.digest;U(I.value,{componentStack:oe,digest:te})}if(Jo){Jo=!1;var Ge=dd;throw dd=null,Ge}return Yn(el,fe)&&e.tag!==gi&&Ka(),u=e.pendingLanes,Yn(u,fe)?(XS(),e===md?tl++:(tl=0,md=e)):tl=0,La(),Lv(),null}function Ka(){if(Ar!==null){var e=Mv(el),t=Sg(gu,e),n=pt.transition,a=da();try{return pt.transition=null,Gt(t),aR()}finally{Gt(a),pt.transition=n}}return!1}function nR(e){vd.push(e),ri||(ri=!0,rs(Ti,function(){return Ka(),null}))}function aR(){if(Ar===null)return!1;var e=pd;pd=null;var t=Ar,n=el;if(Ar=null,el=z,(Ce&(ln|aa))!==Nt)throw new Error("Cannot flush passive effects while already rendering.");hd=!0,Zo=!1,Qg(n);var a=Ce;Ce|=aa,sE(t.current),iE(t,t.current,n,e);{var r=vd;vd=[];for(var i=0;i<r.length;i++){var u=r[i];Pb(t,u)}}Pg(),lh(t.current,!0),Ce=a,La(),Zo?t===ko?Qi++:(Qi=0,ko=t):Qi=0,hd=!1,Zo=!1,Og(t);{var o=t.current.stateNode;o.effectDuration=0,o.passiveEffectDuration=0}return!0}function rh(e){return qi!==null&&qi.has(e)}function rR(e){qi===null?qi=new Set([e]):qi.add(e)}function iR(e){Jo||(Jo=!0,dd=e)}var uR=iR;function ih(e,t,n){var a=ti(n,t),r=em(e,a,fe),i=Dr(e,r,fe),u=pn();i!==null&&(hu(i,fe,u),An(i,u))}function Ie(e,t,n){if(Fb(n),il(!1),e.tag===se){ih(e,e,n);return}var a=null;for(a=t;a!==null;){if(a.tag===se){ih(a,e,n);return}else if(a.tag===ye){var r=a.type,i=a.stateNode;if(typeof r.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&!rh(i)){var u=ti(n,e),o=Lf(a,u,fe),s=Dr(a,o,fe),f=pn();s!==null&&(hu(s,fe,f),An(s,f));return}}a=a.return}h(`Internal React error: Attempted to capture a commit phase error inside a detached tree. This indicates a bug in React. Likely causes include deleting the same fiber more than once, committing an already-finished tree, or an inconsistent return pointer.

Error message:

%s`,n)}function lR(e,t,n){var a=e.pingCache;a!==null&&a.delete(t);var r=pn();Dv(e,n),hR(e),Tn===e&&Ri(jt,n)&&(Ft===ku||Ft===Go&&Rv(jt)&&Kt()-fd<Km?ii(e,z):Xo=Ee(Xo,n)),An(e,r)}function uh(e,t){t===Pt&&(t=FE(e));var n=pn(),a=En(e,t);a!==null&&(hu(a,t,n),An(a,n))}function oR(e){var t=e.memoizedState,n=Pt;t!==null&&(n=t.retryLane),uh(e,n)}function sR(e,t){var n=Pt,a;switch(e.tag){case Fe:a=e.stateNode;var r=e.memoizedState;r!==null&&(n=r.retryLane);break;case St:a=e.stateNode;break;default:throw new Error("Pinged unknown suspense boundary type. This is probably a bug in React.")}a!==null&&a.delete(t),uh(e,n)}function cR(e){return e<120?120:e<480?480:e<1080?1080:e<1920?1920:e<3e3?3e3:e<4320?4320:zE(e/1960)*1960}function fR(){if(tl>LE)throw tl=0,md=null,new Error("Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.");Qi>jE&&(Qi=0,ko=null,h("Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render."))}function dR(){ma.flushLegacyContextWarning(),ma.flushPendingUnsafeLifecycleWarnings()}function lh(e,t){Et(e),ns(e,be,gE),t&&ns(e,Ve,SE),ns(e,be,hE),t&&ns(e,Ve,yE),bn()}function ns(e,t,n){for(var a=e,r=null;a!==null;){var i=a.subtreeFlags&t;a!==r&&a.child!==null&&i!==X?a=a.child:((a.flags&t)!==X&&n(a),a.sibling!==null?a=a.sibling:a=r=a.return)}}var as=null;function oh(e){{if((Ce&ln)!==Nt||!(e.mode&Be))return;var t=e.tag;if(t!==yn&&t!==se&&t!==ye&&t!==Ne&&t!==Me&&t!==st&&t!==Ae)return;var n=W(e)||"ReactComponent";if(as!==null){if(as.has(n))return;as.add(n)}else as=new Set([n]);var a=Qn;try{Et(e),h("Can't perform a React state update on a component that hasn't mounted yet. This indicates that you have a side-effect in your render function that asynchronously later calls tries to update the component. Move this work to useEffect instead.")}finally{a?Et(e):bn()}}}var bd;{var vR=null;bd=function(e,t,n){var a=hh(vR,t);try{return Sm(e,t,n)}catch(i){if(aS()||i!==null&&typeof i=="object"&&typeof i.then=="function")throw i;if(kl(),_p(),Cm(e,t),hh(t,a),t.mode&ke&&Nf(t),_m(null,Sm,null,e,t,n),Lb()){var r=Om();typeof r=="object"&&r!==null&&r._suppressLogging&&typeof i=="object"&&i!==null&&!i._suppressLogging&&(i._suppressLogging=!0)}throw i}}}var sh=!1,Ed;Ed=new Set;function pR(e){if(Eu&&!PS())switch(e.tag){case Ne:case Me:case Ae:{var t=mt&&W(mt)||"Unknown",n=t;if(!Ed.has(n)){Ed.add(n);var a=W(e)||"Unknown";h("Cannot update a component (`%s`) while rendering a different component (`%s`). To locate the bad setState() call inside `%s`, follow the stack trace as described in https://reactjs.org/link/setstate-in-render",a,t,t)}break}case ye:{sh||(h("Cannot update during an existing state transition (such as within `render`). Render methods should be a pure function of props and state."),sh=!0);break}}}function rl(e,t){if(va){var n=e.memoizedUpdaters;n.forEach(function(a){_v(e,a,t)})}}var Rd={};function rs(e,t){{var n=Ta.current;return n!==null?(n.push(t),Rd):Av(e,t)}}function ch(e){if(e!==Rd)return bg(e)}function fh(){return Ta.current!==null}function mR(e){{if(e.mode&Be){if(!Pm())return}else if(!AE()||Ce!==Nt||e.tag!==Ne&&e.tag!==Me&&e.tag!==Ae)return;if(Ta.current===null){var t=Qn;try{Et(e),h(`An update to %s inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act`,W(e))}finally{t?Et(e):bn()}}}}function hR(e){e.tag!==gi&&Pm()&&Ta.current===null&&h(`A suspended resource finished loading inside a test, but the event was not wrapped in act(...).

When testing, code that resolves suspended data should be wrapped into act(...):

act(() => {
  /* finish loading suspended data */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act`)}function il(e){Zm=e}var ra=null,Pi=null,yR=function(e){ra=e};function Gi(e){{if(ra===null)return e;var t=ra(e);return t===void 0?e:t.current}}function Td(e){return Gi(e)}function Cd(e){{if(ra===null)return e;var t=ra(e);if(t===void 0){if(e!=null&&typeof e.render=="function"){var n=Gi(e.render);if(e.render!==n){var a={$$typeof:bt,render:n};return e.displayName!==void 0&&(a.displayName=e.displayName),a}}return e}return t.current}}function dh(e,t){{if(ra===null)return!1;var n=e.elementType,a=t.type,r=!1,i=typeof a=="object"&&a!==null?a.$$typeof:null;switch(e.tag){case ye:{typeof a=="function"&&(r=!0);break}case Ne:{(typeof a=="function"||i===it)&&(r=!0);break}case Me:{(i===bt||i===it)&&(r=!0);break}case st:case Ae:{(i===kn||i===it)&&(r=!0);break}default:return!1}if(r){var u=ra(n);if(u!==void 0&&u===ra(a))return!0}return!1}}function vh(e){{if(ra===null||typeof WeakSet!="function")return;Pi===null&&(Pi=new WeakSet),Pi.add(e)}}var gR=function(e,t){{if(ra===null)return;var n=t.staleFamilies,a=t.updatedFamilies;Ka(),$o(function(){xd(e.current,a,n)})}},SR=function(e,t){{if(e.context!==Vn)return;Ka(),$o(function(){Sh(t,e,null,null)})}};function xd(e,t,n){{var a=e.alternate,r=e.child,i=e.sibling,u=e.tag,o=e.type,s=null;switch(u){case Ne:case Ae:case ye:s=o;break;case Me:s=o.render;break}if(ra===null)throw new Error("Expected resolveFamily to be set during hot reload.");var f=!1,m=!1;if(s!==null){var S=ra(s);S!==void 0&&(n.has(S)?m=!0:t.has(S)&&(u===ye?m=!0:f=!0))}if(Pi!==null&&(Pi.has(e)||a!==null&&Pi.has(a))&&(m=!0),m&&(e._debugNeedsRemount=!0),m||f){var C=En(e,fe);C!==null&&Ut(C,e,fe,ot)}r!==null&&!m&&xd(r,t,n),i!==null&&xd(i,t,n)}}var bR=function(e,t){{var n=new Set,a=new Set(t.map(function(r){return r.current}));return Dd(e.current,a,n),n}};function Dd(e,t,n){{var a=e.child,r=e.sibling,i=e.tag,u=e.type,o=null;switch(i){case Ne:case Ae:case ye:o=u;break;case Me:o=u.render;break}var s=!1;o!==null&&t.has(o)&&(s=!0),s?ER(e,n):a!==null&&Dd(a,t,n),r!==null&&Dd(r,t,n)}}function ER(e,t){{var n=RR(e,t);if(n)return;for(var a=e;;){switch(a.tag){case $:t.add(a.stateNode);return;case Ue:t.add(a.stateNode.containerInfo);return;case se:t.add(a.stateNode.containerInfo);return}if(a.return===null)throw new Error("Expected to reach root first.");a=a.return}}}function RR(e,t){for(var n=e,a=!1;;){if(n.tag===$)a=!0,t.add(n.stateNode);else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)return a;for(;n.sibling===null;){if(n.return===null||n.return===e)return a;n=n.return}n.sibling.return=n.return,n=n.sibling}return!1}var _d;{_d=!1;try{var ph=Object.preventExtensions({})}catch(e){_d=!0}}function TR(e,t,n,a){this.tag=e,this.key=n,this.elementType=null,this.type=null,this.stateNode=null,this.return=null,this.child=null,this.sibling=null,this.index=0,this.ref=null,this.pendingProps=t,this.memoizedProps=null,this.updateQueue=null,this.memoizedState=null,this.dependencies=null,this.mode=a,this.flags=X,this.subtreeFlags=X,this.deletions=null,this.lanes=z,this.childLanes=z,this.alternate=null,this.actualDuration=Number.NaN,this.actualStartTime=Number.NaN,this.selfBaseDuration=Number.NaN,this.treeBaseDuration=Number.NaN,this.actualDuration=0,this.actualStartTime=-1,this.selfBaseDuration=0,this.treeBaseDuration=0,this._debugSource=null,this._debugOwner=null,this._debugNeedsRemount=!1,this._debugHookTypes=null,!_d&&typeof Object.preventExtensions=="function"&&Object.preventExtensions(this)}var Pn=function(e,t,n,a){return new TR(e,t,n,a)};function Od(e){var t=e.prototype;return!!(t&&t.isReactComponent)}function CR(e){return typeof e=="function"&&!Od(e)&&e.defaultProps===void 0}function xR(e){if(typeof e=="function")return Od(e)?ye:Ne;if(e!=null){var t=e.$$typeof;if(t===bt)return Me;if(t===kn)return st}return yn}function li(e,t){var n=e.alternate;n===null?(n=Pn(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n._debugSource=e._debugSource,n._debugOwner=e._debugOwner,n._debugHookTypes=e._debugHookTypes,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=X,n.subtreeFlags=X,n.deletions=null,n.actualDuration=0,n.actualStartTime=-1),n.flags=e.flags&Qe,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue;var a=e.dependencies;switch(n.dependencies=a===null?null:{lanes:a.lanes,firstContext:a.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.selfBaseDuration=e.selfBaseDuration,n.treeBaseDuration=e.treeBaseDuration,n._debugNeedsRemount=e._debugNeedsRemount,n.tag){case yn:case Ne:case Ae:n.type=Gi(e.type);break;case ye:n.type=Td(e.type);break;case Me:n.type=Cd(e.type);break}return n}function DR(e,t){e.flags&=Qe|qe;var n=e.alternate;if(n===null)e.childLanes=z,e.lanes=t,e.child=null,e.subtreeFlags=X,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null,e.selfBaseDuration=0,e.treeBaseDuration=0;else{e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=X,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type;var a=n.dependencies;e.dependencies=a===null?null:{lanes:a.lanes,firstContext:a.firstContext},e.selfBaseDuration=n.selfBaseDuration,e.treeBaseDuration=n.treeBaseDuration}return e}function _R(e,t,n){var a;return e===gv?(a=Be,t===!0&&(a|=_t,a|=Ma)):a=le,va&&(a|=ke),Pn(se,null,null,a)}function Nd(e,t,n,a,r,i){var u=yn,o=e;if(typeof e=="function")Od(e)?(u=ye,o=Td(o)):o=Gi(o);else if(typeof e=="string")u=$;else e:switch(e){case Zn:return Lr(n.children,r,i,t);case Hn:u=dt,r|=_t,(r&Be)!==le&&(r|=Ma);break;case Bt:return OR(n,r,i,t);case Vt:return NR(n,r,i,t);case ua:return UR(n,r,i,t);case xa:return mh(n,r,i,t);case Da:case jn:case Fn:case vr:case Ia:default:{if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Sn:u=Rt;break e;case Ln:u=ue;break e;case bt:u=Me,o=Cd(o);break e;case kn:u=st;break e;case it:u=Ct,o=null;break e}var s="";{(e===void 0||typeof e=="object"&&e!==null&&Object.keys(e).length===0)&&(s+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var f=a?W(a):null;f&&(s+=`

Check the render method of \``+f+"`.")}throw new Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) "+("but got: "+(e==null?e:typeof e)+"."+s))}}var m=Pn(u,n,t,r);return m.elementType=e,m.type=o,m.lanes=i,m._debugOwner=a,m}function Ud(e,t,n){var a=null;a=e._owner;var r=e.type,i=e.key,u=e.props,o=Nd(r,i,u,a,t,n);return o._debugSource=e._source,o._debugOwner=e._owner,o}function Lr(e,t,n,a){var r=Pn(Te,e,a,t);return r.lanes=n,r}function OR(e,t,n,a){typeof e.id!="string"&&h('Profiler must specify an "id" of type `string` as a prop. Received the type `%s` instead.',typeof e.id);var r=Pn(Tt,e,a,t|ke);return r.elementType=Bt,r.lanes=n,r.stateNode={effectDuration:0,passiveEffectDuration:0},r}function NR(e,t,n,a){var r=Pn(Fe,e,a,t);return r.elementType=Vt,r.lanes=n,r}function UR(e,t,n,a){var r=Pn(St,e,a,t);return r.elementType=ua,r.lanes=n,r}function mh(e,t,n,a){var r=Pn(rt,e,a,t);r.elementType=xa,r.lanes=n;var i={isHidden:!1};return r.stateNode=i,r}function Md(e,t,n){var a=Pn(ge,e,null,t);return a.lanes=n,a}function MR(){var e=Pn($,null,null,le);return e.elementType="DELETED",e}function AR(e){var t=Pn(Xn,null,null,le);return t.stateNode=e,t}function Ad(e,t,n){var a=e.children!==null?e.children:[],r=Pn(Ue,a,e.key,t);return r.lanes=n,r.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},r}function hh(e,t){return e===null&&(e=Pn(yn,null,null,le)),e.tag=t.tag,e.key=t.key,e.elementType=t.elementType,e.type=t.type,e.stateNode=t.stateNode,e.return=t.return,e.child=t.child,e.sibling=t.sibling,e.index=t.index,e.ref=t.ref,e.pendingProps=t.pendingProps,e.memoizedProps=t.memoizedProps,e.updateQueue=t.updateQueue,e.memoizedState=t.memoizedState,e.dependencies=t.dependencies,e.mode=t.mode,e.flags=t.flags,e.subtreeFlags=t.subtreeFlags,e.deletions=t.deletions,e.lanes=t.lanes,e.childLanes=t.childLanes,e.alternate=t.alternate,e.actualDuration=t.actualDuration,e.actualStartTime=t.actualStartTime,e.selfBaseDuration=t.selfBaseDuration,e.treeBaseDuration=t.treeBaseDuration,e._debugSource=t._debugSource,e._debugOwner=t._debugOwner,e._debugNeedsRemount=t._debugNeedsRemount,e._debugHookTypes=t._debugHookTypes,e}function zR(e,t,n,a,r){this.tag=t,this.containerInfo=e,this.pendingChildren=null,this.current=null,this.pingCache=null,this.finishedWork=null,this.timeoutHandle=Sr,this.context=null,this.pendingContext=null,this.callbackNode=null,this.callbackPriority=Pt,this.eventTimes=mc(z),this.expirationTimes=mc(ot),this.pendingLanes=z,this.suspendedLanes=z,this.pingedLanes=z,this.expiredLanes=z,this.mutableReadLanes=z,this.finishedLanes=z,this.entangledLanes=z,this.entanglements=mc(z),this.identifierPrefix=a,this.onRecoverableError=r,Mt&&(this.mutableSourceEagerHydrationData=null),this.effectDuration=0,this.passiveEffectDuration=0;{this.memoizedUpdaters=new Set;for(var i=this.pendingUpdatersLaneMap=[],u=0;u<Ks;u++)i.push(new Set)}switch(t){case gv:this._debugRootType=n?"hydrateRoot()":"createRoot()";break;case gi:this._debugRootType=n?"hydrate()":"render()";break}}function yh(e,t,n,a,r,i,u,o,s,f){var m=new zR(e,t,n,o,s),S=_R(t,i);m.current=S,S.stateNode=m;{var C={element:a,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null};S.memoizedState=C}return Lc(S),m}var HR="18.2.0";function LR(e,t,n){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null;return SS(a),{$$typeof:_n,key:a==null?null:""+a,children:e,containerInfo:t,implementation:n}}var zd,Hd;zd=!1,Hd={};function gh(e){if(!e)return Vn;var t=he(e),n=$y(t);if(t.tag===ye){var a=t.type;if(Ua(a))return hv(t,a,n)}return n}function jR(e){var t=he(e);if(t===void 0){if(typeof e.render=="function")throw new Error("Unable to find node on an unmounted component.");var n=Object.keys(e).join(",");throw new Error("Argument appears to not be a ReactComponent. Keys: "+n)}var a=yr(t);return a===null?null:a.stateNode}function FR(e,t){{var n=he(e);if(n===void 0){if(typeof e.render=="function")throw new Error("Unable to find node on an unmounted component.");var a=Object.keys(e).join(",");throw new Error("Argument appears to not be a ReactComponent. Keys: "+a)}var r=yr(n);if(r===null)return null;if(r.mode&_t){var i=W(n)||"Component";if(!Hd[i]){Hd[i]=!0;var u=Qn;try{Et(r),n.mode&_t?h("%s is deprecated in StrictMode. %s was passed an instance of %s which is inside StrictMode. Instead, add a ref directly to the element you want to reference. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-find-node",t,t,i):h("%s is deprecated in StrictMode. %s was passed an instance of %s which renders StrictMode children. Instead, add a ref directly to the element you want to reference. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-find-node",t,t,i)}finally{u?Et(u):bn()}}}return r.stateNode}}function BR(e,t,n,a,r,i,u,o){var s=!1,f=null;return yh(e,t,s,f,n,a,r,i,u)}function VR(e,t,n,a,r,i,u,o,s,f){var m=!0,S=yh(n,a,m,e,r,i,u,o,s);S.context=gh(null);var C=S.current,O=pn(),U=zr(C),N=lr(O,U);return N.callback=t!=null?t:null,Dr(C,N,U),BE(S,U,O),S}function Sh(e,t,n,a){Dg(t,e);var r=t.current,i=pn(),u=zr(r);Kg(u);var o=gh(n);t.context===null?t.context=o:t.pendingContext=o,Eu&&Qn!==null&&!zd&&(zd=!0,h(`Render methods should be a pure function of props and state; triggering nested component updates from render is not allowed. If necessary, trigger nested updates in componentDidUpdate.

Check the render method of %s.`,W(Qn)||"Unknown"));var s=lr(i,u);s.payload={element:e},a=a===void 0?null:a,a!==null&&(typeof a!="function"&&h("render(...): Expected the last optional `callback` argument to be a function. Instead received: %s.",a),s.callback=a);var f=Dr(r,s,u);return f!==null&&(Ut(f,r,u,i),to(f,r,u)),u}function wR(e){var t=e.current;if(!t.child)return null;switch(t.child.tag){case $:return ci(t.child.stateNode);default:return t.child.stateNode}}function YR(e){switch(e.tag){case se:{var t=e.stateNode;if(qv(t)){var n=lg(t);qE(t,n)}break}case Fe:{$o(function(){var r=En(e,fe);if(r!==null){var i=pn();Ut(r,e,fe,i)}});var a=fe;is(e,a);break}}}function bh(e,t){var n=e.memoizedState;n!==null&&n.dehydrated!==null&&(n.retryLane=vg(n.retryLane,t))}function is(e,t){bh(e,t);var n=e.alternate;n&&bh(n,t)}function qR(e){if(e.tag===Fe){var t=fe,n=En(e,t);if(n!==null){var a=pn();Ut(n,e,t,a)}is(e,t)}}function QR(e){if(e.tag===Fe){var t=du,n=En(e,t);if(n!==null){var a=pn();Ut(n,e,t,a)}is(e,t)}}function PR(e){if(e.tag===Fe){var t=zr(e),n=En(e,t);if(n!==null){var a=pn();Ut(n,e,t,a)}is(e,t)}}function GR(e){var t=dl(e);return t===null?null:t.stateNode}var Eh=function(e){return null};function Rh(e){return Eh(e)}var Th=function(e){return!1};function Ch(e){return Th(e)}var xh=null,Dh=null,_h=null,Oh=null,Nh=null,Uh=null,Mh=null,Ah=null,zh=null;{var Hh=function(e,t,n){var a=t[n],r=fn(e)?e.slice():Q({},e);return n+1===t.length?(fn(r)?r.splice(a,1):delete r[a],r):(r[a]=Hh(e[a],t,n+1),r)},Lh=function(e,t){return Hh(e,t,0)},jh=function(e,t,n,a){var r=t[a],i=fn(e)?e.slice():Q({},e);if(a+1===t.length){var u=n[a];i[u]=i[r],fn(i)?i.splice(r,1):delete i[r]}else i[r]=jh(e[r],t,n,a+1);return i},Fh=function(e,t,n){if(t.length!==n.length){A("copyWithRename() expects paths of the same length");return}else for(var a=0;a<n.length-1;a++)if(t[a]!==n[a]){A("copyWithRename() expects paths to be the same except for the deepest key");return}return jh(e,t,n,0)},Bh=function(e,t,n,a){if(n>=t.length)return a;var r=t[n],i=fn(e)?e.slice():Q({},e);return i[r]=Bh(e[r],t,n+1,a),i},Vh=function(e,t,n){return Bh(e,t,0,n)},Ld=function(e,t){for(var n=e.memoizedState;n!==null&&t>0;)n=n.next,t--;return n};xh=function(e,t,n,a){var r=Ld(e,t);if(r!==null){var i=Vh(r.memoizedState,n,a);r.memoizedState=i,r.baseState=i,e.memoizedProps=Q({},e.memoizedProps);var u=En(e,fe);u!==null&&Ut(u,e,fe,ot)}},Dh=function(e,t,n){var a=Ld(e,t);if(a!==null){var r=Lh(a.memoizedState,n);a.memoizedState=r,a.baseState=r,e.memoizedProps=Q({},e.memoizedProps);var i=En(e,fe);i!==null&&Ut(i,e,fe,ot)}},_h=function(e,t,n,a){var r=Ld(e,t);if(r!==null){var i=Fh(r.memoizedState,n,a);r.memoizedState=i,r.baseState=i,e.memoizedProps=Q({},e.memoizedProps);var u=En(e,fe);u!==null&&Ut(u,e,fe,ot)}},Oh=function(e,t,n){e.pendingProps=Vh(e.memoizedProps,t,n),e.alternate&&(e.alternate.pendingProps=e.pendingProps);var a=En(e,fe);a!==null&&Ut(a,e,fe,ot)},Nh=function(e,t){e.pendingProps=Lh(e.memoizedProps,t),e.alternate&&(e.alternate.pendingProps=e.pendingProps);var n=En(e,fe);n!==null&&Ut(n,e,fe,ot)},Uh=function(e,t,n){e.pendingProps=Fh(e.memoizedProps,t,n),e.alternate&&(e.alternate.pendingProps=e.pendingProps);var a=En(e,fe);a!==null&&Ut(a,e,fe,ot)},Mh=function(e){var t=En(e,fe);t!==null&&Ut(t,e,fe,ot)},Ah=function(e){Eh=e},zh=function(e){Th=e}}function KR(e){var t=yr(e);return t===null?null:t.stateNode}function XR(e){return null}function JR(){return Qn}function ZR(e){var t=e.findFiberByHostInstance,n=M.ReactCurrentDispatcher;return xg({bundleType:e.bundleType,version:e.version,rendererPackageName:e.rendererPackageName,rendererConfig:e.rendererConfig,overrideHookState:xh,overrideHookStateDeletePath:Dh,overrideHookStateRenamePath:_h,overrideProps:Oh,overridePropsDeletePath:Nh,overridePropsRenamePath:Uh,setErrorHandler:Ah,setSuspenseHandler:zh,scheduleUpdate:Mh,currentDispatcherRef:n,findHostInstanceByFiber:KR,findFiberByHostInstance:t||XR,findHostInstancesForRefresh:bR,scheduleRefresh:gR,scheduleRoot:SR,setRefreshHandler:yR,getCurrentFiber:JR,reconcilerVersion:HR})}return y.attemptContinuousHydration=QR,y.attemptDiscreteHydration=qR,y.attemptHydrationAtCurrentPriority=PR,y.attemptSynchronousHydration=YR,y.batchedUpdates=PE,y.createComponentSelector=bE,y.createContainer=BR,y.createHasPseudoClassSelector=EE,y.createHydrationContainer=VR,y.createPortal=LR,y.createRoleSelector=RE,y.createTestNameSelector=CE,y.createTextSelector=TE,y.deferredUpdates=QE,y.discreteUpdates=GE,y.findAllNodes=Qo,y.findBoundingRects=_E,y.findHostInstance=jR,y.findHostInstanceWithNoPortals=GR,y.findHostInstanceWithWarning=FR,y.flushControlled=XE,y.flushPassiveEffects=Ka,y.flushSync=$o,y.focusWithin=OE,y.getCurrentUpdatePriority=da,y.getFindAllNodesFailureDescription=DE,y.getPublicRootInstance=wR,y.injectIntoDevTools=ZR,y.isAlreadyRendering=KE,y.observeVisibleRects=UE,y.registerMutableSourceForHydration=BS,y.runWithPriority=yg,y.shouldError=Rh,y.shouldSuspend=Ch,y.updateContainer=Sh,y}});var ly=jr((fC,uy)=>{"use strict";uy.exports=iy()});var vy=jr(hs=>{"use strict";(function(){"use strict";var v=Za(),c=Symbol.for("react.element"),y=Symbol.for("react.portal"),T=Symbol.for("react.fragment"),g=Symbol.for("react.strict_mode"),M=Symbol.for("react.profiler"),q=Symbol.for("react.provider"),B=Symbol.for("react.context"),A=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),F=Symbol.for("react.suspense_list"),Q=Symbol.for("react.memo"),he=Symbol.for("react.lazy"),at=Symbol.for("react.offscreen"),Ke=Symbol.iterator,Ye="@@iterator";function Xe(d){if(d===null||typeof d!="object")return null;var H=Ke&&d[Ke]||d[Ye];return typeof H=="function"?H:null}var Re=v.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function xe(d){{for(var H=arguments.length,V=new Array(H>1?H-1:0),re=1;re<H;re++)V[re-1]=arguments[re];ft("error",d,V)}}function ft(d,H,V){{var re=Re.ReactDebugCurrentFrame,be=re.getStackAddendum();be!==""&&(H+="%s",V=V.concat([be]));var Ve=V.map(function(De){return String(De)});Ve.unshift("Warning: "+H),Function.prototype.apply.call(console[d],console,Ve)}}var gt=!1,zn=!1,Ne=!1,ye=!1,yn=!1,se;se=Symbol.for("react.module.reference");function Ue(d){return!!(typeof d=="string"||typeof d=="function"||d===T||d===M||yn||d===g||d===h||d===F||ye||d===at||gt||zn||Ne||typeof d=="object"&&d!==null&&(d.$$typeof===he||d.$$typeof===Q||d.$$typeof===q||d.$$typeof===B||d.$$typeof===A||d.$$typeof===se||d.getModuleId!==void 0))}function $(d,H,V){var re=d.displayName;if(re)return re;var be=H.displayName||H.name||"";return be!==""?V+"("+be+")":V}function ge(d){return d.displayName||"Context"}function Te(d){if(d==null)return null;if(typeof d.tag=="number"&&xe("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof d=="function")return d.displayName||d.name||null;if(typeof d=="string")return d;switch(d){case T:return"Fragment";case y:return"Portal";case M:return"Profiler";case g:return"StrictMode";case h:return"Suspense";case F:return"SuspenseList"}if(typeof d=="object")switch(d.$$typeof){case B:var H=d;return ge(H)+".Consumer";case q:var V=d;return ge(V._context)+".Provider";case A:return $(d,d.render,"ForwardRef");case Q:var re=d.displayName||null;return re!==null?re:Te(d.type)||"Memo";case he:{var be=d,Ve=be._payload,De=be._init;try{return Te(De(Ve))}catch(Se){return null}}}return null}var dt=Object.assign,ue=0,Rt,Me,Tt,Fe,st,Ae,Ct;function Zt(){}Zt.__reactDisabledLog=!0;function Xn(){{if(ue===0){Rt=console.log,Me=console.info,Tt=console.warn,Fe=console.error,st=console.group,Ae=console.groupCollapsed,Ct=console.groupEnd;var d={configurable:!0,enumerable:!0,value:Zt,writable:!0};Object.defineProperties(console,{info:d,log:d,warn:d,error:d,group:d,groupCollapsed:d,groupEnd:d})}ue++}}function St(){{if(ue--,ue===0){var d={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:dt({},d,{value:Rt}),info:dt({},d,{value:Me}),warn:dt({},d,{value:Tt}),error:dt({},d,{value:Fe}),group:dt({},d,{value:st}),groupCollapsed:dt({},d,{value:Ae}),groupEnd:dt({},d,{value:Ct})})}ue<0&&xe("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var gn=Re.ReactCurrentDispatcher,rt;function kt(d,H,V){{if(rt===void 0)try{throw Error()}catch(be){var re=be.stack.trim().match(/\n( *(at )?)/);rt=re&&re[1]||""}return`
`+rt+d}}var ia=!1,Jn;{var Dn=typeof WeakMap=="function"?WeakMap:Map;Jn=new Dn}function _n(d,H){if(!d||ia)return"";{var V=Jn.get(d);if(V!==void 0)return V}var re;ia=!0;var be=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var Ve;Ve=gn.current,gn.current=null,Xn();try{if(H){var De=function(){throw Error()};if(Object.defineProperty(De.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(De,[])}catch(qt){re=qt}Reflect.construct(d,[],De)}else{try{De.call()}catch(qt){re=qt}d.call(De.prototype)}}else{try{throw Error()}catch(qt){re=qt}d()}}catch(qt){if(qt&&re&&typeof qt.stack=="string"){for(var Se=qt.stack.split(`
`),vt=re.stack.split(`
`),Je=Se.length-1,Qe=vt.length-1;Je>=1&&Qe>=0&&Se[Je]!==vt[Qe];)Qe--;for(;Je>=1&&Qe>=0;Je--,Qe--)if(Se[Je]!==vt[Qe]){if(Je!==1||Qe!==1)do if(Je--,Qe--,Qe<0||Se[Je]!==vt[Qe]){var cn=`
`+Se[Je].replace(" at new "," at ");return d.displayName&&cn.includes("<anonymous>")&&(cn=cn.replace("<anonymous>",d.displayName)),typeof d=="function"&&Jn.set(d,cn),cn}while(Je>=1&&Qe>=0);break}}}finally{ia=!1,gn.current=Ve,St(),Error.prepareStackTrace=be}var Bn=d?d.displayName||d.name:"",ca=Bn?kt(Bn):"";return typeof d=="function"&&Jn.set(d,ca),ca}function Zn(d,H,V){return _n(d,!1)}function Hn(d){var H=d.prototype;return!!(H&&H.isReactComponent)}function Bt(d,H,V){if(d==null)return"";if(typeof d=="function")return _n(d,Hn(d));if(typeof d=="string")return kt(d);switch(d){case h:return kt("Suspense");case F:return kt("SuspenseList")}if(typeof d=="object")switch(d.$$typeof){case A:return Zn(d.render);case Q:return Bt(d.type,H,V);case he:{var re=d,be=re._payload,Ve=re._init;try{return Bt(Ve(be),H,V)}catch(De){}}}return""}var Sn=Object.prototype.hasOwnProperty,Ln={},bt=Re.ReactDebugCurrentFrame;function Vt(d){if(d){var H=d._owner,V=Bt(d.type,d._source,H?H.type:null);bt.setExtraStackFrame(V)}else bt.setExtraStackFrame(null)}function ua(d,H,V,re,be){{var Ve=Function.call.bind(Sn);for(var De in d)if(Ve(d,De)){var Se=void 0;try{if(typeof d[De]!="function"){var vt=Error((re||"React class")+": "+V+" type `"+De+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof d[De]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw vt.name="Invariant Violation",vt}Se=d[De](H,De,re,V,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(Je){Se=Je}Se&&!(Se instanceof Error)&&(Vt(be),xe("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",re||"React class",V,De,typeof Se),Vt(null)),Se instanceof Error&&!(Se.message in Ln)&&(Ln[Se.message]=!0,Vt(be),xe("Failed %s type: %s",V,Se.message),Vt(null))}}}var kn=Array.isArray;function it(d){return kn(d)}function jn(d){{var H=typeof Symbol=="function"&&Symbol.toStringTag,V=H&&d[Symbol.toStringTag]||d.constructor.name||"Object";return V}}function Ia(d){try{return xa(d),!1}catch(H){return!0}}function xa(d){return""+d}function Da(d){if(Ia(d))return xe("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",jn(d)),xa(d)}var Fn=Re.ReactCurrentOwner,vr={key:!0,ref:!0,__self:!0,__source:!0},la,pr,w;w={};function ne(d){if(Sn.call(d,"ref")){var H=Object.getOwnPropertyDescriptor(d,"ref").get;if(H&&H.isReactWarning)return!1}return d.ref!==void 0}function pe(d){if(Sn.call(d,"key")){var H=Object.getOwnPropertyDescriptor(d,"key").get;if(H&&H.isReactWarning)return!1}return d.key!==void 0}function ee(d,H){if(typeof d.ref=="string"&&Fn.current&&H&&Fn.current.stateNode!==H){var V=Te(Fn.current.type);w[V]||(xe('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',Te(Fn.current.type),d.ref),w[V]=!0)}}function ut(d,H){{var V=function(){la||(la=!0,xe("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",H))};V.isReactWarning=!0,Object.defineProperty(d,"key",{get:V,configurable:!0})}}function wt(d,H){{var V=function(){pr||(pr=!0,xe("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",H))};V.isReactWarning=!0,Object.defineProperty(d,"ref",{get:V,configurable:!0})}}var W=function(d,H,V,re,be,Ve,De){var Se={$$typeof:c,type:d,key:H,ref:V,props:De,_owner:Ve};return Se._store={},Object.defineProperty(Se._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(Se,"_self",{configurable:!1,enumerable:!1,writable:!1,value:re}),Object.defineProperty(Se,"_source",{configurable:!1,enumerable:!1,writable:!1,value:be}),Object.freeze&&(Object.freeze(Se.props),Object.freeze(Se)),Se};function X(d,H,V,re,be){{var Ve,De={},Se=null,vt=null;V!==void 0&&(Da(V),Se=""+V),pe(H)&&(Da(H.key),Se=""+H.key),ne(H)&&(vt=H.ref,ee(H,be));for(Ve in H)Sn.call(H,Ve)&&!vr.hasOwnProperty(Ve)&&(De[Ve]=H[Ve]);if(d&&d.defaultProps){var Je=d.defaultProps;for(Ve in Je)De[Ve]===void 0&&(De[Ve]=Je[Ve])}if(Se||vt){var Qe=typeof d=="function"?d.displayName||d.name||"Unknown":d;Se&&ut(De,Qe),vt&&wt(De,Qe)}return W(d,Se,vt,be,re,Fn.current,De)}}var $e=Re.ReactCurrentOwner,qe=Re.ReactDebugCurrentFrame;function ce(d){if(d){var H=d._owner,V=Bt(d.type,d._source,H?H.type:null);qe.setExtraStackFrame(V)}else qe.setExtraStackFrame(null)}var Wt;Wt=!1;function oa(d){return typeof d=="object"&&d!==null&&d.$$typeof===c}function mr(){{if($e.current){var d=Te($e.current.type);if(d)return`

Check the render method of \``+d+"`."}return""}}function ze(d){{if(d!==void 0){var H=d.fileName.replace(/^.*[\\\/]/,""),V=d.lineNumber;return`

Check your code at `+H+":"+V+"."}return""}}var On={};function Wn(d){{var H=mr();if(!H){var V=typeof d=="string"?d:d.displayName||d.name;V&&(H=`

Check the top-level render call using <`+V+">.")}return H}}function sn(d,H){{if(!d._store||d._store.validated||d.key!=null)return;d._store.validated=!0;var V=Wn(H);if(On[V])return;On[V]=!0;var re="";d&&d._owner&&d._owner!==$e.current&&(re=" It was passed a child from "+Te(d._owner.type)+"."),ce(d),xe('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',V,re),ce(null)}}function Yt(d,H){{if(typeof d!="object")return;if(it(d))for(var V=0;V<d.length;V++){var re=d[V];oa(re)&&sn(re,H)}else if(oa(d))d._store&&(d._store.validated=!0);else if(d){var be=Xe(d);if(typeof be=="function"&&be!==d.entries)for(var Ve=be.call(d),De;!(De=Ve.next()).done;)oa(De.value)&&sn(De.value,H)}}}function Nn(d){{var H=d.type;if(H==null||typeof H=="string")return;var V;if(typeof H=="function")V=H.propTypes;else if(typeof H=="object"&&(H.$$typeof===A||H.$$typeof===Q))V=H.propTypes;else return;if(V){var re=Te(H);ua(V,d.props,"prop",re,d)}else if(H.PropTypes!==void 0&&!Wt){Wt=!0;var be=Te(H);xe("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",be||"Unknown")}typeof H.getDefaultProps=="function"&&!H.getDefaultProps.isReactClassApproved&&xe("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function sa(d){{for(var H=Object.keys(d.props),V=0;V<H.length;V++){var re=H[V];if(re!=="children"&&re!=="key"){ce(d),xe("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",re),ce(null);break}}d.ref!==null&&(ce(d),xe("Invalid attribute `ref` supplied to `React.Fragment`."),ce(null))}}var $a={};function si(d,H,V,re,be,Ve){{var De=Ue(d);if(!De){var Se="";(d===void 0||typeof d=="object"&&d!==null&&Object.keys(d).length===0)&&(Se+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var vt=ze(be);vt?Se+=vt:Se+=mr();var Je;d===null?Je="null":it(d)?Je="array":d!==void 0&&d.$$typeof===c?(Je="<"+(Te(d.type)||"Unknown")+" />",Se=" Did you accidentally export a JSX literal instead of a component?"):Je=typeof d,xe("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",Je,Se)}var Qe=X(d,H,V,be,Ve);if(Qe==null)return Qe;if(De){var cn=H.children;if(cn!==void 0)if(re)if(it(cn)){for(var Bn=0;Bn<cn.length;Bn++)Yt(cn[Bn],d);Object.freeze&&Object.freeze(cn)}else xe("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else Yt(cn,d)}if(Sn.call(H,"key")){var ca=Te(d),qt=Object.keys(H).filter(function(yr){return yr!=="key"}),hr=qt.length>0?"{key: someKey, "+qt.join(": ..., ")+": ...}":"{key: someKey}";if(!$a[ca+hr]){var Oa=qt.length>0?"{"+qt.join(": ..., ")+": ...}":"{}";xe(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,hr,ca,Oa,ca),$a[ca+hr]=!0}}return d===T?sa(Qe):Nn(Qe),Qe}}function Wi(d,H,V){return si(d,H,V,!0)}function _a(d,H,V){return si(d,H,V,!1)}var xt=_a,Vr=Wi;hs.Fragment=T,hs.jsx=xt,hs.jsxs=Vr})()});var ki=jr((hC,py)=>{"use strict";py.exports=vy()});var oi=Kn(Za());function Xi(v){if(v==null||typeof v!="object")return!1;let c=Object.getPrototypeOf(v);return c==null||c===Object.prototype}function Ca(v){return v!=null&&v.kind===3}var ll="__current",Bd={},lT=[];function qd(v,{strict:c=!0,components:y}={}){let T=0,g={strict:c,mounted:!1,channel:v,children:lT,nodes:new WeakSet,parents:new WeakMap,tops:new WeakMap,components:new WeakMap,fragments:new WeakMap};c&&Object.freeze(y);let M={kind:0,options:c?Object.freeze({strict:c,components:y}):{strict:c,components:y},get children(){return g.children},createComponent(q,...B){if(y&&y.indexOf(q)<0)throw new Error(`Unsupported component: ${q}`);let[A,h,...F]=B,Q=A!=null?A:{},he=[],at={};if(A)for(let Re of Object.keys(A))Re!=="children"&&(at[Re]=Br(Wh(A[Re])));if(h)if(Array.isArray(h))for(let Re of h)he.push(Jt(Re,M));else{he.push(Jt(h,M));for(let Re of F)he.push(Jt(Re,M))}let Ke=`${T++}`,Ye={externalProps:c?Object.freeze(Q):Q,internalProps:at,children:c?Object.freeze(he):he},Xe=Ja({kind:1,get children(){return Ye.children},get props(){return Ye.externalProps},get remoteProps(){return Ye.internalProps},remove:()=>Kh(Xe),updateProps:Re=>cT(Xe,Re,Ye,g),append:(...Re)=>cs(Xe,Re.map(xe=>Jt(xe,M)),Ye,g),appendChild:Re=>fs(Xe,Jt(Re,M),Ye,g),removeChild:Re=>ds(Xe,Re,Ye,g),replaceChildren:(...Re)=>Vd(Xe,Re.map(xe=>Jt(xe,M)),Ye,g),insertBefore:(Re,xe)=>Ji(Xe,Jt(Re,M),xe,Ye,g),insertChildBefore:(Re,xe)=>Ji(Xe,Jt(Re,M),xe,Ye,g)},Bd);g.components.set(Xe,Ye),Object.defineProperty(Xe,"type",{value:q,configurable:!1,writable:!1,enumerable:!0}),wd(Xe,g),Yd(Xe,Ke,M);for(let Re of Ye.children)sl(Xe,Re,g);return Xe},createText(q=""){let B=`${T++}`,A={text:q},h=Q=>sT(F,Q,A,g),F=Ja({kind:2,get text(){return A.text},update:h,updateText:h,remove:()=>Kh(F)},Bd);return wd(F,g),Yd(F,B,M),F},createFragment(){let q=`${T++}`,B={children:c?Object.freeze([]):[]},A=Ja({kind:3,get children(){return B.children},append:(...h)=>cs(A,h.map(F=>Jt(F,M)),B,g),appendChild:h=>fs(A,Jt(h,M),B,g),removeChild:h=>ds(A,h,B,g),replaceChildren:(...h)=>Vd(A,h.map(F=>Jt(F,M)),B,g),insertBefore:(h,F)=>Ji(A,Jt(h,M),F,B,g),insertChildBefore:(h,F)=>Ji(A,Jt(h,M),F,B,g)},Bd);return g.fragments.set(A,B),wd(A,g),Yd(A,q,M),A},append:(...q)=>cs(M,q.map(B=>Jt(B,M)),g,g),appendChild:q=>fs(M,Jt(q,M),g,g),replaceChildren:(...q)=>Vd(M,q.map(B=>Jt(B,M)),g,g),removeChild:q=>ds(M,q,g,g),insertBefore:(q,B)=>Ji(M,Jt(q,M),B,g,g),insertChildBefore:(q,B)=>Ji(M,Jt(q,M),B,g,g),mount(){return g.mounted?Promise.resolve():(g.mounted=!0,Promise.resolve(v(0,g.children.map(cl))))}};return M}function oT(v,{tops:c}){var y;return((y=c.get(v))===null||y===void 0?void 0:y.kind)===0}function kh(v,c){let y=T=>{if("children"in T)for(let g of T.children)c(g),y(g)};y(v)}function ol(v,c,{remote:y,local:T}){let{mounted:g,channel:M}=c;g&&(v.kind===0||oT(v,c))&&y(M),T()}function sT(v,c,y,T){return ol(v,T,{remote:g=>g(3,v.id,c),local:()=>{y.text=c}})}var Fr=Symbol("ignore");function cT(v,c,y,T){let{strict:g}=T,{internalProps:M,externalProps:q}=y,B={},A=[],h=!1;for(let F of Object.keys(c)){if(F==="children")continue;let Q=q[F],he=c[F],at=M[F],Ke=Wh(he);if(at===Ke&&(Ke==null||typeof Ke!="object"))continue;let[Ye,Xe]=Qd(at,Ke);Xe&&A.push(...Xe),Ye!==Fr&&(h=!0,B[F]=Ye,Ca(Q)&&Pd(Q,T),Ca(he)&&sl(v,he,T))}return ol(v,T,{remote:F=>{h&&F(4,v.id,B)},local:()=>{let F=Ja(Ja({},q),c);y.externalProps=g?Object.freeze(F):F,y.internalProps=Ja(Ja({},y.internalProps),B);for(let[Q,he]of A)Q[ll]=he}})}function Qd(v,c,y=new Set){return y.has(v)?[Fr]:typeof v=="function"&&ll in v?(y.add(v),[typeof c=="function"?Fr:Br(c),[[v,c]]]):Array.isArray(v)?(y.add(v),vT(v,c,y)):Xi(v)&&!Ca(v)?(y.add(v),dT(v,c,y)):[v===c?Fr:c]}function Br(v,c=new Map){let y=c.get(v);if(y)return y;if(Ca(v))return c.set(v,v),v;if(Array.isArray(v)){let T=[];c.set(v,T);for(let g of v)T.push(Br(g,c));return T}if(Xi(v)){let T={};c.set(v,T);for(let g of Object.keys(v))T[g]=Br(v[g],c);return T}if(typeof v=="function"){let T=(...g)=>T[ll](...g);return Object.defineProperty(T,ll,{enumerable:!1,configurable:!1,writable:!0,value:v}),c.set(v,T),T}return v}function Zi(v,c=new Set){if(!c.has(v)){if(c.add(v),Array.isArray(v))return v.reduce((y,T)=>{let g=Zi(T,c);return g?[...y,...g]:y},[]);if(Xi(v))return Object.keys(v).reduce((y,T)=>{let g=Zi(v[T],c);return g?[...y,...g]:y},[]);if(typeof v=="function")return ll in v?[v]:void 0}}function Kh(v){var c;(c=v.parent)===null||c===void 0||c.removeChild(v)}function cs(v,c,y,T){for(let g of c)fs(v,g,y,T)}function fs(v,c,y,T){var g;let{nodes:M,strict:q}=T;if(!M.has(c))throw new Error("Cannot append a node that was not created by this remote root");let B=c.parent,A=(g=B==null?void 0:B.children.indexOf(c))!==null&&g!==void 0?g:-1;return ol(v,T,{remote:h=>{h(1,v.id,A<0?v.children.length:v.children.length-1,cl(c),B?B.id:!1)},local:()=>{sl(v,c,T);let h;if(B){let F=Ih(B,T),Q=[...F.children];Q.splice(A,1),B===v?h=Q:(F.children=q?Object.freeze(Q):Q,h=[...y.children])}else h=[...y.children];h.push(c),y.children=q?Object.freeze(h):h}})}function Vd(v,c,y,T){for(let g of v.children)ds(v,g,y,T);cs(v,c,y,T)}function ds(v,c,y,T){let{strict:g}=T,M=v.children.indexOf(c);if(M!==-1)return ol(v,T,{remote:q=>q(2,v.id,M),local:()=>{Pd(c,T);let q=[...y.children];q.splice(q.indexOf(c),1),y.children=g?Object.freeze(q):q}})}function Ji(v,c,y,T,g){var M;let{strict:q,nodes:B}=g;if(!B.has(c))throw new Error("Cannot insert a node that was not created by this remote root");let A=c.parent,h=(M=A==null?void 0:A.children.indexOf(c))!==null&&M!==void 0?M:-1;return ol(v,g,{remote:F=>{let Q=y==null?v.children.length-1:v.children.indexOf(y);F(1,v.id,Q<h||h<0?Q:Q-1,cl(c),A?A.id:!1)},local:()=>{sl(v,c,g);let F;if(A){let Q=Ih(A,g),he=[...Q.children];he.splice(h,1),A===v?F=he:(Q.children=q?Object.freeze(he):he,F=[...T.children])}else F=[...T.children];y==null?F.push(c):F.splice(F.indexOf(y),0,c),T.children=q?Object.freeze(F):F}})}function Jt(v,c){return typeof v=="string"?c.createText(v):v}function sl(v,c,y){let{tops:T,parents:g}=y,M=v.kind===0?v:T.get(v);T.set(c,M),g.set(c,v),Xh(c,y),kh(c,q=>{T.set(q,M),Xh(q,y)})}function Xh(v,c){if(v.kind!==1)return;let y=v.props;y&&Object.values(y).forEach(T=>{Ca(T)&&sl(v,T,c)})}function Pd(v,c){let{tops:y,parents:T}=c;y.delete(v),T.delete(v),kh(v,g=>{y.delete(g),Jh(g,c)}),Jh(v,c)}function Jh(v,c){if(v.kind!==1)return;let y=v.remoteProps;for(let T of Object.keys(y!=null?y:{})){let g=y[T];Ca(g)&&Pd(g,c)}}function wd(v,{parents:c,tops:y,nodes:T}){T.add(v),Object.defineProperty(v,"parent",{get(){return c.get(v)},configurable:!0,enumerable:!0}),Object.defineProperty(v,"top",{get(){return y.get(v)},configurable:!0,enumerable:!0})}function cl(v){return v.kind===2?{id:v.id,kind:v.kind,text:v.text}:{id:v.id,kind:v.kind,type:v.type,props:v.remoteProps,children:v.children.map(c=>cl(c))}}function Wh(v){return Ca(v)?fT(v):v}function fT(v){return{id:v.id,kind:v.kind,get children(){return v.children.map(c=>cl(c))}}}function Ih(v,c){return v.kind===0?c:v.kind===3?c.fragments.get(v):c.components.get(v)}function Yd(v,c,y){Object.defineProperty(v,"id",{value:c,configurable:!0,writable:!1,enumerable:!1}),Object.defineProperty(v,"root",{value:y,configurable:!0,writable:!1,enumerable:!1})}function dT(v,c,y){if(!Xi(c)){var T;return[Br(c),(T=Zi(v))===null||T===void 0?void 0:T.map(B=>[B,void 0])]}let g=!1,M=[],q={};for(let B in v){let A=v[B];if(!(B in c)){g=!0;let he=Zi(A);he&&M.push(...he.map(at=>[at,void 0]))}let h=c[B],[F,Q]=Qd(A,h,y);Q&&M.push(...Q),F!==Fr&&(g=!0,q[B]=F)}for(let B in c)B in q||(g=!0,q[B]=Br(c[B]));return[g?q:Fr,M]}function vT(v,c,y){if(!Array.isArray(c)){var T;return[Br(c),(T=Zi(v))===null||T===void 0?void 0:T.map(F=>[F,void 0])]}let g=!1,M=[],q=c.length,B=v.length,A=Math.max(B,q),h=[];for(let F=0;F<A;F++){let Q=v[F],he=c[F];if(F<q){if(F>=B){g=!0,h[F]=Br(he);continue}let[at,Ke]=Qd(Q,he,y);if(Ke&&M.push(...Ke),at===Fr){h[F]=Q;continue}g=!0,h[F]=at}else{g=!0;let at=Zi(Q);at&&M.push(...at.map(Ke=>[Ke,void 0]))}}return[g?h:Fr,M]}function $h(){return(c,y)=>{var T;function g(...M){return os(this,null,function*(){if(M.length===1)return y(...M);let[{channel:q,components:B},A]=M,h=qd(q,{components:B,strict:!0}),F=y(h,A);return typeof F=="object"&&F!=null&&"then"in F&&(F=yield F),h.mount(),F})}return(T=globalThis.shopify)===null||T===void 0||T.extend(c,g),g}}var Gd=$h();var Kd="AdminAction";var Xd="BlockStack";var Jd="Button";var Zd="Text";var ey=Kn(Za(),1),ps=(0,ey.createContext)(null);var hy=Kn(Za(),1);var cy=Kn(ly(),1);var fy=v=>{var c;return(0,cy.default)({now:Date.now,scheduleTimeout:setTimeout,cancelTimeout:clearTimeout,noTimeout:!1,supportsMicrotasks:!0,scheduleMicrotask:oy,queueMicrotask:oy,isPrimaryRenderer:(c=v==null?void 0:v.primary)!==null&&c!==void 0?c:!0,supportsMutation:!0,supportsHydration:!1,supportsPersistence:!1,getRootHostContext(){return{}},getChildHostContext(y){return y},createTextInstance(y,T){return T.createText(y)},createInstance(y,T,g){let B=T,{children:M}=B,q=ls(B,["children"]);return g.createComponent(y,q)},commitTextUpdate(y,T,g){y.update(g)},prepareUpdate(y,T,g,M){let q={},B=!1;for(let A in g)!sy(g,A)||A==="children"||(A in M?g[A]!==M[A]&&(B=!0,q[A]=M[A]):(B=!0,q[A]=void 0));for(let A in M)!sy(M,A)||A==="children"||A in g||(B=!0,q[A]=M[A]);return B?q:null},commitUpdate(y,T){y.updateProps(T)},appendChildToContainer(y,T){y.append(T)},insertInContainerBefore(y,T,g){y.insertBefore(T,g)},removeChildFromContainer(y,T){y.removeChild(T)},clearContainer(y){for(let T of y.children)y.removeChild(T)},appendInitialChild(y,T){y.append(T)},appendChild(y,T){y.append(T)},insertBefore(y,T,g){y.insertBefore(T,g)},removeChild(y,T){y.removeChild(T)},finalizeInitialChildren(){return!1},shouldSetTextContent(){return!1},getPublicInstance(){},prepareForCommit(){return null},resetAfterCommit(){},commitMount(){},preparePortalMount(){},detachDeletedInstance(){}})};function oy(v){return typeof queueMicrotask=="function"?queueMicrotask:Promise.resolve(null).then(v).catch(pT)}function pT(v){setTimeout(()=>{throw v})}var{hasOwnProperty:mT}={};function sy(v,c){return mT.call(v,c)}var dy=Kn(Za(),1),ms=(0,dy.createContext)(null);var yy=Kn(ki(),1),ys=new WeakMap,my=0,hT=fy();function Wd(v){return{render(c){kd(c,v)},unmount(){ys.has(v)&&(kd(null,v),ys.delete(v))}}}function kd(v,c,y,T=hT){let g=ys.get(c);if(!g){var M;let h={container:Number(((M=hy.version.split("."))===null||M===void 0?void 0:M[0])||18)>=18?T.createContainer(c,my,null,!1,null,"r-ui",()=>null,null):T.createContainer(c,my,!1,null),renderContext:{root:c,reconciler:T}};ys.set(c,h),g=h}let{container:q,renderContext:B}=g;T.updateContainer(v&&(0,yy.jsx)(ms.Provider,{value:B,children:v}),q,null,y)}var ka=Kn(Za(),1);var by=Kn(ki(),1);var gy=Kn(Za(),1);function Sy(){let v=(0,gy.useContext)(ms);if(v==null)throw new Error("No remote-ui Render instance found in context");return v}function dr(v,{fragmentProps:c}={}){if(!c||!c.length)return v;let y=yT(v,c);return y.displayName=v,y}function yT(v,c){let y=v;return(0,ka.memo)(function(q){var B=q,{children:g=[]}=B,M=ls(B,["children"]);let A=(0,ka.useRef)({}),{root:h,reconciler:F}=Sy(),{props:Q,children:he}=(0,ka.useMemo)(()=>{let at=[],Ke={};for(let Ye of Object.keys(M)){let Xe=M[Ye];if(c.includes(Ye)&&(0,ka.isValidElement)(Xe)){let Re=A.current[Ye],xe=Ca(Re)?Re:h.createFragment();A.current[Ye]=xe,Object.assign(xe,{createText(...gt){return h.createText(...gt)},createComponent(gt,...zn){return h.createComponent(gt,...zn)}});let ft=F.createPortal(Xe,xe,null,null);at.push(ft),Ke[Ye]=xe}else Ke[Ye]=Xe,delete A.current[Ye]}return{props:Ke,children:[...ka.Children.toArray(g),...at]}},[g,M,h,F,A]);return(0,by.jsx)(y,qh(Ja({},Q),{children:he}))})}var Ey=(v,c)=>new Promise((y,T)=>{try{let g=Wd(c);g.render(v),y(()=>g.unmount())}catch(g){console.error(g),T(g)}});var Ry=Kn(ki(),1);function gs(v,c){return Gd(v,(y,T)=>os(null,null,function*(){if(!v.match(/\.render$/))throw new Error(`reactExtension can only be used for .render extension targets, got: ${v}`);let g=yield c(T);return Ey((0,Ry.jsx)(ps.Provider,{value:T,children:g}),y)}))}var Id=dr(Kd,{fragmentProps:["primaryAction","secondaryAction"]});var $d=dr(Xd);var Ss=dr(Jd);var fl=dr(Zd);var Ty=Kn(Za(),1);var bs=class extends Error{constructor(...c){super(...c),this.name="AdminUIExtensionError"}};function ev(v){let c=(0,Ty.useContext)(ps);if(c==null)throw new bs("No extension api found.");return c}var Wa=Kn(ki()),Cy="admin.order-details.action.render",lx=gs(Cy,()=>(0,Wa.jsx)(gT,{}));function gT(){let{close:v,data:c}=ev(Cy),[y,T]=(0,oi.useState)(""),[g,M]=(0,oi.useState)(""),[q,B]=(0,oi.useState)(!1);return(0,oi.useEffect)(()=>{var A,h;if((h=(A=c==null?void 0:c.selected)==null?void 0:A[0])!=null&&h.id){let Q=c.selected[0].id.split("/").pop();T(Q)}},[c]),(0,oi.useEffect)(()=>{y&&(B(!0),fetch("/resources/order-details",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderId:y})}).then(A=>A.json()).then(A=>{var h;if((h=A.order)!=null&&h.customer){let{first_name:F,last_name:Q}=A.order.customer;M(`${F} ${Q}`),console.log(A)}else M("Customer info not found")}).catch(A=>{console.error("Fetch error:",A),M("Error fetching order")}).finally(()=>B(!1)))},[y]),(0,Wa.jsx)(Id,{primaryAction:(0,Wa.jsx)(Ss,{loading:q,disabled:!y,children:"Move to App"}),secondaryAction:(0,Wa.jsx)(Ss,{onPress:v,children:"Cancel"}),children:(0,Wa.jsxs)($d,{spacing:"tight",children:[(0,Wa.jsx)(fl,{fontWeight:"bold",children:"Order Details"}),(0,Wa.jsxs)(fl,{children:["Order ID: ",y||"Loading..."]}),(0,Wa.jsxs)(fl,{children:["Customer Name: ",g||"Loading..."]})]})})}})();
//# sourceMappingURL=logistic-action.js.map
