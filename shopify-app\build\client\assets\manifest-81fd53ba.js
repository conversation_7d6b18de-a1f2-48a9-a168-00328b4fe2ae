window.__remixManifest={"entry":{"module":"/assets/entry.client-BMUJfrQ-.js","imports":["/assets/jsx-runtime-DlxonYWr.js","/assets/index-y-Yv8VR0.js","/assets/components-7DQbiWt_.js"],"css":[]},"routes":{"root":{"id":"root","path":"","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/root-DsEoCCF4.js","imports":["/assets/jsx-runtime-DlxonYWr.js","/assets/index-y-Yv8VR0.js","/assets/components-7DQbiWt_.js"],"css":[]},"routes/resources.bulk-order-details":{"id":"routes/resources.bulk-order-details","parentId":"root","path":"resources/bulk-order-details","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/resources.bulk-order-details-l0sNRNKZ.js","imports":[],"css":[]},"routes/webhooks.app.scopes_update":{"id":"routes/webhooks.app.scopes_update","parentId":"root","path":"webhooks/app/scopes_update","hasAction":true,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/webhooks.app.scopes_update-l0sNRNKZ.js","imports":[],"css":[]},"routes/webhooks.app.uninstalled":{"id":"routes/webhooks.app.uninstalled","parentId":"root","path":"webhooks/app/uninstalled","hasAction":true,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/webhooks.app.uninstalled-l0sNRNKZ.js","imports":[],"css":[]},"routes/resources.order-details":{"id":"routes/resources.order-details","parentId":"root","path":"resources/order-details","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/resources.order-details-l0sNRNKZ.js","imports":[],"css":[]},"routes/api.save-token":{"id":"routes/api.save-token","parentId":"root","path":"api/save-token","hasAction":true,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/api.save-token-l0sNRNKZ.js","imports":[],"css":[]},"routes/api.booking":{"id":"routes/api.booking","parentId":"root","path":"api/booking","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/api.booking-l0sNRNKZ.js","imports":[],"css":[]},"routes/api.orders":{"id":"routes/api.orders","parentId":"root","path":"api/orders","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/api.orders-l0sNRNKZ.js","imports":[],"css":[]},"routes/auth.login":{"id":"routes/auth.login","parentId":"root","path":"auth/login","hasAction":true,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/route-Tm1Dupvw.js","imports":["/assets/jsx-runtime-DlxonYWr.js","/assets/index-y-Yv8VR0.js","/assets/styles-B33d-VxP.js","/assets/components-7DQbiWt_.js","/assets/Page-CD-2c-Tv.js","/assets/context-QCCGjp8E.js","/assets/context-C0av8SGa.js"],"css":[]},"routes/auth.$":{"id":"routes/auth.$","parentId":"root","path":"auth/*","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/auth._-l0sNRNKZ.js","imports":[],"css":[]},"routes/_index":{"id":"routes/_index","parentId":"root","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/route-DbFXt4-c.js","imports":["/assets/jsx-runtime-DlxonYWr.js","/assets/components-7DQbiWt_.js","/assets/index-y-Yv8VR0.js"],"css":["/assets/route-Cnm7FvdT.css"]},"routes/app":{"id":"routes/app","parentId":"root","path":"app","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":true,"module":"/assets/app-BY82hW--.js","imports":["/assets/jsx-runtime-DlxonYWr.js","/assets/index-y-Yv8VR0.js","/assets/components-7DQbiWt_.js","/assets/styles-B33d-VxP.js","/assets/context-QCCGjp8E.js","/assets/context-C0av8SGa.js"],"css":[]},"routes/app.additional":{"id":"routes/app.additional","parentId":"routes/app","path":"additional","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/app.additional-DScXaeyM.js","imports":["/assets/jsx-runtime-DlxonYWr.js","/assets/Page-CD-2c-Tv.js","/assets/Layout-DyjgeZaH.js","/assets/index-y-Yv8VR0.js","/assets/context-QCCGjp8E.js"],"css":[]},"routes/app.analytics":{"id":"routes/app.analytics","parentId":"routes/app","path":"analytics","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/app.analytics-Ifjm8sAL.js","imports":["/assets/jsx-runtime-DlxonYWr.js"],"css":[]},"routes/app.settings":{"id":"routes/app.settings","parentId":"routes/app","path":"settings","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/app.settings-BLc2cfeF.js","imports":["/assets/jsx-runtime-DlxonYWr.js","/assets/components-7DQbiWt_.js","/assets/index-y-Yv8VR0.js"],"css":[]},"routes/app.tracking":{"id":"routes/app.tracking","parentId":"routes/app","path":"tracking","hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/app.tracking-BpwBPRtM.js","imports":["/assets/jsx-runtime-DlxonYWr.js","/assets/components-7DQbiWt_.js","/assets/index-y-Yv8VR0.js"],"css":[]},"routes/app.orders":{"id":"routes/app.orders","parentId":"routes/app","path":"orders","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/app.orders-uQIK5qVY.js","imports":["/assets/jsx-runtime-DlxonYWr.js"],"css":[]},"routes/app.riders":{"id":"routes/app.riders","parentId":"routes/app","path":"riders","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/app.riders-F5WKdmvx.js","imports":["/assets/jsx-runtime-DlxonYWr.js"],"css":[]},"routes/app._index":{"id":"routes/app._index","parentId":"routes/app","index":true,"hasAction":false,"hasLoader":true,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/app._index-Dm9g_uIE.js","imports":["/assets/app._index-Czrwxyor.js","/assets/jsx-runtime-DlxonYWr.js","/assets/index-y-Yv8VR0.js","/assets/components-7DQbiWt_.js","/assets/Page-CD-2c-Tv.js","/assets/context-QCCGjp8E.js","/assets/Layout-DyjgeZaH.js","/assets/context-C0av8SGa.js"],"css":[]},"routes/app.setup":{"id":"routes/app.setup","parentId":"routes/app","path":"setup","hasAction":true,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasErrorBoundary":false,"module":"/assets/app.setup-l0sNRNKZ.js","imports":[],"css":[]}},"url":"/assets/manifest-81fd53ba.js","version":"81fd53ba"};