var Sa=Object.defineProperty;var xa=(e,t,r)=>t in e?Sa(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var Lt=(e,t,r)=>xa(e,typeof t!="symbol"?t+"":t,r);import{r as d,b as La,c as Pa}from"./index-y-Yv8VR0.js";/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function K(){return K=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},K.apply(this,arguments)}var Z;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Z||(Z={}));const Vr="popstate";function zl(e){e===void 0&&(e={});function t(n,a){let{pathname:i,search:o,hash:s}=n.location;return ut("",{pathname:i,search:o,hash:s},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(n,a){return typeof a=="string"?a:Te(a)}return Ca(t,r,null,e)}function I(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function q(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Da(){return Math.random().toString(36).substr(2,8)}function Yr(e,t){return{usr:e.state,key:e.key,idx:t}}function ut(e,t,r,n){return r===void 0&&(r=null),K({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Se(t):t,{state:r,key:t&&t.key||n||Da()})}function Te(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function Se(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function Ca(e,t,r,n){n===void 0&&(n={});let{window:a=document.defaultView,v5Compat:i=!1}=n,o=a.history,s=Z.Pop,l=null,c=h();c==null&&(c=0,o.replaceState(K({},o.state,{idx:c}),""));function h(){return(o.state||{idx:null}).idx}function u(){s=Z.Pop;let R=h(),P=R==null?null:R-c;c=R,l&&l({action:s,location:E.location,delta:P})}function m(R,P){s=Z.Push;let b=ut(E.location,R,P);c=h()+1;let D=Yr(b,c),x=E.createHref(b);try{o.pushState(D,"",x)}catch(F){if(F instanceof DOMException&&F.name==="DataCloneError")throw F;a.location.assign(x)}i&&l&&l({action:s,location:E.location,delta:1})}function g(R,P){s=Z.Replace;let b=ut(E.location,R,P);c=h();let D=Yr(b,c),x=E.createHref(b);o.replaceState(D,"",x),i&&l&&l({action:s,location:E.location,delta:0})}function y(R){let P=a.location.origin!=="null"?a.location.origin:a.location.href,b=typeof R=="string"?R:Te(R);return b=b.replace(/ $/,"%20"),I(P,"No window.location.(origin|href) available to create URL for href: "+b),new URL(b,P)}let E={get action(){return s},get location(){return e(a,o)},listen(R){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener(Vr,u),l=R,()=>{a.removeEventListener(Vr,u),l=null}},createHref(R){return t(a,R)},createURL:y,encodeLocation(R){let P=y(R);return{pathname:P.pathname,search:P.search,hash:P.hash}},push:m,replace:g,go(R){return o.go(R)}};return E}var V;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(V||(V={}));const _a=new Set(["lazy","caseSensitive","path","id","index","children"]);function Ta(e){return e.index===!0}function jt(e,t,r,n){return r===void 0&&(r=[]),n===void 0&&(n={}),e.map((a,i)=>{let o=[...r,String(i)],s=typeof a.id=="string"?a.id:o.join("-");if(I(a.index!==!0||!a.children,"Cannot specify children on an index route"),I(!n[s],'Found a route id collision on id "'+s+`".  Route id's must be globally unique within Data Router usages`),Ta(a)){let l=K({},a,t(a),{id:s});return n[s]=l,l}else{let l=K({},a,t(a),{id:s,children:void 0});return n[s]=l,a.children&&(l.children=jt(a.children,t,o,n)),l}})}function be(e,t,r){return r===void 0&&(r="/"),Mt(e,t,r,!1)}function Mt(e,t,r,n){let a=typeof t=="string"?Se(t):t,i=ce(a.pathname||"/",r);if(i==null)return null;let o=bn(e);Oa(o);let s=null;for(let l=0;s==null&&l<o.length;++l){let c=Ba(i);s=$a(o[l],c,n)}return s}function En(e,t){let{route:r,pathname:n,params:a}=e;return{id:r.id,pathname:n,params:a,data:t[r.id],handle:r.handle}}function bn(e,t,r,n){t===void 0&&(t=[]),r===void 0&&(r=[]),n===void 0&&(n="");let a=(i,o,s)=>{let l={relativePath:s===void 0?i.path||"":s,caseSensitive:i.caseSensitive===!0,childrenIndex:o,route:i};l.relativePath.startsWith("/")&&(I(l.relativePath.startsWith(n),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(n.length));let c=ve([n,l.relativePath]),h=r.concat(l);i.children&&i.children.length>0&&(I(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),bn(i.children,t,h,c)),!(i.path==null&&!i.index)&&t.push({path:c,score:Ia(c,i.index),routesMeta:h})};return e.forEach((i,o)=>{var s;if(i.path===""||!((s=i.path)!=null&&s.includes("?")))a(i,o);else for(let l of Rn(i.path))a(i,o,l)}),t}function Rn(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,a=r.endsWith("?"),i=r.replace(/\?$/,"");if(n.length===0)return a?[i,""]:[i];let o=Rn(n.join("/")),s=[];return s.push(...o.map(l=>l===""?i:[i,l].join("/"))),a&&s.push(...o),s.map(l=>e.startsWith("/")&&l===""?"/":l)}function Oa(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Ua(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const Ma=/^:[\w-]+$/,Na=3,Fa=2,Aa=1,ka=10,ja=-2,Jr=e=>e==="*";function Ia(e,t){let r=e.split("/"),n=r.length;return r.some(Jr)&&(n+=ja),t&&(n+=Fa),r.filter(a=>!Jr(a)).reduce((a,i)=>a+(Ma.test(i)?Na:i===""?Aa:ka),n)}function Ua(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function $a(e,t,r){r===void 0&&(r=!1);let{routesMeta:n}=e,a={},i="/",o=[];for(let s=0;s<n.length;++s){let l=n[s],c=s===n.length-1,h=i==="/"?t:t.slice(i.length)||"/",u=It({path:l.relativePath,caseSensitive:l.caseSensitive,end:c},h),m=l.route;if(!u&&c&&r&&!n[n.length-1].route.index&&(u=It({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},h)),!u)return null;Object.assign(a,u.params),o.push({params:a,pathname:ve([i,u.pathname]),pathnameBase:Va(ve([i,u.pathnameBase])),route:m}),u.pathnameBase!=="/"&&(i=ve([i,u.pathnameBase]))}return o}function It(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=Ha(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let i=a[0],o=i.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:n.reduce((c,h,u)=>{let{paramName:m,isOptional:g}=h;if(m==="*"){let E=s[u]||"";o=i.slice(0,i.length-E.length).replace(/(.)\/+$/,"$1")}const y=s[u];return g&&!y?c[m]=void 0:c[m]=(y||"").replace(/%2F/g,"/"),c},{}),pathname:i,pathnameBase:o,pattern:e}}function Ha(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),q(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,s,l)=>(n.push({paramName:s,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function Ba(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return q(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function ce(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function za(e,t){t===void 0&&(t="/");let{pathname:r,search:n="",hash:a=""}=typeof e=="string"?Se(e):e;return{pathname:r?r.startsWith("/")?r:Wa(r,t):t,search:Ya(n),hash:Ja(a)}}function Wa(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function Qt(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Sn(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function dr(e,t){let r=Sn(e);return t?r.map((n,a)=>a===r.length-1?n.pathname:n.pathnameBase):r.map(n=>n.pathnameBase)}function fr(e,t,r,n){n===void 0&&(n=!1);let a;typeof e=="string"?a=Se(e):(a=K({},e),I(!a.pathname||!a.pathname.includes("?"),Qt("?","pathname","search",a)),I(!a.pathname||!a.pathname.includes("#"),Qt("#","pathname","hash",a)),I(!a.search||!a.search.includes("#"),Qt("#","search","hash",a)));let i=e===""||a.pathname==="",o=i?"/":a.pathname,s;if(o==null)s=r;else{let u=t.length-1;if(!n&&o.startsWith("..")){let m=o.split("/");for(;m[0]==="..";)m.shift(),u-=1;a.pathname=m.join("/")}s=u>=0?t[u]:"/"}let l=za(a,s),c=o&&o!=="/"&&o.endsWith("/"),h=(i||o===".")&&r.endsWith("/");return!l.pathname.endsWith("/")&&(c||h)&&(l.pathname+="/"),l}const ve=e=>e.join("/").replace(/\/\/+/g,"/"),Va=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ya=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ja=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;class Ka{constructor(t,r){this.type="DataWithResponseInit",this.data=t,this.init=r||null}}function Xa(e,t){return new Ka(e,typeof t=="number"?{status:t}:t)}class Ut extends Error{}class Ga{constructor(t,r){this.pendingKeysSet=new Set,this.subscribers=new Set,this.deferredKeys=[],I(t&&typeof t=="object"&&!Array.isArray(t),"defer() only accepts plain objects");let n;this.abortPromise=new Promise((i,o)=>n=o),this.controller=new AbortController;let a=()=>n(new Ut("Deferred data aborted"));this.unlistenAbortSignal=()=>this.controller.signal.removeEventListener("abort",a),this.controller.signal.addEventListener("abort",a),this.data=Object.entries(t).reduce((i,o)=>{let[s,l]=o;return Object.assign(i,{[s]:this.trackPromise(s,l)})},{}),this.done&&this.unlistenAbortSignal(),this.init=r}trackPromise(t,r){if(!(r instanceof Promise))return r;this.deferredKeys.push(t),this.pendingKeysSet.add(t);let n=Promise.race([r,this.abortPromise]).then(a=>this.onSettle(n,t,void 0,a),a=>this.onSettle(n,t,a));return n.catch(()=>{}),Object.defineProperty(n,"_tracked",{get:()=>!0}),n}onSettle(t,r,n,a){if(this.controller.signal.aborted&&n instanceof Ut)return this.unlistenAbortSignal(),Object.defineProperty(t,"_error",{get:()=>n}),Promise.reject(n);if(this.pendingKeysSet.delete(r),this.done&&this.unlistenAbortSignal(),n===void 0&&a===void 0){let i=new Error('Deferred data for key "'+r+'" resolved/rejected with `undefined`, you must resolve/reject with a value or `null`.');return Object.defineProperty(t,"_error",{get:()=>i}),this.emit(!1,r),Promise.reject(i)}return a===void 0?(Object.defineProperty(t,"_error",{get:()=>n}),this.emit(!1,r),Promise.reject(n)):(Object.defineProperty(t,"_data",{get:()=>a}),this.emit(!1,r),a)}emit(t,r){this.subscribers.forEach(n=>n(t,r))}subscribe(t){return this.subscribers.add(t),()=>this.subscribers.delete(t)}cancel(){this.controller.abort(),this.pendingKeysSet.forEach((t,r)=>this.pendingKeysSet.delete(r)),this.emit(!0)}async resolveData(t){let r=!1;if(!this.done){let n=()=>this.cancel();t.addEventListener("abort",n),r=await new Promise(a=>{this.subscribe(i=>{t.removeEventListener("abort",n),(i||this.done)&&a(i)})})}return r}get done(){return this.pendingKeysSet.size===0}get unwrappedData(){return I(this.data!==null&&this.done,"Can only unwrap data on initialized and settled deferreds"),Object.entries(this.data).reduce((t,r)=>{let[n,a]=r;return Object.assign(t,{[n]:Za(a)})},{})}get pendingKeys(){return Array.from(this.pendingKeysSet)}}function Qa(e){return e instanceof Promise&&e._tracked===!0}function Za(e){if(!Qa(e))return e;if(e._error)throw e._error;return e._data}const xn=function(t,r){r===void 0&&(r=302);let n=r;typeof n=="number"?n={status:n}:typeof n.status>"u"&&(n.status=302);let a=new Headers(n.headers);return a.set("Location",t),new Response(null,K({},n,{headers:a}))};class Ie{constructor(t,r,n,a){a===void 0&&(a=!1),this.status=t,this.statusText=r||"",this.internal=a,n instanceof Error?(this.data=n.toString(),this.error=n):this.data=n}}function Ue(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Ln=["post","put","patch","delete"],qa=new Set(Ln),ei=["get",...Ln],ti=new Set(ei),ri=new Set([301,302,303,307,308]),ni=new Set([307,308]),Zt={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},ai={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},at={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},hr=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ii=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),Pn="remix-router-transitions";function Wl(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u",n=!r;I(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let a;if(e.mapRouteProperties)a=e.mapRouteProperties;else if(e.detectErrorBoundary){let f=e.detectErrorBoundary;a=p=>({hasErrorBoundary:f(p)})}else a=ii;let i={},o=jt(e.routes,a,void 0,i),s,l=e.basename||"/",c=e.dataStrategy||ui,h=e.patchRoutesOnNavigation,u=K({v7_fetcherPersist:!1,v7_normalizeFormMethod:!1,v7_partialHydration:!1,v7_prependBasename:!1,v7_relativeSplatPath:!1,v7_skipActionErrorRevalidation:!1},e.future),m=null,g=new Set,y=null,E=null,R=null,P=e.hydrationData!=null,b=be(o,e.history.location,l),D=!1,x=null;if(b==null&&!h){let f=le(404,{pathname:e.history.location.pathname}),{matches:p,route:w}=an(o);b=p,x={[w.id]:f}}b&&!e.hydrationData&&bt(b,o,e.history.location.pathname).active&&(b=null);let F;if(b)if(b.some(f=>f.route.lazy))F=!1;else if(!b.some(f=>f.route.loader))F=!0;else if(u.v7_partialHydration){let f=e.hydrationData?e.hydrationData.loaderData:null,p=e.hydrationData?e.hydrationData.errors:null;if(p){let w=b.findIndex(S=>p[S.route.id]!==void 0);F=b.slice(0,w+1).every(S=>!ir(S.route,f,p))}else F=b.every(w=>!ir(w.route,f,p))}else F=e.hydrationData!=null;else if(F=!1,b=[],u.v7_partialHydration){let f=bt(null,o,e.history.location.pathname);f.active&&f.matches&&(D=!0,b=f.matches)}let _,v={historyAction:e.history.action,location:e.history.location,matches:b,initialized:F,navigation:Zt,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||x,fetchers:new Map,blockers:new Map},C=Z.Pop,$=!1,j,Y=!1,W=new Map,ie=null,ae=!1,te=!1,Le=[],vt=new Set,re=new Map,yt=0,Ze=-1,Be=new Map,ge=new Set,ze=new Map,qe=new Map,fe=new Set,Oe=new Map,Me=new Map,gt;function oa(){if(m=e.history.listen(f=>{let{action:p,location:w,delta:S}=f;if(gt){gt(),gt=void 0;return}q(Me.size===0||S!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let L=Hr({currentLocation:v.location,nextLocation:w,historyAction:p});if(L&&S!=null){let A=new Promise(k=>{gt=k});e.history.go(S*-1),Et(L,{state:"blocked",location:w,proceed(){Et(L,{state:"proceeding",proceed:void 0,reset:void 0,location:w}),A.then(()=>e.history.go(S))},reset(){let k=new Map(v.blockers);k.set(L,at),oe({blockers:k})}});return}return Ne(p,w)}),r){Si(t,W);let f=()=>xi(t,W);t.addEventListener("pagehide",f),ie=()=>t.removeEventListener("pagehide",f)}return v.initialized||Ne(Z.Pop,v.location,{initialHydration:!0}),_}function la(){m&&m(),ie&&ie(),g.clear(),j&&j.abort(),v.fetchers.forEach((f,p)=>wt(p)),v.blockers.forEach((f,p)=>$r(p))}function sa(f){return g.add(f),()=>g.delete(f)}function oe(f,p){p===void 0&&(p={}),v=K({},v,f);let w=[],S=[];u.v7_fetcherPersist&&v.fetchers.forEach((L,A)=>{L.state==="idle"&&(fe.has(A)?S.push(A):w.push(A))}),fe.forEach(L=>{!v.fetchers.has(L)&&!re.has(L)&&S.push(L)}),[...g].forEach(L=>L(v,{deletedFetchers:S,viewTransitionOpts:p.viewTransitionOpts,flushSync:p.flushSync===!0})),u.v7_fetcherPersist?(w.forEach(L=>v.fetchers.delete(L)),S.forEach(L=>wt(L))):S.forEach(L=>fe.delete(L))}function We(f,p,w){var S,L;let{flushSync:A}=w===void 0?{}:w,k=v.actionData!=null&&v.navigation.formMethod!=null&&he(v.navigation.formMethod)&&v.navigation.state==="loading"&&((S=f.state)==null?void 0:S._isRedirect)!==!0,O;p.actionData?Object.keys(p.actionData).length>0?O=p.actionData:O=null:k?O=v.actionData:O=null;let M=p.loaderData?rn(v.loaderData,p.loaderData,p.matches||[],p.errors):v.loaderData,T=v.blockers;T.size>0&&(T=new Map(T),T.forEach((H,ne)=>T.set(ne,at)));let N=$===!0||v.navigation.formMethod!=null&&he(v.navigation.formMethod)&&((L=f.state)==null?void 0:L._isRedirect)!==!0;s&&(o=s,s=void 0),ae||C===Z.Pop||(C===Z.Push?e.history.push(f,f.state):C===Z.Replace&&e.history.replace(f,f.state));let U;if(C===Z.Pop){let H=W.get(v.location.pathname);H&&H.has(f.pathname)?U={currentLocation:v.location,nextLocation:f}:W.has(f.pathname)&&(U={currentLocation:f,nextLocation:v.location})}else if(Y){let H=W.get(v.location.pathname);H?H.add(f.pathname):(H=new Set([f.pathname]),W.set(v.location.pathname,H)),U={currentLocation:v.location,nextLocation:f}}oe(K({},p,{actionData:O,loaderData:M,historyAction:C,location:f,initialized:!0,navigation:Zt,revalidation:"idle",restoreScrollPosition:zr(f,p.matches||v.matches),preventScrollReset:N,blockers:T}),{viewTransitionOpts:U,flushSync:A===!0}),C=Z.Pop,$=!1,Y=!1,ae=!1,te=!1,Le=[]}async function Nr(f,p){if(typeof f=="number"){e.history.go(f);return}let w=ar(v.location,v.matches,l,u.v7_prependBasename,f,u.v7_relativeSplatPath,p==null?void 0:p.fromRouteId,p==null?void 0:p.relative),{path:S,submission:L,error:A}=Kr(u.v7_normalizeFormMethod,!1,w,p),k=v.location,O=ut(v.location,S,p&&p.state);O=K({},O,e.history.encodeLocation(O));let M=p&&p.replace!=null?p.replace:void 0,T=Z.Push;M===!0?T=Z.Replace:M===!1||L!=null&&he(L.formMethod)&&L.formAction===v.location.pathname+v.location.search&&(T=Z.Replace);let N=p&&"preventScrollReset"in p?p.preventScrollReset===!0:void 0,U=(p&&p.flushSync)===!0,H=Hr({currentLocation:k,nextLocation:O,historyAction:T});if(H){Et(H,{state:"blocked",location:O,proceed(){Et(H,{state:"proceeding",proceed:void 0,reset:void 0,location:O}),Nr(f,p)},reset(){let ne=new Map(v.blockers);ne.set(H,at),oe({blockers:ne})}});return}return await Ne(T,O,{submission:L,pendingError:A,preventScrollReset:N,replace:p&&p.replace,enableViewTransition:p&&p.viewTransition,flushSync:U})}function ua(){if(Jt(),oe({revalidation:"loading"}),v.navigation.state!=="submitting"){if(v.navigation.state==="idle"){Ne(v.historyAction,v.location,{startUninterruptedRevalidation:!0});return}Ne(C||v.historyAction,v.navigation.location,{overrideNavigation:v.navigation,enableViewTransition:Y===!0})}}async function Ne(f,p,w){j&&j.abort(),j=null,C=f,ae=(w&&w.startUninterruptedRevalidation)===!0,wa(v.location,v.matches),$=(w&&w.preventScrollReset)===!0,Y=(w&&w.enableViewTransition)===!0;let S=s||o,L=w&&w.overrideNavigation,A=w!=null&&w.initialHydration&&v.matches&&v.matches.length>0&&!D?v.matches:be(S,p,l),k=(w&&w.flushSync)===!0;if(A&&v.initialized&&!te&&pi(v.location,p)&&!(w&&w.submission&&he(w.submission.formMethod))){We(p,{matches:A},{flushSync:k});return}let O=bt(A,S,p.pathname);if(O.active&&O.matches&&(A=O.matches),!A){let{error:J,notFoundMatches:z,route:X}=Kt(p.pathname);We(p,{matches:z,loaderData:{},errors:{[X.id]:J}},{flushSync:k});return}j=new AbortController;let M=Ke(e.history,p,j.signal,w&&w.submission),T;if(w&&w.pendingError)T=[ke(A).route.id,{type:V.error,error:w.pendingError}];else if(w&&w.submission&&he(w.submission.formMethod)){let J=await ca(M,p,w.submission,A,O.active,{replace:w.replace,flushSync:k});if(J.shortCircuited)return;if(J.pendingActionResult){let[z,X]=J.pendingActionResult;if(se(X)&&Ue(X.error)&&X.error.status===404){j=null,We(p,{matches:J.matches,loaderData:{},errors:{[z]:X.error}});return}}A=J.matches||A,T=J.pendingActionResult,L=qt(p,w.submission),k=!1,O.active=!1,M=Ke(e.history,M.url,M.signal)}let{shortCircuited:N,matches:U,loaderData:H,errors:ne}=await da(M,p,A,O.active,L,w&&w.submission,w&&w.fetcherSubmission,w&&w.replace,w&&w.initialHydration===!0,k,T);N||(j=null,We(p,K({matches:U||A},nn(T),{loaderData:H,errors:ne})))}async function ca(f,p,w,S,L,A){A===void 0&&(A={}),Jt();let k=bi(p,w);if(oe({navigation:k},{flushSync:A.flushSync===!0}),L){let T=await Rt(S,p.pathname,f.signal);if(T.type==="aborted")return{shortCircuited:!0};if(T.type==="error"){let N=ke(T.partialMatches).route.id;return{matches:T.partialMatches,pendingActionResult:[N,{type:V.error,error:T.error}]}}else if(T.matches)S=T.matches;else{let{notFoundMatches:N,error:U,route:H}=Kt(p.pathname);return{matches:N,pendingActionResult:[H.id,{type:V.error,error:U}]}}}let O,M=st(S,p);if(!M.route.action&&!M.route.lazy)O={type:V.error,error:le(405,{method:f.method,pathname:p.pathname,routeId:M.route.id})};else if(O=(await et("action",v,f,[M],S,null))[M.route.id],f.signal.aborted)return{shortCircuited:!0};if(je(O)){let T;return A&&A.replace!=null?T=A.replace:T=qr(O.response.headers.get("Location"),new URL(f.url),l)===v.location.pathname+v.location.search,await Fe(f,O,!0,{submission:w,replace:T}),{shortCircuited:!0}}if(_e(O))throw le(400,{type:"defer-action"});if(se(O)){let T=ke(S,M.route.id);return(A&&A.replace)!==!0&&(C=Z.Push),{matches:S,pendingActionResult:[T.route.id,O]}}return{matches:S,pendingActionResult:[M.route.id,O]}}async function da(f,p,w,S,L,A,k,O,M,T,N){let U=L||qt(p,A),H=A||k||ln(U),ne=!ae&&(!u.v7_partialHydration||!M);if(S){if(ne){let G=Fr(N);oe(K({navigation:U},G!==void 0?{actionData:G}:{}),{flushSync:T})}let B=await Rt(w,p.pathname,f.signal);if(B.type==="aborted")return{shortCircuited:!0};if(B.type==="error"){let G=ke(B.partialMatches).route.id;return{matches:B.partialMatches,loaderData:{},errors:{[G]:B.error}}}else if(B.matches)w=B.matches;else{let{error:G,notFoundMatches:Ye,route:nt}=Kt(p.pathname);return{matches:Ye,loaderData:{},errors:{[nt.id]:G}}}}let J=s||o,[z,X]=Gr(e.history,v,w,H,p,u.v7_partialHydration&&M===!0,u.v7_skipActionErrorRevalidation,te,Le,vt,fe,ze,ge,J,l,N);if(Xt(B=>!(w&&w.some(G=>G.route.id===B))||z&&z.some(G=>G.route.id===B)),Ze=++yt,z.length===0&&X.length===0){let B=Ir();return We(p,K({matches:w,loaderData:{},errors:N&&se(N[1])?{[N[0]]:N[1].error}:null},nn(N),B?{fetchers:new Map(v.fetchers)}:{}),{flushSync:T}),{shortCircuited:!0}}if(ne){let B={};if(!S){B.navigation=U;let G=Fr(N);G!==void 0&&(B.actionData=G)}X.length>0&&(B.fetchers=fa(X)),oe(B,{flushSync:T})}X.forEach(B=>{De(B.key),B.controller&&re.set(B.key,B.controller)});let Ve=()=>X.forEach(B=>De(B.key));j&&j.signal.addEventListener("abort",Ve);let{loaderResults:tt,fetcherResults:Ee}=await Ar(v,w,z,X,f);if(f.signal.aborted)return{shortCircuited:!0};j&&j.signal.removeEventListener("abort",Ve),X.forEach(B=>re.delete(B.key));let pe=Pt(tt);if(pe)return await Fe(f,pe.result,!0,{replace:O}),{shortCircuited:!0};if(pe=Pt(Ee),pe)return ge.add(pe.key),await Fe(f,pe.result,!0,{replace:O}),{shortCircuited:!0};let{loaderData:Gt,errors:rt}=tn(v,w,tt,N,X,Ee,Oe);Oe.forEach((B,G)=>{B.subscribe(Ye=>{(Ye||B.done)&&Oe.delete(G)})}),u.v7_partialHydration&&M&&v.errors&&(rt=K({},v.errors,rt));let Ae=Ir(),St=Ur(Ze),xt=Ae||St||X.length>0;return K({matches:w,loaderData:Gt,errors:rt},xt?{fetchers:new Map(v.fetchers)}:{})}function Fr(f){if(f&&!se(f[1]))return{[f[0]]:f[1].data};if(v.actionData)return Object.keys(v.actionData).length===0?null:v.actionData}function fa(f){return f.forEach(p=>{let w=v.fetchers.get(p.key),S=it(void 0,w?w.data:void 0);v.fetchers.set(p.key,S)}),new Map(v.fetchers)}function ha(f,p,w,S){if(n)throw new Error("router.fetch() was called during the server render, but it shouldn't be. You are likely calling a useFetcher() method in the body of your component. Try moving it to a useEffect or a callback.");De(f);let L=(S&&S.flushSync)===!0,A=s||o,k=ar(v.location,v.matches,l,u.v7_prependBasename,w,u.v7_relativeSplatPath,p,S==null?void 0:S.relative),O=be(A,k,l),M=bt(O,A,k);if(M.active&&M.matches&&(O=M.matches),!O){we(f,p,le(404,{pathname:k}),{flushSync:L});return}let{path:T,submission:N,error:U}=Kr(u.v7_normalizeFormMethod,!0,k,S);if(U){we(f,p,U,{flushSync:L});return}let H=st(O,T),ne=(S&&S.preventScrollReset)===!0;if(N&&he(N.formMethod)){ma(f,p,T,H,O,M.active,L,ne,N);return}ze.set(f,{routeId:p,path:T}),pa(f,p,T,H,O,M.active,L,ne,N)}async function ma(f,p,w,S,L,A,k,O,M){Jt(),ze.delete(f);function T(Q){if(!Q.route.action&&!Q.route.lazy){let Je=le(405,{method:M.formMethod,pathname:w,routeId:p});return we(f,p,Je,{flushSync:k}),!0}return!1}if(!A&&T(S))return;let N=v.fetchers.get(f);Pe(f,Ri(M,N),{flushSync:k});let U=new AbortController,H=Ke(e.history,w,U.signal,M);if(A){let Q=await Rt(L,new URL(H.url).pathname,H.signal,f);if(Q.type==="aborted")return;if(Q.type==="error"){we(f,p,Q.error,{flushSync:k});return}else if(Q.matches){if(L=Q.matches,S=st(L,w),T(S))return}else{we(f,p,le(404,{pathname:w}),{flushSync:k});return}}re.set(f,U);let ne=yt,z=(await et("action",v,H,[S],L,f))[S.route.id];if(H.signal.aborted){re.get(f)===U&&re.delete(f);return}if(u.v7_fetcherPersist&&fe.has(f)){if(je(z)||se(z)){Pe(f,Ce(void 0));return}}else{if(je(z))if(re.delete(f),Ze>ne){Pe(f,Ce(void 0));return}else return ge.add(f),Pe(f,it(M)),Fe(H,z,!1,{fetcherSubmission:M,preventScrollReset:O});if(se(z)){we(f,p,z.error);return}}if(_e(z))throw le(400,{type:"defer-action"});let X=v.navigation.location||v.location,Ve=Ke(e.history,X,U.signal),tt=s||o,Ee=v.navigation.state!=="idle"?be(tt,v.navigation.location,l):v.matches;I(Ee,"Didn't find any matches after fetcher action");let pe=++yt;Be.set(f,pe);let Gt=it(M,z.data);v.fetchers.set(f,Gt);let[rt,Ae]=Gr(e.history,v,Ee,M,X,!1,u.v7_skipActionErrorRevalidation,te,Le,vt,fe,ze,ge,tt,l,[S.route.id,z]);Ae.filter(Q=>Q.key!==f).forEach(Q=>{let Je=Q.key,Wr=v.fetchers.get(Je),Ra=it(void 0,Wr?Wr.data:void 0);v.fetchers.set(Je,Ra),De(Je),Q.controller&&re.set(Je,Q.controller)}),oe({fetchers:new Map(v.fetchers)});let St=()=>Ae.forEach(Q=>De(Q.key));U.signal.addEventListener("abort",St);let{loaderResults:xt,fetcherResults:B}=await Ar(v,Ee,rt,Ae,Ve);if(U.signal.aborted)return;U.signal.removeEventListener("abort",St),Be.delete(f),re.delete(f),Ae.forEach(Q=>re.delete(Q.key));let G=Pt(xt);if(G)return Fe(Ve,G.result,!1,{preventScrollReset:O});if(G=Pt(B),G)return ge.add(G.key),Fe(Ve,G.result,!1,{preventScrollReset:O});let{loaderData:Ye,errors:nt}=tn(v,Ee,xt,void 0,Ae,B,Oe);if(v.fetchers.has(f)){let Q=Ce(z.data);v.fetchers.set(f,Q)}Ur(pe),v.navigation.state==="loading"&&pe>Ze?(I(C,"Expected pending action"),j&&j.abort(),We(v.navigation.location,{matches:Ee,loaderData:Ye,errors:nt,fetchers:new Map(v.fetchers)})):(oe({errors:nt,loaderData:rn(v.loaderData,Ye,Ee,nt),fetchers:new Map(v.fetchers)}),te=!1)}async function pa(f,p,w,S,L,A,k,O,M){let T=v.fetchers.get(f);Pe(f,it(M,T?T.data:void 0),{flushSync:k});let N=new AbortController,U=Ke(e.history,w,N.signal);if(A){let z=await Rt(L,new URL(U.url).pathname,U.signal,f);if(z.type==="aborted")return;if(z.type==="error"){we(f,p,z.error,{flushSync:k});return}else if(z.matches)L=z.matches,S=st(L,w);else{we(f,p,le(404,{pathname:w}),{flushSync:k});return}}re.set(f,N);let H=yt,J=(await et("loader",v,U,[S],L,f))[S.route.id];if(_e(J)&&(J=await mr(J,U.signal,!0)||J),re.get(f)===N&&re.delete(f),!U.signal.aborted){if(fe.has(f)){Pe(f,Ce(void 0));return}if(je(J))if(Ze>H){Pe(f,Ce(void 0));return}else{ge.add(f),await Fe(U,J,!1,{preventScrollReset:O});return}if(se(J)){we(f,p,J.error);return}I(!_e(J),"Unhandled fetcher deferred data"),Pe(f,Ce(J.data))}}async function Fe(f,p,w,S){let{submission:L,fetcherSubmission:A,preventScrollReset:k,replace:O}=S===void 0?{}:S;p.response.headers.has("X-Remix-Revalidate")&&(te=!0);let M=p.response.headers.get("Location");I(M,"Expected a Location header on the redirect Response"),M=qr(M,new URL(f.url),l);let T=ut(v.location,M,{_isRedirect:!0});if(r){let z=!1;if(p.response.headers.has("X-Remix-Reload-Document"))z=!0;else if(hr.test(M)){const X=e.history.createURL(M);z=X.origin!==t.location.origin||ce(X.pathname,l)==null}if(z){O?t.location.replace(M):t.location.assign(M);return}}j=null;let N=O===!0||p.response.headers.has("X-Remix-Replace")?Z.Replace:Z.Push,{formMethod:U,formAction:H,formEncType:ne}=v.navigation;!L&&!A&&U&&H&&ne&&(L=ln(v.navigation));let J=L||A;if(ni.has(p.response.status)&&J&&he(J.formMethod))await Ne(N,T,{submission:K({},J,{formAction:M}),preventScrollReset:k||$,enableViewTransition:w?Y:void 0});else{let z=qt(T,L);await Ne(N,T,{overrideNavigation:z,fetcherSubmission:A,preventScrollReset:k||$,enableViewTransition:w?Y:void 0})}}async function et(f,p,w,S,L,A){let k,O={};try{k=await ci(c,f,p,w,S,L,A,i,a)}catch(M){return S.forEach(T=>{O[T.route.id]={type:V.error,error:M}}),O}for(let[M,T]of Object.entries(k))if(vi(T)){let N=T.result;O[M]={type:V.redirect,response:hi(N,w,M,L,l,u.v7_relativeSplatPath)}}else O[M]=await fi(T);return O}async function Ar(f,p,w,S,L){let A=f.matches,k=et("loader",f,L,w,p,null),O=Promise.all(S.map(async N=>{if(N.matches&&N.match&&N.controller){let H=(await et("loader",f,Ke(e.history,N.path,N.controller.signal),[N.match],N.matches,N.key))[N.match.route.id];return{[N.key]:H}}else return Promise.resolve({[N.key]:{type:V.error,error:le(404,{pathname:N.path})}})})),M=await k,T=(await O).reduce((N,U)=>Object.assign(N,U),{});return await Promise.all([wi(p,M,L.signal,A,f.loaderData),Ei(p,T,S)]),{loaderResults:M,fetcherResults:T}}function Jt(){te=!0,Le.push(...Xt()),ze.forEach((f,p)=>{re.has(p)&&vt.add(p),De(p)})}function Pe(f,p,w){w===void 0&&(w={}),v.fetchers.set(f,p),oe({fetchers:new Map(v.fetchers)},{flushSync:(w&&w.flushSync)===!0})}function we(f,p,w,S){S===void 0&&(S={});let L=ke(v.matches,p);wt(f),oe({errors:{[L.route.id]:w},fetchers:new Map(v.fetchers)},{flushSync:(S&&S.flushSync)===!0})}function kr(f){return qe.set(f,(qe.get(f)||0)+1),fe.has(f)&&fe.delete(f),v.fetchers.get(f)||ai}function wt(f){let p=v.fetchers.get(f);re.has(f)&&!(p&&p.state==="loading"&&Be.has(f))&&De(f),ze.delete(f),Be.delete(f),ge.delete(f),u.v7_fetcherPersist&&fe.delete(f),vt.delete(f),v.fetchers.delete(f)}function va(f){let p=(qe.get(f)||0)-1;p<=0?(qe.delete(f),fe.add(f),u.v7_fetcherPersist||wt(f)):qe.set(f,p),oe({fetchers:new Map(v.fetchers)})}function De(f){let p=re.get(f);p&&(p.abort(),re.delete(f))}function jr(f){for(let p of f){let w=kr(p),S=Ce(w.data);v.fetchers.set(p,S)}}function Ir(){let f=[],p=!1;for(let w of ge){let S=v.fetchers.get(w);I(S,"Expected fetcher: "+w),S.state==="loading"&&(ge.delete(w),f.push(w),p=!0)}return jr(f),p}function Ur(f){let p=[];for(let[w,S]of Be)if(S<f){let L=v.fetchers.get(w);I(L,"Expected fetcher: "+w),L.state==="loading"&&(De(w),Be.delete(w),p.push(w))}return jr(p),p.length>0}function ya(f,p){let w=v.blockers.get(f)||at;return Me.get(f)!==p&&Me.set(f,p),w}function $r(f){v.blockers.delete(f),Me.delete(f)}function Et(f,p){let w=v.blockers.get(f)||at;I(w.state==="unblocked"&&p.state==="blocked"||w.state==="blocked"&&p.state==="blocked"||w.state==="blocked"&&p.state==="proceeding"||w.state==="blocked"&&p.state==="unblocked"||w.state==="proceeding"&&p.state==="unblocked","Invalid blocker state transition: "+w.state+" -> "+p.state);let S=new Map(v.blockers);S.set(f,p),oe({blockers:S})}function Hr(f){let{currentLocation:p,nextLocation:w,historyAction:S}=f;if(Me.size===0)return;Me.size>1&&q(!1,"A router only supports one blocker at a time");let L=Array.from(Me.entries()),[A,k]=L[L.length-1],O=v.blockers.get(A);if(!(O&&O.state==="proceeding")&&k({currentLocation:p,nextLocation:w,historyAction:S}))return A}function Kt(f){let p=le(404,{pathname:f}),w=s||o,{matches:S,route:L}=an(w);return Xt(),{notFoundMatches:S,route:L,error:p}}function Xt(f){let p=[];return Oe.forEach((w,S)=>{(!f||f(S))&&(w.cancel(),p.push(S),Oe.delete(S))}),p}function ga(f,p,w){if(y=f,R=p,E=w||null,!P&&v.navigation===Zt){P=!0;let S=zr(v.location,v.matches);S!=null&&oe({restoreScrollPosition:S})}return()=>{y=null,R=null,E=null}}function Br(f,p){return E&&E(f,p.map(S=>En(S,v.loaderData)))||f.key}function wa(f,p){if(y&&R){let w=Br(f,p);y[w]=R()}}function zr(f,p){if(y){let w=Br(f,p),S=y[w];if(typeof S=="number")return S}return null}function bt(f,p,w){if(h)if(f){if(Object.keys(f[0].params).length>0)return{active:!0,matches:Mt(p,w,l,!0)}}else return{active:!0,matches:Mt(p,w,l,!0)||[]};return{active:!1,matches:null}}async function Rt(f,p,w,S){if(!h)return{type:"success",matches:f};let L=f;for(;;){let A=s==null,k=s||o,O=i;try{await h({signal:w,path:p,matches:L,fetcherKey:S,patch:(N,U)=>{w.aborted||Zr(N,U,k,O,a)}})}catch(N){return{type:"error",error:N,partialMatches:L}}finally{A&&!w.aborted&&(o=[...o])}if(w.aborted)return{type:"aborted"};let M=be(k,p,l);if(M)return{type:"success",matches:M};let T=Mt(k,p,l,!0);if(!T||L.length===T.length&&L.every((N,U)=>N.route.id===T[U].route.id))return{type:"success",matches:null};L=T}}function Ea(f){i={},s=jt(f,a,void 0,i)}function ba(f,p){let w=s==null;Zr(f,p,s||o,i,a),w&&(o=[...o],oe({}))}return _={get basename(){return l},get future(){return u},get state(){return v},get routes(){return o},get window(){return t},initialize:oa,subscribe:sa,enableScrollRestoration:ga,navigate:Nr,fetch:ha,revalidate:ua,createHref:f=>e.history.createHref(f),encodeLocation:f=>e.history.encodeLocation(f),getFetcher:kr,deleteFetcher:va,dispose:la,getBlocker:ya,deleteBlocker:$r,patchRoutes:ba,_internalFetchControllers:re,_internalActiveDeferreds:Oe,_internalSetRoutes:Ea},_}function oi(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function ar(e,t,r,n,a,i,o,s){let l,c;if(o){l=[];for(let u of t)if(l.push(u),u.route.id===o){c=u;break}}else l=t,c=t[t.length-1];let h=fr(a||".",dr(l,i),ce(e.pathname,r)||e.pathname,s==="path");if(a==null&&(h.search=e.search,h.hash=e.hash),(a==null||a===""||a===".")&&c){let u=pr(h.search);if(c.route.index&&!u)h.search=h.search?h.search.replace(/^\?/,"?index&"):"?index";else if(!c.route.index&&u){let m=new URLSearchParams(h.search),g=m.getAll("index");m.delete("index"),g.filter(E=>E).forEach(E=>m.append("index",E));let y=m.toString();h.search=y?"?"+y:""}}return n&&r!=="/"&&(h.pathname=h.pathname==="/"?r:ve([r,h.pathname])),Te(h)}function Kr(e,t,r,n){if(!n||!oi(n))return{path:r};if(n.formMethod&&!gi(n.formMethod))return{path:r,error:le(405,{method:n.formMethod})};let a=()=>({path:r,error:le(400,{type:"invalid-body"})}),i=n.formMethod||"get",o=e?i.toUpperCase():i.toLowerCase(),s=_n(r);if(n.body!==void 0){if(n.formEncType==="text/plain"){if(!he(o))return a();let m=typeof n.body=="string"?n.body:n.body instanceof FormData||n.body instanceof URLSearchParams?Array.from(n.body.entries()).reduce((g,y)=>{let[E,R]=y;return""+g+E+"="+R+`
`},""):String(n.body);return{path:r,submission:{formMethod:o,formAction:s,formEncType:n.formEncType,formData:void 0,json:void 0,text:m}}}else if(n.formEncType==="application/json"){if(!he(o))return a();try{let m=typeof n.body=="string"?JSON.parse(n.body):n.body;return{path:r,submission:{formMethod:o,formAction:s,formEncType:n.formEncType,formData:void 0,json:m,text:void 0}}}catch{return a()}}}I(typeof FormData=="function","FormData is not available in this environment");let l,c;if(n.formData)l=or(n.formData),c=n.formData;else if(n.body instanceof FormData)l=or(n.body),c=n.body;else if(n.body instanceof URLSearchParams)l=n.body,c=en(l);else if(n.body==null)l=new URLSearchParams,c=new FormData;else try{l=new URLSearchParams(n.body),c=en(l)}catch{return a()}let h={formMethod:o,formAction:s,formEncType:n&&n.formEncType||"application/x-www-form-urlencoded",formData:c,json:void 0,text:void 0};if(he(h.formMethod))return{path:r,submission:h};let u=Se(r);return t&&u.search&&pr(u.search)&&l.append("index",""),u.search="?"+l,{path:Te(u),submission:h}}function Xr(e,t,r){r===void 0&&(r=!1);let n=e.findIndex(a=>a.route.id===t);return n>=0?e.slice(0,r?n+1:n):e}function Gr(e,t,r,n,a,i,o,s,l,c,h,u,m,g,y,E){let R=E?se(E[1])?E[1].error:E[1].data:void 0,P=e.createURL(t.location),b=e.createURL(a),D=r;i&&t.errors?D=Xr(r,Object.keys(t.errors)[0],!0):E&&se(E[1])&&(D=Xr(r,E[0]));let x=E?E[1].statusCode:void 0,F=o&&x&&x>=400,_=D.filter((C,$)=>{let{route:j}=C;if(j.lazy)return!0;if(j.loader==null)return!1;if(i)return ir(j,t.loaderData,t.errors);if(li(t.loaderData,t.matches[$],C)||l.some(ie=>ie===C.route.id))return!0;let Y=t.matches[$],W=C;return Qr(C,K({currentUrl:P,currentParams:Y.params,nextUrl:b,nextParams:W.params},n,{actionResult:R,actionStatus:x,defaultShouldRevalidate:F?!1:s||P.pathname+P.search===b.pathname+b.search||P.search!==b.search||Dn(Y,W)}))}),v=[];return u.forEach((C,$)=>{if(i||!r.some(ae=>ae.route.id===C.routeId)||h.has($))return;let j=be(g,C.path,y);if(!j){v.push({key:$,routeId:C.routeId,path:C.path,matches:null,match:null,controller:null});return}let Y=t.fetchers.get($),W=st(j,C.path),ie=!1;m.has($)?ie=!1:c.has($)?(c.delete($),ie=!0):Y&&Y.state!=="idle"&&Y.data===void 0?ie=s:ie=Qr(W,K({currentUrl:P,currentParams:t.matches[t.matches.length-1].params,nextUrl:b,nextParams:r[r.length-1].params},n,{actionResult:R,actionStatus:x,defaultShouldRevalidate:F?!1:s})),ie&&v.push({key:$,routeId:C.routeId,path:C.path,matches:j,match:W,controller:new AbortController})}),[_,v]}function ir(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=t!=null&&t[e.id]!==void 0,a=r!=null&&r[e.id]!==void 0;return!n&&a?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!n&&!a}function li(e,t,r){let n=!t||r.route.id!==t.route.id,a=e[r.route.id]===void 0;return n||a}function Dn(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function Qr(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function Zr(e,t,r,n,a){var i;let o;if(e){let c=n[e];I(c,"No route found to patch children into: routeId = "+e),c.children||(c.children=[]),o=c.children}else o=r;let s=t.filter(c=>!o.some(h=>Cn(c,h))),l=jt(s,a,[e||"_","patch",String(((i=o)==null?void 0:i.length)||"0")],n);o.push(...l)}function Cn(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,n)=>{var a;return(a=t.children)==null?void 0:a.some(i=>Cn(r,i))}):!1}async function si(e,t,r){if(!e.lazy)return;let n=await e.lazy();if(!e.lazy)return;let a=r[e.id];I(a,"No route found in manifest");let i={};for(let o in n){let l=a[o]!==void 0&&o!=="hasErrorBoundary";q(!l,'Route "'+a.id+'" has a static property "'+o+'" defined but its lazy function is also returning a value for this property. '+('The lazy route property "'+o+'" will be ignored.')),!l&&!_a.has(o)&&(i[o]=n[o])}Object.assign(a,i),Object.assign(a,K({},t(a),{lazy:void 0}))}async function ui(e){let{matches:t}=e,r=t.filter(a=>a.shouldLoad);return(await Promise.all(r.map(a=>a.resolve()))).reduce((a,i,o)=>Object.assign(a,{[r[o].route.id]:i}),{})}async function ci(e,t,r,n,a,i,o,s,l,c){let h=i.map(g=>g.route.lazy?si(g.route,l,s):void 0),u=i.map((g,y)=>{let E=h[y],R=a.some(b=>b.route.id===g.route.id);return K({},g,{shouldLoad:R,resolve:async b=>(b&&n.method==="GET"&&(g.route.lazy||g.route.loader)&&(R=!0),R?di(t,n,g,E,b,c):Promise.resolve({type:V.data,result:void 0}))})}),m=await e({matches:u,request:n,params:i[0].params,fetcherKey:o,context:c});try{await Promise.all(h)}catch{}return m}async function di(e,t,r,n,a,i){let o,s,l=c=>{let h,u=new Promise((y,E)=>h=E);s=()=>h(),t.signal.addEventListener("abort",s);let m=y=>typeof c!="function"?Promise.reject(new Error("You cannot call the handler for a route which defines a boolean "+('"'+e+'" [routeId: '+r.route.id+"]"))):c({request:t,params:r.params,context:i},...y!==void 0?[y]:[]),g=(async()=>{try{return{type:"data",result:await(a?a(E=>m(E)):m())}}catch(y){return{type:"error",result:y}}})();return Promise.race([g,u])};try{let c=r.route[e];if(n)if(c){let h,[u]=await Promise.all([l(c).catch(m=>{h=m}),n]);if(h!==void 0)throw h;o=u}else if(await n,c=r.route[e],c)o=await l(c);else if(e==="action"){let h=new URL(t.url),u=h.pathname+h.search;throw le(405,{method:t.method,pathname:u,routeId:r.route.id})}else return{type:V.data,result:void 0};else if(c)o=await l(c);else{let h=new URL(t.url),u=h.pathname+h.search;throw le(404,{pathname:u})}I(o.result!==void 0,"You defined "+(e==="action"?"an action":"a loader")+" for route "+('"'+r.route.id+"\" but didn't return anything from your `"+e+"` ")+"function. Please return a value or `null`.")}catch(c){return{type:V.error,result:c}}finally{s&&t.signal.removeEventListener("abort",s)}return o}async function fi(e){let{result:t,type:r}=e;if(Tn(t)){let u;try{let m=t.headers.get("Content-Type");m&&/\bapplication\/json\b/.test(m)?t.body==null?u=null:u=await t.json():u=await t.text()}catch(m){return{type:V.error,error:m}}return r===V.error?{type:V.error,error:new Ie(t.status,t.statusText,u),statusCode:t.status,headers:t.headers}:{type:V.data,data:u,statusCode:t.status,headers:t.headers}}if(r===V.error){if(on(t)){var n,a;if(t.data instanceof Error){var i,o;return{type:V.error,error:t.data,statusCode:(i=t.init)==null?void 0:i.status,headers:(o=t.init)!=null&&o.headers?new Headers(t.init.headers):void 0}}return{type:V.error,error:new Ie(((n=t.init)==null?void 0:n.status)||500,void 0,t.data),statusCode:Ue(t)?t.status:void 0,headers:(a=t.init)!=null&&a.headers?new Headers(t.init.headers):void 0}}return{type:V.error,error:t,statusCode:Ue(t)?t.status:void 0}}if(yi(t)){var s,l;return{type:V.deferred,deferredData:t,statusCode:(s=t.init)==null?void 0:s.status,headers:((l=t.init)==null?void 0:l.headers)&&new Headers(t.init.headers)}}if(on(t)){var c,h;return{type:V.data,data:t.data,statusCode:(c=t.init)==null?void 0:c.status,headers:(h=t.init)!=null&&h.headers?new Headers(t.init.headers):void 0}}return{type:V.data,data:t}}function hi(e,t,r,n,a,i){let o=e.headers.get("Location");if(I(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!hr.test(o)){let s=n.slice(0,n.findIndex(l=>l.route.id===r)+1);o=ar(new URL(t.url),s,a,!0,o,i),e.headers.set("Location",o)}return e}function qr(e,t,r){if(hr.test(e)){let n=e,a=n.startsWith("//")?new URL(t.protocol+n):new URL(n),i=ce(a.pathname,r)!=null;if(a.origin===t.origin&&i)return a.pathname+a.search+a.hash}return e}function Ke(e,t,r,n){let a=e.createURL(_n(t)).toString(),i={signal:r};if(n&&he(n.formMethod)){let{formMethod:o,formEncType:s}=n;i.method=o.toUpperCase(),s==="application/json"?(i.headers=new Headers({"Content-Type":s}),i.body=JSON.stringify(n.json)):s==="text/plain"?i.body=n.text:s==="application/x-www-form-urlencoded"&&n.formData?i.body=or(n.formData):i.body=n.formData}return new Request(a,i)}function or(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,typeof n=="string"?n:n.name);return t}function en(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function mi(e,t,r,n,a){let i={},o=null,s,l=!1,c={},h=r&&se(r[1])?r[1].error:void 0;return e.forEach(u=>{if(!(u.route.id in t))return;let m=u.route.id,g=t[m];if(I(!je(g),"Cannot handle redirect results in processLoaderData"),se(g)){let y=g.error;h!==void 0&&(y=h,h=void 0),o=o||{};{let E=ke(e,m);o[E.route.id]==null&&(o[E.route.id]=y)}i[m]=void 0,l||(l=!0,s=Ue(g.error)?g.error.status:500),g.headers&&(c[m]=g.headers)}else _e(g)?(n.set(m,g.deferredData),i[m]=g.deferredData.data,g.statusCode!=null&&g.statusCode!==200&&!l&&(s=g.statusCode),g.headers&&(c[m]=g.headers)):(i[m]=g.data,g.statusCode&&g.statusCode!==200&&!l&&(s=g.statusCode),g.headers&&(c[m]=g.headers))}),h!==void 0&&r&&(o={[r[0]]:h},i[r[0]]=void 0),{loaderData:i,errors:o,statusCode:s||200,loaderHeaders:c}}function tn(e,t,r,n,a,i,o){let{loaderData:s,errors:l}=mi(t,r,n,o);return a.forEach(c=>{let{key:h,match:u,controller:m}=c,g=i[h];if(I(g,"Did not find corresponding fetcher result"),!(m&&m.signal.aborted))if(se(g)){let y=ke(e.matches,u==null?void 0:u.route.id);l&&l[y.route.id]||(l=K({},l,{[y.route.id]:g.error})),e.fetchers.delete(h)}else if(je(g))I(!1,"Unhandled fetcher revalidation redirect");else if(_e(g))I(!1,"Unhandled fetcher deferred data");else{let y=Ce(g.data);e.fetchers.set(h,y)}}),{loaderData:s,errors:l}}function rn(e,t,r,n){let a=K({},t);for(let i of r){let o=i.route.id;if(t.hasOwnProperty(o)?t[o]!==void 0&&(a[o]=t[o]):e[o]!==void 0&&i.route.loader&&(a[o]=e[o]),n&&n.hasOwnProperty(o))break}return a}function nn(e){return e?se(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function ke(e,t){return(t?e.slice(0,e.findIndex(n=>n.route.id===t)+1):[...e]).reverse().find(n=>n.route.hasErrorBoundary===!0)||e[0]}function an(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function le(e,t){let{pathname:r,routeId:n,method:a,type:i,message:o}=t===void 0?{}:t,s="Unknown Server Error",l="Unknown @remix-run/router error";return e===400?(s="Bad Request",a&&r&&n?l="You made a "+a+' request to "'+r+'" but '+('did not provide a `loader` for route "'+n+'", ')+"so there is no way to handle the request.":i==="defer-action"?l="defer() is not supported in actions":i==="invalid-body"&&(l="Unable to encode submission body")):e===403?(s="Forbidden",l='Route "'+n+'" does not match URL "'+r+'"'):e===404?(s="Not Found",l='No route matches URL "'+r+'"'):e===405&&(s="Method Not Allowed",a&&r&&n?l="You made a "+a.toUpperCase()+' request to "'+r+'" but '+('did not provide an `action` for route "'+n+'", ')+"so there is no way to handle the request.":a&&(l='Invalid request method "'+a.toUpperCase()+'"')),new Ie(e||500,s,new Error(l),!0)}function Pt(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[n,a]=t[r];if(je(a))return{key:n,result:a}}}function _n(e){let t=typeof e=="string"?Se(e):e;return Te(K({},t,{hash:""}))}function pi(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function vi(e){return Tn(e.result)&&ri.has(e.result.status)}function _e(e){return e.type===V.deferred}function se(e){return e.type===V.error}function je(e){return(e&&e.type)===V.redirect}function on(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function yi(e){let t=e;return t&&typeof t=="object"&&typeof t.data=="object"&&typeof t.subscribe=="function"&&typeof t.cancel=="function"&&typeof t.resolveData=="function"}function Tn(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function gi(e){return ti.has(e.toLowerCase())}function he(e){return qa.has(e.toLowerCase())}async function wi(e,t,r,n,a){let i=Object.entries(t);for(let o=0;o<i.length;o++){let[s,l]=i[o],c=e.find(m=>(m==null?void 0:m.route.id)===s);if(!c)continue;let h=n.find(m=>m.route.id===c.route.id),u=h!=null&&!Dn(h,c)&&(a&&a[c.route.id])!==void 0;_e(l)&&u&&await mr(l,r,!1).then(m=>{m&&(t[s]=m)})}}async function Ei(e,t,r){for(let n=0;n<r.length;n++){let{key:a,routeId:i,controller:o}=r[n],s=t[a];e.find(c=>(c==null?void 0:c.route.id)===i)&&_e(s)&&(I(o,"Expected an AbortController for revalidating fetcher deferred result"),await mr(s,o.signal,!0).then(c=>{c&&(t[a]=c)}))}}async function mr(e,t,r){if(r===void 0&&(r=!1),!await e.deferredData.resolveData(t)){if(r)try{return{type:V.data,data:e.deferredData.unwrappedData}}catch(a){return{type:V.error,error:a}}return{type:V.data,data:e.deferredData.data}}}function pr(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function st(e,t){let r=typeof t=="string"?Se(t).search:t.search;if(e[e.length-1].route.index&&pr(r||""))return e[e.length-1];let n=Sn(e);return n[n.length-1]}function ln(e){let{formMethod:t,formAction:r,formEncType:n,text:a,formData:i,json:o}=e;if(!(!t||!r||!n)){if(a!=null)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:a};if(i!=null)return{formMethod:t,formAction:r,formEncType:n,formData:i,json:void 0,text:void 0};if(o!==void 0)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:o,text:void 0}}}function qt(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function bi(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function it(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function Ri(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function Ce(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function Si(e,t){try{let r=e.sessionStorage.getItem(Pn);if(r){let n=JSON.parse(r);for(let[a,i]of Object.entries(n||{}))i&&Array.isArray(i)&&t.set(a,new Set(i||[]))}}catch{}}function xi(e,t){if(t.size>0){let r={};for(let[n,a]of t)r[n]=[...a];try{e.sessionStorage.setItem(Pn,JSON.stringify(r))}catch(n){q(!1,"Failed to save applied view transitions in sessionStorage ("+n+").")}}}/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function $t(){return $t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$t.apply(this,arguments)}const He=d.createContext(null);He.displayName="DataRouter";const Ge=d.createContext(null);Ge.displayName="DataRouterState";const ct=d.createContext(null);ct.displayName="Await";const de=d.createContext(null);de.displayName="Navigation";const Bt=d.createContext(null);Bt.displayName="Location";const ye=d.createContext({outlet:null,matches:[],isDataRoute:!1});ye.displayName="Route";const vr=d.createContext(null);vr.displayName="RouteError";function yr(e,t){let{relative:r}=t===void 0?{}:t;ft()||I(!1,"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:a}=d.useContext(de),{hash:i,pathname:o,search:s}=ht(e,{relative:r}),l=o;return n!=="/"&&(l=o==="/"?n:ve([n,o])),a.createHref({pathname:l,search:s,hash:i})}function ft(){return d.useContext(Bt)!=null}function xe(){return ft()||I(!1,"useLocation() may be used only in the context of a <Router> component."),d.useContext(Bt).location}const On="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Mn(e){d.useContext(de).static||d.useLayoutEffect(e)}function Li(){let{isDataRoute:e}=d.useContext(ye);return e?Wi():Pi()}function Pi(){ft()||I(!1,"useNavigate() may be used only in the context of a <Router> component.");let e=d.useContext(He),{basename:t,future:r,navigator:n}=d.useContext(de),{matches:a}=d.useContext(ye),{pathname:i}=xe(),o=JSON.stringify(dr(a,r.v7_relativeSplatPath)),s=d.useRef(!1);return Mn(()=>{s.current=!0}),d.useCallback(function(c,h){if(h===void 0&&(h={}),q(s.current,On),!s.current)return;if(typeof c=="number"){n.go(c);return}let u=fr(c,JSON.parse(o),i,h.relative==="path");e==null&&t!=="/"&&(u.pathname=u.pathname==="/"?t:ve([t,u.pathname])),(h.replace?n.replace:n.push)(u,h.state,h)},[t,n,o,i,e])}const Di=d.createContext(null);function Ci(e){let t=d.useContext(ye).outlet;return t&&d.createElement(Di.Provider,{value:e},t)}function ht(e,t){let{relative:r}=t===void 0?{}:t,{future:n}=d.useContext(de),{matches:a}=d.useContext(ye),{pathname:i}=xe(),o=JSON.stringify(dr(a,n.v7_relativeSplatPath));return d.useMemo(()=>fr(e,JSON.parse(o),i,r==="path"),[e,o,i,r])}function _i(e,t,r,n){ft()||I(!1,"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a,static:i}=d.useContext(de),{matches:o}=d.useContext(ye),s=o[o.length-1],l=s?s.params:{},c=s?s.pathname:"/",h=s?s.pathnameBase:"/",u=s&&s.route;{let b=u&&u.path||"";An(c,!u||b.endsWith("*"),"You rendered descendant <Routes> (or called `useRoutes()`) at "+('"'+c+'" (under <Route path="'+b+'">) but the ')+`parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

`+('Please change the parent <Route path="'+b+'"> to <Route ')+('path="'+(b==="/"?"*":b+"/*")+'">.'))}let m=xe(),g;g=m;let y=g.pathname||"/",E=y;if(h!=="/"){let b=h.replace(/^\//,"").split("/");E="/"+y.replace(/^\//,"").split("/").slice(b.length).join("/")}let R=!i&&r&&r.matches&&r.matches.length>0?r.matches:be(e,{pathname:E});return q(u||R!=null,'No routes matched location "'+g.pathname+g.search+g.hash+'" '),q(R==null||R[R.length-1].route.element!==void 0||R[R.length-1].route.Component!==void 0||R[R.length-1].route.lazy!==void 0,'Matched leaf route at location "'+g.pathname+g.search+g.hash+'" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.'),Fi(R&&R.map(b=>Object.assign({},b,{params:Object.assign({},l,b.params),pathname:ve([h,a.encodeLocation?a.encodeLocation(b.pathname).pathname:b.pathname]),pathnameBase:b.pathnameBase==="/"?h:ve([h,a.encodeLocation?a.encodeLocation(b.pathnameBase).pathname:b.pathnameBase])})),o,r,n)}function Ti(){let e=Fn(),t=Ue(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:n},i={padding:"2px 4px",backgroundColor:n},o=null;return console.error("Error handled by React Router default ErrorBoundary:",e),o=d.createElement(d.Fragment,null,d.createElement("p",null,"💿 Hey developer 👋"),d.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",d.createElement("code",{style:i},"ErrorBoundary")," or"," ",d.createElement("code",{style:i},"errorElement")," prop on your route.")),d.createElement(d.Fragment,null,d.createElement("h2",null,"Unexpected Application Error!"),d.createElement("h3",{style:{fontStyle:"italic"}},t),r?d.createElement("pre",{style:a},r):null,o)}const Oi=d.createElement(Ti,null);class Mi extends d.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error!==void 0?d.createElement(ye.Provider,{value:this.props.routeContext},d.createElement(vr.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Ni(e){let{routeContext:t,match:r,children:n}=e,a=d.useContext(He);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),d.createElement(ye.Provider,{value:t},n)}function Fi(e,t,r,n){var a;if(t===void 0&&(t=[]),r===void 0&&(r=null),n===void 0&&(n=null),e==null){var i;if(!r)return null;if(r.errors)e=r.matches;else if((i=n)!=null&&i.v7_partialHydration&&t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let o=e,s=(a=r)==null?void 0:a.errors;if(s!=null){let h=o.findIndex(u=>u.route.id&&(s==null?void 0:s[u.route.id])!==void 0);h>=0||I(!1,"Could not find a matching route for errors on route IDs: "+Object.keys(s).join(",")),o=o.slice(0,Math.min(o.length,h+1))}let l=!1,c=-1;if(r&&n&&n.v7_partialHydration)for(let h=0;h<o.length;h++){let u=o[h];if((u.route.HydrateFallback||u.route.hydrateFallbackElement)&&(c=h),u.route.id){let{loaderData:m,errors:g}=r,y=u.route.loader&&m[u.route.id]===void 0&&(!g||g[u.route.id]===void 0);if(u.route.lazy||y){l=!0,c>=0?o=o.slice(0,c+1):o=[o[0]];break}}}return o.reduceRight((h,u,m)=>{let g,y=!1,E=null,R=null;r&&(g=s&&u.route.id?s[u.route.id]:void 0,E=u.route.errorElement||Oi,l&&(c<0&&m===0?(An("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),y=!0,R=null):c===m&&(y=!0,R=u.route.hydrateFallbackElement||null)));let P=t.concat(o.slice(0,m+1)),b=()=>{let D;return g?D=E:y?D=R:u.route.Component?D=d.createElement(u.route.Component,null):u.route.element?D=u.route.element:D=h,d.createElement(Ni,{match:u,routeContext:{outlet:h,matches:P,isDataRoute:r!=null},children:D})};return r&&(u.route.ErrorBoundary||u.route.errorElement||m===0)?d.createElement(Mi,{location:r.location,revalidation:r.revalidation,component:E,error:g,children:b(),routeContext:{outlet:null,matches:P,isDataRoute:!0}}):b()},null)}var Nn=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Nn||{}),me=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(me||{});function gr(e){return e+" must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router."}function Ai(e){let t=d.useContext(He);return t||I(!1,gr(e)),t}function mt(e){let t=d.useContext(Ge);return t||I(!1,gr(e)),t}function ki(e){let t=d.useContext(ye);return t||I(!1,gr(e)),t}function pt(e){let t=ki(e),r=t.matches[t.matches.length-1];return r.route.id||I(!1,e+' can only be used on routes that contain a unique "id"'),r.route.id}function ji(){return pt(me.UseRouteId)}function Ii(){return mt(me.UseNavigation).navigation}function Ui(){let{matches:e,loaderData:t}=mt(me.UseMatches);return d.useMemo(()=>e.map(r=>En(r,t)),[e,t])}function $i(){let e=mt(me.UseLoaderData),t=pt(me.UseLoaderData);if(e.errors&&e.errors[t]!=null){console.error("You cannot `useLoaderData` in an errorElement (routeId: "+t+")");return}return e.loaderData[t]}function Hi(){let e=mt(me.UseActionData),t=pt(me.UseLoaderData);return e.actionData?e.actionData[t]:void 0}function Fn(){var e;let t=d.useContext(vr),r=mt(me.UseRouteError),n=pt(me.UseRouteError);return t!==void 0?t:(e=r.errors)==null?void 0:e[n]}function Bi(){let e=d.useContext(ct);return e==null?void 0:e._data}function zi(){let e=d.useContext(ct);return e==null?void 0:e._error}function Wi(){let{router:e}=Ai(Nn.UseNavigateStable),t=pt(me.UseNavigateStable),r=d.useRef(!1);return Mn(()=>{r.current=!0}),d.useCallback(function(a,i){i===void 0&&(i={}),q(r.current,On),r.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,$t({fromRouteId:t},i)))},[e,t])}const sn={};function An(e,t,r){!t&&!sn[e]&&(sn[e]=!0,q(!1,r))}const un={};function Vi(e,t){un[t]||(un[t]=!0,console.warn(t))}const Xe=(e,t,r)=>Vi(e,"⚠️ React Router Future Flag Warning: "+t+". "+("You can use the `"+e+"` future flag to opt-in early. ")+("For more information, see "+r+"."));function Yi(e,t){(e==null?void 0:e.v7_startTransition)===void 0&&Xe("v7_startTransition","React Router will begin wrapping state updates in `React.startTransition` in v7","https://reactrouter.com/v6/upgrading/future#v7_starttransition"),(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||!t.v7_relativeSplatPath)&&Xe("v7_relativeSplatPath","Relative route resolution within Splat routes is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath"),t&&(t.v7_fetcherPersist===void 0&&Xe("v7_fetcherPersist","The persistence behavior of fetchers is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist"),t.v7_normalizeFormMethod===void 0&&Xe("v7_normalizeFormMethod","Casing of `formMethod` fields is being normalized to uppercase in v7","https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod"),t.v7_partialHydration===void 0&&Xe("v7_partialHydration","`RouterProvider` hydration behavior is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_partialhydration"),t.v7_skipActionErrorRevalidation===void 0&&Xe("v7_skipActionErrorRevalidation","The revalidation behavior after 4xx/5xx `action` responses is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation"))}function Vl(e){return Ci(e.context)}function Ji(e){let{basename:t="/",children:r=null,location:n,navigationType:a=Z.Pop,navigator:i,static:o=!1,future:s}=e;ft()&&I(!1,"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=t.replace(/^\/*/,"/"),c=d.useMemo(()=>({basename:l,navigator:i,static:o,future:$t({v7_relativeSplatPath:!1},s)}),[l,s,i,o]);typeof n=="string"&&(n=Se(n));let{pathname:h="/",search:u="",hash:m="",state:g=null,key:y="default"}=n,E=d.useMemo(()=>{let R=ce(h,l);return R==null?null:{location:{pathname:R,search:u,hash:m,state:g,key:y},navigationType:a}},[l,h,u,m,g,y,a]);return q(E!=null,'<Router basename="'+l+'"> is not able to match the URL '+('"'+h+u+m+'" because it does not start with the ')+"basename, so the <Router> won't render anything."),E==null?null:d.createElement(de.Provider,{value:c},d.createElement(Bt.Provider,{children:r,value:E}))}function Ki(e){let{children:t,errorElement:r,resolve:n}=e;return d.createElement(Gi,{resolve:n,errorElement:r},d.createElement(Qi,null,t))}var ue=function(e){return e[e.pending=0]="pending",e[e.success=1]="success",e[e.error=2]="error",e}(ue||{});const Xi=new Promise(()=>{});class Gi extends d.Component{constructor(t){super(t),this.state={error:null}}static getDerivedStateFromError(t){return{error:t}}componentDidCatch(t,r){console.error("<Await> caught the following error during render",t,r)}render(){let{children:t,errorElement:r,resolve:n}=this.props,a=null,i=ue.pending;if(!(n instanceof Promise))i=ue.success,a=Promise.resolve(),Object.defineProperty(a,"_tracked",{get:()=>!0}),Object.defineProperty(a,"_data",{get:()=>n});else if(this.state.error){i=ue.error;let o=this.state.error;a=Promise.reject().catch(()=>{}),Object.defineProperty(a,"_tracked",{get:()=>!0}),Object.defineProperty(a,"_error",{get:()=>o})}else n._tracked?(a=n,i="_error"in a?ue.error:"_data"in a?ue.success:ue.pending):(i=ue.pending,Object.defineProperty(n,"_tracked",{get:()=>!0}),a=n.then(o=>Object.defineProperty(n,"_data",{get:()=>o}),o=>Object.defineProperty(n,"_error",{get:()=>o})));if(i===ue.error&&a._error instanceof Ut)throw Xi;if(i===ue.error&&!r)throw a._error;if(i===ue.error)return d.createElement(ct.Provider,{value:a,children:r});if(i===ue.success)return d.createElement(ct.Provider,{value:a,children:t});throw a}}function Qi(e){let{children:t}=e,r=Bi(),n=typeof t=="function"?t(r):t;return d.createElement(d.Fragment,null,n)}function Yl(e){let t={hasErrorBoundary:e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&(e.element&&q(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:d.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&q(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:d.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&q(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:d.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function $e(){return $e=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$e.apply(this,arguments)}function wr(e,t){if(e==null)return{};var r={},n=Object.keys(e),a,i;for(i=0;i<n.length;i++)a=n[i],!(t.indexOf(a)>=0)&&(r[a]=e[a]);return r}const Nt="get",Ft="application/x-www-form-urlencoded";function zt(e){return e!=null&&typeof e.tagName=="string"}function Zi(e){return zt(e)&&e.tagName.toLowerCase()==="button"}function qi(e){return zt(e)&&e.tagName.toLowerCase()==="form"}function eo(e){return zt(e)&&e.tagName.toLowerCase()==="input"}function to(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function ro(e,t){return e.button===0&&(!t||t==="_self")&&!to(e)}let Dt=null;function no(){if(Dt===null)try{new FormData(document.createElement("form"),0),Dt=!1}catch{Dt=!0}return Dt}const ao=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function er(e){return e!=null&&!ao.has(e)?(q(!1,'"'+e+'" is not a valid `encType` for `<Form>`/`<fetcher.Form>` '+('and will default to "'+Ft+'"')),null):e}function io(e,t){let r,n,a,i,o;if(qi(e)){let s=e.getAttribute("action");n=s?ce(s,t):null,r=e.getAttribute("method")||Nt,a=er(e.getAttribute("enctype"))||Ft,i=new FormData(e)}else if(Zi(e)||eo(e)&&(e.type==="submit"||e.type==="image")){let s=e.form;if(s==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||s.getAttribute("action");if(n=l?ce(l,t):null,r=e.getAttribute("formmethod")||s.getAttribute("method")||Nt,a=er(e.getAttribute("formenctype"))||er(s.getAttribute("enctype"))||Ft,i=new FormData(s,e),!no()){let{name:c,type:h,value:u}=e;if(h==="image"){let m=c?c+".":"";i.append(m+"x","0"),i.append(m+"y","0")}else c&&i.append(c,u)}}else{if(zt(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=Nt,n=null,a=Ft,o=e}return i&&a==="text/plain"&&(o=i,i=void 0),{action:n,method:r.toLowerCase(),encType:a,formData:i,body:o}}const oo=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],lo=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],so=["fetcherKey","navigate","reloadDocument","replace","state","method","action","onSubmit","relative","preventScrollReset","viewTransition"],uo="6";try{window.__reactRouterVersion=uo}catch{}const Er=d.createContext({isTransitioning:!1});Er.displayName="ViewTransition";const kn=d.createContext(new Map);kn.displayName="Fetchers";const co="startTransition",cn=La[co],fo="flushSync",dn=Pa[fo];function ho(e){cn?cn(e):e()}function ot(e){dn?dn(e):e()}let mo=class{constructor(){this.status="pending",this.promise=new Promise((t,r)=>{this.resolve=n=>{this.status==="pending"&&(this.status="resolved",t(n))},this.reject=n=>{this.status==="pending"&&(this.status="rejected",r(n))}})}};function Kl(e){let{fallbackElement:t,router:r,future:n}=e,[a,i]=d.useState(r.state),[o,s]=d.useState(),[l,c]=d.useState({isTransitioning:!1}),[h,u]=d.useState(),[m,g]=d.useState(),[y,E]=d.useState(),R=d.useRef(new Map),{v7_startTransition:P}=n||{},b=d.useCallback(C=>{P?ho(C):C()},[P]),D=d.useCallback((C,$)=>{let{deletedFetchers:j,flushSync:Y,viewTransitionOpts:W}=$;C.fetchers.forEach((ae,te)=>{ae.data!==void 0&&R.current.set(te,ae.data)}),j.forEach(ae=>R.current.delete(ae));let ie=r.window==null||r.window.document==null||typeof r.window.document.startViewTransition!="function";if(!W||ie){Y?ot(()=>i(C)):b(()=>i(C));return}if(Y){ot(()=>{m&&(h&&h.resolve(),m.skipTransition()),c({isTransitioning:!0,flushSync:!0,currentLocation:W.currentLocation,nextLocation:W.nextLocation})});let ae=r.window.document.startViewTransition(()=>{ot(()=>i(C))});ae.finished.finally(()=>{ot(()=>{u(void 0),g(void 0),s(void 0),c({isTransitioning:!1})})}),ot(()=>g(ae));return}m?(h&&h.resolve(),m.skipTransition(),E({state:C,currentLocation:W.currentLocation,nextLocation:W.nextLocation})):(s(C),c({isTransitioning:!0,flushSync:!1,currentLocation:W.currentLocation,nextLocation:W.nextLocation}))},[r.window,m,h,R,b]);d.useLayoutEffect(()=>r.subscribe(D),[r,D]),d.useEffect(()=>{l.isTransitioning&&!l.flushSync&&u(new mo)},[l]),d.useEffect(()=>{if(h&&o&&r.window){let C=o,$=h.promise,j=r.window.document.startViewTransition(async()=>{b(()=>i(C)),await $});j.finished.finally(()=>{u(void 0),g(void 0),s(void 0),c({isTransitioning:!1})}),g(j)}},[b,o,h,r.window]),d.useEffect(()=>{h&&o&&a.location.key===o.location.key&&h.resolve()},[h,m,a.location,o]),d.useEffect(()=>{!l.isTransitioning&&y&&(s(y.state),c({isTransitioning:!0,flushSync:!1,currentLocation:y.currentLocation,nextLocation:y.nextLocation}),E(void 0))},[l.isTransitioning,y]),d.useEffect(()=>{q(t==null||!r.future.v7_partialHydration,"`<RouterProvider fallbackElement>` is deprecated when using `v7_partialHydration`, use a `HydrateFallback` component instead")},[]);let x=d.useMemo(()=>({createHref:r.createHref,encodeLocation:r.encodeLocation,go:C=>r.navigate(C),push:(C,$,j)=>r.navigate(C,{state:$,preventScrollReset:j==null?void 0:j.preventScrollReset}),replace:(C,$,j)=>r.navigate(C,{replace:!0,state:$,preventScrollReset:j==null?void 0:j.preventScrollReset})}),[r]),F=r.basename||"/",_=d.useMemo(()=>({router:r,navigator:x,static:!1,basename:F}),[r,x,F]),v=d.useMemo(()=>({v7_relativeSplatPath:r.future.v7_relativeSplatPath}),[r.future.v7_relativeSplatPath]);return d.useEffect(()=>Yi(n,r.future),[n,r.future]),d.createElement(d.Fragment,null,d.createElement(He.Provider,{value:_},d.createElement(Ge.Provider,{value:a},d.createElement(kn.Provider,{value:R.current},d.createElement(Er.Provider,{value:l},d.createElement(Ji,{basename:F,location:a.location,navigationType:a.historyAction,navigator:x,future:v},a.initialized||r.future.v7_partialHydration?d.createElement(po,{routes:r.routes,future:r.future,state:a}):t))))),null)}const po=d.memo(vo);function vo(e){let{routes:t,future:r,state:n}=e;return _i(t,void 0,n,r)}const yo=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",go=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,br=d.forwardRef(function(t,r){let{onClick:n,relative:a,reloadDocument:i,replace:o,state:s,target:l,to:c,preventScrollReset:h,viewTransition:u}=t,m=wr(t,oo),{basename:g}=d.useContext(de),y,E=!1;if(typeof c=="string"&&go.test(c)&&(y=c,yo))try{let D=new URL(window.location.href),x=c.startsWith("//")?new URL(D.protocol+c):new URL(c),F=ce(x.pathname,g);x.origin===D.origin&&F!=null?c=F+x.search+x.hash:E=!0}catch{q(!1,'<Link to="'+c+'"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.')}let R=yr(c,{relative:a}),P=Eo(c,{replace:o,state:s,target:l,preventScrollReset:h,relative:a,viewTransition:u});function b(D){n&&n(D),D.defaultPrevented||P(D)}return d.createElement("a",$e({},m,{href:y||R,onClick:E||i?n:b,ref:r,target:l}))});br.displayName="Link";const jn=d.forwardRef(function(t,r){let{"aria-current":n="page",caseSensitive:a=!1,className:i="",end:o=!1,style:s,to:l,viewTransition:c,children:h}=t,u=wr(t,lo),m=ht(l,{relative:u.relative}),g=xe(),y=d.useContext(Ge),{navigator:E,basename:R}=d.useContext(de),P=y!=null&&Do(m)&&c===!0,b=E.encodeLocation?E.encodeLocation(m).pathname:m.pathname,D=g.pathname,x=y&&y.navigation&&y.navigation.location?y.navigation.location.pathname:null;a||(D=D.toLowerCase(),x=x?x.toLowerCase():null,b=b.toLowerCase()),x&&R&&(x=ce(x,R)||x);const F=b!=="/"&&b.endsWith("/")?b.length-1:b.length;let _=D===b||!o&&D.startsWith(b)&&D.charAt(F)==="/",v=x!=null&&(x===b||!o&&x.startsWith(b)&&x.charAt(b.length)==="/"),C={isActive:_,isPending:v,isTransitioning:P},$=_?n:void 0,j;typeof i=="function"?j=i(C):j=[i,_?"active":null,v?"pending":null,P?"transitioning":null].filter(Boolean).join(" ");let Y=typeof s=="function"?s(C):s;return d.createElement(br,$e({},u,{"aria-current":$,className:j,ref:r,style:Y,to:l,viewTransition:c}),typeof h=="function"?h(C):h)});jn.displayName="NavLink";const In=d.forwardRef((e,t)=>{let{fetcherKey:r,navigate:n,reloadDocument:a,replace:i,state:o,method:s=Nt,action:l,onSubmit:c,relative:h,preventScrollReset:u,viewTransition:m}=e,g=wr(e,so),y=xo(),E=Lo(l,{relative:h}),R=s.toLowerCase()==="get"?"get":"post",P=b=>{if(c&&c(b),b.defaultPrevented)return;b.preventDefault();let D=b.nativeEvent.submitter,x=(D==null?void 0:D.getAttribute("formmethod"))||s;y(D||b.currentTarget,{fetcherKey:r,method:x,navigate:n,replace:i,state:o,relative:h,preventScrollReset:u,viewTransition:m})};return d.createElement("form",$e({ref:t,method:R,action:E,onSubmit:a?c:P},g))});In.displayName="Form";var dt;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(dt||(dt={}));var lr;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(lr||(lr={}));function Un(e){return e+" must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router."}function Rr(e){let t=d.useContext(He);return t||I(!1,Un(e)),t}function wo(e){let t=d.useContext(Ge);return t||I(!1,Un(e)),t}function Eo(e,t){let{target:r,replace:n,state:a,preventScrollReset:i,relative:o,viewTransition:s}=t===void 0?{}:t,l=Li(),c=xe(),h=ht(e,{relative:o});return d.useCallback(u=>{if(ro(u,r)){u.preventDefault();let m=n!==void 0?n:Te(c)===Te(h);l(e,{replace:m,state:a,preventScrollReset:i,relative:o,viewTransition:s})}},[c,l,h,n,a,r,e,i,o,s])}function bo(){if(typeof document>"u")throw new Error("You are calling submit during the server render. Try calling submit within a `useEffect` or callback instead.")}let Ro=0,So=()=>"__"+String(++Ro)+"__";function xo(){let{router:e}=Rr(dt.UseSubmit),{basename:t}=d.useContext(de),r=ji();return d.useCallback(function(n,a){a===void 0&&(a={}),bo();let{action:i,method:o,encType:s,formData:l,body:c}=io(n,t);if(a.navigate===!1){let h=a.fetcherKey||So();e.fetch(h,r,a.action||i,{preventScrollReset:a.preventScrollReset,formData:l,body:c,formMethod:a.method||o,formEncType:a.encType||s,flushSync:a.flushSync})}else e.navigate(a.action||i,{preventScrollReset:a.preventScrollReset,formData:l,body:c,formMethod:a.method||o,formEncType:a.encType||s,replace:a.replace,state:a.state,fromRouteId:r,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,r])}function Lo(e,t){let{relative:r}=t===void 0?{}:t,{basename:n}=d.useContext(de),a=d.useContext(ye);a||I(!1,"useFormAction must be used inside a RouteContext");let[i]=a.matches.slice(-1),o=$e({},ht(e||".",{relative:r})),s=xe();if(e==null){o.search=s.search;let l=new URLSearchParams(o.search),c=l.getAll("index");if(c.some(u=>u==="")){l.delete("index"),c.filter(m=>m).forEach(m=>l.append("index",m));let u=l.toString();o.search=u?"?"+u:""}}return(!e||e===".")&&i.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),n!=="/"&&(o.pathname=o.pathname==="/"?n:ve([n,o.pathname])),Te(o)}const fn="react-router-scroll-positions";let Ct={};function Xl(e){let{getKey:t,storageKey:r}=e===void 0?{}:e,{router:n}=Rr(dt.UseScrollRestoration),{restoreScrollPosition:a,preventScrollReset:i}=wo(lr.UseScrollRestoration),{basename:o}=d.useContext(de),s=xe(),l=Ui(),c=Ii();d.useEffect(()=>(window.history.scrollRestoration="manual",()=>{window.history.scrollRestoration="auto"}),[]),Po(d.useCallback(()=>{if(c.state==="idle"){let h=(t?t(s,l):null)||s.key;Ct[h]=window.scrollY}try{sessionStorage.setItem(r||fn,JSON.stringify(Ct))}catch(h){q(!1,"Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly ("+h+").")}window.history.scrollRestoration="auto"},[r,t,c.state,s,l])),typeof document<"u"&&(d.useLayoutEffect(()=>{try{let h=sessionStorage.getItem(r||fn);h&&(Ct=JSON.parse(h))}catch{}},[r]),d.useLayoutEffect(()=>{let h=t&&o!=="/"?(m,g)=>t($e({},m,{pathname:ce(m.pathname,o)||m.pathname}),g):t,u=n==null?void 0:n.enableScrollRestoration(Ct,()=>window.scrollY,h);return()=>u&&u()},[n,o,t]),d.useLayoutEffect(()=>{if(a!==!1){if(typeof a=="number"){window.scrollTo(0,a);return}if(s.hash){let h=document.getElementById(decodeURIComponent(s.hash.slice(1)));if(h){h.scrollIntoView();return}}i!==!0&&window.scrollTo(0,0)}},[s,a,i]))}function Po(e,t){let{capture:r}={};d.useEffect(()=>{let n=r!=null?{capture:r}:void 0;return window.addEventListener("pagehide",e,n),()=>{window.removeEventListener("pagehide",e,n)}},[e,r])}function Do(e,t){t===void 0&&(t={});let r=d.useContext(Er);r==null&&I(!1,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:n}=Rr(dt.useViewTransitionState),a=ht(e,{relative:t.relative});if(!r.isTransitioning)return!1;let i=ce(r.currentLocation.pathname,n)||r.currentLocation.pathname,o=ce(r.nextLocation.pathname,n)||r.nextLocation.pathname;return It(a.pathname,o)!=null||It(a.pathname,i)!=null}var Co=-1,_o=-2,To=-3,Oo=-4,Mo=-5,No=-6,Fo=-7,Ao="B",ko="D",$n="E",jo="M",Io="N",Hn="P",Uo="R",$o="S",Ho="Y",Bo="U",zo="Z",Bn=class{constructor(){Lt(this,"promise");Lt(this,"resolve");Lt(this,"reject");this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}};function Wo(){const e=new TextDecoder;let t="";return new TransformStream({transform(r,n){const a=e.decode(r,{stream:!0}),i=(t+a).split(`
`);t=i.pop()||"";for(const o of i)n.enqueue(o)},flush(r){t&&r.enqueue(t)}})}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var tr=typeof window<"u"?window:typeof globalThis<"u"?globalThis:void 0;function sr(e){const{hydrated:t,values:r}=this;if(typeof e=="number")return hn.call(this,e);if(!Array.isArray(e)||!e.length)throw new SyntaxError;const n=r.length;for(const a of e)r.push(a);return t.length=r.length,hn.call(this,n)}function hn(e){const{hydrated:t,values:r,deferred:n,plugins:a}=this;let i;const o=[[e,l=>{i=l}]];let s=[];for(;o.length>0;){const[l,c]=o.pop();switch(l){case Fo:c(void 0);continue;case Mo:c(null);continue;case _o:c(NaN);continue;case No:c(1/0);continue;case To:c(-1/0);continue;case Oo:c(-0);continue}if(t[l]){c(t[l]);continue}const h=r[l];if(!h||typeof h!="object"){t[l]=h,c(h);continue}if(Array.isArray(h))if(typeof h[0]=="string"){const[u,m,g]=h;switch(u){case ko:c(t[l]=new Date(m));continue;case Bo:c(t[l]=new URL(m));continue;case Ao:c(t[l]=BigInt(m));continue;case Uo:c(t[l]=new RegExp(m,g));continue;case Ho:c(t[l]=Symbol.for(m));continue;case $o:const y=new Set;t[l]=y;for(let x=1;x<h.length;x++)o.push([h[x],F=>{y.add(F)}]);c(y);continue;case jo:const E=new Map;t[l]=E;for(let x=1;x<h.length;x+=2){const F=[];o.push([h[x+1],_=>{F[1]=_}]),o.push([h[x],_=>{F[0]=_}]),s.push(()=>{E.set(F[0],F[1])})}c(E);continue;case Io:const R=Object.create(null);t[l]=R;for(const x of Object.keys(m).reverse()){const F=[];o.push([m[x],_=>{F[1]=_}]),o.push([Number(x.slice(1)),_=>{F[0]=_}]),s.push(()=>{R[F[0]]=F[1]})}c(R);continue;case Hn:if(t[m])c(t[l]=t[m]);else{const x=new Bn;n[m]=x,c(t[l]=x.promise)}continue;case $n:const[,P,b]=h;let D=b&&tr&&tr[b]?new tr[b](P):new Error(P);t[l]=D,c(D);continue;case zo:c(t[l]=t[m]);continue;default:if(Array.isArray(a)){const x=[],F=h.slice(1);for(let _=0;_<F.length;_++){const v=F[_];o.push([v,C=>{x[_]=C}])}s.push(()=>{for(const _ of a){const v=_(h[0],...x);if(v){c(t[l]=v.value);return}}throw new SyntaxError});continue}throw new SyntaxError}}else{const u=[];t[l]=u;for(let m=0;m<h.length;m++){const g=h[m];g!==Co&&o.push([g,y=>{u[m]=y}])}c(u);continue}else{const u={};t[l]=u;for(const m of Object.keys(h).reverse()){const g=[];o.push([h[m],y=>{g[1]=y}]),o.push([Number(m.slice(1)),y=>{g[0]=y}]),s.push(()=>{u[g[0]]=g[1]})}c(u);continue}}for(;s.length>0;)s.pop()();return i}async function Vo(e,t){const{plugins:r}=t??{},n=new Bn,a=e.pipeThrough(Wo()).getReader(),i={values:[],hydrated:[],deferred:{},plugins:r},o=await Yo.call(i,a);let s=n.promise;return o.done?n.resolve():s=Jo.call(i,a).then(n.resolve).catch(l=>{for(const c of Object.values(i.deferred))c.reject(l);n.reject(l)}),{done:s.then(()=>a.closed),value:o.value}}async function Yo(e){const t=await e.read();if(!t.value)throw new SyntaxError;let r;try{r=JSON.parse(t.value)}catch{throw new SyntaxError}return{done:t.done,value:sr.call(this,r)}}async function Jo(e){let t=await e.read();for(;!t.done;){if(!t.value)continue;const r=t.value;switch(r[0]){case Hn:{const n=r.indexOf(":"),a=Number(r.slice(1,n)),i=this.deferred[a];if(!i)throw new Error(`Deferred ID ${a} not found in stream`);const o=r.slice(n+1);let s;try{s=JSON.parse(o)}catch{throw new SyntaxError}const l=sr.call(this,s);i.resolve(l);break}case $n:{const n=r.indexOf(":"),a=Number(r.slice(1,n)),i=this.deferred[a];if(!i)throw new Error(`Deferred ID ${a} not found in stream`);const o=r.slice(n+1);let s;try{s=JSON.parse(o)}catch{throw new SyntaxError}const l=sr.call(this,s);i.reject(l);break}default:throw new SyntaxError}t=await e.read()}}/**
 * @remix-run/server-runtime v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const zn=Symbol("SingleFetchRedirect");/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ee(){return ee=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ee.apply(this,arguments)}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Re(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */async function Wn(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__remixContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ko(e,t,r){let n=e.map(i=>{var o;let s=t[i.route.id],l=r.routes[i.route.id];return[l.css?l.css.map(c=>({rel:"stylesheet",href:c})):[],(s==null||(o=s.links)===null||o===void 0?void 0:o.call(s))||[]]}).flat(2),a=el(e,r);return Yn(n,a)}async function Vn(e,t){var r,n;if(!e.css&&!t.links||!rl())return;let a=[((r=e.css)===null||r===void 0?void 0:r.map(s=>({rel:"stylesheet",href:s})))??[],((n=t.links)===null||n===void 0?void 0:n.call(t))??[]].flat(1);if(a.length===0)return;let i=[];for(let s of a)!Sr(s)&&s.rel==="stylesheet"&&i.push({...s,rel:"preload",as:"style"});let o=i.filter(s=>(!s.media||window.matchMedia(s.media).matches)&&!document.querySelector(`link[rel="stylesheet"][href="${s.href}"]`));await Promise.all(o.map(Xo))}async function Xo(e){return new Promise(t=>{let r=document.createElement("link");Object.assign(r,e);function n(){document.head.contains(r)&&document.head.removeChild(r)}r.onload=()=>{n(),t()},r.onerror=()=>{n(),t()},document.head.appendChild(r)})}function Sr(e){return e!=null&&typeof e.page=="string"}function Go(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function Qo(e,t,r){let n=await Promise.all(e.map(async a=>{let i=await Wn(t.routes[a.route.id],r);return i.links?i.links():[]}));return Yn(n.flat(1).filter(Go).filter(a=>a.rel==="stylesheet"||a.rel==="preload").map(a=>a.rel==="stylesheet"?{...a,rel:"prefetch",as:"style"}:{...a,rel:"prefetch"}))}function mn(e,t,r,n,a,i,o){let s=Jn(e),l=(u,m)=>r[m]?u.route.id!==r[m].route.id:!0,c=(u,m)=>{var g;return r[m].pathname!==u.pathname||((g=r[m].route.path)===null||g===void 0?void 0:g.endsWith("*"))&&r[m].params["*"]!==u.params["*"]};return o==="data"&&(i.v3_singleFetch||a.search!==s.search)?t.filter((u,m)=>{if(!n.routes[u.route.id].hasLoader)return!1;if(l(u,m)||c(u,m))return!0;let y=i.v3_singleFetch||a.search!==s.search;if(u.route.shouldRevalidate){var E;let R=u.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:((E=r[0])===null||E===void 0?void 0:E.params)||{},nextUrl:new URL(e,window.origin),nextParams:u.params,defaultShouldRevalidate:y});if(typeof R=="boolean")return R}return y}):t.filter((u,m)=>{let g=n.routes[u.route.id];return(o==="assets"||g.hasLoader)&&(l(u,m)||c(u,m))})}function Zo(e,t,r){let n=Jn(e);return xr(t.filter(a=>r.routes[a.route.id].hasLoader&&!r.routes[a.route.id].hasClientLoader).map(a=>{let{pathname:i,search:o}=n,s=new URLSearchParams(o);return s.set("_data",a.route.id),`${i}?${s}`}))}function qo(e,t){return xr(e.map(r=>{let n=t.routes[r.route.id],a=[n.module];return n.imports&&(a=a.concat(n.imports)),a}).flat(1))}function el(e,t){return xr(e.map(r=>{let n=t.routes[r.route.id],a=[n.module];return n.imports&&(a=a.concat(n.imports)),a}).flat(1))}function xr(e){return[...new Set(e)]}function tl(e){let t={},r=Object.keys(e).sort();for(let n of r)t[n]=e[n];return t}function Yn(e,t){let r=new Set,n=new Set(t);return e.reduce((a,i)=>{if(t&&!Sr(i)&&i.as==="script"&&i.href&&n.has(i.href))return a;let s=JSON.stringify(tl(i));return r.has(s)||(r.add(s),a.push({key:s,link:i})),a},[])}function Jn(e){let t=Se(e);return t.search===void 0&&(t.search=""),t}let _t;function rl(){if(_t!==void 0)return _t;let e=document.createElement("link");return _t=e.relList.supports("preload"),e=null,_t}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const nl={"&":"\\u0026",">":"\\u003e","<":"\\u003c","\u2028":"\\u2028","\u2029":"\\u2029"},al=/[&><\u2028\u2029]/g;function Tt(e){return e.replace(al,t=>nl[t])}function pn(e){return{__html:e}}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function il(e){return e.headers.get("X-Remix-Catch")!=null}function ol(e){return e.headers.get("X-Remix-Error")!=null}function ll(e){return Lr(e)&&e.status>=400&&e.headers.get("X-Remix-Error")==null&&e.headers.get("X-Remix-Catch")==null&&e.headers.get("X-Remix-Response")==null}function sl(e){return e.headers.get("X-Remix-Redirect")!=null}function ul(e){var t;return!!((t=e.headers.get("Content-Type"))!==null&&t!==void 0&&t.match(/text\/remix-deferred/))}function Lr(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function cl(e){let t=e;return t&&typeof t=="object"&&typeof t.data=="object"&&typeof t.subscribe=="function"&&typeof t.cancel=="function"&&typeof t.resolveData=="function"}async function Kn(e,t,r=0){let n=new URL(e.url);n.searchParams.set("_data",t),r>0&&await new Promise(s=>setTimeout(s,5**r*10));let a=await Wt(e),i=window.__remixRevalidation,o=await fetch(n.href,a).catch(s=>{if(typeof i=="number"&&i===window.__remixRevalidation&&(s==null?void 0:s.name)==="TypeError"&&r<3)return Kn(e,t,r+1);throw s});if(ol(o)){let s=await o.json(),l=new Error(s.message);return l.stack=s.stack,l}if(ll(o)){let s=await o.text(),l=new Error(s);return l.stack=void 0,l}return o}async function Wt(e){let t={signal:e.signal};if(e.method!=="GET"){t.method=e.method;let r=e.headers.get("Content-Type");r&&/\bapplication\/json\b/.test(r)?(t.headers={"Content-Type":r},t.body=JSON.stringify(await e.json())):r&&/\btext\/plain\b/.test(r)?(t.headers={"Content-Type":r},t.body=await e.text()):r&&/\bapplication\/x-www-form-urlencoded\b/.test(r)?t.body=new URLSearchParams(await e.text()):t.body=await e.formData()}return t}const dl="__deferred_promise:";async function fl(e){if(!e)throw new Error("parseDeferredReadableStream requires stream argument");let t,r={};try{let n=hl(e),i=(await n.next()).value;if(!i)throw new Error("no critical data");let o=JSON.parse(i);if(typeof o=="object"&&o!==null)for(let[s,l]of Object.entries(o))typeof l!="string"||!l.startsWith(dl)||(t=t||{},t[s]=new Promise((c,h)=>{r[s]={resolve:u=>{c(u),delete r[s]},reject:u=>{h(u),delete r[s]}}}));return(async()=>{try{for await(let s of n){let[l,...c]=s.split(":"),h=c.join(":"),u=JSON.parse(h);if(l==="data")for(let[m,g]of Object.entries(u))r[m]&&r[m].resolve(g);else if(l==="error")for(let[m,g]of Object.entries(u)){let y=new Error(g.message);y.stack=g.stack,r[m]&&r[m].reject(y)}}for(let[s,l]of Object.entries(r))l.reject(new Ut(`Deferred ${s} will never be resolved`))}catch(s){for(let l of Object.values(r))l.reject(s)}})(),new Ga({...o,...t})}catch(n){for(let a of Object.values(r))a.reject(n);throw n}}async function*hl(e){let t=e.getReader(),r=[],n=[],a=!1,i=new TextEncoder,o=new TextDecoder,s=async()=>{if(n.length>0)return n.shift();for(;!a&&n.length===0;){let c=await t.read();if(c.done){a=!0;break}r.push(c.value);try{let u=o.decode(vn(...r)).split(`

`);if(u.length>=2&&(n.push(...u.slice(0,-1)),r=[i.encode(u.slice(-1).join(`

`))]),n.length>0)break}catch{continue}}return n.length>0||r.length>0&&(n=o.decode(vn(...r)).split(`

`).filter(h=>h),r=[]),n.shift()},l=await s();for(;l;)yield l,l=await s()}function vn(...e){let t=new Uint8Array(e.reduce((n,a)=>n+a.length,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Gl(e,t,r){return async({request:n,matches:a,fetcherKey:i})=>n.method!=="GET"?ml(n,a):i?vl(n,a):pl(e,t,r(),n,a)}async function ml(e,t){let r=t.find(i=>i.shouldLoad);Re(r,"No action match found");let n,a=await r.resolve(async i=>await i(async()=>{let s=Vt(e.url),l=await Wt(e),{data:c,status:h}=await Pr(s,l);return n=h,ur(c,r.route.id)}));return Lr(a.result)||Ue(a.result)?{[r.route.id]:a}:{[r.route.id]:{type:a.type,result:Xa(a.result,n)}}}async function pl(e,t,r,n,a){let i=new Set,o=!1,s=a.map(()=>yn()),l=Promise.all(s.map(y=>y.promise)),c=yn(),h=Gn(Vt(n.url)),u=await Wt(n),m={},g=Promise.all(a.map(async(y,E)=>y.resolve(async R=>{if(s[E].resolve(),!y.shouldLoad){var P;if(!r.state.initialized)return;if(y.route.id in r.state.loaderData&&e.routes[y.route.id]&&(P=t[y.route.id])!==null&&P!==void 0&&P.shouldRevalidate){e.routes[y.route.id].hasLoader&&(o=!0);return}}if(e.routes[y.route.id].hasClientLoader){e.routes[y.route.id].hasLoader&&(o=!0);try{let b=await Xn(R,h,u,y.route.id);m[y.route.id]={type:"data",result:b}}catch(b){m[y.route.id]={type:"error",result:b}}return}e.routes[y.route.id].hasLoader&&i.add(y.route.id);try{let b=await R(async()=>{let D=await c.promise;return Qn(D,y.route.id)});m[y.route.id]={type:"data",result:b}}catch(b){m[y.route.id]={type:"error",result:b}}})));if(await l,(!r.state.initialized||i.size===0)&&!window.__remixHdrActive)c.resolve({});else try{o&&i.size>0&&h.searchParams.set("_routes",a.filter(E=>i.has(E.route.id)).map(E=>E.route.id).join(","));let y=await Pr(h,u);c.resolve(y.data)}catch(y){c.reject(y)}return await g,m}async function vl(e,t){let r=t.find(a=>a.shouldLoad);Re(r,"No fetcher match found");let n=await r.resolve(async a=>{let i=Gn(Vt(e.url)),o=await Wt(e);return Xn(a,i,o,r.route.id)});return{[r.route.id]:n}}function Xn(e,t,r,n){return e(async()=>{let a=new URL(t);a.searchParams.set("_routes",n);let{data:i}=await Pr(a,r);return Qn(i,n)})}function Gn(e){let t=e.searchParams.getAll("index");e.searchParams.delete("index");let r=[];for(let n of t)n&&r.push(n);for(let n of r)e.searchParams.append("index",n);return e}function Vt(e){let t=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return t.pathname==="/"?t.pathname="_root.data":t.pathname=`${t.pathname.replace(/\/$/,"")}.data`,t}async function Pr(e,t){let r=await fetch(e,t);if(new Set([100,101,204,205]).has(r.status))return!t.method||t.method==="GET"?{status:r.status,data:{}}:{status:r.status,data:{data:null}};Re(r.body,"No response body to decode");try{let a=await yl(r.body,window);return{status:r.status,data:a.value}}catch(a){throw console.error(a),new Error(`Unable to decode turbo-stream response from URL: ${e.toString()}`)}}function yl(e,t){return Vo(e,{plugins:[(r,...n)=>{if(r==="SanitizedError"){let[a,i,o]=n,s=Error;a&&a in t&&typeof t[a]=="function"&&(s=t[a]);let l=new s(i);return l.stack=o,{value:l}}if(r==="ErrorResponse"){let[a,i,o]=n;return{value:new Ie(i,o,a)}}if(r==="SingleFetchRedirect")return{value:{[zn]:n[0]}}},(r,n)=>{if(r==="SingleFetchFallback")return{value:void 0};if(r==="SingleFetchClassInstance")return{value:n}}]})}function Qn(e,t){let r=e[zn];return r?ur(r,t):e[t]!==void 0?ur(e[t],t):null}function ur(e,t){if("error"in e)throw e.error;if("redirect"in e){let r={};throw e.revalidate&&(r["X-Remix-Revalidate"]="yes"),e.reload&&(r["X-Remix-Reload-Document"]="yes"),e.replace&&(r["X-Remix-Replace"]="yes"),xn(e.redirect,{status:e.status,headers:r})}else{if("data"in e)return e.data;throw new Error(`No response found for routeId "${t}"`)}}function yn(){let e,t,r=new Promise((n,a)=>{e=async i=>{n(i);try{await r}catch{}},t=async i=>{a(i);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */class Ql extends d.Component{constructor(t){super(t),this.state={error:t.error||null,location:t.location}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location?{error:t.error||null,location:t.location}:{error:t.error||r.error,location:r.location}}render(){return this.state.error?d.createElement(Zn,{error:this.state.error,isOutsideRemixApp:!0}):this.props.children}}function Zn({error:e,isOutsideRemixApp:t}){console.error(e);let r=d.createElement("script",{dangerouslySetInnerHTML:{__html:`
        console.log(
          "💿 Hey developer 👋. You can provide a way better UX than this when your app throws errors. Check out https://remix.run/guides/errors for more information."
        );
      `}});if(Ue(e))return d.createElement(cr,{title:"Unhandled Thrown Response!"},d.createElement("h1",{style:{fontSize:"24px"}},e.status," ",e.statusText),r);let n;if(e instanceof Error)n=e;else{let a=e==null?"Unknown Error":typeof e=="object"&&"toString"in e?e.toString():JSON.stringify(e);n=new Error(a)}return d.createElement(cr,{title:"Application Error!",isOutsideRemixApp:t},d.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),d.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},n.stack),r)}function cr({title:e,renderScripts:t,isOutsideRemixApp:r,children:n}){var a;let{routeModules:i}=Qe();return(a=i.root)!==null&&a!==void 0&&a.Layout&&!r?n:d.createElement("html",{lang:"en"},d.createElement("head",null,d.createElement("meta",{charSet:"utf-8"}),d.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),d.createElement("title",null,e)),d.createElement("body",null,d.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},n,t?d.createElement(Il,null):null)))}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function gl(){return d.createElement(cr,{title:"Loading...",renderScripts:!0},d.createElement("script",{dangerouslySetInnerHTML:{__html:`
              console.log(
                "💿 Hey developer 👋. You can provide a way better UX than this " +
                "when your app is loading JS modules and/or running \`clientLoader\` " +
                "functions. Check out https://remix.run/route/hydrate-fallback " +
                "for more information."
              );
            `}}))}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function qn(e){let t={};return Object.values(e).forEach(r=>{let n=r.parentId||"";t[n]||(t[n]=[]),t[n].push(r)}),t}function wl(e,t,r){let n=ea(t),a=t.HydrateFallback&&(!r||e.id==="root")?t.HydrateFallback:e.id==="root"?gl:void 0,i=t.ErrorBoundary?t.ErrorBoundary:e.id==="root"?()=>d.createElement(Zn,{error:Fn()}):void 0;return e.id==="root"&&t.Layout?{...n?{element:d.createElement(t.Layout,null,d.createElement(n,null))}:{Component:n},...i?{errorElement:d.createElement(t.Layout,null,d.createElement(i,null))}:{ErrorBoundary:i},...a?{hydrateFallbackElement:d.createElement(t.Layout,null,d.createElement(a,null))}:{HydrateFallback:a}}:{Component:n,ErrorBoundary:i,HydrateFallback:a}}function Zl(e,t,r,n,a,i){return Dr(t,r,n,a,i,"",qn(t),e)}function Ot(e,t,r){if(r){let o=`You cannot call ${e==="action"?"serverAction()":"serverLoader()"} in SPA Mode (routeId: "${t.id}")`;throw console.error(o),new Ie(400,"Bad Request",new Error(o),!0)}let a=`You are trying to call ${e==="action"?"serverAction()":"serverLoader()"} on a route that does not have a server ${e} (routeId: "${t.id}")`;if(e==="loader"&&!t.hasLoader||e==="action"&&!t.hasAction)throw console.error(a),new Ie(400,"Bad Request",new Error(a),!0)}function rr(e,t){let r=e==="clientAction"?"a":"an",n=`Route "${t}" does not have ${r} ${e}, but you are trying to submit to it. To fix this, please add ${r} \`${e}\` function to the route`;throw console.error(n),new Ie(405,"Method Not Allowed",new Error(n),!0)}function Dr(e,t,r,n,a,i="",o=qn(e),s){return(o[i]||[]).map(l=>{let c=t[l.id];async function h(D,x,F){if(typeof F=="function")return await F();let _=await Rl(D,l);return x?Sl(_):_}function u(D,x,F){return l.hasLoader?h(D,x,F):Promise.resolve(null)}function m(D,x,F){if(!l.hasAction)throw rr("action",l.id);return h(D,x,F)}async function g(D){let x=t[l.id],F=x?Vn(l,x):Promise.resolve();try{return D()}finally{await F}}let y={id:l.id,index:l.index,path:l.path};if(c){var E,R,P;Object.assign(y,{...y,...wl(l,c,a),handle:c.handle,shouldRevalidate:gn(n,c,l.id,s)});let D=r==null||(E=r.loaderData)===null||E===void 0?void 0:E[l.id],x=r==null||(R=r.errors)===null||R===void 0?void 0:R[l.id],F=s==null&&(((P=c.clientLoader)===null||P===void 0?void 0:P.hydrate)===!0||!l.hasLoader);y.loader=async({request:_,params:v},C)=>{try{return await g(async()=>(Re(c,"No `routeModule` available for critical-route loader"),c.clientLoader?c.clientLoader({request:_,params:v,async serverLoader(){if(Ot("loader",l,a),F){if(D!==void 0)return D;if(x!==void 0)throw x;return null}return u(_,!0,C)}}):a?null:u(_,!1,C)))}finally{F=!1}},y.loader.hydrate=Ll(l,c,a),y.action=({request:_,params:v},C)=>g(async()=>{if(Re(c,"No `routeModule` available for critical-route action"),!c.clientAction){if(a)throw rr("clientAction",l.id);return m(_,!1,C)}return c.clientAction({request:_,params:v,async serverAction(){return Ot("action",l,a),m(_,!0,C)}})})}else l.hasClientLoader||(y.loader=({request:D},x)=>g(()=>a?Promise.resolve(null):u(D,!1,x))),l.hasClientAction||(y.action=({request:D},x)=>g(()=>{if(a)throw rr("clientAction",l.id);return m(D,!1,x)})),y.lazy=async()=>{let D=await bl(l,t),x={...D};if(D.clientLoader){let F=D.clientLoader;x.loader=(_,v)=>F({..._,async serverLoader(){return Ot("loader",l,a),u(_.request,!0,v)}})}if(D.clientAction){let F=D.clientAction;x.action=(_,v)=>F({..._,async serverAction(){return Ot("action",l,a),m(_.request,!0,v)}})}return{...x.loader?{loader:x.loader}:{},...x.action?{action:x.action}:{},hasErrorBoundary:x.hasErrorBoundary,shouldRevalidate:gn(n,x,l.id,s),handle:x.handle,Component:x.Component,ErrorBoundary:x.ErrorBoundary}};let b=Dr(e,t,r,n,a,l.id,o,s);return b.length>0&&(y.children=b),y})}function gn(e,t,r,n){if(n)return El(r,t.shouldRevalidate,n);if(e.v3_singleFetch&&t.shouldRevalidate){let a=t.shouldRevalidate;return i=>a({...i,defaultShouldRevalidate:!0})}return t.shouldRevalidate}function El(e,t,r){let n=!1;return a=>n?t?t(a):a.defaultShouldRevalidate:(n=!0,r.has(e))}async function bl(e,t){let r=await Wn(e,t);return await Vn(e,r),{Component:ea(r),ErrorBoundary:r.ErrorBoundary,clientAction:r.clientAction,clientLoader:r.clientLoader,handle:r.handle,links:r.links,meta:r.meta,shouldRevalidate:r.shouldRevalidate}}async function Rl(e,t){let r=await Kn(e,t.id);if(r instanceof Error)throw r;if(sl(r))throw xl(r);if(il(r))throw r;return ul(r)&&r.body?await fl(r.body):r}function Sl(e){if(cl(e))return e.data;if(Lr(e)){let t=e.headers.get("Content-Type");return t&&/\bapplication\/json\b/.test(t)?e.json():e.text()}return e}function xl(e){let t=parseInt(e.headers.get("X-Remix-Status"),10)||302,r=e.headers.get("X-Remix-Redirect"),n={},a=e.headers.get("X-Remix-Revalidate");a&&(n["X-Remix-Revalidate"]=a);let i=e.headers.get("X-Remix-Reload-Document");i&&(n["X-Remix-Reload-Document"]=i);let o=e.headers.get("X-Remix-Replace");return o&&(n["X-Remix-Replace"]=o),xn(r,{status:t,headers:n})}function ea(e){if(e.default==null)return;if(!(typeof e.default=="object"&&Object.keys(e.default).length===0))return e.default}function Ll(e,t,r){return r&&e.id!=="root"||t.clientLoader!=null&&(t.clientLoader.hydrate===!0||e.hasLoader!==!0)}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const At=new Set,Pl=1e3,Ht=new Set,Dl=7680;function Cr(e,t){return e.v3_lazyRouteDiscovery===!0&&!t}function Cl(e,t){let r=new Set(t.state.matches.map(o=>o.route.id)),n=t.state.location.pathname.split("/").filter(Boolean),a=["/"];for(n.pop();n.length>0;)a.push(`/${n.join("/")}`),n.pop();a.forEach(o=>{let s=be(t.routes,o,t.basename);s&&s.forEach(l=>r.add(l.route.id))});let i=[...r].reduce((o,s)=>Object.assign(o,{[s]:e.routes[s]}),{});return{...e,routes:i}}function ql(e,t,r,n,a){if(Cr(r,n))return async({path:i,patch:o,signal:s,fetcherKey:l})=>{Ht.has(i)||await ta([i],l?window.location.href:i,e,t,r,n,a,o,s)}}function es(e,t,r,n,a){d.useEffect(()=>{var i;if(!Cr(n,a)||((i=navigator.connection)===null||i===void 0?void 0:i.saveData)===!0)return;function o(u){let m=u.tagName==="FORM"?u.getAttribute("action"):u.getAttribute("href");if(!m)return;let g=new URL(m,window.location.origin);Ht.has(g.pathname)||At.add(g.pathname)}async function s(){let u=Array.from(At.keys()).filter(m=>Ht.has(m)?(At.delete(m),!1):!0);if(u.length!==0)try{await ta(u,null,t,r,n,a,e.basename,e.patchRoutes)}catch(m){console.error("Failed to fetch manifest patches",m)}}document.body.querySelectorAll("a[data-discover], form[data-discover]").forEach(u=>o(u)),s();let l=Tl(s,100);function c(u){return u.nodeType===Node.ELEMENT_NODE}let h=new MutationObserver(u=>{let m=new Set;u.forEach(g=>{[g.target,...g.addedNodes].forEach(y=>{c(y)&&((y.tagName==="A"&&y.getAttribute("data-discover")||y.tagName==="FORM"&&y.getAttribute("data-discover"))&&m.add(y),y.tagName!=="A"&&y.querySelectorAll("a[data-discover], form[data-discover]").forEach(E=>m.add(E)))})}),m.forEach(g=>o(g)),l()});return h.observe(document.documentElement,{subtree:!0,childList:!0,attributes:!0,attributeFilter:["data-discover","href","action"]}),()=>h.disconnect()},[n,a,t,r,e])}const nr="remix-manifest-version";async function ta(e,t,r,n,a,i,o,s,l){let c=`${o??"/"}/__manifest`.replace(/\/+/g,"/"),h=new URL(c,window.location.origin);if(e.sort().forEach(E=>h.searchParams.append("p",E)),h.searchParams.set("version",r.version),h.toString().length>Dl){At.clear();return}let u;try{let E=await fetch(h,{signal:l});if(E.ok){if(E.status===204&&E.headers.has("X-Remix-Reload-Document")){if(!t){console.warn("Detected a manifest version mismatch during eager route discovery. The next navigation/fetch to an undiscovered route will result in a new document navigation to sync up with the latest manifest.");return}if(sessionStorage.getItem(nr)===r.version){console.error("Unable to discover routes due to manifest version mismatch.");return}throw sessionStorage.setItem(nr,r.version),window.location.href=t,new Error("Detected manifest version mismatch, reloading...")}else if(E.status>=400)throw new Error(await E.text())}else throw new Error(`${E.status} ${E.statusText}`);sessionStorage.removeItem(nr),u=await E.json()}catch(E){if(l!=null&&l.aborted)return;throw E}let m=new Set(Object.keys(r.routes)),g=Object.values(u).reduce((E,R)=>m.has(R.id)?E:Object.assign(E,{[R.id]:R}),{});Object.assign(r.routes,g),e.forEach(E=>_l(E,Ht));let y=new Set;Object.values(g).forEach(E=>{(!E.parentId||!g[E.parentId])&&y.add(E.parentId)}),y.forEach(E=>s(E||null,Dr(g,n,null,a,i,E)))}function _l(e,t){if(t.size>=Pl){let r=t.values().next().value;typeof r=="string"&&t.delete(r)}t.add(e)}function Tl(e,t){let r;return(...n)=>{window.clearTimeout(r),r=window.setTimeout(()=>e(...n),t)}}function ra(){let e=d.useContext(He);return Re(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function Yt(){let e=d.useContext(Ge);return Re(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}const na=d.createContext(void 0);na.displayName="Remix";function Qe(){let e=d.useContext(na);return Re(e,"You must render this element inside a <Remix> element"),e}function aa(e,t){let[r,n]=d.useState(!1),[a,i]=d.useState(!1),{onFocus:o,onBlur:s,onMouseEnter:l,onMouseLeave:c,onTouchStart:h}=t,u=d.useRef(null);d.useEffect(()=>{if(e==="render"&&i(!0),e==="viewport"){let y=R=>{R.forEach(P=>{i(P.isIntersecting)})},E=new IntersectionObserver(y,{threshold:.5});return u.current&&E.observe(u.current),()=>{E.disconnect()}}},[e]);let m=()=>{e==="intent"&&n(!0)},g=()=>{e==="intent"&&(n(!1),i(!1))};return d.useEffect(()=>{if(r){let y=setTimeout(()=>{i(!0)},100);return()=>{clearTimeout(y)}}},[r]),[a,u,{onFocus:lt(o,m),onBlur:lt(s,g),onMouseEnter:lt(l,m),onMouseLeave:lt(c,g),onTouchStart:lt(h,m)}]}const _r=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i;function Tr(e,t,r){return e==="render"&&!t&&!r?"true":void 0}let Ol=d.forwardRef(({to:e,prefetch:t="none",discover:r="render",...n},a)=>{let i=typeof e=="string"&&_r.test(e),o=yr(e),[s,l,c]=aa(t,n);return d.createElement(d.Fragment,null,d.createElement(jn,ee({},n,c,{ref:ia(a,l),to:e,"data-discover":Tr(r,i,n.reloadDocument)})),s&&!i?d.createElement(Mr,{page:o}):null)});Ol.displayName="NavLink";let Ml=d.forwardRef(({to:e,prefetch:t="none",discover:r="render",...n},a)=>{let i=typeof e=="string"&&_r.test(e),o=yr(e),[s,l,c]=aa(t,n);return d.createElement(d.Fragment,null,d.createElement(br,ee({},n,c,{ref:ia(a,l),to:e,"data-discover":Tr(r,i,n.reloadDocument)})),s&&!i?d.createElement(Mr,{page:o}):null)});Ml.displayName="Link";let Nl=d.forwardRef(({discover:e="render",...t},r)=>{let n=typeof t.action=="string"&&_r.test(t.action);return d.createElement(In,ee({},t,{ref:r,"data-discover":Tr(e,n,t.reloadDocument)}))});Nl.displayName="Form";function lt(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function Or(e,t,r){if(r&&!kt)return[e[0]];if(t){let n=e.findIndex(a=>t[a.route.id]!==void 0);return e.slice(0,n+1)}return e}function ts(){let{isSpaMode:e,manifest:t,routeModules:r,criticalCss:n}=Qe(),{errors:a,matches:i}=Yt(),o=Or(i,a,e),s=d.useMemo(()=>Ko(o,r,t),[o,r,t]);return d.createElement(d.Fragment,null,n?d.createElement("style",{dangerouslySetInnerHTML:{__html:n}}):null,s.map(({key:l,link:c})=>Sr(c)?d.createElement(Mr,ee({key:l},c)):d.createElement("link",ee({key:l},c))))}function Mr({page:e,...t}){let{router:r}=ra(),n=d.useMemo(()=>be(r.routes,e,r.basename),[r.routes,e,r.basename]);return n?d.createElement(Al,ee({page:e,matches:n},t)):(console.warn(`Tried to prefetch ${e} but no routes matched.`),null)}function Fl(e){let{manifest:t,routeModules:r}=Qe(),[n,a]=d.useState([]);return d.useEffect(()=>{let i=!1;return Qo(e,t,r).then(o=>{i||a(o)}),()=>{i=!0}},[e,t,r]),n}function Al({page:e,matches:t,...r}){let n=xe(),{future:a,manifest:i,routeModules:o}=Qe(),{loaderData:s,matches:l}=Yt(),c=d.useMemo(()=>mn(e,t,l,i,n,a,"data"),[e,t,l,i,n,a]),h=d.useMemo(()=>{if(!a.v3_singleFetch)return Zo(e,c,i);if(e===n.pathname+n.search+n.hash)return[];let y=new Set,E=!1;if(t.forEach(P=>{var b;i.routes[P.route.id].hasLoader&&(!c.some(D=>D.route.id===P.route.id)&&P.route.id in s&&(b=o[P.route.id])!==null&&b!==void 0&&b.shouldRevalidate||i.routes[P.route.id].hasClientLoader?E=!0:y.add(P.route.id))}),y.size===0)return[];let R=Vt(e);return E&&y.size>0&&R.searchParams.set("_routes",t.filter(P=>y.has(P.route.id)).map(P=>P.route.id).join(",")),[R.pathname+R.search]},[a.v3_singleFetch,s,n,i,c,t,e,o]),u=d.useMemo(()=>mn(e,t,l,i,n,a,"assets"),[e,t,l,i,n,a]),m=d.useMemo(()=>qo(u,i),[u,i]),g=Fl(u);return d.createElement(d.Fragment,null,h.map(y=>d.createElement("link",ee({key:y,rel:"prefetch",as:"fetch",href:y},r))),m.map(y=>d.createElement("link",ee({key:y,rel:"modulepreload",href:y},r))),g.map(({key:y,link:E})=>d.createElement("link",ee({key:y},E))))}function rs(){let{isSpaMode:e,routeModules:t}=Qe(),{errors:r,matches:n,loaderData:a}=Yt(),i=xe(),o=Or(n,r,e),s=null;r&&(s=r[o[o.length-1].route.id]);let l=[],c=null,h=[];for(let u=0;u<o.length;u++){let m=o[u],g=m.route.id,y=a[g],E=m.params,R=t[g],P=[],b={id:g,data:y,meta:[],params:m.params,pathname:m.pathname,handle:m.route.handle,error:s};if(h[u]=b,R!=null&&R.meta?P=typeof R.meta=="function"?R.meta({data:y,params:E,location:i,matches:h,error:s}):Array.isArray(R.meta)?[...R.meta]:R.meta:c&&(P=[...c]),P=P||[],!Array.isArray(P))throw new Error("The route at "+m.route.path+` returns an invalid value. All route meta functions must return an array of meta objects.

To reference the meta function API, see https://remix.run/route/meta`);b.meta=P,h[u]=b,l=[...P],c=l}return d.createElement(d.Fragment,null,l.flat().map(u=>{if(!u)return null;if("tagName"in u){let{tagName:m,...g}=u;if(!kl(m))return console.warn(`A meta object uses an invalid tagName: ${m}. Expected either 'link' or 'meta'`),null;let y=m;return d.createElement(y,ee({key:JSON.stringify(g)},g))}if("title"in u)return d.createElement("title",{key:"title"},String(u.title));if("charset"in u&&(u.charSet??(u.charSet=u.charset),delete u.charset),"charSet"in u&&u.charSet!=null)return typeof u.charSet=="string"?d.createElement("meta",{key:"charSet",charSet:u.charSet}):null;if("script:ld+json"in u)try{let m=JSON.stringify(u["script:ld+json"]);return d.createElement("script",{key:`script:ld+json:${m}`,type:"application/ld+json",dangerouslySetInnerHTML:{__html:m}})}catch{return null}return d.createElement("meta",ee({key:JSON.stringify(u)},u))}))}function kl(e){return typeof e=="string"&&/^(meta|link)$/.test(e)}function jl(e){return d.createElement(Ki,e)}let kt=!1;function Il(e){let{manifest:t,serverHandoffString:r,abortDelay:n,serializeError:a,isSpaMode:i,future:o,renderMeta:s}=Qe(),{router:l,static:c,staticContext:h}=ra(),{matches:u}=Yt(),m=Cr(o,i);s&&(s.didRenderScripts=!0);let g=Or(u,null,i);d.useEffect(()=>{kt=!0},[]);let y=(_,v)=>{let C;return a&&v instanceof Error?C=a(v):C=v,`${JSON.stringify(_)}:__remixContext.p(!1, ${Tt(JSON.stringify(C))})`},E=(_,v,C)=>{let $;try{$=JSON.stringify(C)}catch(j){return y(v,j)}return`${JSON.stringify(v)}:__remixContext.p(${Tt($)})`},R=(_,v,C)=>{let $;return a&&C instanceof Error?$=a(C):$=C,`__remixContext.r(${JSON.stringify(_)}, ${JSON.stringify(v)}, !1, ${Tt(JSON.stringify($))})`},P=(_,v,C)=>{let $;try{$=JSON.stringify(C)}catch(j){return R(_,v,j)}return`__remixContext.r(${JSON.stringify(_)}, ${JSON.stringify(v)}, ${Tt($)})`},b=[],D=d.useMemo(()=>{var _;let v=o.v3_singleFetch?"window.__remixContext.stream = new ReadableStream({start(controller){window.__remixContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());":"",C=h?`window.__remixContext = ${r};${v}`:" ",$=o.v3_singleFetch||h==null?void 0:h.activeDeferreds;C+=$?["__remixContext.p = function(v,e,p,x) {","  if (typeof e !== 'undefined') {",`    x=new Error(e.message);
    x.stack=e.stack;`,"    p=Promise.reject(x);","  } else {","    p=Promise.resolve(v);","  }","  return p;","};","__remixContext.n = function(i,k) {","  __remixContext.t = __remixContext.t || {};","  __remixContext.t[i] = __remixContext.t[i] || {};","  let p = new Promise((r, e) => {__remixContext.t[i][k] = {r:(v)=>{r(v);},e:(v)=>{e(v);}};});",typeof n=="number"?`setTimeout(() => {if(typeof p._error !== "undefined" || typeof p._data !== "undefined"){return;} __remixContext.t[i][k].e(new Error("Server timeout."))}, ${n});`:"","  return p;","};","__remixContext.r = function(i,k,v,e,p,x) {","  p = __remixContext.t[i][k];","  if (typeof e !== 'undefined') {",`    x=new Error(e.message);
    x.stack=e.stack;`,"    p.e(x);","  } else {","    p.r(v);","  }","};"].join(`
`)+Object.entries($).map(([Y,W])=>{let ie=new Set(W.pendingKeys),ae=W.deferredKeys.map(te=>{if(ie.has(te))return b.push(d.createElement(wn,{key:`${Y} | ${te}`,deferredData:W,routeId:Y,dataKey:te,scriptProps:e,serializeData:P,serializeError:R})),`${JSON.stringify(te)}:__remixContext.n(${JSON.stringify(Y)}, ${JSON.stringify(te)})`;{let Le=W.data[te];return typeof Le._error<"u"?y(te,Le._error):E(Y,te,Le._data)}}).join(`,
`);return`Object.assign(__remixContext.state.loaderData[${JSON.stringify(Y)}], {${ae}});`}).join(`
`)+(b.length>0?`__remixContext.a=${b.length};`:""):"";let j=c?`${(_=t.hmr)!==null&&_!==void 0&&_.runtime?`import ${JSON.stringify(t.hmr.runtime)};`:""}${m?"":`import ${JSON.stringify(t.url)}`};
${g.map((Y,W)=>`import * as route${W} from ${JSON.stringify(t.routes[Y.route.id].module)};`).join(`
`)}
${m?`window.__remixManifest = ${JSON.stringify(Cl(t,l),null,2)};`:""}
window.__remixRouteModules = {${g.map((Y,W)=>`${JSON.stringify(Y.route.id)}:route${W}`).join(",")}};

import(${JSON.stringify(t.entry.module)});`:" ";return d.createElement(d.Fragment,null,d.createElement("script",ee({},e,{suppressHydrationWarning:!0,dangerouslySetInnerHTML:pn(C),type:void 0})),d.createElement("script",ee({},e,{suppressHydrationWarning:!0,dangerouslySetInnerHTML:pn(j),type:"module",async:!0})))},[]);if(!c&&typeof __remixContext=="object"&&__remixContext.a)for(let _=0;_<__remixContext.a;_++)b.push(d.createElement(wn,{key:_,scriptProps:e,serializeData:P,serializeError:R}));let x=g.map(_=>{let v=t.routes[_.route.id];return(v.imports||[]).concat([v.module])}).flat(1),F=kt?[]:t.entry.imports.concat(x);return kt?null:d.createElement(d.Fragment,null,m?null:d.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin}),d.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin}),$l(F).map(_=>d.createElement("link",{key:_,rel:"modulepreload",href:_,crossOrigin:e.crossOrigin})),D,b)}function wn({dataKey:e,deferredData:t,routeId:r,scriptProps:n,serializeData:a,serializeError:i}){return typeof document>"u"&&t&&e&&r&&Re(t.pendingKeys.includes(e),`Deferred data for route ${r} with key ${e} was not pending but tried to render a script for it.`),d.createElement(d.Suspense,{fallback:typeof document>"u"&&t&&e&&r?null:d.createElement("script",ee({},n,{async:!0,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:" "}}))},typeof document>"u"&&t&&e&&r?d.createElement(jl,{resolve:t.data[e],errorElement:d.createElement(Ul,{dataKey:e,routeId:r,scriptProps:n,serializeError:i}),children:o=>d.createElement("script",ee({},n,{async:!0,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:a(r,e,o)}}))}):d.createElement("script",ee({},n,{async:!0,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:" "}})))}function Ul({dataKey:e,routeId:t,scriptProps:r,serializeError:n}){let a=zi();return d.createElement("script",ee({},r,{suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:n(t,e,a)}}))}function $l(e){return[...new Set(e)]}function ns(){return $i()}function as(){return Hi()}function ia(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}export{Ie as E,Nl as F,ts as L,rs as M,Vl as O,na as R,Il as S,ee as _,Qe as a,xe as b,Ui as c,Xl as d,as as e,yl as f,Dr as g,Wl as h,Re as i,ql as j,Gl as k,zl as l,be as m,Zl as n,es as o,Ql as p,Kl as q,Yl as r,Ll as s,Ml as t,ns as u,Fn as v};
