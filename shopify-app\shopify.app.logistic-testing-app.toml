# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "0e950c1153128703e47cf026dcd8be41"
name = "logistic-testing-app"
handle = "logistic-testing-app"
application_url = "https://explicitly-copyrighted-series-em.trycloudflare.com"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-07"
[[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customers,read_orders,write_products"

[auth]
redirect_urls = ["https://explicitly-copyrighted-series-em.trycloudflare.com/auth/callback", "https://explicitly-copyrighted-series-em.trycloudflare.com/auth/shopify/callback", "https://explicitly-copyrighted-series-em.trycloudflare.com/api/auth/callback"]

[pos]
embedded = false
