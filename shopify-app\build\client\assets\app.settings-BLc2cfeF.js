import{j as o}from"./jsx-runtime-DlxonYWr.js";import{u as r}from"./components-7DQbiWt_.js";import"./index-y-Yv8VR0.js";function i(){const{apiToken:n}=r();return o.jsxs("main",{style:{padding:20},children:[o.jsx("h2",{children:"Account Setting<PERSON>"}),o.jsx("p",{children:o.jsx("strong",{children:"API Token:"})}),o.jsx("code",{style:{wordBreak:"break-all",backgroundColor:"#f5f5f5",padding:8,borderRadius:4},children:n}),o.jsx("p",{children:"You can copy this API token and paste it into your Shopify app to connect your store."})]})}export{i as default};
