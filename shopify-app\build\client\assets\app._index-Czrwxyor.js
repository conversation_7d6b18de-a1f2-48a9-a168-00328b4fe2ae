const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.es-DFQKMsfX.js","assets/index-y-Yv8VR0.js","assets/jsx-runtime-DlxonYWr.js","assets/components-7DQbiWt_.js","assets/Page-CD-2c-Tv.js","assets/context-QCCGjp8E.js","assets/Layout-DyjgeZaH.js","assets/context-C0av8SGa.js"])))=>i.map(i=>d[i]);
import{j as ce}from"./jsx-runtime-DlxonYWr.js";import{r as de,R,g as Tf,e as as}from"./index-y-Yv8VR0.js";import{u as pd}from"./components-7DQbiWt_.js";import{c as ct,v as ki,g as jf,e as ho,s as Bf,T as Ye,f as gd,h as vo,j as md,M as yc,W as wc,u as Zr,d as Xr,k as bd,a as ut,I as Ot,B as Ir,l as Mf,m as vd,n as po,S as yd,o as ys,p as Rf,A as Ff,q as Zl,r as wd,w as Of,t as ws,K as Ql,x as ec,y as xd,z as Ad,D as Sd,E as Nd,F as su,G as Ld,H as Pd,J as xs,L as _d,N as or,O as kd,Q as Cd,R as Ed,P as gs,C as br,b as Si}from"./Page-CD-2c-Tv.js";import{B as Id,L as Bn}from"./Layout-DyjgeZaH.js";import{k as Td,u as jd,f as Bd,o as Md,E as Df,x as Rd,r as qf,d as mo}from"./context-QCCGjp8E.js";import{S as Fd,F as Od}from"./context-C0av8SGa.js";let Ni;(function(t){t.All="all",t.Page="page",t.Multi="multi",t.Single="single",t.Range="range"})(Ni||(Ni={}));function lu(t){if("id"in t)return t.id;throw new Error("Your resource does not directly contain an `id`. Pass a `resourceIDResolver` to `useIndexResourceState`")}function Dd(t,{selectedResources:e=[],allResourcesSelected:n=!1,resourceIDResolver:r=lu,resourceFilter:o=void 0}={selectedResources:[],allResourcesSelected:!1,resourceIDResolver:lu,resourceFilter:void 0}){const[i,s]=de.useState(e),[l,u]=de.useState(n),p=de.useCallback((v,d,_,S)=>{switch(v===Ni.All?u(d):l&&u(!1),v){case Ni.Single:s(T=>d?[...T,_]:T.filter(A=>A!==_));break;case Ni.All:case Ni.Page:if(o){const T=t.filter(o);s(d&&i.length<T.length?T.map(r):[])}else s(d?t.map(r):[]);break;case Ni.Multi:if(!_)break;s(T=>{const A=[],j=o?t.filter(o):t;for(let q=_[0];q<=_[1];q++)if(j.includes(t[q])){const Y=r(t[q]);(d&&!T.includes(Y)||!d&&T.includes(Y))&&A.push(Y)}return d?[...T,...A]:T.filter(q=>!A.includes(q))});break;case Ni.Range:if(!_)break;s(T=>{const q=(o?t.filter(o):t).map(r).slice(Number(_[0]),Number(_[1])+1),Y=q.some(F=>i.includes(F));return!q.every(F=>i.includes(F))&&(d||Y)?[...new Set([...T,...q]).values()]:T.filter(F=>!q.includes(F))});break}},[l,o,i,t,r]),g=de.useCallback(()=>{s([]),u(!1)},[]),b=de.useCallback(v=>{const _=[...i].filter(S=>!v.includes(S));s(_),_.length===0&&u(!1)},[i]);return{selectedResources:i,allResourcesSelected:l,handleSelectionChange:p,clearSelection:g,removeSelectedResources:b}}var Uf=function(e){return R.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),R.createElement("path",{d:"M10 6a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5a.75.75 0 0 1 .75-.75Z"}),R.createElement("path",{d:"M11 13a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"}),R.createElement("path",{fillRule:"evenodd",d:"M11.237 3.177a1.75 1.75 0 0 0-2.474 0l-5.586 5.585a1.75 1.75 0 0 0 0 2.475l5.586 5.586a1.75 1.75 0 0 0 2.474 0l5.586-5.586a1.75 1.75 0 0 0 0-2.475l-5.586-5.585Zm-1.414 1.06a.25.25 0 0 1 .354 0l5.586 5.586a.25.25 0 0 1 0 .354l-5.586 5.585a.25.25 0 0 1-.354 0l-5.586-5.585a.25.25 0 0 1 0-.354l5.586-5.586Z"}))};Uf.displayName="AlertDiamondIcon";var zf=function(e){return R.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),R.createElement("path",{d:"M10 6.75a.75.75 0 0 1 .75.75v3.5a.75.75 0 1 1-1.5 0v-3.5a.75.75 0 0 1 .75-.75Z"}),R.createElement("path",{d:"M11 13.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"}),R.createElement("path",{fillRule:"evenodd",d:"M10 3.5c-1.045 0-1.784.702-2.152 1.447a449.26 449.26 0 0 1-2.005 3.847l-.028.052a403.426 403.426 0 0 0-2.008 3.856c-.372.752-.478 1.75.093 2.614.57.863 1.542 1.184 2.464 1.184h7.272c.922 0 1.895-.32 2.464-1.184.57-.864.465-1.862.093-2.614-.21-.424-1.113-2.147-2.004-3.847l-.032-.061a429.497 429.497 0 0 1-2.005-3.847c-.368-.745-1.107-1.447-2.152-1.447Zm-.808 2.112c.404-.816 1.212-.816 1.616 0 .202.409 1.112 2.145 2.022 3.88a418.904 418.904 0 0 1 2.018 3.875c.404.817 0 1.633-1.212 1.633h-7.272c-1.212 0-1.617-.816-1.212-1.633.202-.408 1.113-2.147 2.023-3.883a421.932 421.932 0 0 0 2.017-3.872Z"}))};zf.displayName="AlertTriangleIcon";var Hf=function(e){return R.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),R.createElement("path",{fillRule:"evenodd",d:"M15.78 5.97a.75.75 0 0 1 0 1.06l-6.5 6.5a.75.75 0 0 1-1.06 0l-3.25-3.25a.75.75 0 1 1 1.06-1.06l2.72 2.72 5.97-5.97a.75.75 0 0 1 1.06 0Z"}))};Hf.displayName="CheckIcon";var Wf=function(e){return R.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),R.createElement("path",{d:"M10 14a.75.75 0 0 1-.75-.75v-3.5a.75.75 0 0 1 1.5 0v3.5a.75.75 0 0 1-.75.75Z"}),R.createElement("path",{d:"M9 7a1 1 0 1 1 2 0 1 1 0 0 1-2 0Z"}),R.createElement("path",{fillRule:"evenodd",d:"M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Zm-1.5 0a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0Z"}))};Wf.displayName="InfoIcon";var Vf=function(e){return R.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),R.createElement("path",{fillRule:"evenodd",d:"M5 10c0-.414.336-.75.75-.75h8.5c.414 0 .75.336.75.75s-.336.75-.75.75h-8.5c-.414 0-.75-.336-.75-.75Z"}))};Vf.displayName="MinusIcon";var tc=function(e){return R.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),R.createElement("path",{fillRule:"evenodd",d:"M9.116 4.323a1.25 1.25 0 0 1 1.768 0l2.646 2.647a.75.75 0 0 1-1.06 1.06l-2.47-2.47-2.47 2.47a.75.75 0 1 1-1.06-1.06l2.646-2.647Z"}),R.createElement("path",{fillOpacity:.33,fillRule:"evenodd",d:"M9.116 15.677a1.25 1.25 0 0 0 1.768 0l2.646-2.647a.75.75 0 0 0-1.06-1.06l-2.47 2.47-2.47-2.47a.75.75 0 0 0-1.06 1.06l2.646 2.647Z"}))};tc.displayName="SortAscendingIcon";var nc=function(e){return R.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),R.createElement("path",{fillOpacity:.33,fillRule:"evenodd",d:"M9.116 4.823a1.25 1.25 0 0 1 1.768 0l2.646 2.647a.75.75 0 0 1-1.06 1.06l-2.47-2.47-2.47 2.47a.75.75 0 1 1-1.06-1.06l2.646-2.647Z"}),R.createElement("path",{fillRule:"evenodd",d:"M9.116 15.177a1.25 1.25 0 0 0 1.768 0l2.646-2.647a.75.75 0 0 0-1.06-1.06l-2.47 2.47-2.47-2.47a.75.75 0 0 0-1.06 1.06l2.646 2.647Z"}))};nc.displayName="SortDescendingIcon";var xc=function(e){return R.createElement("svg",Object.assign({viewBox:"0 0 20 20"},e),R.createElement("path",{d:"M12.72 13.78a.75.75 0 1 0 1.06-1.06l-2.72-2.72 2.72-2.72a.75.75 0 0 0-1.06-1.06l-2.72 2.72-2.72-2.72a.75.75 0 0 0-1.06 1.06l2.72 2.72-2.72 2.72a.75.75 0 1 0 1.06 1.06l2.72-2.72 2.72 2.72Z"}))};xc.displayName="XIcon";function qd({alt:t,sourceSet:e,source:n,crossOrigin:r,onLoad:o,className:i,...s}){const l=e?e.map(({source:p,descriptor:g})=>`${p} ${g}`).join(","):null,u=de.useCallback(()=>{o&&o()},[o]);return R.createElement("img",Object.assign({alt:t,src:n,crossOrigin:r,className:i,onLoad:u},l?{srcSet:l}:{},s))}const Ud=de.createContext(!1);var Pr={Checkbox:"Polaris-Checkbox",ChoiceLabel:"Polaris-Checkbox__ChoiceLabel",Backdrop:"Polaris-Checkbox__Backdrop",Input:"Polaris-Checkbox__Input","Input-indeterminate":"Polaris-Checkbox__Input--indeterminate",Icon:"Polaris-Checkbox__Icon",animated:"Polaris-Checkbox--animated",toneMagic:"Polaris-Checkbox--toneMagic",hover:"Polaris-Checkbox--hover",error:"Polaris-Checkbox--error",checked:"Polaris-Checkbox--checked",pathAnimation:"Polaris-Checkbox--pathAnimation"},$r={Choice:"Polaris-Choice",labelHidden:"Polaris-Choice--labelHidden",Label:"Polaris-Choice__Label",Control:"Polaris-Choice__Control",disabled:"Polaris-Choice--disabled",toneMagic:"Polaris-Choice--toneMagic",Descriptions:"Polaris-Choice__Descriptions",HelpText:"Polaris-Choice__HelpText"};function zd({id:t,label:e,disabled:n,error:r,children:o,labelHidden:i,helpText:s,onClick:l,labelClassName:u,fill:p,bleed:g,bleedBlockStart:b,bleedBlockEnd:v,bleedInlineStart:d,bleedInlineEnd:_,tone:S}){const T=ct($r.Choice,i&&$r.labelHidden,n&&$r.disabled,S&&$r[ki("tone",S)],u),A={...ho("choice","bleed-block-end","space",v||g),...ho("choice","bleed-block-start","space",b||g),...ho("choice","bleed-inline-start","space",d||g),...ho("choice","bleed-inline-end","space",_||g),...Object.fromEntries(Object.entries(jf("choice","fill",p)).map(([ae,$])=>[ae,$?"100%":"auto"]))},j=R.createElement("label",{className:T,htmlFor:t,onClick:l,style:Bf(A)},R.createElement("span",{className:$r.Control},o),R.createElement("span",{className:$r.Label},R.createElement(Ye,{as:"span",variant:"bodyMd"},e))),q=s?R.createElement("div",{className:$r.HelpText,id:Gf(t)},R.createElement(Ye,{as:"span",tone:n?void 0:"subdued"},s)):null,Y=r&&typeof r!="boolean"&&R.createElement("div",{className:$r.Error},R.createElement(gd,{message:r,fieldID:t})),le=q||Y?R.createElement("div",{className:$r.Descriptions},Y,q):null;return le?R.createElement("div",null,j,le):j}function Gf(t){return`${t}HelpText`}const Ac=de.forwardRef(function({ariaControls:e,ariaDescribedBy:n,label:r,labelHidden:o,checked:i=!1,helpText:s,disabled:l,id:u,name:p,value:g,error:b,onChange:v,onFocus:d,onBlur:_,labelClassName:S,fill:T,bleed:A,bleedBlockStart:j,bleedBlockEnd:q,bleedInlineStart:Y,bleedInlineEnd:le,tone:ae},$){const F=de.useRef(null),J=de.useId(),re=u??J,N=de.useContext(Ud);de.useImperativeHandle($,()=>({focus:()=>{F.current&&F.current.focus()}}));const k=()=>{_&&_()},O=()=>{v==null||F.current==null||l||(v(F.current.checked,re),F.current.focus())},C=[];b&&typeof b!="boolean"&&C.push(md(re)),s&&C.push(Gf(re)),n&&C.push(n);const X=C.length?C.join(" "):void 0,ne=ct(Pr.Checkbox,b&&Pr.error),ue=i==="indeterminate",Q=!ue&&!!i,he=ue?{indeterminate:"true","aria-checked":"mixed"}:{"aria-checked":Q},V=R.createElement("svg",{viewBox:"0 0 16 16",shapeRendering:"geometricPrecision",textRendering:"geometricPrecision"},R.createElement("path",{className:ct(i&&Pr.checked),d:"M1.5,5.5L3.44655,8.22517C3.72862,8.62007,4.30578,8.64717,4.62362,8.28044L10.5,1.5",transform:"translate(2 2.980376)",opacity:"0",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",pathLength:"1"})),me=ct(Pr.Input,ue&&Pr["Input-indeterminate"],ae&&Pr[ki("tone",ae)]),x={helpText:s,error:b,bleed:A,bleedBlockStart:j,bleedBlockEnd:q,bleedInlineStart:Y,bleedInlineEnd:le};return R.createElement(zd,Object.assign({id:re,label:r,labelHidden:o,disabled:l,labelClassName:ct(Pr.ChoiceLabel,S),fill:T,tone:ae},x),R.createElement("span",{className:ne},R.createElement("input",Object.assign({ref:F,id:re,name:p,value:g,type:"checkbox",checked:Q,disabled:l,className:me,onBlur:k,onChange:Hd,onClick:O,onFocus:d,"aria-invalid":b!=null,"aria-controls":e,"aria-describedby":X,role:N?"presentation":"checkbox"},he)),R.createElement("span",{className:Pr.Backdrop,onClick:cu,onKeyUp:cu}),R.createElement("span",{className:ct(Pr.Icon,!ue&&Pr.animated)},ue?R.createElement(vo,{source:Vf}):V)))});function Hd(){}function cu(t){t.stopPropagation()}var ll={Backdrop:"Polaris-Backdrop",transparent:"Polaris-Backdrop--transparent",belowNavigation:"Polaris-Backdrop--belowNavigation"};function Wd(){const t=de.useContext(Fd);if(!t)throw new yc("No ScrollLockManager was provided.");return t}function Vd(t){const e=Wd();return de.useEffect(()=>(e.registerScrollLock(),()=>{e.unregisterScrollLock()}),[e]),null}function Gd(t){const{onClick:e,onTouchStart:n,belowNavigation:r,transparent:o,setClosing:i}=t,s=ct(ll.Backdrop,r&&ll.belowNavigation,o&&ll.transparent),l=()=>{i&&i(!0)},u=()=>{i&&i(!1),e&&e()};return R.createElement(R.Fragment,null,R.createElement(Vd,null),R.createElement("div",{className:s,onClick:u,onTouchStart:n,onMouseDown:l}))}var Yi={Banner:"Polaris-Banner",keyFocused:"Polaris-Banner--keyFocused",withinContentContainer:"Polaris-Banner--withinContentContainer",withinPage:"Polaris-Banner--withinPage",DismissIcon:"Polaris-Banner__DismissIcon","text-success-on-bg-fill":"Polaris-Banner--textSuccessOnBgFill","text-success":"Polaris-Banner__text--success","text-warning-on-bg-fill":"Polaris-Banner--textWarningOnBgFill","text-warning":"Polaris-Banner__text--warning","text-critical-on-bg-fill":"Polaris-Banner--textCriticalOnBgFill","text-critical":"Polaris-Banner__text--critical","text-info-on-bg-fill":"Polaris-Banner--textInfoOnBgFill","text-info":"Polaris-Banner__text--info","icon-secondary":"Polaris-Banner__icon--secondary"};const cl={success:{withinPage:{background:"bg-fill-success",text:"text-success-on-bg-fill",icon:"text-success-on-bg-fill"},withinContentContainer:{background:"bg-surface-success",text:"text-success",icon:"text-success"},icon:Hf},warning:{withinPage:{background:"bg-fill-warning",text:"text-warning-on-bg-fill",icon:"text-warning-on-bg-fill"},withinContentContainer:{background:"bg-surface-warning",text:"text-warning",icon:"text-warning"},icon:zf},critical:{withinPage:{background:"bg-fill-critical",text:"text-critical-on-bg-fill",icon:"text-critical-on-bg-fill"},withinContentContainer:{background:"bg-surface-critical",text:"text-critical",icon:"text-critical"},icon:Uf},info:{withinPage:{background:"bg-fill-info",text:"text-info-on-bg-fill",icon:"text-info-on-bg-fill"},withinContentContainer:{background:"bg-surface-info",text:"text-info",icon:"text-info"},icon:Wf}};function Yd(t){const e=de.useRef(null),[n,r]=de.useState(!1);return de.useImperativeHandle(t,()=>({focus:()=>{var l;(l=e.current)==null||l.focus(),r(!0)}}),[]),{wrapperRef:e,handleKeyUp:l=>{l.target===e.current&&r(!0)},handleBlur:()=>r(!1),handleMouseUp:l=>{l.currentTarget.blur(),r(!1)},shouldShowFocus:n}}const Yf=de.forwardRef(function(e,n){const{tone:r,stopAnnouncements:o}=e,i=de.useContext(wc),{wrapperRef:s,handleKeyUp:l,handleBlur:u,handleMouseUp:p,shouldShowFocus:g}=Yd(n),b=ct(Yi.Banner,g&&Yi.keyFocused,i?Yi.withinContentContainer:Yi.withinPage);return R.createElement(Id.Provider,{value:!0},R.createElement("div",{className:b,tabIndex:0,ref:s,role:r==="warning"||r==="critical"?"alert":"status","aria-live":o?"off":"polite",onMouseUp:p,onKeyUp:l,onBlur:u},R.createElement($d,e)))});function $d({tone:t="info",icon:e,hideIcon:n,onDismiss:r,action:o,secondaryAction:i,title:s,children:l}){const u=Zr(),p=de.useContext(wc),g=!s&&!p,b=Object.keys(cl).includes(t)?t:"info",v=cl[b][p?"withinContentContainer":"withinPage"],d={backgroundColor:v.background,textColor:v.text,bannerTitle:s?R.createElement(Ye,{as:"h2",variant:"headingSm",breakWord:!0},s):null,bannerIcon:n?null:R.createElement("span",{className:Yi[v.icon]},R.createElement(vo,{source:e??cl[b].icon})),actionButtons:o||i?R.createElement(bd,null,o&&R.createElement(Xr,Object.assign({onClick:o.onAction},o),o.content),i&&R.createElement(Xr,Object.assign({onClick:i.onAction},i),i.content)):null,dismissButton:r?R.createElement(Xr,{variant:"tertiary",icon:R.createElement("span",{className:Yi[g?"icon-secondary":v.icon]},R.createElement(vo,{source:xc})),onClick:r,accessibilityLabel:u.translate("Polaris.Banner.dismissButton")}):null},_=l?R.createElement(Ye,{as:"span",variant:"bodyMd"},l):null;return p?R.createElement(Xd,d,_):g?R.createElement(Kd,d,_):R.createElement(Jd,d,_)}function Jd({backgroundColor:t,textColor:e,bannerTitle:n,bannerIcon:r,actionButtons:o,dismissButton:i,children:s}){const{smUp:l}=Td(),u=s||o;return R.createElement(ut,{width:"100%"},R.createElement(Ir,{align:"space-between"},R.createElement(ut,{background:t,color:e,borderStartStartRadius:l?"300":void 0,borderStartEndRadius:l?"300":void 0,borderEndStartRadius:!u&&l?"300":void 0,borderEndEndRadius:!u&&l?"300":void 0,padding:"300"},R.createElement(Ot,{align:"space-between",blockAlign:"center",gap:"200",wrap:!1},R.createElement(Ot,{gap:"100",wrap:!1},r,n),i)),u&&R.createElement(ut,{padding:{xs:"300",md:"400"},paddingBlockStart:"300"},R.createElement(Ir,{gap:"200"},R.createElement("div",null,s),o))))}function Kd({backgroundColor:t,bannerIcon:e,actionButtons:n,dismissButton:r,children:o}){const[i,s]=de.useState("center"),l=de.useRef(null),u=de.useRef(null),p=de.useRef(null),g=de.useCallback(()=>{var d,_,S;const b=(d=l.current)==null?void 0:d.offsetHeight,v=((_=u.current)==null?void 0:_.offsetHeight)||((S=p.current)==null?void 0:S.offsetHeight);!b||!v||(b>v?s("start"):s("center"))},[]);return de.useEffect(()=>g(),[g]),Mf("resize",g),R.createElement(ut,{width:"100%",padding:"300",borderRadius:"300"},R.createElement(Ot,{align:"space-between",blockAlign:i,wrap:!1},R.createElement(ut,{width:"100%"},R.createElement(Ot,{gap:"200",wrap:!1,blockAlign:i},e?R.createElement("div",{ref:u},R.createElement(ut,{background:t,borderRadius:"200",padding:"100"},e)):null,R.createElement(ut,{ref:l,width:"100%"},R.createElement(Ir,{gap:"200"},R.createElement("div",null,o),n)))),R.createElement("div",{ref:p,className:Yi.DismissIcon},r)))}function Xd({backgroundColor:t,textColor:e,bannerTitle:n,bannerIcon:r,actionButtons:o,dismissButton:i,children:s}){return R.createElement(ut,{width:"100%",background:t,padding:"200",borderRadius:"200",color:e},R.createElement(Ot,{align:"space-between",blockAlign:"start",wrap:!1,gap:"200"},R.createElement(Ot,{gap:"150",wrap:!1},r,R.createElement(ut,{width:"100%"},R.createElement(Ir,{gap:"200"},R.createElement(Ir,{gap:"050"},n,R.createElement("div",null,s)),o))),i))}function uu(t=[],e,n,r){const o=n.reduce((u,p)=>u+p,0),i=t.map((u,p)=>p),s=[],l=[];if(r>o)s.push(...i);else{let u=0,p=!1;i.forEach(g=>{const b=n[g];if(u+b>=r-e||p){l.push(g),p=!0;return}s.push(g),u+=b})}return{visiblePromotedActions:s,hiddenPromotedActions:l}}function Zd(t){const e=t.filter(n=>n.items);return t.length===e.length}function Qd(t){const e=t.filter(n=>!n.items);return t.length===e.length}function rc(t){return"title"in t&&"actions"in t}function e1(t){return"items"in t}function t1(t){if(!(!t||t.length===0)){if(Zd(t))return t;if(Qd(t))return[{items:t}]}}function fu(t){var e;if(!t)return!1;for(const n of t)for(const r of n.items)if(((e=r.badge)==null?void 0:e.tone)==="new")return!0;return!1}var Kr={BulkActionsOuterLayout:"Polaris-BulkActions__BulkActionsOuterLayout",BulkActionsSelectAllWrapper:"Polaris-BulkActions__BulkActionsSelectAllWrapper",BulkActionsPromotedActionsWrapper:"Polaris-BulkActions__BulkActionsPromotedActionsWrapper",BulkActionsLayout:"Polaris-BulkActions__BulkActionsLayout","BulkActionsLayout--measuring":"Polaris-BulkActions--bulkActionsLayoutMeasuring",BulkActionsMeasurerLayout:"Polaris-BulkActions__BulkActionsMeasurerLayout",BulkActionButton:"Polaris-BulkActions__BulkActionButton",AllAction:"Polaris-BulkActions__AllAction"},hu={Indicator:"Polaris-Indicator",pulseIndicator:"Polaris-Indicator--pulseIndicator"};function n1({pulse:t=!0}){const e=ct(hu.Indicator,t&&hu.pulseIndicator);return R.createElement("span",{className:e})}function Ea({handleMeasurement:t,url:e,external:n,onAction:r,content:o,disclosure:i,accessibilityLabel:s,disabled:l,destructive:u,indicator:p,showContentInButton:g,size:b}){const v=de.useRef(null);vd(()=>{if(t&&v.current){const T=v.current.getBoundingClientRect().width;t(T)}});const d=i&&!g,_=d?void 0:o,S=R.createElement(Xr,{external:n,url:e,accessibilityLabel:d?o:s,tone:u?"critical":void 0,disclosure:i&&g,onClick:r,disabled:l,size:b,icon:d?R.createElement(vo,{source:yd,tone:"base"}):void 0},_);return R.createElement("div",{className:Kr.BulkActionButton,ref:v},d?R.createElement(po,{content:o,preferredPosition:"below"},S):S,p&&R.createElement(n1,null))}function r1({title:t,actions:e,isNewBadgeInBadgeActions:n,size:r}){const{value:o,toggle:i}=ys(!1);return R.createElement(R.Fragment,null,R.createElement(Rf,{active:o,activator:R.createElement(Ea,{disclosure:!0,showContentInButton:!0,onAction:i,content:t,indicator:n,size:r}),onClose:i,preferInputActivator:!0},R.createElement(Ff,{items:e,onActionAnyItem:i})))}var ul={CheckableButton:"Polaris-CheckableButton",Checkbox:"Polaris-CheckableButton__Checkbox",Label:"Polaris-CheckableButton__Label"};const i1=de.forwardRef(function({accessibilityLabel:e,label:n="",onToggleAll:r,selected:o,disabled:i,ariaLive:s},l){const u=de.useRef(null);function p(){var g;(g=u==null?void 0:u.current)==null||g.focus()}return de.useImperativeHandle(l,()=>({focus:p})),R.createElement("div",{className:ul.CheckableButton,onClick:r},R.createElement("div",{className:ul.Checkbox},R.createElement(Ac,{label:e,labelHidden:!0,checked:o,disabled:i,onChange:r,ref:u})),n?R.createElement("span",{className:ul.Label,"aria-live":s},R.createElement(Ye,{as:"span",variant:"bodySm",fontWeight:"medium"},n)):null)}),a1=4;function o1({promotedActions:t=[],disabled:e,buttonSize:n,handleMeasurement:r}){const o=Zr(),i=de.useRef(null),s=o.translate("Polaris.ResourceList.BulkActions.moreActionsActivatorLabel"),l=R.createElement(Ea,{disclosure:!0,content:s}),u=de.useCallback(()=>{if(!i.current)return;const g=i.current.offsetWidth,b=i.current.children,d=Array.from(b).map(S=>Math.ceil(S.getBoundingClientRect().width)+a1),_=d.pop()||0;r({containerWidth:g,disclosureWidth:_,hiddenActionsWidths:d})},[r]);de.useEffect(()=>{u()},[u,t]);const p=t.map((g,b)=>rc(g)?R.createElement(Ea,{key:b,disclosure:!0,showContentInButton:!0,content:g.title,size:n}):R.createElement(Ea,Object.assign({key:b,disabled:e},g,{size:n})));return Mf("resize",u),R.createElement("div",{className:Kr.BulkActionsMeasurerLayout,ref:i},p,l)}const s1=de.forwardRef(function({promotedActions:e,actions:n,disabled:r,buttonSize:o,paginatedSelectAllAction:i,paginatedSelectAllText:s,label:l,accessibilityLabel:u,selected:p,onToggleAll:g,onMoreActionPopoverToggle:b,width:v,selectMode:d},_){const S=Zr(),[T,A]=de.useState(!1),[j,q]=de.useReducer((H,G)=>({...H,...G}),{disclosureWidth:0,containerWidth:1/0,actionsWidths:[],visiblePromotedActions:[],hiddenPromotedActions:[],hasMeasured:!1}),{visiblePromotedActions:Y,hiddenPromotedActions:le,containerWidth:ae,disclosureWidth:$,actionsWidths:F,hasMeasured:J}=j;de.useEffect(()=>{if(ae===0||!e||e.length===0)return;const{visiblePromotedActions:H,hiddenPromotedActions:G}=uu(e,$,F,ae);q({visiblePromotedActions:H,hiddenPromotedActions:G,hasMeasured:ae!==1/0})},[ae,$,e,F]);const re=!e||e&&Y.length===0?S.translate("Polaris.ResourceList.BulkActions.actionsActivatorLabel"):S.translate("Polaris.ResourceList.BulkActions.moreActionsActivatorLabel"),N=i?R.createElement(Zl,{className:Kr.AllAction,onClick:i.onAction,size:"slim",disabled:r},R.createElement(Ye,{as:"span",variant:"bodySm",fontWeight:"medium"},i.content)):null,k=s&&i,C={accessibilityLabel:u,label:k?s:l,selected:p,onToggleAll:g,disabled:r,ariaLive:k?"polite":void 0,ref:_},X=de.useCallback(()=>{b==null||b(T),A(H=>!H)},[b,T]),ne=de.useCallback(H=>{const{hiddenActionsWidths:G,containerWidth:ee,disclosureWidth:oe}=H;if(!e||e.length===0)return;const{visiblePromotedActions:ve,hiddenPromotedActions:Se}=uu(e,oe,G,ee);q({visiblePromotedActions:ve,hiddenPromotedActions:Se,actionsWidths:G,containerWidth:ee,disclosureWidth:oe,hasMeasured:!0})},[e]),ue=t1(n),Q=e?e.filter((H,G)=>!!Y.includes(G)).map((H,G)=>rc(H)?R.createElement(r1,Object.assign({key:G},H,{isNewBadgeInBadgeActions:fu(ue),size:o})):R.createElement(Ea,Object.assign({key:G,disabled:r},H,{size:o}))):null,me={items:le.map(H=>e==null?void 0:e[H]).reduce((H,G)=>G?rc(G)?H.concat(G.actions):H.concat(G):H,[])},x=de.useMemo(()=>{if(ue)return ue;if(!n)return[];let H=!0;return n.filter(G=>G).reduce((G,ee)=>{if(e1(ee))return H=!1,G.concat(ee);if(H){if(G.length===0)return[{items:[ee]}];const oe=G[G.length-1];return G.splice(G.length-1,1,{items:[...oe.items,ee]}),G}return H=!0,G.concat({items:[ee]})},[])},[n,ue]),B=R.createElement(Ea,{disclosure:!0,showContentInButton:!Q,onAction:X,content:re,disabled:r,indicator:fu(ue),size:o}),M=x.length>0?R.createElement(Rf,{active:T,activator:B,preferredAlignment:"right",onClose:X},R.createElement(Ff,{sections:me.items.length>0?[me,...x]:x,onActionAnyItem:X})):null,z=R.createElement(o1,{promotedActions:e,disabled:r,buttonSize:o,handleMeasurement:ne});return R.createElement("div",{className:Kr.BulkActions,style:v?{width:v}:void 0},R.createElement(Ot,{gap:"400",blockAlign:"center"},R.createElement("div",{className:Kr.BulkActionsSelectAllWrapper},R.createElement(i1,C),N),d?R.createElement("div",{className:Kr.BulkActionsPromotedActionsWrapper},R.createElement(Ot,{gap:"100",blockAlign:"center"},R.createElement("div",{className:Kr.BulkActionsOuterLayout},z,R.createElement("div",{className:ct(Kr.BulkActionsLayout,!J&&Kr["BulkActionsLayout--measuring"])},Q)),M)):null))});var Li={LegacyStack:"Polaris-LegacyStack",Item:"Polaris-LegacyStack__Item",noWrap:"Polaris-LegacyStack--noWrap",spacingNone:"Polaris-LegacyStack--spacingNone",spacingExtraTight:"Polaris-LegacyStack--spacingExtraTight",spacingTight:"Polaris-LegacyStack--spacingTight",spacingBaseTight:"Polaris-LegacyStack--spacingBaseTight",spacingLoose:"Polaris-LegacyStack--spacingLoose",spacingExtraLoose:"Polaris-LegacyStack--spacingExtraLoose",distributionLeading:"Polaris-LegacyStack--distributionLeading",distributionTrailing:"Polaris-LegacyStack--distributionTrailing",distributionCenter:"Polaris-LegacyStack--distributionCenter",distributionEqualSpacing:"Polaris-LegacyStack--distributionEqualSpacing",distributionFill:"Polaris-LegacyStack--distributionFill",distributionFillEvenly:"Polaris-LegacyStack--distributionFillEvenly",alignmentLeading:"Polaris-LegacyStack--alignmentLeading",alignmentTrailing:"Polaris-LegacyStack--alignmentTrailing",alignmentCenter:"Polaris-LegacyStack--alignmentCenter",alignmentFill:"Polaris-LegacyStack--alignmentFill",alignmentBaseline:"Polaris-LegacyStack--alignmentBaseline",vertical:"Polaris-LegacyStack--vertical","Item-fill":"Polaris-LegacyStack__Item--fill"};function $f({children:t,fill:e}){const n=ct(Li.Item,e&&Li["Item-fill"]);return R.createElement("div",{className:n},t)}const Sc=de.memo(function({children:e,vertical:n,spacing:r,distribution:o,alignment:i,wrap:s}){const l=ct(Li.LegacyStack,n&&Li.vertical,r&&Li[ki("spacing",r)],o&&Li[ki("distribution",o)],i&&Li[ki("alignment",i)],s===!1&&Li.noWrap),u=wd(e).map((p,g)=>Of(p,$f,{key:g}));return R.createElement("div",{className:l},u)});Sc.Item=$f;var l1={InlineGrid:"Polaris-InlineGrid"};function c1({children:t,columns:e,gap:n,alignItems:r}){const o={...jf("inline-grid","grid-template-columns",u1(e)),...ho("inline-grid","gap","space",n),"--pc-inline-grid-align-items":r};return R.createElement("div",{className:l1.InlineGrid,style:Bf(o)},t)}function u1(t){return typeof t=="object"&&t!==null&&!Array.isArray(t)?Object.fromEntries(Object.entries(t).map(([e,n])=>[e,du(n)])):du(t)}function du(t){if(t)return typeof t=="number"||!isNaN(Number(t))?`repeat(${Number(t)}, minmax(0, 1fr))`:typeof t=="string"?t:t.map(e=>{switch(e){case"oneThird":return"minmax(0, 1fr)";case"oneHalf":return"minmax(0, 1fr)";case"twoThirds":return"minmax(0, 2fr)"}}).join(" ")}const f1=de.createContext(void 0);function h1({children:t,onMount:e,fallback:n=null}){const r=jd(),o=r?t:n;return de.useEffect(()=>{r&&e&&e()},[r,e]),R.createElement(R.Fragment,null,o)}function d1(){const t=de.useContext(Bd);if(!t)throw new yc("No StickyManager was provided.");return t}class p1 extends de.Component{constructor(...e){super(...e),this.state={isSticky:!1,style:{}},this.placeHolderNode=null,this.stickyNode=null,this.setPlaceHolderNode=n=>{this.placeHolderNode=n},this.setStickyNode=n=>{this.stickyNode=n},this.handlePositioning=(n,r=0,o=0,i=0)=>{const{isSticky:s}=this.state;(n&&!s||!n&&s)&&(this.adjustPlaceHolderNode(n),this.setState({isSticky:!s},()=>{if(this.props.onStickyChange==null||(this.props.onStickyChange(!s),this.props.boundingElement==null))return null;this.props.boundingElement.toggleAttribute("data-sticky-active")}));const l=n?{position:"fixed",top:r,left:o,width:i}:{};this.setState({style:l})},this.adjustPlaceHolderNode=n=>{this.placeHolderNode&&this.stickyNode&&(this.placeHolderNode.style.paddingBottom=n?`${Md(this.stickyNode).height}px`:"0px")}}componentDidMount(){const{boundingElement:e,offset:n=!1,disableWhenStacked:r=!1,stickyManager:o}=this.props;!this.stickyNode||!this.placeHolderNode||o.registerStickyItem({stickyNode:this.stickyNode,placeHolderNode:this.placeHolderNode,handlePositioning:this.handlePositioning,offset:n,boundingElement:e,disableWhenStacked:r})}componentWillUnmount(){const{stickyManager:e}=this.props;this.stickyNode&&e.unregisterStickyItem(this.stickyNode)}render(){const{style:e,isSticky:n}=this.state,{children:r}=this.props,o=g1(r)?r(n):r;return R.createElement("div",null,R.createElement("div",{ref:this.setPlaceHolderNode}),R.createElement("div",{ref:this.setStickyNode,style:e},o))}}function g1(t){return typeof t=="function"}function m1(t){const e=d1();return R.createElement(p1,Object.assign({},t,{stickyManager:e}))}var b1={Divider:"Polaris-Divider"};const pu=({borderColor:t="border-secondary",borderWidth:e="025"})=>{const n=t==="transparent"?t:`var(--p-color-${t})`;return R.createElement("hr",{className:b1.Divider,style:{borderBlockStart:`var(--p-border-width-${e}) solid ${n}`}})};var v1="data:image/svg+xml,%3csvg width='60' height='60' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill-rule='evenodd' d='M41.87 24a17.87 17.87 0 11-35.74 0 17.87 17.87 0 0135.74 0zm-3.15 18.96a24 24 0 114.24-4.24L59.04 54.8a3 3 0 11-4.24 4.24L38.72 42.96z' fill='%238C9196'/%3e%3c/svg%3e",y1=v1;function w1({title:t,description:e,withIllustration:n}){const o=Zr().translate("Polaris.EmptySearchResult.altText"),i=e?R.createElement("p",null,e):null,s=n?R.createElement(qd,{alt:o,source:y1,draggable:!1}):null;return R.createElement(Sc,{alignment:"center",vertical:!0},s,R.createElement(Ye,{variant:"headingLg",as:"p"},t),R.createElement(Ye,{tone:"subdued",as:"span"},i))}const x1=de.memo(function({children:e,disabled:n,root:r}){return de.useEffect(()=>{if(n||!r)return;const o=A1(r)?r.current:r;!o||o.querySelector("[autofocus]")||ws(o,!1)},[n,r]),R.createElement(R.Fragment,null,e)});function A1(t){return t.current!==void 0}function yo(){return yo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)({}).hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},yo.apply(null,arguments)}function Nc(t,e){if(t==null)return{};var n={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(e.indexOf(r)!==-1)continue;n[r]=t[r]}return n}function ic(t,e){return ic=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,r){return n.__proto__=r,n},ic(t,e)}function Lc(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,ic(t,e)}var fl={exports:{}},hl={exports:{}},xt={},gu;function S1(){if(gu)return xt;gu=1;/** @license React v16.13.1
 * react-is.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */return function(){var t=typeof Symbol=="function"&&Symbol.for,e=t?Symbol.for("react.element"):60103,n=t?Symbol.for("react.portal"):60106,r=t?Symbol.for("react.fragment"):60107,o=t?Symbol.for("react.strict_mode"):60108,i=t?Symbol.for("react.profiler"):60114,s=t?Symbol.for("react.provider"):60109,l=t?Symbol.for("react.context"):60110,u=t?Symbol.for("react.async_mode"):60111,p=t?Symbol.for("react.concurrent_mode"):60111,g=t?Symbol.for("react.forward_ref"):60112,b=t?Symbol.for("react.suspense"):60113,v=t?Symbol.for("react.suspense_list"):60120,d=t?Symbol.for("react.memo"):60115,_=t?Symbol.for("react.lazy"):60116,S=t?Symbol.for("react.block"):60121,T=t?Symbol.for("react.fundamental"):60117,A=t?Symbol.for("react.responder"):60118,j=t?Symbol.for("react.scope"):60119;function q(be){return typeof be=="string"||typeof be=="function"||be===r||be===p||be===i||be===o||be===b||be===v||typeof be=="object"&&be!==null&&(be.$$typeof===_||be.$$typeof===d||be.$$typeof===s||be.$$typeof===l||be.$$typeof===g||be.$$typeof===T||be.$$typeof===A||be.$$typeof===j||be.$$typeof===S)}function Y(be){if(typeof be=="object"&&be!==null){var Ce=be.$$typeof;switch(Ce){case e:var ze=be.type;switch(ze){case u:case p:case r:case i:case o:case b:return ze;default:var ye=ze&&ze.$$typeof;switch(ye){case l:case g:case _:case d:case s:return ye;default:return Ce}}case n:return Ce}}}var le=u,ae=p,$=l,F=s,J=e,re=g,N=r,k=_,O=d,C=n,X=i,ne=o,ue=b,Q=!1;function he(be){return Q||(Q=!0,console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.")),V(be)||Y(be)===u}function V(be){return Y(be)===p}function me(be){return Y(be)===l}function x(be){return Y(be)===s}function B(be){return typeof be=="object"&&be!==null&&be.$$typeof===e}function M(be){return Y(be)===g}function z(be){return Y(be)===r}function H(be){return Y(be)===_}function G(be){return Y(be)===d}function ee(be){return Y(be)===n}function oe(be){return Y(be)===i}function ve(be){return Y(be)===o}function Se(be){return Y(be)===b}xt.AsyncMode=le,xt.ConcurrentMode=ae,xt.ContextConsumer=$,xt.ContextProvider=F,xt.Element=J,xt.ForwardRef=re,xt.Fragment=N,xt.Lazy=k,xt.Memo=O,xt.Portal=C,xt.Profiler=X,xt.StrictMode=ne,xt.Suspense=ue,xt.isAsyncMode=he,xt.isConcurrentMode=V,xt.isContextConsumer=me,xt.isContextProvider=x,xt.isElement=B,xt.isForwardRef=M,xt.isFragment=z,xt.isLazy=H,xt.isMemo=G,xt.isPortal=ee,xt.isProfiler=oe,xt.isStrictMode=ve,xt.isSuspense=Se,xt.isValidElementType=q,xt.typeOf=Y}(),xt}var mu;function Jf(){return mu||(mu=1,hl.exports=S1()),hl.exports}/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var dl,bu;function N1(){if(bu)return dl;bu=1;var t=Object.getOwnPropertySymbols,e=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable;function r(i){if(i==null)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(i)}function o(){try{if(!Object.assign)return!1;var i=new String("abc");if(i[5]="de",Object.getOwnPropertyNames(i)[0]==="5")return!1;for(var s={},l=0;l<10;l++)s["_"+String.fromCharCode(l)]=l;var u=Object.getOwnPropertyNames(s).map(function(g){return s[g]});if(u.join("")!=="**********")return!1;var p={};return"abcdefghijklmnopqrst".split("").forEach(function(g){p[g]=g}),Object.keys(Object.assign({},p)).join("")==="abcdefghijklmnopqrst"}catch{return!1}}return dl=o()?Object.assign:function(i,s){for(var l,u=r(i),p,g=1;g<arguments.length;g++){l=Object(arguments[g]);for(var b in l)e.call(l,b)&&(u[b]=l[b]);if(t){p=t(l);for(var v=0;v<p.length;v++)n.call(l,p[v])&&(u[p[v]]=l[p[v]])}}return u},dl}var pl,vu;function Kf(){if(vu)return pl;vu=1;var t="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return pl=t,pl}var gl,yu;function Xf(){return yu||(yu=1,gl=Function.call.bind(Object.prototype.hasOwnProperty)),gl}var ml,wu;function L1(){if(wu)return ml;wu=1;var t=function(){};{var e=Kf(),n={},r=Xf();t=function(i){var s="Warning: "+i;typeof console<"u"&&console.error(s);try{throw new Error(s)}catch{}}}function o(i,s,l,u,p){for(var g in i)if(r(i,g)){var b;try{if(typeof i[g]!="function"){var v=Error((u||"React class")+": "+l+" type `"+g+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof i[g]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw v.name="Invariant Violation",v}b=i[g](s,g,u,l,null,e)}catch(_){b=_}if(b&&!(b instanceof Error)&&t((u||"React class")+": type specification of "+l+" `"+g+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof b+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),b instanceof Error&&!(b.message in n)){n[b.message]=!0;var d=p?p():"";t("Failed "+l+" type: "+b.message+(d??""))}}}return o.resetWarningCache=function(){n={}},ml=o,ml}var bl,xu;function P1(){if(xu)return bl;xu=1;var t=Jf(),e=N1(),n=Kf(),r=Xf(),o=L1(),i=function(){};i=function(l){var u="Warning: "+l;typeof console<"u"&&console.error(u);try{throw new Error(u)}catch{}};function s(){return null}return bl=function(l,u){var p=typeof Symbol=="function"&&Symbol.iterator,g="@@iterator";function b(V){var me=V&&(p&&V[p]||V[g]);if(typeof me=="function")return me}var v="<<anonymous>>",d={array:A("array"),bigint:A("bigint"),bool:A("boolean"),func:A("function"),number:A("number"),object:A("object"),string:A("string"),symbol:A("symbol"),any:j(),arrayOf:q,element:Y(),elementType:le(),instanceOf:ae,node:re(),objectOf:F,oneOf:$,oneOfType:J,shape:k,exact:O};function _(V,me){return V===me?V!==0||1/V===1/me:V!==V&&me!==me}function S(V,me){this.message=V,this.data=me&&typeof me=="object"?me:{},this.stack=""}S.prototype=Error.prototype;function T(V){var me={},x=0;function B(z,H,G,ee,oe,ve,Se){if(ee=ee||v,ve=ve||G,Se!==n){if(u){var be=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types");throw be.name="Invariant Violation",be}else if(typeof console<"u"){var Ce=ee+":"+G;!me[Ce]&&x<3&&(i("You are manually calling a React.PropTypes validation function for the `"+ve+"` prop on `"+ee+"`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details."),me[Ce]=!0,x++)}}return H[G]==null?z?H[G]===null?new S("The "+oe+" `"+ve+"` is marked as required "+("in `"+ee+"`, but its value is `null`.")):new S("The "+oe+" `"+ve+"` is marked as required in "+("`"+ee+"`, but its value is `undefined`.")):null:V(H,G,ee,oe,ve)}var M=B.bind(null,!1);return M.isRequired=B.bind(null,!0),M}function A(V){function me(x,B,M,z,H,G){var ee=x[B],oe=ne(ee);if(oe!==V){var ve=ue(ee);return new S("Invalid "+z+" `"+H+"` of type "+("`"+ve+"` supplied to `"+M+"`, expected ")+("`"+V+"`."),{expectedType:V})}return null}return T(me)}function j(){return T(s)}function q(V){function me(x,B,M,z,H){if(typeof V!="function")return new S("Property `"+H+"` of component `"+M+"` has invalid PropType notation inside arrayOf.");var G=x[B];if(!Array.isArray(G)){var ee=ne(G);return new S("Invalid "+z+" `"+H+"` of type "+("`"+ee+"` supplied to `"+M+"`, expected an array."))}for(var oe=0;oe<G.length;oe++){var ve=V(G,oe,M,z,H+"["+oe+"]",n);if(ve instanceof Error)return ve}return null}return T(me)}function Y(){function V(me,x,B,M,z){var H=me[x];if(!l(H)){var G=ne(H);return new S("Invalid "+M+" `"+z+"` of type "+("`"+G+"` supplied to `"+B+"`, expected a single ReactElement."))}return null}return T(V)}function le(){function V(me,x,B,M,z){var H=me[x];if(!t.isValidElementType(H)){var G=ne(H);return new S("Invalid "+M+" `"+z+"` of type "+("`"+G+"` supplied to `"+B+"`, expected a single ReactElement type."))}return null}return T(V)}function ae(V){function me(x,B,M,z,H){if(!(x[B]instanceof V)){var G=V.name||v,ee=he(x[B]);return new S("Invalid "+z+" `"+H+"` of type "+("`"+ee+"` supplied to `"+M+"`, expected ")+("instance of `"+G+"`."))}return null}return T(me)}function $(V){if(!Array.isArray(V))return arguments.length>1?i("Invalid arguments supplied to oneOf, expected an array, got "+arguments.length+" arguments. A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z])."):i("Invalid argument supplied to oneOf, expected an array."),s;function me(x,B,M,z,H){for(var G=x[B],ee=0;ee<V.length;ee++)if(_(G,V[ee]))return null;var oe=JSON.stringify(V,function(Se,be){var Ce=ue(be);return Ce==="symbol"?String(be):be});return new S("Invalid "+z+" `"+H+"` of value `"+String(G)+"` "+("supplied to `"+M+"`, expected one of "+oe+"."))}return T(me)}function F(V){function me(x,B,M,z,H){if(typeof V!="function")return new S("Property `"+H+"` of component `"+M+"` has invalid PropType notation inside objectOf.");var G=x[B],ee=ne(G);if(ee!=="object")return new S("Invalid "+z+" `"+H+"` of type "+("`"+ee+"` supplied to `"+M+"`, expected an object."));for(var oe in G)if(r(G,oe)){var ve=V(G,oe,M,z,H+"."+oe,n);if(ve instanceof Error)return ve}return null}return T(me)}function J(V){if(!Array.isArray(V))return i("Invalid argument supplied to oneOfType, expected an instance of array."),s;for(var me=0;me<V.length;me++){var x=V[me];if(typeof x!="function")return i("Invalid argument supplied to oneOfType. Expected an array of check functions, but received "+Q(x)+" at index "+me+"."),s}function B(M,z,H,G,ee){for(var oe=[],ve=0;ve<V.length;ve++){var Se=V[ve],be=Se(M,z,H,G,ee,n);if(be==null)return null;be.data&&r(be.data,"expectedType")&&oe.push(be.data.expectedType)}var Ce=oe.length>0?", expected one of type ["+oe.join(", ")+"]":"";return new S("Invalid "+G+" `"+ee+"` supplied to "+("`"+H+"`"+Ce+"."))}return T(B)}function re(){function V(me,x,B,M,z){return C(me[x])?null:new S("Invalid "+M+" `"+z+"` supplied to "+("`"+B+"`, expected a ReactNode."))}return T(V)}function N(V,me,x,B,M){return new S((V||"React class")+": "+me+" type `"+x+"."+B+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+M+"`.")}function k(V){function me(x,B,M,z,H){var G=x[B],ee=ne(G);if(ee!=="object")return new S("Invalid "+z+" `"+H+"` of type `"+ee+"` "+("supplied to `"+M+"`, expected `object`."));for(var oe in V){var ve=V[oe];if(typeof ve!="function")return N(M,z,H,oe,ue(ve));var Se=ve(G,oe,M,z,H+"."+oe,n);if(Se)return Se}return null}return T(me)}function O(V){function me(x,B,M,z,H){var G=x[B],ee=ne(G);if(ee!=="object")return new S("Invalid "+z+" `"+H+"` of type `"+ee+"` "+("supplied to `"+M+"`, expected `object`."));var oe=e({},x[B],V);for(var ve in oe){var Se=V[ve];if(r(V,ve)&&typeof Se!="function")return N(M,z,H,ve,ue(Se));if(!Se)return new S("Invalid "+z+" `"+H+"` key `"+ve+"` supplied to `"+M+"`.\nBad object: "+JSON.stringify(x[B],null,"  ")+`
Valid keys: `+JSON.stringify(Object.keys(V),null,"  "));var be=Se(G,ve,M,z,H+"."+ve,n);if(be)return be}return null}return T(me)}function C(V){switch(typeof V){case"number":case"string":case"undefined":return!0;case"boolean":return!V;case"object":if(Array.isArray(V))return V.every(C);if(V===null||l(V))return!0;var me=b(V);if(me){var x=me.call(V),B;if(me!==V.entries){for(;!(B=x.next()).done;)if(!C(B.value))return!1}else for(;!(B=x.next()).done;){var M=B.value;if(M&&!C(M[1]))return!1}}else return!1;return!0;default:return!1}}function X(V,me){return V==="symbol"?!0:me?me["@@toStringTag"]==="Symbol"||typeof Symbol=="function"&&me instanceof Symbol:!1}function ne(V){var me=typeof V;return Array.isArray(V)?"array":V instanceof RegExp?"object":X(me,V)?"symbol":me}function ue(V){if(typeof V>"u"||V===null)return""+V;var me=ne(V);if(me==="object"){if(V instanceof Date)return"date";if(V instanceof RegExp)return"regexp"}return me}function Q(V){var me=ue(V);switch(me){case"array":case"object":return"an "+me;case"boolean":case"date":case"regexp":return"a "+me;default:return me}}function he(V){return!V.constructor||!V.constructor.name?v:V.constructor.name}return d.checkPropTypes=o,d.resetWarningCache=o.resetWarningCache,d.PropTypes=d,d},bl}var Au;function _1(){if(Au)return fl.exports;Au=1;{var t=Jf(),e=!0;fl.exports=P1()(t.isElement,e)}return fl.exports}var k1=_1();const Je=Tf(k1);function C1(t,e){return t.classList?!!e&&t.classList.contains(e):(" "+(t.className.baseVal||t.className)+" ").indexOf(" "+e+" ")!==-1}function E1(t,e){t.classList?t.classList.add(e):C1(t,e)||(typeof t.className=="string"?t.className=t.className+" "+e:t.setAttribute("class",(t.className&&t.className.baseVal||"")+" "+e))}function Su(t,e){return t.replace(new RegExp("(^|\\s)"+e+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}function I1(t,e){t.classList?t.classList.remove(e):typeof t.className=="string"?t.className=Su(t.className,e):t.setAttribute("class",Su(t.className&&t.className.baseVal||"",e))}const Nu={disabled:!1};var T1=Je.oneOfType([Je.number,Je.shape({enter:Je.number,exit:Je.number,appear:Je.number}).isRequired]),j1=Je.oneOfType([Je.string,Je.shape({enter:Je.string,exit:Je.string,active:Je.string}),Je.shape({enter:Je.string,enterDone:Je.string,enterActive:Je.string,exit:Je.string,exitDone:Je.string,exitActive:Je.string})]);const ms=R.createContext(null);var Zf=function(e){return e.scrollTop},go="unmounted",Hi="exited",Wi="entering",Pa="entered",ac="exiting",vr=function(t){Lc(e,t);function e(r,o){var i;i=t.call(this,r,o)||this;var s=o,l=s&&!s.isMounting?r.enter:r.appear,u;return i.appearStatus=null,r.in?l?(u=Hi,i.appearStatus=Wi):u=Pa:r.unmountOnExit||r.mountOnEnter?u=go:u=Hi,i.state={status:u},i.nextCallback=null,i}e.getDerivedStateFromProps=function(o,i){var s=o.in;return s&&i.status===go?{status:Hi}:null};var n=e.prototype;return n.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},n.componentDidUpdate=function(o){var i=null;if(o!==this.props){var s=this.state.status;this.props.in?s!==Wi&&s!==Pa&&(i=Wi):(s===Wi||s===Pa)&&(i=ac)}this.updateStatus(!1,i)},n.componentWillUnmount=function(){this.cancelNextCallback()},n.getTimeouts=function(){var o=this.props.timeout,i,s,l;return i=s=l=o,o!=null&&typeof o!="number"&&(i=o.exit,s=o.enter,l=o.appear!==void 0?o.appear:s),{exit:i,enter:s,appear:l}},n.updateStatus=function(o,i){if(o===void 0&&(o=!1),i!==null)if(this.cancelNextCallback(),i===Wi){if(this.props.unmountOnExit||this.props.mountOnEnter){var s=this.props.nodeRef?this.props.nodeRef.current:as.findDOMNode(this);s&&Zf(s)}this.performEnter(o)}else this.performExit();else this.props.unmountOnExit&&this.state.status===Hi&&this.setState({status:go})},n.performEnter=function(o){var i=this,s=this.props.enter,l=this.context?this.context.isMounting:o,u=this.props.nodeRef?[l]:[as.findDOMNode(this),l],p=u[0],g=u[1],b=this.getTimeouts(),v=l?b.appear:b.enter;if(!o&&!s||Nu.disabled){this.safeSetState({status:Pa},function(){i.props.onEntered(p)});return}this.props.onEnter(p,g),this.safeSetState({status:Wi},function(){i.props.onEntering(p,g),i.onTransitionEnd(v,function(){i.safeSetState({status:Pa},function(){i.props.onEntered(p,g)})})})},n.performExit=function(){var o=this,i=this.props.exit,s=this.getTimeouts(),l=this.props.nodeRef?void 0:as.findDOMNode(this);if(!i||Nu.disabled){this.safeSetState({status:Hi},function(){o.props.onExited(l)});return}this.props.onExit(l),this.safeSetState({status:ac},function(){o.props.onExiting(l),o.onTransitionEnd(s.exit,function(){o.safeSetState({status:Hi},function(){o.props.onExited(l)})})})},n.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},n.safeSetState=function(o,i){i=this.setNextCallback(i),this.setState(o,i)},n.setNextCallback=function(o){var i=this,s=!0;return this.nextCallback=function(l){s&&(s=!1,i.nextCallback=null,o(l))},this.nextCallback.cancel=function(){s=!1},this.nextCallback},n.onTransitionEnd=function(o,i){this.setNextCallback(i);var s=this.props.nodeRef?this.props.nodeRef.current:as.findDOMNode(this),l=o==null&&!this.props.addEndListener;if(!s||l){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var u=this.props.nodeRef?[this.nextCallback]:[s,this.nextCallback],p=u[0],g=u[1];this.props.addEndListener(p,g)}o!=null&&setTimeout(this.nextCallback,o)},n.render=function(){var o=this.state.status;if(o===go)return null;var i=this.props,s=i.children;i.in,i.mountOnEnter,i.unmountOnExit,i.appear,i.enter,i.exit,i.timeout,i.addEndListener,i.onEnter,i.onEntering,i.onEntered,i.onExit,i.onExiting,i.onExited,i.nodeRef;var l=Nc(i,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return R.createElement(ms.Provider,{value:null},typeof s=="function"?s(o,l):R.cloneElement(R.Children.only(s),l))},e}(R.Component);vr.contextType=ms;vr.propTypes={nodeRef:Je.shape({current:typeof Element>"u"?Je.any:function(t,e,n,r,o,i){var s=t[e];return Je.instanceOf(s&&"ownerDocument"in s?s.ownerDocument.defaultView.Element:Element)(t,e,n,r,o,i)}}),children:Je.oneOfType([Je.func.isRequired,Je.element.isRequired]).isRequired,in:Je.bool,mountOnEnter:Je.bool,unmountOnExit:Je.bool,appear:Je.bool,enter:Je.bool,exit:Je.bool,timeout:function(e){var n=T1;e.addEndListener||(n=n.isRequired);for(var r=arguments.length,o=new Array(r>1?r-1:0),i=1;i<r;i++)o[i-1]=arguments[i];return n.apply(void 0,[e].concat(o))},addEndListener:Je.func,onEnter:Je.func,onEntering:Je.func,onEntered:Je.func,onExit:Je.func,onExiting:Je.func,onExited:Je.func};function Aa(){}vr.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:Aa,onEntering:Aa,onEntered:Aa,onExit:Aa,onExiting:Aa,onExited:Aa};vr.UNMOUNTED=go;vr.EXITED=Hi;vr.ENTERING=Wi;vr.ENTERED=Pa;vr.EXITING=ac;var B1=function(e,n){return e&&n&&n.split(" ").forEach(function(r){return E1(e,r)})},vl=function(e,n){return e&&n&&n.split(" ").forEach(function(r){return I1(e,r)})},As=function(t){Lc(e,t);function e(){for(var r,o=arguments.length,i=new Array(o),s=0;s<o;s++)i[s]=arguments[s];return r=t.call.apply(t,[this].concat(i))||this,r.appliedClasses={appear:{},enter:{},exit:{}},r.onEnter=function(l,u){var p=r.resolveArguments(l,u),g=p[0],b=p[1];r.removeClasses(g,"exit"),r.addClass(g,b?"appear":"enter","base"),r.props.onEnter&&r.props.onEnter(l,u)},r.onEntering=function(l,u){var p=r.resolveArguments(l,u),g=p[0],b=p[1],v=b?"appear":"enter";r.addClass(g,v,"active"),r.props.onEntering&&r.props.onEntering(l,u)},r.onEntered=function(l,u){var p=r.resolveArguments(l,u),g=p[0],b=p[1],v=b?"appear":"enter";r.removeClasses(g,v),r.addClass(g,v,"done"),r.props.onEntered&&r.props.onEntered(l,u)},r.onExit=function(l){var u=r.resolveArguments(l),p=u[0];r.removeClasses(p,"appear"),r.removeClasses(p,"enter"),r.addClass(p,"exit","base"),r.props.onExit&&r.props.onExit(l)},r.onExiting=function(l){var u=r.resolveArguments(l),p=u[0];r.addClass(p,"exit","active"),r.props.onExiting&&r.props.onExiting(l)},r.onExited=function(l){var u=r.resolveArguments(l),p=u[0];r.removeClasses(p,"exit"),r.addClass(p,"exit","done"),r.props.onExited&&r.props.onExited(l)},r.resolveArguments=function(l,u){return r.props.nodeRef?[r.props.nodeRef.current,l]:[l,u]},r.getClassNames=function(l){var u=r.props.classNames,p=typeof u=="string",g=p&&u?u+"-":"",b=p?""+g+l:u[l],v=p?b+"-active":u[l+"Active"],d=p?b+"-done":u[l+"Done"];return{baseClassName:b,activeClassName:v,doneClassName:d}},r}var n=e.prototype;return n.addClass=function(o,i,s){var l=this.getClassNames(i)[s+"ClassName"],u=this.getClassNames("enter"),p=u.doneClassName;i==="appear"&&s==="done"&&p&&(l+=" "+p),s==="active"&&o&&Zf(o),l&&(this.appliedClasses[i][s]=l,B1(o,l))},n.removeClasses=function(o,i){var s=this.appliedClasses[i],l=s.base,u=s.active,p=s.done;this.appliedClasses[i]={},l&&vl(o,l),u&&vl(o,u),p&&vl(o,p)},n.render=function(){var o=this.props;o.classNames;var i=Nc(o,["classNames"]);return R.createElement(vr,yo({},i,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},e}(R.Component);As.defaultProps={classNames:""};As.propTypes=yo({},vr.propTypes,{classNames:j1,onEnter:Je.func,onEntering:Je.func,onEntered:Je.func,onExit:Je.func,onExiting:Je.func,onExited:Je.func});function M1(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Pc(t,e){var n=function(i){return e&&de.isValidElement(i)?e(i):i},r=Object.create(null);return t&&de.Children.map(t,function(o){return o}).forEach(function(o){r[o.key]=n(o)}),r}function R1(t,e){t=t||{},e=e||{};function n(g){return g in e?e[g]:t[g]}var r=Object.create(null),o=[];for(var i in t)i in e?o.length&&(r[i]=o,o=[]):o.push(i);var s,l={};for(var u in e){if(r[u])for(s=0;s<r[u].length;s++){var p=r[u][s];l[r[u][s]]=n(p)}l[u]=n(u)}for(s=0;s<o.length;s++)l[o[s]]=n(o[s]);return l}function $i(t,e,n){return n[e]!=null?n[e]:t.props[e]}function F1(t,e){return Pc(t.children,function(n){return de.cloneElement(n,{onExited:e.bind(null,n),in:!0,appear:$i(n,"appear",t),enter:$i(n,"enter",t),exit:$i(n,"exit",t)})})}function O1(t,e,n){var r=Pc(t.children),o=R1(e,r);return Object.keys(o).forEach(function(i){var s=o[i];if(de.isValidElement(s)){var l=i in e,u=i in r,p=e[i],g=de.isValidElement(p)&&!p.props.in;u&&(!l||g)?o[i]=de.cloneElement(s,{onExited:n.bind(null,s),in:!0,exit:$i(s,"exit",t),enter:$i(s,"enter",t)}):!u&&l&&!g?o[i]=de.cloneElement(s,{in:!1}):u&&l&&de.isValidElement(p)&&(o[i]=de.cloneElement(s,{onExited:n.bind(null,s),in:p.props.in,exit:$i(s,"exit",t),enter:$i(s,"enter",t)}))}}),o}var D1=Object.values||function(t){return Object.keys(t).map(function(e){return t[e]})},q1={component:"div",childFactory:function(e){return e}},_c=function(t){Lc(e,t);function e(r,o){var i;i=t.call(this,r,o)||this;var s=i.handleExited.bind(M1(i));return i.state={contextValue:{isMounting:!0},handleExited:s,firstRender:!0},i}var n=e.prototype;return n.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},n.componentWillUnmount=function(){this.mounted=!1},e.getDerivedStateFromProps=function(o,i){var s=i.children,l=i.handleExited,u=i.firstRender;return{children:u?F1(o,l):O1(o,s,l),firstRender:!1}},n.handleExited=function(o,i){var s=Pc(this.props.children);o.key in s||(o.props.onExited&&o.props.onExited(i),this.mounted&&this.setState(function(l){var u=yo({},l.children);return delete u[o.key],{children:u}}))},n.render=function(){var o=this.props,i=o.component,s=o.childFactory,l=Nc(o,["component","childFactory"]),u=this.state.contextValue,p=D1(this.state.children).map(s);return delete l.appear,delete l.enter,delete l.exit,i===null?R.createElement(ms.Provider,{value:u},p):R.createElement(ms.Provider,{value:u},R.createElement(i,l,p))},e}(R.Component);_c.propTypes={component:Je.any,children:Je.node,appear:Je.bool,enter:Je.bool,exit:Je.bool,childFactory:Je.func};_c.defaultProps=q1;function U1(t,e,n){if(!document)return;document.documentElement.style.setProperty(t,e)}var yl={Body:"Polaris-Modal__Body",NoScrollBody:"Polaris-Modal__NoScrollBody",IFrame:"Polaris-Modal__IFrame"},Lu={Section:"Polaris-Modal-Section",titleHidden:"Polaris-Modal-Section--titleHidden"};function Qf({children:t,flush:e=!1,subdued:n=!1,titleHidden:r=!1}){const o=ct(Lu.Section,r&&Lu.titleHidden);return R.createElement("div",{className:o},R.createElement(ut,Object.assign({as:"section",padding:e?"0":"400"},r&&{paddingInlineEnd:"0"},n&&{background:"bg-surface-tertiary"}),t))}var hn={Container:"Polaris-Modal-Dialog__Container",Dialog:"Polaris-Modal-Dialog",Modal:"Polaris-Modal-Dialog__Modal",limitHeight:"Polaris-Modal-Dialog--limitHeight",sizeSmall:"Polaris-Modal-Dialog--sizeSmall",sizeLarge:"Polaris-Modal-Dialog--sizeLarge",sizeFullScreen:"Polaris-Modal-Dialog--sizeFullScreen",animateFadeUp:"Polaris-Modal-Dialog--animateFadeUp",entering:"Polaris-Modal-Dialog--entering",exiting:"Polaris-Modal-Dialog--exiting",exited:"Polaris-Modal-Dialog--exited",entered:"Polaris-Modal-Dialog--entered"};function z1({trapping:t}){const e=de.useContext(Od),n=de.useId();if(!e)throw new yc("No FocusManager was provided.");const{trapFocusList:r,add:o,remove:i}=e,s=r[0]===n,l=de.useMemo(()=>({canSafelyFocus:s}),[s]);return de.useEffect(()=>{if(t)return o(n),()=>{i(n)}},[o,n,i,t]),l}function H1({trapping:t=!0,children:e}){const{canSafelyFocus:n}=z1({trapping:t}),r=de.useRef(null),[o,i]=de.useState(!0);de.useEffect(()=>{const u=n&&!(r.current&&r.current.contains(document.activeElement))?!t:!0;i(u)},[n,t]);const s=u=>{const p=r.current&&r.current.contains(document.activeElement);t===!1||!r.current||p||u.target instanceof Element&&u.target.matches(`${Rd.selector} *`)||n&&u.target instanceof HTMLElement&&r.current!==u.target&&!r.current.contains(u.target)&&ws(r.current)},l=u=>{if(t===!1||!r.current)return;const p=xd(r.current),g=Ad(r.current);u.target===g&&!u.shiftKey&&(u.preventDefault(),Sd(r.current)),u.target===p&&u.shiftKey&&(u.preventDefault(),Nd(r.current))};return R.createElement(x1,{disabled:o,root:r.current},R.createElement("div",{ref:r},R.createElement(Df,{event:"focusin",handler:s}),R.createElement(Ql,{keyCode:ec.Tab,keyEvent:"keydown",handler:l}),e))}function W1({instant:t,labelledBy:e,children:n,limitHeight:r,size:o,onClose:i,onExited:s,onEntered:l,setClosing:u,hasToasts:p,...g}){const b=qf(),v=de.useRef(null),d=de.useContext(f1);let _;d&&(_=d.toastMessages);const S=ct(hn.Modal,o&&hn[ki("size",o)],r&&hn.limitHeight),T=t?vr:G1;de.useEffect(()=>{v.current&&!v.current.contains(document.activeElement)&&ws(v.current)},[]);const A=()=>{u&&u(!0)},j=()=>{u&&u(!1),i()},q=R.createElement("div",{"aria-live":"assertive"},_?_.map(Y=>R.createElement(Ye,{visuallyHidden:!0,as:"p",key:Y.id},Y.content)):null);return R.createElement(T,Object.assign({},g,{nodeRef:v,mountOnEnter:!0,unmountOnExit:!0,timeout:parseInt(b.motion["motion-duration-200"],10),onEntered:l,onExited:s}),R.createElement("div",{className:hn.Container,"data-polaris-layer":!0,"data-polaris-overlay":!0,ref:v},R.createElement(H1,null,R.createElement("div",{role:"dialog","aria-modal":!0,"aria-label":e,"aria-labelledby":e,tabIndex:-1,className:hn.Dialog},R.createElement("div",{className:S},R.createElement(Ql,{keyCode:ec.Escape,keyEvent:"keydown",handler:A}),R.createElement(Ql,{keyCode:ec.Escape,handler:j}),n),q))))}const V1={appear:ct(hn.animateFadeUp,hn.entering),appearActive:ct(hn.animateFadeUp,hn.entered),enter:ct(hn.animateFadeUp,hn.entering),enterActive:ct(hn.animateFadeUp,hn.entered),exit:ct(hn.animateFadeUp,hn.exiting),exitActive:ct(hn.animateFadeUp,hn.exited)};function G1({children:t,...e}){return R.createElement(As,Object.assign({},e,{classNames:V1}),t)}function Pu({pressed:t,onClick:e}){const n=Zr();return R.createElement(Xr,{variant:"tertiary",pressed:t,icon:xc,onClick:e,accessibilityLabel:n.translate("Polaris.Common.close")})}function Y1({id:t,children:e,closing:n,titleHidden:r,onClose:o}){const i="400",s="400";return r||!e?R.createElement(ut,{position:"absolute",insetInlineEnd:i,insetBlockStart:s,zIndex:"1"},R.createElement(Pu,{onClick:o})):R.createElement(ut,{paddingBlockStart:"400",paddingBlockEnd:"400",paddingInlineStart:i,paddingInlineEnd:i,borderBlockEndWidth:"025",borderColor:"border",background:"bg-surface-tertiary"},R.createElement(c1,{columns:{xs:"1fr auto"},gap:"400"},R.createElement(Ot,{gap:"400",blockAlign:"center"},R.createElement(Ye,{id:t,as:"h2",variant:"headingMd",breakWord:!0},e)),R.createElement(Pu,{pressed:n,onClick:o})))}function $1({primaryAction:t,secondaryActions:e,children:n}){const r=t&&su(t,{variant:"primary"})||null,o=e&&su(e)||null,i=r||o?R.createElement(Ot,{gap:"200"},o,r):null;return R.createElement(Ot,{gap:"400",blockAlign:"center"},R.createElement(ut,{borderColor:"border",borderBlockStartWidth:"025",padding:"400",width:"100%"},R.createElement(Ot,{gap:"400",blockAlign:"center",align:"space-between"},R.createElement(ut,null,n),i)))}const _u=200,J1=400,oc=function({children:e,title:n,titleHidden:r=!1,src:o,iFrameName:i,open:s,instant:l,sectioned:u,loading:p,size:g,limitHeight:b,footer:v,primaryAction:d,secondaryActions:_,onScrolledToBottom:S,activator:T,activatorWrapper:A="div",onClose:j,onIFrameLoad:q,onTransitionEnd:Y,noScroll:le}){const[ae,$]=de.useState(_u),[F,J]=de.useState(!1),re=de.useId(),N=de.useRef(null),O=Zr().translate("Polaris.Modal.iFrameTitle");let C,X;const ne=de.useCallback(()=>{Y&&Y()},[Y]),ue=de.useCallback(()=>{$(_u);const me=T&&ku(T)?T&&T.current:N.current;me&&requestAnimationFrame(()=>ws(me))},[T]),Q=de.useCallback(me=>{const x=me.target;if(x&&x.contentWindow)try{$(x.contentWindow.document.body.scrollHeight)}catch{$(J1)}q!=null&&q(me)},[q]);if(s){const me=!v&&!d&&!_?null:R.createElement($1,{primaryAction:d,secondaryActions:_},v),x=u?Of(e,Qf,{titleHidden:r}):e,B=p?R.createElement(ut,{padding:"400"},R.createElement(Ot,{gap:"400",align:"center",blockAlign:"center"},R.createElement(xs,null))):x,M=le?R.createElement("div",{className:yl.NoScrollBody},R.createElement(ut,{width:"100%",overflowX:"hidden",overflowY:"hidden"},B)):R.createElement(Pd,{shadow:!0,className:yl.Body,onScrolledToBottom:S},B),z=o?R.createElement("iframe",{name:i,title:O,src:o,className:yl.IFrame,onLoad:Q,style:{height:`${ae}px`}}):M;C=R.createElement(W1,{instant:l,labelledBy:re,onClose:j,onEntered:ne,onExited:ue,size:g,limitHeight:b,setClosing:J},R.createElement(Y1,{titleHidden:r,id:re,closing:F,onClose:j},n),z,me),X=R.createElement(Gd,{setClosing:J,onClick:j})}const he=!l,V=T&&!ku(T)?R.createElement(ut,{ref:N,as:A},T):null;return R.createElement(wc.Provider,{value:!0},V,R.createElement(Ld,{idPrefix:"modal"},R.createElement(_c,{appear:he,enter:he,exit:he},C),X))};function ku(t){return Object.prototype.hasOwnProperty.call(t,"current")}oc.Section=Qf;var Ee={IndexTable:"Polaris-IndexTable",IndexTableWrapper:"Polaris-IndexTable__IndexTableWrapper","IndexTableWrapper-scrollBarHidden":"Polaris-IndexTable__IndexTableWrapper--scrollBarHidden",IndexTableWrapperWithSelectAllActions:"Polaris-IndexTable__IndexTableWrapperWithSelectAllActions","LoadingContainer-enter":"Polaris-IndexTable__LoadingContainer--enter","LoadingContainer-enter-active":"Polaris-IndexTable--loadingContainerEnterActive","LoadingContainer-exit":"Polaris-IndexTable__LoadingContainer--exit","LoadingContainer-exit-active":"Polaris-IndexTable--loadingContainerExitActive",LoadingPanel:"Polaris-IndexTable__LoadingPanel",LoadingPanelRow:"Polaris-IndexTable__LoadingPanelRow",LoadingPanelText:"Polaris-IndexTable__LoadingPanelText",Table:"Polaris-IndexTable__Table","Table-scrolling":"Polaris-IndexTable__Table--scrolling","TableCell-first":"Polaris-IndexTable__TableCell--first","StickyTable-scrolling":"Polaris-IndexTable__StickyTable--scrolling",TableCell:"Polaris-IndexTable__TableCell","TableHeading-first":"Polaris-IndexTable__TableHeading--first","TableHeading-second":"Polaris-IndexTable__TableHeading--second","Table-sticky":"Polaris-IndexTable__Table--sticky",StickyTable:"Polaris-IndexTable__StickyTable","Table-unselectable":"Polaris-IndexTable__Table--unselectable",TableRow:"Polaris-IndexTable__TableRow","TableRow-unclickable":"Polaris-IndexTable__TableRow--unclickable",toneSuccess:"Polaris-IndexTable--toneSuccess","TableRow-child":"Polaris-IndexTable__TableRow--child",toneWarning:"Polaris-IndexTable--toneWarning",toneCritical:"Polaris-IndexTable--toneCritical",toneSubdued:"Polaris-IndexTable--toneSubdued","TableRow-subheader":"Polaris-IndexTable__TableRow--subheader","TableRow-selected":"Polaris-IndexTable__TableRow--selected","TableRow-hovered":"Polaris-IndexTable__TableRow--hovered","TableRow-disabled":"Polaris-IndexTable__TableRow--disabled",ZebraStriping:"Polaris-IndexTable__ZebraStriping",TableHeading:"Polaris-IndexTable__TableHeading","TableHeading-flush":"Polaris-IndexTable__TableHeading--flush","TableHeading-align-center":"Polaris-IndexTable--tableHeadingAlignCenter","TableHeading-align-end":"Polaris-IndexTable--tableHeadingAlignEnd","TableHeading-extra-padding-right":"Polaris-IndexTable--tableHeadingExtraPaddingRight","TableHeading-sortable":"Polaris-IndexTable__TableHeading--sortable",TableHeadingSortButton:"Polaris-IndexTable__TableHeadingSortButton",TableHeadingSortIcon:"Polaris-IndexTable__TableHeadingSortIcon","TableHeadingSortButton-heading-align-end":"Polaris-IndexTable--tableHeadingSortButtonHeadingAlignEnd","TableHeadingSortButton-heading-align-end-currently-sorted":"Polaris-IndexTable--tableHeadingSortButtonHeadingAlignEndCurrentlySorted","TableHeadingSortIcon-heading-align-end":"Polaris-IndexTable--tableHeadingSortIconHeadingAlignEnd","TableHeadingSortButton-heading-align-end-previously-sorted":"Polaris-IndexTable--tableHeadingSortButtonHeadingAlignEndPreviouslySorted","right-aligned-sort-button-slide-out":"Polaris-IndexTable--rightAlignedSortButtonSlideOut","reveal-right-aligned-sort-button-icon":"Polaris-IndexTable--revealRightAlignedSortButtonIcon",TableHeadingUnderline:"Polaris-IndexTable__TableHeadingUnderline",TableHeadingTooltipUnderlinePlaceholder:"Polaris-IndexTable__TableHeadingTooltipUnderlinePlaceholder","TableHeadingSortIcon-visible":"Polaris-IndexTable__TableHeadingSortIcon--visible",TableHeadingSortSvg:"Polaris-IndexTable__TableHeadingSortSvg",SortableTableHeadingWithCustomMarkup:"Polaris-IndexTable__SortableTableHeadingWithCustomMarkup",SortableTableHeaderWrapper:"Polaris-IndexTable__SortableTableHeaderWrapper",ColumnHeaderCheckboxWrapper:"Polaris-IndexTable__ColumnHeaderCheckboxWrapper",FirstStickyHeaderElement:"Polaris-IndexTable__FirstStickyHeaderElement","TableHeading-unselectable":"Polaris-IndexTable__TableHeading--unselectable","TableCell-flush":"Polaris-IndexTable__TableCell--flush","Table-sticky-scrolling":"Polaris-IndexTable--tableStickyScrolling","StickyTableHeader-sticky-scrolling":"Polaris-IndexTable--stickyTableHeaderStickyScrolling","TableHeading-last":"Polaris-IndexTable__TableHeading--last","Table-sticky-last":"Polaris-IndexTable--tableStickyLast","StickyTableHeader-sticky-last":"Polaris-IndexTable--stickyTableHeaderStickyLast","Table-sortable":"Polaris-IndexTable__Table--sortable",StickyTableHeader:"Polaris-IndexTable__StickyTableHeader","StickyTableHeader-isSticky":"Polaris-IndexTable__StickyTableHeader--isSticky",StickyTableHeadings:"Polaris-IndexTable__StickyTableHeadings","StickyTableHeading-second":"Polaris-IndexTable__StickyTableHeading--second",unselectable:"Polaris-IndexTable--unselectable","StickyTableHeading-second-scrolling":"Polaris-IndexTable--stickyTableHeadingSecondScrolling",ScrollLeft:"Polaris-IndexTable__ScrollLeft",ScrollRight:"Polaris-IndexTable__ScrollRight","ScrollRight-onboarding":"Polaris-IndexTable__ScrollRight--onboarding",SelectAllActionsWrapper:"Polaris-IndexTable__SelectAllActionsWrapper",SelectAllActionsWrapperWithPagination:"Polaris-IndexTable__SelectAllActionsWrapperWithPagination",SelectAllActionsWrapperSticky:"Polaris-IndexTable__SelectAllActionsWrapperSticky",SelectAllActionsWrapperAtEnd:"Polaris-IndexTable__SelectAllActionsWrapperAtEnd",SelectAllActionsWrapperAtEndAppear:"Polaris-IndexTable__SelectAllActionsWrapperAtEndAppear",BulkActionsWrapper:"Polaris-IndexTable__BulkActionsWrapper",BulkActionsWrapperVisible:"Polaris-IndexTable__BulkActionsWrapperVisible",PaginationWrapper:"Polaris-IndexTable__PaginationWrapper",PaginationWrapperScrolledPastTop:"Polaris-IndexTable__PaginationWrapperScrolledPastTop",ScrollBarContainer:"Polaris-IndexTable__ScrollBarContainer",ScrollBarContainerWithPagination:"Polaris-IndexTable__ScrollBarContainerWithPagination",ScrollBarContainerScrolledPastTop:"Polaris-IndexTable__ScrollBarContainerScrolledPastTop",ScrollBarContainerWithSelectAllActions:"Polaris-IndexTable__ScrollBarContainerWithSelectAllActions",ScrollBarContainerSelectAllActionsSticky:"Polaris-IndexTable__ScrollBarContainerSelectAllActionsSticky",scrollBarContainerCondensed:"Polaris-IndexTable--scrollBarContainerCondensed",scrollBarContainerHidden:"Polaris-IndexTable--scrollBarContainerHidden",ScrollBar:"Polaris-IndexTable__ScrollBar",disableTextSelection:"Polaris-IndexTable--disableTextSelection",EmptySearchResultWrapper:"Polaris-IndexTable__EmptySearchResultWrapper",condensedRow:"Polaris-IndexTable--condensedRow",CondensedList:"Polaris-IndexTable__CondensedList",HeaderWrapper:"Polaris-IndexTable__HeaderWrapper","StickyTable-condensed":"Polaris-IndexTable__StickyTable--condensed","StickyTableHeader-condensed":"Polaris-IndexTable__StickyTableHeader--condensed",ScrollBarContent:"Polaris-IndexTable__ScrollBarContent"};const Pi="All";let nn;(function(t){t.All="all",t.Page="page",t.Multi="multi",t.Single="single",t.Range="range"})(nn||(nn={}));const eh=de.createContext(void 0),th=de.createContext(void 0),nh=de.createContext(void 0);function rh(){const t=de.useContext(th);if(!t)throw new Error("Missing IndexProvider context");return t}function K1(){const t=de.useContext(nh);if(!t)throw new Error("Missing IndexProvider context");return t}function ih(){const t=de.useContext(eh);if(!t)throw new Error("Missing IndexProvider context");return t}function X1({selectedItemsCount:t,itemCount:e,hasMoreItems:n,resourceName:r,defaultPaginatedSelectAllText:o}){const i=Zr(),s=!!t,l=t==="All"||t>0,u={singular:i.translate("Polaris.IndexProvider.defaultItemSingular"),plural:i.translate("Polaris.IndexProvider.defaultItemPlural")},p=r||u,g=_(),b=S(),v=T();let d="indeterminate";return!t||t===0?d=void 0:(t===Pi||t===e)&&(d=!0),{paginatedSelectAllText:g,bulkActionsLabel:b,bulkActionsAccessibilityLabel:v,resourceName:p,selectMode:l,bulkSelectState:d,selectable:s};function _(){if(!(!s||!n)&&t===Pi)return o||i.translate("Polaris.IndexProvider.allItemsSelected",{itemsLength:e,resourceNamePlural:p.plural.toLocaleLowerCase()})}function S(){const A=t===Pi?`${e}+`:t;return i.translate("Polaris.IndexProvider.selected",{selectedItemsCount:A})}function T(){const A=e,j=t===A;return A===1&&j?i.translate("Polaris.IndexProvider.a11yCheckboxDeselectAllSingle",{resourceNameSingular:p.singular}):A===1?i.translate("Polaris.IndexProvider.a11yCheckboxSelectAllSingle",{resourceNameSingular:p.singular}):j?i.translate("Polaris.IndexProvider.a11yCheckboxDeselectAllMultiple",{itemsLength:e,resourceNamePlural:p.plural}):i.translate("Polaris.IndexProvider.a11yCheckboxSelectAllMultiple",{itemsLength:e,resourceNamePlural:p.plural})}}function Z1({onSelectionChange:t=()=>{}}){const e=de.useRef(null);return de.useCallback((r,o,i,s)=>{const l=e.current;if(nn.Multi&&typeof s=="number"&&(e.current=s),r===nn.Single||r===nn.Multi&&(typeof l!="number"||typeof s!="number"))t(nn.Single,o,i);else if(r===nn.Multi){const u=Math.min(l,s),p=Math.max(l,s);t(r,o,[u,p])}else r===nn.Page||r===nn.All?t(r,o):r===nn.Range&&t(nn.Range,o,i)},[t])}function Q1({children:t,resourceName:e,loading:n,onSelectionChange:r,selectedItemsCount:o=0,itemCount:i,hasMoreItems:s,condensed:l,selectable:u=!0,paginatedSelectAllText:p}){const{paginatedSelectAllText:g,bulkActionsLabel:b,bulkActionsAccessibilityLabel:v,resourceName:d,selectMode:_,bulkSelectState:S}=X1({selectedItemsCount:o,itemCount:i,hasMoreItems:s,resourceName:e,defaultPaginatedSelectAllText:p}),T=Z1({onSelectionChange:r}),A=de.useMemo(()=>({itemCount:i,selectMode:_&&u,selectable:u,resourceName:d,loading:n,paginatedSelectAllText:g,hasMoreItems:s,bulkActionsLabel:b,bulkActionsAccessibilityLabel:v,bulkSelectState:S,selectedItemsCount:o,condensed:l}),[i,_,u,d,n,g,s,b,v,S,o,l]),j=de.useMemo(()=>({selectable:u,selectMode:_&&u,condensed:l}),[l,_,u]);return R.createElement(eh.Provider,{value:A},R.createElement(nh.Provider,{value:j},R.createElement(th.Provider,{value:T},t)))}const e2=de.memo(function({children:e,className:n,flush:r,colSpan:o,headers:i,scope:s,as:l="td",id:u}){const p=ct(n,Ee.TableCell,r&&Ee["TableCell-flush"]);return R.createElement(l,{id:u,colSpan:o,headers:i,scope:s,className:p},e)});var Cu={TableCellContentContainer:"Polaris-IndexTable-Checkbox__TableCellContentContainer",Wrapper:"Polaris-IndexTable-Checkbox__Wrapper"};const kc=de.createContext({}),t2=de.createContext(void 0),ah={scrollableContainer:null,canScrollLeft:!1,canScrollRight:!1},n2=de.createContext(ah),r2=de.memo(function({accessibilityLabel:e}){const n=Zr(),{resourceName:r}=ih(),{itemId:o,selected:i,disabled:s,onInteraction:l}=de.useContext(kc),u=e||n.translate("Polaris.IndexTable.selectItem",{resourceName:r.singular});return R.createElement(i2,null,R.createElement("div",{className:Cu.TableCellContentContainer},R.createElement("div",{className:Cu.Wrapper,onClick:l,onKeyUp:a2},R.createElement(Ac,{id:`Select-${o}`,label:u,labelHidden:!0,checked:i,disabled:s}))))});function i2({children:t}){const{position:e}=de.useContext(kc),n=de.useRef(null),r=de.useCallback(mo(()=>{if(e!==0||!n.current)return;const{width:i}=n.current.getBoundingClientRect();U1("--pc-checkbox-offset",`${i}px`)}),[e]);de.useEffect(()=>{r()},[r]),de.useEffect(()=>{if(n.current)return window.addEventListener("resize",r),()=>{window.removeEventListener("resize",r)}},[r]);const o=ct(Ee.TableCell,Ee["TableCell-first"]);return R.createElement("td",{className:o,ref:n},t)}function a2(){}const o2=de.memo(function({children:e,selected:n,id:r,position:o,tone:i,disabled:s,selectionRange:l,rowType:u="data",accessibilityLabel:p,onNavigation:g,onClick:b}){const{selectable:v,selectMode:d,condensed:_}=K1(),S=rh(),{value:T,setTrue:A,setFalse:j}=ys(!1),q=de.useCallback(O=>{O.stopPropagation();let C=nn.Single;if("key"in O&&O.key!==" "||!S)return;O.nativeEvent.shiftKey?C=nn.Multi:l&&(C=nn.Range),S(C,!n,l??r,o)},[r,S,n,l,o]),Y=de.useMemo(()=>({itemId:r,selected:n,position:o,onInteraction:q,disabled:s}),[r,n,s,o,q]),le=de.useRef(null),ae=de.useRef(!1),$=de.useRef(null),F=de.useCallback(O=>{$.current=O;const C=O==null?void 0:O.querySelector("[data-primary-link]");C&&(le.current=C)},[]),J=ct(Ee.TableRow,u==="subheader"&&Ee["TableRow-subheader"],u==="child"&&Ee["TableRow-child"],v&&_&&Ee.condensedRow,n&&Ee["TableRow-selected"],T&&!_&&Ee["TableRow-hovered"],s&&Ee["TableRow-disabled"],i&&Ee[ki("tone",i)],!v&&!b&&!le.current&&Ee["TableRow-unclickable"]);let re;(!s&&v||b||le.current)&&(re=O=>{if(u!=="subheader"&&!(!$.current||ae.current)){if(O.stopPropagation(),O.preventDefault(),b){b();return}if(le.current&&!d){ae.current=!0;const{ctrlKey:C,metaKey:X}=O.nativeEvent;if(g&&g(r),(C||X)&&le.current instanceof HTMLAnchorElement){ae.current=!1,window.open(le.current.href,"_blank");return}le.current.dispatchEvent(new MouseEvent(O.type,O.nativeEvent))}else ae.current=!1,q(O)}});const N=_?"li":"tr",k=v?R.createElement(r2,{accessibilityLabel:p}):null;return R.createElement(kc.Provider,{value:Y},R.createElement(t2.Provider,{value:T},R.createElement(N,{key:r,id:r,className:J,onMouseEnter:A,onMouseLeave:j,onClick:re,ref:F},k,e)))});function Eu(t,e){return t?Array.from(t.querySelectorAll(e)):[]}var s2={ScrollContainer:"Polaris-IndexTable-ScrollContainer"};function l2({children:t,scrollableContainerRef:e,onScroll:n}){de.useEffect(()=>{e.current&&e.current.dispatchEvent(new Event("scroll"))},[e]);const[r,o]=de.useState(ah),i=de.useCallback(mo(()=>{if(!e.current)return;const s=e.current.scrollWidth-e.current.offsetWidth,l=e.current.scrollLeft>0,u=e.current.scrollLeft<s;n(l,u),o({scrollableContainer:e.current,canScrollLeft:l,canScrollRight:u})},40,{trailing:!0,leading:!0,maxWait:40}),[n,e]);return R.createElement(n2.Provider,{value:r},R.createElement("div",{className:s2.ScrollContainer,ref:e,onScroll:i},t))}const c2=16,u2=300;function f2({headings:t,bulkActions:e=[],promotedBulkActions:n=[],children:r,emptyState:o,sort:i,paginatedSelectAllActionText:s,lastColumnSticky:l=!1,sortable:u,sortDirection:p,defaultSortDirection:g="descending",sortColumnIndex:b,onSort:v,sortToggleLabels:d,hasZebraStriping:_,pagination:S,...T}){const A=qf(),{loading:j,bulkSelectState:q,resourceName:Y,bulkActionsAccessibilityLabel:le,selectMode:ae,selectable:$=T.selectable,paginatedSelectAllText:F,itemCount:J,hasMoreItems:re,selectedItemsCount:N,condensed:k}=ih(),O=rh(),C=Zr(),{value:X,toggle:ne}=ys(!1),ue=de.useRef({top:0,left:0}),Q=de.useRef([]),he=de.useRef(null),V=de.useRef(null),me=de.useRef(null),x=de.useRef(null),B=de.useRef(null),[M,z]=de.useState(!1),[H,G]=de.useState(null),[ee,oe]=de.useState(!0),ve=de.useRef([]),Se=de.useRef([]),be=de.useRef(null),Ce=de.useRef(null),ze=de.useRef(null),ye=de.useRef(null),W=de.useRef(null),Ze=de.useRef(!1),qe=de.useRef(!1),ke=de.useRef(b),Ie=de.useRef(!1),Fe=de.useRef(0),Te=de.useRef(!1);N!==Fe.current&&(Ie.current=!0,Fe.current=N),!Te.current&&N!==0&&(Te.current=!0);const He=de.useCallback(Me=>{Me!==null&&!M&&z(!0),me.current=Me},[M]),Qe=de.useCallback(()=>{O(N===Pi?nn.Page:nn.All,!0)},[O,N]),et=de.useMemo(()=>mo(()=>{var ft,Kt;if(!V.current||!he.current)return;const Me=he.current.getBoundingClientRect();ue.current={top:Me.top,left:Me.left},Q.current=ve.current.map(en=>({offsetWidth:en.offsetWidth||0,offsetLeft:en.offsetLeft||0})),ve.current.length!==0&&($&&ve.current.length>1&&(ve.current[1].style.left=`${Q.current[0].offsetWidth}px`,(ft=Se.current)!=null&&ft.length&&(Se.current[1].style.left=`${Q.current[0].offsetWidth}px`)),(Kt=Se.current)!=null&&Kt.length&&Se.current.forEach((en,cn)=>{var _n;en.style.minWidth=`${((_n=Q.current[cn])==null?void 0:_n.offsetWidth)||0}px`}))}),[$]),rt=de.useCallback(()=>{var Me,ft;ye.current&&V.current&&M&&(ye.current.style.setProperty("--pc-index-table-scroll-bar-content-width",`${V.current.offsetWidth-c2}px`),oe(((Me=W.current)==null?void 0:Me.offsetWidth)===((ft=V.current)==null?void 0:ft.offsetWidth)))},[M]),dt=de.useCallback(mo(rt,u2,{trailing:!0}),[rt]),[yt,$e]=de.useState(!0),st=de.useCallback(mo(()=>{if(!l||!V.current||!he.current)return;const Me=V.current.getBoundingClientRect(),ft=he.current.getBoundingClientRect();$e(Me.width>ft.width)}),[l]);de.useEffect(()=>{st()},[st]);const[je,rn]=de.useState(!0),ht=de.useCallback(()=>{if(!he.current||!ve.current.length)return;const Me=he.current.getBoundingClientRect(),ft=$?ve.current[0].getBoundingClientRect().width:0,Kt=ve.current[$?1:0].getBoundingClientRect().width,en=$?ve.current.length>2:1,cn=l&&en?ve.current[ve.current.length-1].getBoundingClientRect().width:0;rn(Me.width>Kt+ft+cn+100)},[l,$]);de.useEffect(()=>{M&&ht()},[ht,M]);const Rn=de.useCallback(()=>{var Me;(Me=ye.current)==null||Me.style.setProperty("--pc-index-table-scroll-bar-content-width","0px"),et(),dt(),st(),ht()},[et,dt,st,ht]),At=de.useCallback((Me,ft)=>{!he.current||!ye.current||(Ze.current||(qe.current=!0,ye.current.scrollLeft=he.current.scrollLeft),Ze.current=!1,ze.current&&(ze.current.scrollLeft=he.current.scrollLeft),(Me&&!X||!Me&&X)&&ne(),$e(ft))},[X,ne]),Ct=de.useCallback(()=>{!he.current||!ye.current||(qe.current||(Ze.current=!0,he.current.scrollLeft=ye.current.scrollLeft),qe.current=!1)},[]);de.useLayoutEffect(()=>{ve.current=Eu(V.current,"[data-index-table-heading]"),Se.current=Eu(be.current,"[data-index-table-sticky-heading]"),et()},[t,et,Ce,M]),de.useEffect(()=>{rt(),G(k?x.current:V.current)},[M,rt,k]);const $n=t.map((Me,ft)=>zn(Me,ft,"th",{"data-index-table-heading":!0},Me.id)),vt=t.map((Me,ft)=>zn(Me,ft,"div",{"data-index-table-sticky-heading":!0})),[Br,Bt]=de.useState(N===Pi?`${J}+`:N);de.useEffect(()=>{(N===Pi||N>0)&&Bt(N===Pi?`${J}+`:N)},[N,J]);const Fn=C.translate("Polaris.IndexTable.selected",{selectedItemsCount:Br}),sr=de.useCallback(()=>{O(nn.Page,!q||q==="indeterminate")},[q,O]),Mt=ei(),tt={enter:Ee["LoadingContainer-enter"],enterActive:Ee["LoadingContainer-enter-active"],exit:Ee["LoadingContainer-exit"],exitActive:Ee["LoadingContainer-exit-active"]},gt=R.createElement(As,{in:j,classNames:tt,timeout:parseInt(A.motion["motion-duration-100"],10),nodeRef:B,appear:!0,unmountOnExit:!0},R.createElement("div",{className:Ee.LoadingPanel,ref:B},R.createElement("div",{className:Ee.LoadingPanelRow},R.createElement(xs,{size:"small"}),R.createElement("span",{className:Ee.LoadingPanelText},C.translate("Polaris.IndexTable.resourceLoadingAccessibilityLabel",{resourceNamePlural:Y.plural.toLocaleLowerCase()}))))),Mr=ct(Ee.StickyTable,X&&Ee["StickyTable-scrolling"],k&&Ee["StickyTable-condensed"]),Ht=!k||N,Jn=Ht?n:[],Kn=Ht?e:[],Ve=R.createElement("div",{className:Mr,role:"presentation"},R.createElement(m1,{boundingElement:H},Me=>{const ft=ct(Ee.StickyTableHeader,Me&&Ee["StickyTableHeader-isSticky"],je&&Ee["StickyTableHeader-sticky"],X&&Ee["StickyTableHeader-scrolling"],je&&l&&Ee["StickyTableHeader-sticky-last"],je&&l&&yt&&Ee["StickyTableHeader-sticky-scrolling"]),Kt=ct(Ee.BulkActionsWrapper,ae&&Ee.BulkActionsWrapperVisible,k&&Ee["StickyTableHeader-condensed"],Me&&Ee["StickyTableHeader-isSticky"]),en=Ht&&!k?R.createElement("div",{className:Kt},R.createElement(s1,{selectMode:ae,onToggleAll:sr,paginatedSelectAllText:F,paginatedSelectAllAction:Mt,accessibilityLabel:le,selected:q,promotedActions:Jn,actions:Kn,onSelectModeToggle:k?ti:void 0,label:Fn,buttonSize:"micro"})):null,cn=k?R.createElement("div",{className:ct(Ee.HeaderWrapper,(!$||k)&&Ee.unselectable)},gt,i):R.createElement("div",{className:ft,ref:be},gt,R.createElement("div",{className:Ee.StickyTableHeadings,ref:ze},vt));return R.createElement(R.Fragment,null,cn,en)})),On=ct(Ee.ScrollBarContainer,S&&Ee.ScrollBarContainerWithPagination,k&&Ee.scrollBarContainerCondensed,ee&&Ee.scrollBarContainerHidden),Xn=ct(V.current&&M&&Ee.ScrollBarContent),dn=J>0?R.createElement(h1,{onMount:rt},R.createElement("div",{className:On,ref:W},R.createElement("div",{onScroll:Ct,className:Ee.ScrollBar,ref:ye},R.createElement("div",{className:Xn})))):null,Wt=u==null?void 0:u.some(Me=>Me),Dt=ct(Ee.Table,X&&Ee["Table-scrolling"],ae&&Ee.disableTextSelection,!$&&Ee["Table-unselectable"],je&&Ee["Table-sticky"],Wt&&Ee["Table-sortable"],je&&l&&Ee["Table-sticky-last"],je&&l&&yt&&Ee["Table-sticky-scrolling"],_&&Ee.ZebraStriping),xn=o||R.createElement(w1,{title:C.translate("Polaris.IndexTable.emptySearchTitle",{resourceNamePlural:Y.plural}),description:C.translate("Polaris.IndexTable.emptySearchDescription"),withIllustration:!0}),Rr=R.createElement(R.Fragment,null,R.createElement(Df,{event:"resize",handler:Rn}),Ve),lr=ct(Ee.CondensedList,_&&Ee.ZebraStriping),Dn=k?R.createElement(R.Fragment,null,Rr,R.createElement("ul",{"data-selectmode":!!ae,className:lr,ref:x},r)):R.createElement(R.Fragment,null,Rr,R.createElement(l2,{scrollableContainerRef:he,onScroll:At},R.createElement("table",{ref:V,className:Dt},R.createElement("thead",null,R.createElement("tr",{className:Ee.HeadingRow},$n)),R.createElement("tbody",{ref:He},r)))),qn=J>0?Dn:R.createElement("div",{className:Ee.EmptySearchResultWrapper},xn),Un=S?R.createElement("div",{className:Ee.PaginationWrapper},R.createElement(_d,Object.assign({type:"table"},S))):null;return R.createElement(R.Fragment,null,R.createElement("div",{className:Ee.IndexTable},R.createElement("div",{className:Ee.IndexTableWrapper},!k&&gt,qn,dn,Un)));function zn(Me,ft,Kt,en,cn){const _n=ft===0,xr=ft===t.length-1,An=u==null?void 0:u.some(Or=>Or===!0),ni=Me.alignment||"start",ri=ct(Ee.TableHeading,ni==="center"&&Ee["TableHeading-align-center"],ni==="end"&&Ee["TableHeading-align-end"],An&&Ee["TableHeading-sortable"],_n&&Ee["TableHeading-second"],xr&&!Me.hidden&&Ee["TableHeading-last"],!$&&Ee["TableHeading-unselectable"],Me.flush&&Ee["TableHeading-flush"]),Ii=$!==!1&&_n&&Q.current&&Q.current.length>0?{left:Q.current[0].offsetWidth}:void 0,kn=R.createElement(Kt,Object.assign({id:cn,className:ri,key:h2(Me),style:Ii},en),Fr(Me,ft));if(ft!==0||!$)return kn;const ii=ct(Ee.TableHeading,An&&Ee["TableHeading-sortable"],ft===0&&Ee["TableHeading-first"]);return[R.createElement(Kt,Object.assign({className:ii,key:`${Me}-${ft}`},en),Pn()),kn]}function Pn(){return R.createElement("div",{className:Ee.ColumnHeaderCheckboxWrapper},R.createElement(Ac,{label:C.translate("Polaris.IndexTable.selectAllLabel",{resourceNamePlural:Y.plural}),labelHidden:!0,onChange:Qr,checked:q}))}function Zn(Me,ft){Ie.current=!1,Te.current=!1,ke.current=b,v==null||v(Me,ft)}function Fr(Me,ft){let Kt;const en={width:Me.tooltipWidth??"default",activatorWrapper:"div",dismissOnMouseOut:!0,persistOnClick:Me.tooltipPersistsOnClick},cn={...en,padding:"400",borderRadius:"200",content:Me.tooltipContent,preferredPosition:"above"},_n=R.createElement(Ye,{as:"span",variant:"bodySm",fontWeight:"medium",visuallyHidden:Me.hidden},Me.title);Me.new?Kt=R.createElement(Sc,{wrap:!1,alignment:"center"},_n,R.createElement(or,{tone:"new"},C.translate("Polaris.IndexTable.onboardingBadgeText"))):Kt=_n;const xr={"--pc-index-table-heading-extra-padding-right":Me.paddingBlockEnd?`var(--p-space-${Me.paddingBlockEnd})`:"0"};if(u!=null&&u[ft]){const An=ft===b,ni=!An&&ft===ke.current,ri=Ie.current||!Te.current&&N!==0,Ii=p==="ascending";let kn=Me.defaultSortDirection??g,ii=kn==="ascending"?tc:nc;An&&(kn=Ii?"descending":"ascending",ii=p==="ascending"?tc:nc);const Tt=R.createElement("span",{className:ct(Ee.TableHeadingSortIcon,(Me==null?void 0:Me.alignment)==="end"&&Ee["TableHeadingSortIcon-heading-align-end"],An&&Ee["TableHeadingSortIcon-visible"])},R.createElement(ii,{focusable:"false","aria-hidden":"true",className:Ee.TableHeadingSortSvg})),Or={onClick:()=>Zn(ft,kn),className:ct(Ee.TableHeadingSortButton,!An&&(Me==null?void 0:Me.alignment)==="end"&&Ee["TableHeadingSortButton-heading-align-end"],An&&(Me==null?void 0:Me.alignment)==="end"&&Ee["TableHeadingSortButton-heading-align-end-currently-sorted"],ni&&!ri&&(Me==null?void 0:Me.alignment)==="end"&&Ee["TableHeadingSortButton-heading-align-end-previously-sorted"]),tabIndex:ae?-1:0},Ti=R.createElement(Zl,Or,Tt,R.createElement("span",{className:ct(d&&ae&&Me.tooltipContent&&Ee.TableHeadingTooltipUnderlinePlaceholder)},Kt));if(!d||ae)return R.createElement("div",{className:Ee.SortableTableHeadingWithCustomMarkup},Ti);const Qi=An?p:kn,ai=d[ft][Qi];if(!Me.tooltipContent)return R.createElement("div",{style:xr,className:ct(Me.paddingBlockEnd&&Ee["TableHeading-extra-padding-right"])},R.createElement(po,Object.assign({},en,{content:ai,preferredPosition:"above"}),Ti));if(Me.tooltipContent)return R.createElement("div",{className:ct(Ee.SortableTableHeadingWithCustomMarkup,Me.paddingBlockEnd&&Ee["TableHeading-extra-padding-right"]),style:xr},R.createElement(Zl,Or,R.createElement(po,cn,R.createElement("span",{className:Ee.TableHeadingUnderline},Kt)),R.createElement(po,Object.assign({},en,{content:ai,preferredPosition:"above"}),Tt)))}return Me.tooltipContent?R.createElement("div",{style:xr,className:ct(Me.paddingBlockEnd&&Ee["TableHeading-extra-padding-right"])},R.createElement(po,Object.assign({},cn,{activatorWrapper:"span"}),R.createElement("span",{className:ct(Ee.TableHeadingUnderline,Ee.SortableTableHeaderWrapper)},Kt))):R.createElement("div",{style:xr,className:ct(Me.paddingBlockEnd&&Ee["TableHeading-extra-padding-right"])},Kt)}function Qr(Me){O(nn.Page,Me)}function ei(){if(!$||!re)return;const Me=s??C.translate("Polaris.IndexTable.selectAllItems",{itemsLength:J,resourceNamePlural:Y.plural.toLocaleLowerCase()});return{content:N===Pi?C.translate("Polaris.IndexTable.undo"):Me,onAction:Qe}}function ti(){O(nn.All,!1)}}function h2(t){return t.id?t.id:typeof t.title=="string"?t.title:""}function Rt({children:t,selectable:e=!0,itemCount:n,selectedItemsCount:r=0,resourceName:o,loading:i,hasMoreItems:s,condensed:l,onSelectionChange:u,paginatedSelectAllText:p,...g}){return R.createElement(R.Fragment,null,R.createElement(Q1,{selectable:e&&!l,itemCount:n,selectedItemsCount:r,resourceName:o,loading:i,hasMoreItems:s,condensed:l,onSelectionChange:u,paginatedSelectAllText:p},R.createElement(f2,g,t)))}Rt.Cell=e2;Rt.Row=o2;var _r={Select:"Polaris-Select",disabled:"Polaris-Select--disabled",error:"Polaris-Select--error",Backdrop:"Polaris-Select__Backdrop",Input:"Polaris-Select__Input",Content:"Polaris-Select__Content",InlineLabel:"Polaris-Select__InlineLabel",Icon:"Polaris-Select__Icon",SelectedOption:"Polaris-Select__SelectedOption",Prefix:"Polaris-Select__Prefix",hover:"Polaris-Select--hover",toneMagic:"Polaris-Select--toneMagic"};const Iu="";function d2({options:t,label:e,labelAction:n,labelHidden:r,labelInline:o,disabled:i,helpText:s,placeholder:l,id:u,name:p,value:g=Iu,error:b,onChange:v,onFocus:d,onBlur:_,requiredIndicator:S,tone:T}){const{value:A,toggle:j}=ys(!1),q=de.useId(),Y=u??q,le=o?!0:r,ae=ct(_r.Select,b&&_r.error,T&&_r[ki("tone",T)],i&&_r.disabled),$=de.useCallback(Q=>{j(),d==null||d(Q)},[d,j]),F=de.useCallback(Q=>{j(),_==null||_(Q)},[_,j]),J=v?Q=>v(Q.currentTarget.value,Y):void 0,re=[];s&&re.push(Ed(Y)),b&&re.push(`${Y}Error`);let k=(t||[]).map(p2);l&&(k=[{label:l,value:Iu,disabled:!0},...k]);const O=o&&R.createElement(ut,{paddingInlineEnd:"100"},R.createElement(Ye,{as:"span",variant:"bodyMd",tone:T&&T==="magic"&&!A?"magic-subdued":"subdued",truncate:!0},e)),C=g2(k,g),X=C.prefix&&R.createElement("div",{className:_r.Prefix},C.prefix),ne=R.createElement("div",{className:_r.Content,"aria-hidden":!0,"aria-disabled":i},O,X,R.createElement("span",{className:_r.SelectedOption},C.label),R.createElement("span",{className:_r.Icon},R.createElement(vo,{source:kd}))),ue=k.map(b2);return R.createElement(Cd,{id:Y,label:e,error:b,action:n,labelHidden:le,helpText:s,requiredIndicator:S,disabled:i},R.createElement("div",{className:ae},R.createElement("select",{id:Y,name:p,value:g,className:_r.Input,disabled:i,onFocus:$,onBlur:F,onChange:J,"aria-invalid":!!b,"aria-describedby":re.length?re.join(" "):void 0,"aria-required":S},ue),ne,R.createElement("div",{className:_r.Backdrop})))}function Tu(t){return typeof t=="string"}function Cc(t){return typeof t=="object"&&"options"in t&&t.options!=null}function ju(t){return{label:t,value:t}}function p2(t){if(Tu(t))return ju(t);if(Cc(t)){const{title:e,options:n}=t;return{title:e,options:n.map(r=>Tu(r)?ju(r):r)}}return t}function g2(t,e){const n=m2(t);let r=n.find(o=>e===o.value);return r===void 0&&(r=n.find(o=>!o.hidden)),r||{value:"",label:""}}function m2(t){let e=[];return t.forEach(n=>{Cc(n)?e=e.concat(n.options):e.push(n)}),e}function Bu(t){const{value:e,label:n,prefix:r,key:o,...i}=t;return R.createElement("option",Object.assign({key:o??e,value:e},i),n)}function b2(t){if(Cc(t)){const{title:e,options:n}=t;return R.createElement("optgroup",{label:e,key:e},n.map(Bu))}return Bu(t)}const v2="modulepreload",y2=function(t){return"/"+t},Mu={},sc=function(e,n,r){let o=Promise.resolve();if(n&&n.length>0){let s=function(p){return Promise.all(p.map(g=>Promise.resolve(g).then(b=>({status:"fulfilled",value:b}),b=>({status:"rejected",reason:b}))))};document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),u=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));o=s(n.map(p=>{if(p=y2(p),p in Mu)return;Mu[p]=!0;const g=p.endsWith(".css"),b=g?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${p}"]${b}`))return;const v=document.createElement("link");if(v.rel=g?"stylesheet":v2,g||(v.as="script"),v.crossOrigin="",v.href=p,u&&v.setAttribute("nonce",u),document.head.appendChild(v),g)return new Promise((d,_)=>{v.addEventListener("load",d),v.addEventListener("error",()=>_(new Error(`Unable to preload CSS for ${p}`)))})}))}function i(s){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=s,window.dispatchEvent(l),!l.defaultPrevented)throw s}return o.then(s=>{for(const l of s||[])l.status==="rejected"&&i(l.reason);return e().catch(i)})};function Lt(t){"@babel/helpers - typeof";return Lt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Lt(t)}var wn=Uint8Array,Yn=Uint16Array,Ec=Int32Array,Ss=new wn([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),Ns=new wn([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),lc=new wn([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),oh=function(t,e){for(var n=new Yn(31),r=0;r<31;++r)n[r]=e+=1<<t[r-1];for(var o=new Ec(n[30]),r=1;r<30;++r)for(var i=n[r];i<n[r+1];++i)o[i]=i-n[r]<<5|r;return{b:n,r:o}},sh=oh(Ss,2),lh=sh.b,cc=sh.r;lh[28]=258,cc[258]=28;var ch=oh(Ns,0),w2=ch.b,Ru=ch.r,uc=new Yn(32768);for(var jt=0;jt<32768;++jt){var wi=(jt&43690)>>1|(jt&21845)<<1;wi=(wi&52428)>>2|(wi&13107)<<2,wi=(wi&61680)>>4|(wi&3855)<<4,uc[jt]=((wi&65280)>>8|(wi&255)<<8)>>1}var Tr=function(t,e,n){for(var r=t.length,o=0,i=new Yn(e);o<r;++o)t[o]&&++i[t[o]-1];var s=new Yn(e);for(o=1;o<e;++o)s[o]=s[o-1]+i[o-1]<<1;var l;if(n){l=new Yn(1<<e);var u=15-e;for(o=0;o<r;++o)if(t[o])for(var p=o<<4|t[o],g=e-t[o],b=s[t[o]-1]++<<g,v=b|(1<<g)-1;b<=v;++b)l[uc[b]>>u]=p}else for(l=new Yn(r),o=0;o<r;++o)t[o]&&(l[o]=uc[s[t[o]-1]++]>>15-t[o]);return l},Ci=new wn(288);for(var jt=0;jt<144;++jt)Ci[jt]=8;for(var jt=144;jt<256;++jt)Ci[jt]=9;for(var jt=256;jt<280;++jt)Ci[jt]=7;for(var jt=280;jt<288;++jt)Ci[jt]=8;var wo=new wn(32);for(var jt=0;jt<32;++jt)wo[jt]=5;var x2=Tr(Ci,9,0),A2=Tr(Ci,9,1),S2=Tr(wo,5,0),N2=Tr(wo,5,1),wl=function(t){for(var e=t[0],n=1;n<t.length;++n)t[n]>e&&(e=t[n]);return e},pr=function(t,e,n){var r=e/8|0;return(t[r]|t[r+1]<<8)>>(e&7)&n},xl=function(t,e){var n=e/8|0;return(t[n]|t[n+1]<<8|t[n+2]<<16)>>(e&7)},Ic=function(t){return(t+7)/8|0},uh=function(t,e,n){return(n==null||n>t.length)&&(n=t.length),new wn(t.subarray(e,n))},L2=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],mr=function(t,e,n){var r=new Error(e||L2[t]);if(r.code=t,Error.captureStackTrace&&Error.captureStackTrace(r,mr),!n)throw r;return r},P2=function(t,e,n,r){var o=t.length,i=0;if(!o||e.f&&!e.l)return n||new wn(0);var s=!n,l=s||e.i!=2,u=e.i;s&&(n=new wn(o*3));var p=function(Se){var be=n.length;if(Se>be){var Ce=new wn(Math.max(be*2,Se));Ce.set(n),n=Ce}},g=e.f||0,b=e.p||0,v=e.b||0,d=e.l,_=e.d,S=e.m,T=e.n,A=o*8;do{if(!d){g=pr(t,b,1);var j=pr(t,b+1,3);if(b+=3,j)if(j==1)d=A2,_=N2,S=9,T=5;else if(j==2){var ae=pr(t,b,31)+257,$=pr(t,b+10,15)+4,F=ae+pr(t,b+5,31)+1;b+=14;for(var J=new wn(F),re=new wn(19),N=0;N<$;++N)re[lc[N]]=pr(t,b+N*3,7);b+=$*3;for(var k=wl(re),O=(1<<k)-1,C=Tr(re,k,1),N=0;N<F;){var X=C[pr(t,b,O)];b+=X&15;var q=X>>4;if(q<16)J[N++]=q;else{var ne=0,ue=0;for(q==16?(ue=3+pr(t,b,3),b+=2,ne=J[N-1]):q==17?(ue=3+pr(t,b,7),b+=3):q==18&&(ue=11+pr(t,b,127),b+=7);ue--;)J[N++]=ne}}var Q=J.subarray(0,ae),he=J.subarray(ae);S=wl(Q),T=wl(he),d=Tr(Q,S,1),_=Tr(he,T,1)}else mr(1);else{var q=Ic(b)+4,Y=t[q-4]|t[q-3]<<8,le=q+Y;if(le>o){u&&mr(0);break}l&&p(v+Y),n.set(t.subarray(q,le),v),e.b=v+=Y,e.p=b=le*8,e.f=g;continue}if(b>A){u&&mr(0);break}}l&&p(v+131072);for(var V=(1<<S)-1,me=(1<<T)-1,x=b;;x=b){var ne=d[xl(t,b)&V],B=ne>>4;if(b+=ne&15,b>A){u&&mr(0);break}if(ne||mr(2),B<256)n[v++]=B;else if(B==256){x=b,d=null;break}else{var M=B-254;if(B>264){var N=B-257,z=Ss[N];M=pr(t,b,(1<<z)-1)+lh[N],b+=z}var H=_[xl(t,b)&me],G=H>>4;H||mr(3),b+=H&15;var he=w2[G];if(G>3){var z=Ns[G];he+=xl(t,b)&(1<<z)-1,b+=z}if(b>A){u&&mr(0);break}l&&p(v+131072);var ee=v+M;if(v<he){var oe=i-he,ve=Math.min(he,ee);for(oe+v<0&&mr(3);v<ve;++v)n[v]=r[oe+v]}for(;v<ee;++v)n[v]=n[v-he]}}e.l=d,e.p=x,e.b=v,e.f=g,d&&(g=1,e.m=S,e.d=_,e.n=T)}while(!g);return v!=n.length&&s?uh(n,0,v):n.subarray(0,v)},Jr=function(t,e,n){n<<=e&7;var r=e/8|0;t[r]|=n,t[r+1]|=n>>8},co=function(t,e,n){n<<=e&7;var r=e/8|0;t[r]|=n,t[r+1]|=n>>8,t[r+2]|=n>>16},Al=function(t,e){for(var n=[],r=0;r<t.length;++r)t[r]&&n.push({s:r,f:t[r]});var o=n.length,i=n.slice();if(!o)return{t:hh,l:0};if(o==1){var s=new wn(n[0].s+1);return s[n[0].s]=1,{t:s,l:1}}n.sort(function(le,ae){return le.f-ae.f}),n.push({s:-1,f:25001});var l=n[0],u=n[1],p=0,g=1,b=2;for(n[0]={s:-1,f:l.f+u.f,l,r:u};g!=o-1;)l=n[n[p].f<n[b].f?p++:b++],u=n[p!=g&&n[p].f<n[b].f?p++:b++],n[g++]={s:-1,f:l.f+u.f,l,r:u};for(var v=i[0].s,r=1;r<o;++r)i[r].s>v&&(v=i[r].s);var d=new Yn(v+1),_=fc(n[g-1],d,0);if(_>e){var r=0,S=0,T=_-e,A=1<<T;for(i.sort(function(ae,$){return d[$.s]-d[ae.s]||ae.f-$.f});r<o;++r){var j=i[r].s;if(d[j]>e)S+=A-(1<<_-d[j]),d[j]=e;else break}for(S>>=T;S>0;){var q=i[r].s;d[q]<e?S-=1<<e-d[q]++-1:++r}for(;r>=0&&S;--r){var Y=i[r].s;d[Y]==e&&(--d[Y],++S)}_=e}return{t:new wn(d),l:_}},fc=function(t,e,n){return t.s==-1?Math.max(fc(t.l,e,n+1),fc(t.r,e,n+1)):e[t.s]=n},Fu=function(t){for(var e=t.length;e&&!t[--e];);for(var n=new Yn(++e),r=0,o=t[0],i=1,s=function(u){n[r++]=u},l=1;l<=e;++l)if(t[l]==o&&l!=e)++i;else{if(!o&&i>2){for(;i>138;i-=138)s(32754);i>2&&(s(i>10?i-11<<5|28690:i-3<<5|12305),i=0)}else if(i>3){for(s(o),--i;i>6;i-=6)s(8304);i>2&&(s(i-3<<5|8208),i=0)}for(;i--;)s(o);i=1,o=t[l]}return{c:n.subarray(0,r),n:e}},uo=function(t,e){for(var n=0,r=0;r<e.length;++r)n+=t[r]*e[r];return n},fh=function(t,e,n){var r=n.length,o=Ic(e+2);t[o]=r&255,t[o+1]=r>>8,t[o+2]=t[o]^255,t[o+3]=t[o+1]^255;for(var i=0;i<r;++i)t[o+i+4]=n[i];return(o+4+r)*8},Ou=function(t,e,n,r,o,i,s,l,u,p,g){Jr(e,g++,n),++o[256];for(var b=Al(o,15),v=b.t,d=b.l,_=Al(i,15),S=_.t,T=_.l,A=Fu(v),j=A.c,q=A.n,Y=Fu(S),le=Y.c,ae=Y.n,$=new Yn(19),F=0;F<j.length;++F)++$[j[F]&31];for(var F=0;F<le.length;++F)++$[le[F]&31];for(var J=Al($,7),re=J.t,N=J.l,k=19;k>4&&!re[lc[k-1]];--k);var O=p+5<<3,C=uo(o,Ci)+uo(i,wo)+s,X=uo(o,v)+uo(i,S)+s+14+3*k+uo($,re)+2*$[16]+3*$[17]+7*$[18];if(u>=0&&O<=C&&O<=X)return fh(e,g,t.subarray(u,u+p));var ne,ue,Q,he;if(Jr(e,g,1+(X<C)),g+=2,X<C){ne=Tr(v,d,0),ue=v,Q=Tr(S,T,0),he=S;var V=Tr(re,N,0);Jr(e,g,q-257),Jr(e,g+5,ae-1),Jr(e,g+10,k-4),g+=14;for(var F=0;F<k;++F)Jr(e,g+3*F,re[lc[F]]);g+=3*k;for(var me=[j,le],x=0;x<2;++x)for(var B=me[x],F=0;F<B.length;++F){var M=B[F]&31;Jr(e,g,V[M]),g+=re[M],M>15&&(Jr(e,g,B[F]>>5&127),g+=B[F]>>12)}}else ne=x2,ue=Ci,Q=S2,he=wo;for(var F=0;F<l;++F){var z=r[F];if(z>255){var M=z>>18&31;co(e,g,ne[M+257]),g+=ue[M+257],M>7&&(Jr(e,g,z>>23&31),g+=Ss[M]);var H=z&31;co(e,g,Q[H]),g+=he[H],H>3&&(co(e,g,z>>5&8191),g+=Ns[H])}else co(e,g,ne[z]),g+=ue[z]}return co(e,g,ne[256]),g+ue[256]},_2=new Ec([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),hh=new wn(0),k2=function(t,e,n,r,o,i){var s=i.z||t.length,l=new wn(r+s+5*(1+Math.ceil(s/7e3))+o),u=l.subarray(r,l.length-o),p=i.l,g=(i.r||0)&7;if(e){g&&(u[0]=i.r>>3);for(var b=_2[e-1],v=b>>13,d=b&8191,_=(1<<n)-1,S=i.p||new Yn(32768),T=i.h||new Yn(_+1),A=Math.ceil(n/3),j=2*A,q=function(ze){return(t[ze]^t[ze+1]<<A^t[ze+2]<<j)&_},Y=new Ec(25e3),le=new Yn(288),ae=new Yn(32),$=0,F=0,J=i.i||0,re=0,N=i.w||0,k=0;J+2<s;++J){var O=q(J),C=J&32767,X=T[O];if(S[C]=X,T[O]=C,N<=J){var ne=s-J;if(($>7e3||re>24576)&&(ne>423||!p)){g=Ou(t,u,0,Y,le,ae,F,re,k,J-k,g),re=$=F=0,k=J;for(var ue=0;ue<286;++ue)le[ue]=0;for(var ue=0;ue<30;++ue)ae[ue]=0}var Q=2,he=0,V=d,me=C-X&32767;if(ne>2&&O==q(J-me))for(var x=Math.min(v,ne)-1,B=Math.min(32767,J),M=Math.min(258,ne);me<=B&&--V&&C!=X;){if(t[J+Q]==t[J+Q-me]){for(var z=0;z<M&&t[J+z]==t[J+z-me];++z);if(z>Q){if(Q=z,he=me,z>x)break;for(var H=Math.min(me,z-2),G=0,ue=0;ue<H;++ue){var ee=J-me+ue&32767,oe=S[ee],ve=ee-oe&32767;ve>G&&(G=ve,X=ee)}}}C=X,X=S[C],me+=C-X&32767}if(he){Y[re++]=268435456|cc[Q]<<18|Ru[he];var Se=cc[Q]&31,be=Ru[he]&31;F+=Ss[Se]+Ns[be],++le[257+Se],++ae[be],N=J+Q,++$}else Y[re++]=t[J],++le[t[J]]}}for(J=Math.max(J,N);J<s;++J)Y[re++]=t[J],++le[t[J]];g=Ou(t,u,p,Y,le,ae,F,re,k,J-k,g),p||(i.r=g&7|u[g/8|0]<<3,g-=7,i.h=T,i.p=S,i.i=J,i.w=N)}else{for(var J=i.w||0;J<s+p;J+=65535){var Ce=J+65535;Ce>=s&&(u[g/8|0]=p,Ce=s),g=fh(u,g+1,t.subarray(J,Ce))}i.i=s}return uh(l,0,r+Ic(g)+o)},dh=function(){var t=1,e=0;return{p:function(n){for(var r=t,o=e,i=n.length|0,s=0;s!=i;){for(var l=Math.min(s+2655,i);s<l;++s)o+=r+=n[s];r=(r&65535)+15*(r>>16),o=(o&65535)+15*(o>>16)}t=r,e=o},d:function(){return t%=65521,e%=65521,(t&255)<<24|(t&65280)<<8|(e&255)<<8|e>>8}}},C2=function(t,e,n,r,o){if(!o&&(o={l:1},e.dictionary)){var i=e.dictionary.subarray(-32768),s=new wn(i.length+t.length);s.set(i),s.set(t,i.length),t=s,o.w=i.length}return k2(t,e.level==null?6:e.level,e.mem==null?o.l?Math.ceil(Math.max(8,Math.min(13,Math.log(t.length)))*1.5):20:12+e.mem,n,r,o)},ph=function(t,e,n){for(;n;++e)t[e]=n,n>>>=8},E2=function(t,e){var n=e.level,r=n==0?0:n<6?1:n==9?3:2;if(t[0]=120,t[1]=r<<6|(e.dictionary&&32),t[1]|=31-(t[0]<<8|t[1])%31,e.dictionary){var o=dh();o.p(e.dictionary),ph(t,2,o.d())}},I2=function(t,e){return((t[0]&15)!=8||t[0]>>4>7||(t[0]<<8|t[1])%31)&&mr(6,"invalid zlib data"),(t[1]>>5&1)==1&&mr(6,"invalid zlib data: "+(t[1]&32?"need":"unexpected")+" dictionary"),(t[1]>>3&4)+2};function hc(t,e){e||(e={});var n=dh();n.p(t);var r=C2(t,e,e.dictionary?6:2,4);return E2(r,e),ph(r,r.length-4,n.d()),r}function T2(t,e){return P2(t.subarray(I2(t),-4),{i:2},e,e)}var j2=typeof TextDecoder<"u"&&new TextDecoder,B2=0;try{j2.decode(hh,{stream:!0}),B2=1}catch{}var Xe=function(){return typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:this}();function Sl(){Xe.console&&typeof Xe.console.log=="function"&&Xe.console.log.apply(Xe.console,arguments)}var kt={log:Sl,warn:function(t){Xe.console&&(typeof Xe.console.warn=="function"?Xe.console.warn.apply(Xe.console,arguments):Sl.call(null,arguments))},error:function(t){Xe.console&&(typeof Xe.console.error=="function"?Xe.console.error.apply(Xe.console,arguments):Sl(t))}};function Nl(t,e,n){var r=new XMLHttpRequest;r.open("GET",t),r.responseType="blob",r.onload=function(){Vi(r.response,e,n)},r.onerror=function(){kt.error("could not download file")},r.send()}function Du(t){var e=new XMLHttpRequest;e.open("HEAD",t,!1);try{e.send()}catch{}return e.status>=200&&e.status<=299}function os(t){try{t.dispatchEvent(new MouseEvent("click"))}catch{var e=document.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),t.dispatchEvent(e)}}var bo,dc,Vi=Xe.saveAs||((typeof window>"u"?"undefined":Lt(window))!=="object"||window!==Xe?function(){}:typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype?function(t,e,n){var r=Xe.URL||Xe.webkitURL,o=document.createElement("a");e=e||t.name||"download",o.download=e,o.rel="noopener",typeof t=="string"?(o.href=t,o.origin!==location.origin?Du(o.href)?Nl(t,e,n):os(o,o.target="_blank"):os(o)):(o.href=r.createObjectURL(t),setTimeout(function(){r.revokeObjectURL(o.href)},4e4),setTimeout(function(){os(o)},0))}:"msSaveOrOpenBlob"in navigator?function(t,e,n){if(e=e||t.name||"download",typeof t=="string")if(Du(t))Nl(t,e,n);else{var r=document.createElement("a");r.href=t,r.target="_blank",setTimeout(function(){os(r)})}else navigator.msSaveOrOpenBlob(function(o,i){return i===void 0?i={autoBom:!1}:Lt(i)!=="object"&&(kt.warn("Deprecated: Expected third argument to be a object"),i={autoBom:!i}),i.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(o.type)?new Blob(["\uFEFF",o],{type:o.type}):o}(t,n),e)}:function(t,e,n,r){if((r=r||open("","_blank"))&&(r.document.title=r.document.body.innerText="downloading..."),typeof t=="string")return Nl(t,e,n);var o=t.type==="application/octet-stream",i=/constructor/i.test(Xe.HTMLElement)||Xe.safari,s=/CriOS\/[\d]+/.test(navigator.userAgent);if((s||o&&i)&&(typeof FileReader>"u"?"undefined":Lt(FileReader))==="object"){var l=new FileReader;l.onloadend=function(){var g=l.result;g=s?g:g.replace(/^data:[^;]*;/,"data:attachment/file;"),r?r.location.href=g:location=g,r=null},l.readAsDataURL(t)}else{var u=Xe.URL||Xe.webkitURL,p=u.createObjectURL(t);r?r.location=p:location.href=p,r=null,setTimeout(function(){u.revokeObjectURL(p)},4e4)}});/**
 * A class to parse color values
 * <AUTHOR> Stefanov <<EMAIL>>
 * {@link   http://www.phpied.com/rgb-color-parser-in-javascript/}
 * @license Use it if you like it
 */function gh(t){var e;t=t||"",this.ok=!1,t.charAt(0)=="#"&&(t=t.substr(1,6)),t={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"}[t=(t=t.replace(/ /g,"")).toLowerCase()]||t;for(var n=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(l){return[parseInt(l[1]),parseInt(l[2]),parseInt(l[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(l){return[parseInt(l[1],16),parseInt(l[2],16),parseInt(l[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(l){return[parseInt(l[1]+l[1],16),parseInt(l[2]+l[2],16),parseInt(l[3]+l[3],16)]}}],r=0;r<n.length;r++){var o=n[r].re,i=n[r].process,s=o.exec(t);s&&(e=i(s),this.r=e[0],this.g=e[1],this.b=e[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var l=this.r.toString(16),u=this.g.toString(16),p=this.b.toString(16);return l.length==1&&(l="0"+l),u.length==1&&(u="0"+u),p.length==1&&(p="0"+p),"#"+l+u+p}}/**
 * @license
 * Joseph Myers does not specify a particular license for his work.
 *
 * Author: Joseph Myers
 * Accessed from: http://www.myersdaily.org/joseph/javascript/md5.js
 *
 * Modified by: Owen Leong
 */function Ll(t,e){var n=t[0],r=t[1],o=t[2],i=t[3];n=mn(n,r,o,i,e[0],7,-680876936),i=mn(i,n,r,o,e[1],12,-389564586),o=mn(o,i,n,r,e[2],17,606105819),r=mn(r,o,i,n,e[3],22,-**********),n=mn(n,r,o,i,e[4],7,-176418897),i=mn(i,n,r,o,e[5],12,**********),o=mn(o,i,n,r,e[6],17,-**********),r=mn(r,o,i,n,e[7],22,-45705983),n=mn(n,r,o,i,e[8],7,**********),i=mn(i,n,r,o,e[9],12,-**********),o=mn(o,i,n,r,e[10],17,-42063),r=mn(r,o,i,n,e[11],22,-**********),n=mn(n,r,o,i,e[12],7,**********),i=mn(i,n,r,o,e[13],12,-40341101),o=mn(o,i,n,r,e[14],17,-**********),n=bn(n,r=mn(r,o,i,n,e[15],22,**********),o,i,e[1],5,-165796510),i=bn(i,n,r,o,e[6],9,-**********),o=bn(o,i,n,r,e[11],14,643717713),r=bn(r,o,i,n,e[0],20,-373897302),n=bn(n,r,o,i,e[5],5,-701558691),i=bn(i,n,r,o,e[10],9,38016083),o=bn(o,i,n,r,e[15],14,-660478335),r=bn(r,o,i,n,e[4],20,-405537848),n=bn(n,r,o,i,e[9],5,568446438),i=bn(i,n,r,o,e[14],9,-1019803690),o=bn(o,i,n,r,e[3],14,-187363961),r=bn(r,o,i,n,e[8],20,1163531501),n=bn(n,r,o,i,e[13],5,-1444681467),i=bn(i,n,r,o,e[2],9,-51403784),o=bn(o,i,n,r,e[7],14,1735328473),n=vn(n,r=bn(r,o,i,n,e[12],20,-1926607734),o,i,e[5],4,-378558),i=vn(i,n,r,o,e[8],11,-2022574463),o=vn(o,i,n,r,e[11],16,1839030562),r=vn(r,o,i,n,e[14],23,-35309556),n=vn(n,r,o,i,e[1],4,-1530992060),i=vn(i,n,r,o,e[4],11,1272893353),o=vn(o,i,n,r,e[7],16,-155497632),r=vn(r,o,i,n,e[10],23,-1094730640),n=vn(n,r,o,i,e[13],4,681279174),i=vn(i,n,r,o,e[0],11,-358537222),o=vn(o,i,n,r,e[3],16,-722521979),r=vn(r,o,i,n,e[6],23,76029189),n=vn(n,r,o,i,e[9],4,-640364487),i=vn(i,n,r,o,e[12],11,-421815835),o=vn(o,i,n,r,e[15],16,530742520),n=yn(n,r=vn(r,o,i,n,e[2],23,-995338651),o,i,e[0],6,-198630844),i=yn(i,n,r,o,e[7],10,1126891415),o=yn(o,i,n,r,e[14],15,-1416354905),r=yn(r,o,i,n,e[5],21,-57434055),n=yn(n,r,o,i,e[12],6,1700485571),i=yn(i,n,r,o,e[3],10,-1894986606),o=yn(o,i,n,r,e[10],15,-1051523),r=yn(r,o,i,n,e[1],21,-2054922799),n=yn(n,r,o,i,e[8],6,1873313359),i=yn(i,n,r,o,e[15],10,-30611744),o=yn(o,i,n,r,e[6],15,-1560198380),r=yn(r,o,i,n,e[13],21,1309151649),n=yn(n,r,o,i,e[4],6,-145523070),i=yn(i,n,r,o,e[11],10,-1120210379),o=yn(o,i,n,r,e[2],15,718787259),r=yn(r,o,i,n,e[9],21,-343485551),t[0]=_i(n,t[0]),t[1]=_i(r,t[1]),t[2]=_i(o,t[2]),t[3]=_i(i,t[3])}function Ls(t,e,n,r,o,i){return e=_i(_i(e,t),_i(r,i)),_i(e<<o|e>>>32-o,n)}function mn(t,e,n,r,o,i,s){return Ls(e&n|~e&r,t,e,o,i,s)}function bn(t,e,n,r,o,i,s){return Ls(e&r|n&~r,t,e,o,i,s)}function vn(t,e,n,r,o,i,s){return Ls(e^n^r,t,e,o,i,s)}function yn(t,e,n,r,o,i,s){return Ls(n^(e|~r),t,e,o,i,s)}function mh(t){var e,n=t.length,r=[1732584193,-271733879,-1732584194,271733878];for(e=64;e<=t.length;e+=64)Ll(r,M2(t.substring(e-64,e)));t=t.substring(e-64);var o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;e<t.length;e++)o[e>>2]|=t.charCodeAt(e)<<(e%4<<3);if(o[e>>2]|=128<<(e%4<<3),e>55)for(Ll(r,o),e=0;e<16;e++)o[e]=0;return o[14]=8*n,Ll(r,o),r}function M2(t){var e,n=[];for(e=0;e<64;e+=4)n[e>>2]=t.charCodeAt(e)+(t.charCodeAt(e+1)<<8)+(t.charCodeAt(e+2)<<16)+(t.charCodeAt(e+3)<<24);return n}bo=Xe.atob.bind(Xe),dc=Xe.btoa.bind(Xe);var qu="**********abcdef".split("");function R2(t){for(var e="",n=0;n<4;n++)e+=qu[t>>8*n+4&15]+qu[t>>8*n&15];return e}function F2(t){return String.fromCharCode((255&t)>>0,(65280&t)>>8,(16711680&t)>>16,(**********&t)>>24)}function pc(t){return mh(t).map(F2).join("")}var O2=function(t){for(var e=0;e<t.length;e++)t[e]=R2(t[e]);return t.join("")}(mh("hello"))!="5d41402abc4b2a76b9719d911017c592";function _i(t,e){if(O2){var n=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(n>>16)<<16|65535&n}return t+e&**********}/**
 * @license
 * FPDF is released under a permissive license: there is no usage restriction.
 * You may embed it freely in your application (commercial or not), with or
 * without modifications.
 *
 * Reference: http://www.fpdf.org/en/script/script37.php
 */function gc(t,e){var n,r,o,i;if(t!==n){for(var s=(o=t,i=1+(256/t.length>>0),new Array(i+1).join(o)),l=[],u=0;u<256;u++)l[u]=u;var p=0;for(u=0;u<256;u++){var g=l[u];p=(p+g+s.charCodeAt(u))%256,l[u]=l[p],l[p]=g}n=t,r=l}else l=r;var b=e.length,v=0,d=0,_="";for(u=0;u<b;u++)d=(d+(g=l[v=(v+1)%256]))%256,l[v]=l[d],l[d]=g,s=l[(l[v]+l[d])%256],_+=String.fromCharCode(e.charCodeAt(u)^s);return _}/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 * Author: Owen Leong (@owenl131)
 * Date: 15 Oct 2020
 * References:
 * https://www.cs.cmu.edu/~dst/Adobe/Gallery/anon21jul01-pdf-encryption.txt
 * https://github.com/foliojs/pdfkit/blob/master/lib/security.js
 * http://www.fpdf.org/en/script/script37.php
 */var Uu={print:4,modify:8,copy:16,"annot-forms":32};function _a(t,e,n,r){this.v=1,this.r=2;var o=192;t.forEach(function(l){if(Uu.perm!==void 0)throw new Error("Invalid permission: "+l);o+=Uu[l]}),this.padding="(¿N^NuAd\0NVÿú\b..\0¶Ðh>/\f©þdSiz";var i=(e+this.padding).substr(0,32),s=(n+this.padding).substr(0,32);this.O=this.processOwnerPassword(i,s),this.P=-(1+(255^o)),this.encryptionKey=pc(i+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(r)).substr(0,5),this.U=gc(this.encryptionKey,this.padding)}function ka(t){if(/[^\u0000-\u00ff]/.test(t))throw new Error("Invalid PDF Name Object: "+t+", Only accept ASCII characters.");for(var e="",n=t.length,r=0;r<n;r++){var o=t.charCodeAt(r);o<33||o===35||o===37||o===40||o===41||o===47||o===60||o===62||o===91||o===93||o===123||o===125||o>126?e+="#"+("0"+o.toString(16)).slice(-2):e+=t[r]}return e}function zu(t){if(Lt(t)!=="object")throw new Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var e={};this.subscribe=function(n,r,o){if(o=o||!1,typeof n!="string"||typeof r!="function"||typeof o!="boolean")throw new Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");e.hasOwnProperty(n)||(e[n]={});var i=Math.random().toString(35);return e[n][i]=[r,!!o],i},this.unsubscribe=function(n){for(var r in e)if(e[r][n])return delete e[r][n],Object.keys(e[r]).length===0&&delete e[r],!0;return!1},this.publish=function(n){if(e.hasOwnProperty(n)){var r=Array.prototype.slice.call(arguments,1),o=[];for(var i in e[n]){var s=e[n][i];try{s[0].apply(t,r)}catch(l){Xe.console&&kt.error("jsPDF PubSub Error",l.message,l)}s[1]&&o.push(i)}o.length&&o.forEach(this.unsubscribe)}},this.getTopics=function(){return e}}function bs(t){if(!(this instanceof bs))return new bs(t);var e="opacity,stroke-opacity".split(",");for(var n in t)t.hasOwnProperty(n)&&e.indexOf(n)>=0&&(this[n]=t[n]);this.id="",this.objectNumber=-1}function bh(t,e){this.gState=t,this.matrix=e,this.id="",this.objectNumber=-1}function Gi(t,e,n,r,o){if(!(this instanceof Gi))return new Gi(t,e,n,r,o);this.type=t==="axial"?2:3,this.coords=e,this.colors=n,bh.call(this,r,o)}function Ca(t,e,n,r,o){if(!(this instanceof Ca))return new Ca(t,e,n,r,o);this.boundingBox=t,this.xStep=e,this.yStep=n,this.stream="",this.cloneIndex=0,bh.call(this,r,o)}function Ke(t){var e,n=typeof arguments[0]=="string"?arguments[0]:"p",r=arguments[1],o=arguments[2],i=arguments[3],s=[],l=1,u=16,p="S",g=null;Lt(t=t||{})==="object"&&(n=t.orientation,r=t.unit||r,o=t.format||o,i=t.compress||t.compressPdf||i,(g=t.encryption||null)!==null&&(g.userPassword=g.userPassword||"",g.ownerPassword=g.ownerPassword||"",g.userPermissions=g.userPermissions||[]),l=typeof t.userUnit=="number"?Math.abs(t.userUnit):1,t.precision!==void 0&&(e=t.precision),t.floatPrecision!==void 0&&(u=t.floatPrecision),p=t.defaultPathOperation||"S"),s=t.filters||(i===!0?["FlateEncode"]:s),r=r||"mm",n=(""+(n||"P")).toLowerCase();var b=t.putOnlyUsedFonts||!1,v={},d={internal:{},__private__:{}};d.__private__.PubSub=zu;var _="1.3",S=d.__private__.getPdfVersion=function(){return _};d.__private__.setPdfVersion=function(f){_=f};var T={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};d.__private__.getPageFormats=function(){return T};var A=d.__private__.getPageFormat=function(f){return T[f]};o=o||"a4";var j={COMPAT:"compat",ADVANCED:"advanced"},q=j.COMPAT;function Y(){this.saveGraphicsState(),W(new Ve(je,0,0,-je,0,li()*je).toString()+" cm"),this.setFontSize(this.getFontSize()/je),p="n",q=j.ADVANCED}function le(){this.restoreGraphicsState(),p="S",q=j.COMPAT}var ae=d.__private__.combineFontStyleAndFontWeight=function(f,w){if(f=="bold"&&w=="normal"||f=="bold"&&w==400||f=="normal"&&w=="italic"||f=="bold"&&w=="italic")throw new Error("Invalid Combination of fontweight and fontstyle");return w&&(f=w==400||w==="normal"?f==="italic"?"italic":"normal":w!=700&&w!=="bold"||f!=="normal"?(w==700?"bold":w)+""+f:"bold"),f};d.advancedAPI=function(f){var w=q===j.COMPAT;return w&&Y.call(this),typeof f!="function"||(f(this),w&&le.call(this)),this},d.compatAPI=function(f){var w=q===j.ADVANCED;return w&&le.call(this),typeof f!="function"||(f(this),w&&Y.call(this)),this},d.isAdvancedAPI=function(){return q===j.ADVANCED};var $,F=function(f){if(q!==j.ADVANCED)throw new Error(f+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},J=d.roundToPrecision=d.__private__.roundToPrecision=function(f,w){var U=e||w;if(isNaN(f)||isNaN(U))throw new Error("Invalid argument passed to jsPDF.roundToPrecision");return f.toFixed(U).replace(/0+$/,"")};$=d.hpf=d.__private__.hpf=typeof u=="number"?function(f){if(isNaN(f))throw new Error("Invalid argument passed to jsPDF.hpf");return J(f,u)}:u==="smart"?function(f){if(isNaN(f))throw new Error("Invalid argument passed to jsPDF.hpf");return J(f,f>-1&&f<1?16:5)}:function(f){if(isNaN(f))throw new Error("Invalid argument passed to jsPDF.hpf");return J(f,16)};var re=d.f2=d.__private__.f2=function(f){if(isNaN(f))throw new Error("Invalid argument passed to jsPDF.f2");return J(f,2)},N=d.__private__.f3=function(f){if(isNaN(f))throw new Error("Invalid argument passed to jsPDF.f3");return J(f,3)},k=d.scale=d.__private__.scale=function(f){if(isNaN(f))throw new Error("Invalid argument passed to jsPDF.scale");return q===j.COMPAT?f*je:q===j.ADVANCED?f:void 0},O=function(f){return q===j.COMPAT?li()-f:q===j.ADVANCED?f:void 0},C=function(f){return k(O(f))};d.__private__.setPrecision=d.setPrecision=function(f){typeof parseInt(f,10)=="number"&&(e=parseInt(f,10))};var X,ne="00000000000000000000000000000000",ue=d.__private__.getFileId=function(){return ne},Q=d.__private__.setFileId=function(f){return ne=f!==void 0&&/^[a-fA-F0-9]{32}$/.test(f)?f.toUpperCase():ne.split("").map(function(){return"ABCDEF**********".charAt(Math.floor(16*Math.random()))}).join(""),g!==null&&(pn=new _a(g.userPermissions,g.userPassword,g.ownerPassword,ne)),ne};d.setFileId=function(f){return Q(f),this},d.getFileId=function(){return ue()};var he=d.__private__.convertDateToPDFDate=function(f){var w=f.getTimezoneOffset(),U=w<0?"+":"-",K=Math.floor(Math.abs(w/60)),fe=Math.abs(w%60),xe=[U,M(K),"'",M(fe),"'"].join("");return["D:",f.getFullYear(),M(f.getMonth()+1),M(f.getDate()),M(f.getHours()),M(f.getMinutes()),M(f.getSeconds()),xe].join("")},V=d.__private__.convertPDFDateToDate=function(f){var w=parseInt(f.substr(2,4),10),U=parseInt(f.substr(6,2),10)-1,K=parseInt(f.substr(8,2),10),fe=parseInt(f.substr(10,2),10),xe=parseInt(f.substr(12,2),10),_e=parseInt(f.substr(14,2),10);return new Date(w,U,K,fe,xe,_e,0)},me=d.__private__.setCreationDate=function(f){var w;if(f===void 0&&(f=new Date),f instanceof Date)w=he(f);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(f))throw new Error("Invalid argument passed to jsPDF.setCreationDate");w=f}return X=w},x=d.__private__.getCreationDate=function(f){var w=X;return f==="jsDate"&&(w=V(X)),w};d.setCreationDate=function(f){return me(f),this},d.getCreationDate=function(f){return x(f)};var B,M=d.__private__.padd2=function(f){return("0"+parseInt(f)).slice(-2)},z=d.__private__.padd2Hex=function(f){return("00"+(f=f.toString())).substr(f.length)},H=0,G=[],ee=[],oe=0,ve=[],Se=[],be=!1,Ce=ee,ze=function(){H=0,oe=0,ee=[],G=[],ve=[],lr=Dt(),Dn=Dt()};d.__private__.setCustomOutputDestination=function(f){be=!0,Ce=f};var ye=function(f){be||(Ce=f)};d.__private__.resetCustomOutputDestination=function(){be=!1,Ce=ee};var W=d.__private__.out=function(f){return f=f.toString(),oe+=f.length+1,Ce.push(f),Ce},Ze=d.__private__.write=function(f){return W(arguments.length===1?f.toString():Array.prototype.join.call(arguments," "))},qe=d.__private__.getArrayBuffer=function(f){for(var w=f.length,U=new ArrayBuffer(w),K=new Uint8Array(U);w--;)K[w]=f.charCodeAt(w);return U},ke=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];d.__private__.getStandardFonts=function(){return ke};var Ie=t.fontSize||16;d.__private__.setFontSize=d.setFontSize=function(f){return Ie=q===j.ADVANCED?f/je:f,this};var Fe,Te=d.__private__.getFontSize=d.getFontSize=function(){return q===j.COMPAT?Ie:Ie*je},He=t.R2L||!1;d.__private__.setR2L=d.setR2L=function(f){return He=f,this},d.__private__.getR2L=d.getR2L=function(){return He};var Qe,et=d.__private__.setZoomMode=function(f){var w=[void 0,null,"fullwidth","fullheight","fullpage","original"];if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(f))Fe=f;else if(isNaN(f)){if(w.indexOf(f)===-1)throw new Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+f+'" is not recognized.');Fe=f}else Fe=parseInt(f,10)};d.__private__.getZoomMode=function(){return Fe};var rt,dt=d.__private__.setPageMode=function(f){if([void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(f)==-1)throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+f+'" is not recognized.');Qe=f};d.__private__.getPageMode=function(){return Qe};var yt=d.__private__.setLayoutMode=function(f){if([void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(f)==-1)throw new Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+f+'" is not recognized.');rt=f};d.__private__.getLayoutMode=function(){return rt},d.__private__.setDisplayMode=d.setDisplayMode=function(f,w,U){return et(f),yt(w),dt(U),this};var $e={title:"",subject:"",author:"",keywords:"",creator:""};d.__private__.getDocumentProperty=function(f){if(Object.keys($e).indexOf(f)===-1)throw new Error("Invalid argument passed to jsPDF.getDocumentProperty");return $e[f]},d.__private__.getDocumentProperties=function(){return $e},d.__private__.setDocumentProperties=d.setProperties=d.setDocumentProperties=function(f){for(var w in $e)$e.hasOwnProperty(w)&&f[w]&&($e[w]=f[w]);return this},d.__private__.setDocumentProperty=function(f,w){if(Object.keys($e).indexOf(f)===-1)throw new Error("Invalid arguments passed to jsPDF.setDocumentProperty");return $e[f]=w};var st,je,rn,ht,Rn,At={},Ct={},$n=[],vt={},Br={},Bt={},Fn={},sr=null,Mt=0,tt=[],gt=new zu(d),Mr=t.hotfixes||[],Ht={},Jn={},Kn=[],Ve=function f(w,U,K,fe,xe,_e){if(!(this instanceof f))return new f(w,U,K,fe,xe,_e);isNaN(w)&&(w=1),isNaN(U)&&(U=0),isNaN(K)&&(K=0),isNaN(fe)&&(fe=1),isNaN(xe)&&(xe=0),isNaN(_e)&&(_e=0),this._matrix=[w,U,K,fe,xe,_e]};Object.defineProperty(Ve.prototype,"sx",{get:function(){return this._matrix[0]},set:function(f){this._matrix[0]=f}}),Object.defineProperty(Ve.prototype,"shy",{get:function(){return this._matrix[1]},set:function(f){this._matrix[1]=f}}),Object.defineProperty(Ve.prototype,"shx",{get:function(){return this._matrix[2]},set:function(f){this._matrix[2]=f}}),Object.defineProperty(Ve.prototype,"sy",{get:function(){return this._matrix[3]},set:function(f){this._matrix[3]=f}}),Object.defineProperty(Ve.prototype,"tx",{get:function(){return this._matrix[4]},set:function(f){this._matrix[4]=f}}),Object.defineProperty(Ve.prototype,"ty",{get:function(){return this._matrix[5]},set:function(f){this._matrix[5]=f}}),Object.defineProperty(Ve.prototype,"a",{get:function(){return this._matrix[0]},set:function(f){this._matrix[0]=f}}),Object.defineProperty(Ve.prototype,"b",{get:function(){return this._matrix[1]},set:function(f){this._matrix[1]=f}}),Object.defineProperty(Ve.prototype,"c",{get:function(){return this._matrix[2]},set:function(f){this._matrix[2]=f}}),Object.defineProperty(Ve.prototype,"d",{get:function(){return this._matrix[3]},set:function(f){this._matrix[3]=f}}),Object.defineProperty(Ve.prototype,"e",{get:function(){return this._matrix[4]},set:function(f){this._matrix[4]=f}}),Object.defineProperty(Ve.prototype,"f",{get:function(){return this._matrix[5]},set:function(f){this._matrix[5]=f}}),Object.defineProperty(Ve.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(Ve.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(Ve.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(Ve.prototype,"isIdentity",{get:function(){return this.sx===1&&this.shy===0&&this.shx===0&&this.sy===1&&this.tx===0&&this.ty===0}}),Ve.prototype.join=function(f){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map($).join(f)},Ve.prototype.multiply=function(f){var w=f.sx*this.sx+f.shy*this.shx,U=f.sx*this.shy+f.shy*this.sy,K=f.shx*this.sx+f.sy*this.shx,fe=f.shx*this.shy+f.sy*this.sy,xe=f.tx*this.sx+f.ty*this.shx+this.tx,_e=f.tx*this.shy+f.ty*this.sy+this.ty;return new Ve(w,U,K,fe,xe,_e)},Ve.prototype.decompose=function(){var f=this.sx,w=this.shy,U=this.shx,K=this.sy,fe=this.tx,xe=this.ty,_e=Math.sqrt(f*f+w*w),Oe=(f/=_e)*U+(w/=_e)*K;U-=f*Oe,K-=w*Oe;var We=Math.sqrt(U*U+K*K);return Oe/=We,f*(K/=We)<w*(U/=We)&&(f=-f,w=-w,Oe=-Oe,_e=-_e),{scale:new Ve(_e,0,0,We,0,0),translate:new Ve(1,0,0,1,fe,xe),rotate:new Ve(f,w,-w,f,0,0),skew:new Ve(1,0,Oe,1,0,0)}},Ve.prototype.toString=function(f){return this.join(" ")},Ve.prototype.inversed=function(){var f=this.sx,w=this.shy,U=this.shx,K=this.sy,fe=this.tx,xe=this.ty,_e=1/(f*K-w*U),Oe=K*_e,We=-w*_e,ot=-U*_e,it=f*_e;return new Ve(Oe,We,ot,it,-Oe*fe-ot*xe,-We*fe-it*xe)},Ve.prototype.applyToPoint=function(f){var w=f.x*this.sx+f.y*this.shx+this.tx,U=f.x*this.shy+f.y*this.sy+this.ty;return new oa(w,U)},Ve.prototype.applyToRectangle=function(f){var w=this.applyToPoint(f),U=this.applyToPoint(new oa(f.x+f.w,f.y+f.h));return new Ha(w.x,w.y,U.x-w.x,U.y-w.y)},Ve.prototype.clone=function(){var f=this.sx,w=this.shy,U=this.shx,K=this.sy,fe=this.tx,xe=this.ty;return new Ve(f,w,U,K,fe,xe)},d.Matrix=Ve;var On=d.matrixMult=function(f,w){return w.multiply(f)},Xn=new Ve(1,0,0,1,0,0);d.unitMatrix=d.identityMatrix=Xn;var dn=function(f,w){if(!Br[f]){var U=(w instanceof Gi?"Sh":"P")+(Object.keys(vt).length+1).toString(10);w.id=U,Br[f]=U,vt[U]=w,gt.publish("addPattern",w)}};d.ShadingPattern=Gi,d.TilingPattern=Ca,d.addShadingPattern=function(f,w){return F("addShadingPattern()"),dn(f,w),this},d.beginTilingPattern=function(f){F("beginTilingPattern()"),jo(f.boundingBox[0],f.boundingBox[1],f.boundingBox[2]-f.boundingBox[0],f.boundingBox[3]-f.boundingBox[1],f.matrix)},d.endTilingPattern=function(f,w){F("endTilingPattern()"),w.stream=Se[B].join(`
`),dn(f,w),gt.publish("endTilingPattern",w),Kn.pop().restore()};var Wt=d.__private__.newObject=function(){var f=Dt();return xn(f,!0),f},Dt=d.__private__.newObjectDeferred=function(){return H++,G[H]=function(){return oe},H},xn=function(f,w){return w=typeof w=="boolean"&&w,G[f]=oe,w&&W(f+" 0 obj"),f},Rr=d.__private__.newAdditionalObject=function(){var f={objId:Dt(),content:""};return ve.push(f),f},lr=Dt(),Dn=Dt(),qn=d.__private__.decodeColorString=function(f){var w=f.split(" ");if(w.length!==2||w[1]!=="g"&&w[1]!=="G")w.length===5&&(w[4]==="k"||w[4]==="K")&&(w=[(1-w[0])*(1-w[3]),(1-w[1])*(1-w[3]),(1-w[2])*(1-w[3]),"r"]);else{var U=parseFloat(w[0]);w=[U,U,U,"r"]}for(var K="#",fe=0;fe<3;fe++)K+=("0"+Math.floor(255*parseFloat(w[fe])).toString(16)).slice(-2);return K},Un=d.__private__.encodeColorString=function(f){var w;typeof f=="string"&&(f={ch1:f});var U=f.ch1,K=f.ch2,fe=f.ch3,xe=f.ch4,_e=f.pdfColorType==="draw"?["G","RG","K"]:["g","rg","k"];if(typeof U=="string"&&U.charAt(0)!=="#"){var Oe=new gh(U);if(Oe.ok)U=Oe.toHex();else if(!/^\d*\.?\d*$/.test(U))throw new Error('Invalid color "'+U+'" passed to jsPDF.encodeColorString.')}if(typeof U=="string"&&/^#[0-9A-Fa-f]{3}$/.test(U)&&(U="#"+U[1]+U[1]+U[2]+U[2]+U[3]+U[3]),typeof U=="string"&&/^#[0-9A-Fa-f]{6}$/.test(U)){var We=parseInt(U.substr(1),16);U=We>>16&255,K=We>>8&255,fe=255&We}if(K===void 0||xe===void 0&&U===K&&K===fe)if(typeof U=="string")w=U+" "+_e[0];else switch(f.precision){case 2:w=re(U/255)+" "+_e[0];break;case 3:default:w=N(U/255)+" "+_e[0]}else if(xe===void 0||Lt(xe)==="object"){if(xe&&!isNaN(xe.a)&&xe.a===0)return w=["1.","1.","1.",_e[1]].join(" ");if(typeof U=="string")w=[U,K,fe,_e[1]].join(" ");else switch(f.precision){case 2:w=[re(U/255),re(K/255),re(fe/255),_e[1]].join(" ");break;default:case 3:w=[N(U/255),N(K/255),N(fe/255),_e[1]].join(" ")}}else if(typeof U=="string")w=[U,K,fe,xe,_e[2]].join(" ");else switch(f.precision){case 2:w=[re(U),re(K),re(fe),re(xe),_e[2]].join(" ");break;case 3:default:w=[N(U),N(K),N(fe),N(xe),_e[2]].join(" ")}return w},zn=d.__private__.getFilters=function(){return s},Pn=d.__private__.putStream=function(f){var w=(f=f||{}).data||"",U=f.filters||zn(),K=f.alreadyAppliedFilters||[],fe=f.addLength1||!1,xe=w.length,_e=f.objectId,Oe=function(gn){return gn};if(g!==null&&_e===void 0)throw new Error("ObjectId must be passed to putStream for file encryption");g!==null&&(Oe=pn.encryptor(_e,0));var We={};U===!0&&(U=["FlateEncode"]);var ot=f.additionalKeyValues||[],it=(We=Ke.API.processDataByFilters!==void 0?Ke.API.processDataByFilters(w,U):{data:w,reverseChain:[]}).reverseChain+(Array.isArray(K)?K.join(" "):K.toString());if(We.data.length!==0&&(ot.push({key:"Length",value:We.data.length}),fe===!0&&ot.push({key:"Length1",value:xe})),it.length!=0)if(it.split("/").length-1==1)ot.push({key:"Filter",value:it});else{ot.push({key:"Filter",value:"["+it+"]"});for(var mt=0;mt<ot.length;mt+=1)if(ot[mt].key==="DecodeParms"){for(var Ft=[],qt=0;qt<We.reverseChain.split("/").length-1;qt+=1)Ft.push("null");Ft.push(ot[mt].value),ot[mt].value="["+Ft.join(" ")+"]"}}W("<<");for(var Xt=0;Xt<ot.length;Xt++)W("/"+ot[Xt].key+" "+ot[Xt].value);W(">>"),We.data.length!==0&&(W("stream"),W(Oe(We.data)),W("endstream"))},Zn=d.__private__.putPage=function(f){var w=f.number,U=f.data,K=f.objId,fe=f.contentsObjId;xn(K,!0),W("<</Type /Page"),W("/Parent "+f.rootDictionaryObjId+" 0 R"),W("/Resources "+f.resourceDictionaryObjId+" 0 R"),W("/MediaBox ["+parseFloat($(f.mediaBox.bottomLeftX))+" "+parseFloat($(f.mediaBox.bottomLeftY))+" "+$(f.mediaBox.topRightX)+" "+$(f.mediaBox.topRightY)+"]"),f.cropBox!==null&&W("/CropBox ["+$(f.cropBox.bottomLeftX)+" "+$(f.cropBox.bottomLeftY)+" "+$(f.cropBox.topRightX)+" "+$(f.cropBox.topRightY)+"]"),f.bleedBox!==null&&W("/BleedBox ["+$(f.bleedBox.bottomLeftX)+" "+$(f.bleedBox.bottomLeftY)+" "+$(f.bleedBox.topRightX)+" "+$(f.bleedBox.topRightY)+"]"),f.trimBox!==null&&W("/TrimBox ["+$(f.trimBox.bottomLeftX)+" "+$(f.trimBox.bottomLeftY)+" "+$(f.trimBox.topRightX)+" "+$(f.trimBox.topRightY)+"]"),f.artBox!==null&&W("/ArtBox ["+$(f.artBox.bottomLeftX)+" "+$(f.artBox.bottomLeftY)+" "+$(f.artBox.topRightX)+" "+$(f.artBox.topRightY)+"]"),typeof f.userUnit=="number"&&f.userUnit!==1&&W("/UserUnit "+f.userUnit),gt.publish("putPage",{objId:K,pageContext:tt[w],pageNumber:w,page:U}),W("/Contents "+fe+" 0 R"),W(">>"),W("endobj");var xe=U.join(`
`);return q===j.ADVANCED&&(xe+=`
Q`),xn(fe,!0),Pn({data:xe,filters:zn(),objectId:fe}),W("endobj"),K},Fr=d.__private__.putPages=function(){var f,w,U=[];for(f=1;f<=Mt;f++)tt[f].objId=Dt(),tt[f].contentsObjId=Dt();for(f=1;f<=Mt;f++)U.push(Zn({number:f,data:Se[f],objId:tt[f].objId,contentsObjId:tt[f].contentsObjId,mediaBox:tt[f].mediaBox,cropBox:tt[f].cropBox,bleedBox:tt[f].bleedBox,trimBox:tt[f].trimBox,artBox:tt[f].artBox,userUnit:tt[f].userUnit,rootDictionaryObjId:lr,resourceDictionaryObjId:Dn}));xn(lr,!0),W("<</Type /Pages");var K="/Kids [";for(w=0;w<Mt;w++)K+=U[w]+" 0 R ";W(K+"]"),W("/Count "+Mt),W(">>"),W("endobj"),gt.publish("postPutPages")},Qr=function(f){gt.publish("putFont",{font:f,out:W,newObject:Wt,putStream:Pn}),f.isAlreadyPutted!==!0&&(f.objectNumber=Wt(),W("<<"),W("/Type /Font"),W("/BaseFont /"+ka(f.postScriptName)),W("/Subtype /Type1"),typeof f.encoding=="string"&&W("/Encoding /"+f.encoding),W("/FirstChar 32"),W("/LastChar 255"),W(">>"),W("endobj"))},ei=function(){for(var f in At)At.hasOwnProperty(f)&&(b===!1||b===!0&&v.hasOwnProperty(f))&&Qr(At[f])},ti=function(f){f.objectNumber=Wt();var w=[];w.push({key:"Type",value:"/XObject"}),w.push({key:"Subtype",value:"/Form"}),w.push({key:"BBox",value:"["+[$(f.x),$(f.y),$(f.x+f.width),$(f.y+f.height)].join(" ")+"]"}),w.push({key:"Matrix",value:"["+f.matrix.toString()+"]"});var U=f.pages[1].join(`
`);Pn({data:U,additionalKeyValues:w,objectId:f.objectNumber}),W("endobj")},Me=function(){for(var f in Ht)Ht.hasOwnProperty(f)&&ti(Ht[f])},ft=function(f,w){var U,K=[],fe=1/(w-1);for(U=0;U<1;U+=fe)K.push(U);if(K.push(1),f[0].offset!=0){var xe={offset:0,color:f[0].color};f.unshift(xe)}if(f[f.length-1].offset!=1){var _e={offset:1,color:f[f.length-1].color};f.push(_e)}for(var Oe="",We=0,ot=0;ot<K.length;ot++){for(U=K[ot];U>f[We+1].offset;)We++;var it=f[We].offset,mt=(U-it)/(f[We+1].offset-it),Ft=f[We].color,qt=f[We+1].color;Oe+=z(Math.round((1-mt)*Ft[0]+mt*qt[0]).toString(16))+z(Math.round((1-mt)*Ft[1]+mt*qt[1]).toString(16))+z(Math.round((1-mt)*Ft[2]+mt*qt[2]).toString(16))}return Oe.trim()},Kt=function(f,w){w||(w=21);var U=Wt(),K=ft(f.colors,w),fe=[];fe.push({key:"FunctionType",value:"0"}),fe.push({key:"Domain",value:"[0.0 1.0]"}),fe.push({key:"Size",value:"["+w+"]"}),fe.push({key:"BitsPerSample",value:"8"}),fe.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),fe.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),Pn({data:K,additionalKeyValues:fe,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:U}),W("endobj"),f.objectNumber=Wt(),W("<< /ShadingType "+f.type),W("/ColorSpace /DeviceRGB");var xe="/Coords ["+$(parseFloat(f.coords[0]))+" "+$(parseFloat(f.coords[1]))+" ";f.type===2?xe+=$(parseFloat(f.coords[2]))+" "+$(parseFloat(f.coords[3])):xe+=$(parseFloat(f.coords[2]))+" "+$(parseFloat(f.coords[3]))+" "+$(parseFloat(f.coords[4]))+" "+$(parseFloat(f.coords[5])),W(xe+="]"),f.matrix&&W("/Matrix ["+f.matrix.toString()+"]"),W("/Function "+U+" 0 R"),W("/Extend [true true]"),W(">>"),W("endobj")},en=function(f,w){var U=Dt(),K=Wt();w.push({resourcesOid:U,objectOid:K}),f.objectNumber=K;var fe=[];fe.push({key:"Type",value:"/Pattern"}),fe.push({key:"PatternType",value:"1"}),fe.push({key:"PaintType",value:"1"}),fe.push({key:"TilingType",value:"1"}),fe.push({key:"BBox",value:"["+f.boundingBox.map($).join(" ")+"]"}),fe.push({key:"XStep",value:$(f.xStep)}),fe.push({key:"YStep",value:$(f.yStep)}),fe.push({key:"Resources",value:U+" 0 R"}),f.matrix&&fe.push({key:"Matrix",value:"["+f.matrix.toString()+"]"}),Pn({data:f.stream,additionalKeyValues:fe,objectId:f.objectNumber}),W("endobj")},cn=function(f){var w;for(w in vt)vt.hasOwnProperty(w)&&(vt[w]instanceof Gi?Kt(vt[w]):vt[w]instanceof Ca&&en(vt[w],f))},_n=function(f){for(var w in f.objectNumber=Wt(),W("<<"),f)switch(w){case"opacity":W("/ca "+re(f[w]));break;case"stroke-opacity":W("/CA "+re(f[w]))}W(">>"),W("endobj")},xr=function(){var f;for(f in Bt)Bt.hasOwnProperty(f)&&_n(Bt[f])},An=function(){for(var f in W("/XObject <<"),Ht)Ht.hasOwnProperty(f)&&Ht[f].objectNumber>=0&&W("/"+f+" "+Ht[f].objectNumber+" 0 R");gt.publish("putXobjectDict"),W(">>")},ni=function(){pn.oid=Wt(),W("<<"),W("/Filter /Standard"),W("/V "+pn.v),W("/R "+pn.r),W("/U <"+pn.toHexString(pn.U)+">"),W("/O <"+pn.toHexString(pn.O)+">"),W("/P "+pn.P),W(">>"),W("endobj")},ri=function(){for(var f in W("/Font <<"),At)At.hasOwnProperty(f)&&(b===!1||b===!0&&v.hasOwnProperty(f))&&W("/"+f+" "+At[f].objectNumber+" 0 R");W(">>")},Ii=function(){if(Object.keys(vt).length>0){for(var f in W("/Shading <<"),vt)vt.hasOwnProperty(f)&&vt[f]instanceof Gi&&vt[f].objectNumber>=0&&W("/"+f+" "+vt[f].objectNumber+" 0 R");gt.publish("putShadingPatternDict"),W(">>")}},kn=function(f){if(Object.keys(vt).length>0){for(var w in W("/Pattern <<"),vt)vt.hasOwnProperty(w)&&vt[w]instanceof d.TilingPattern&&vt[w].objectNumber>=0&&vt[w].objectNumber<f&&W("/"+w+" "+vt[w].objectNumber+" 0 R");gt.publish("putTilingPatternDict"),W(">>")}},ii=function(){if(Object.keys(Bt).length>0){var f;for(f in W("/ExtGState <<"),Bt)Bt.hasOwnProperty(f)&&Bt[f].objectNumber>=0&&W("/"+f+" "+Bt[f].objectNumber+" 0 R");gt.publish("putGStateDict"),W(">>")}},Tt=function(f){xn(f.resourcesOid,!0),W("<<"),W("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),ri(),Ii(),kn(f.objectOid),ii(),An(),W(">>"),W("endobj")},Or=function(){var f=[];ei(),xr(),Me(),cn(f),gt.publish("putResources"),f.forEach(Tt),Tt({resourcesOid:Dn,objectOid:Number.MAX_SAFE_INTEGER}),gt.publish("postPutResources")},Ti=function(){gt.publish("putAdditionalObjects");for(var f=0;f<ve.length;f++){var w=ve[f];xn(w.objId,!0),W(w.content),W("endobj")}gt.publish("postPutAdditionalObjects")},Qi=function(f){Ct[f.fontName]=Ct[f.fontName]||{},Ct[f.fontName][f.fontStyle]=f.id},ai=function(f,w,U,K,fe){var xe={id:"F"+(Object.keys(At).length+1).toString(10),postScriptName:f,fontName:w,fontStyle:U,encoding:K,isStandardFont:fe||!1,metadata:{}};return gt.publish("addFont",{font:xe,instance:this}),At[xe.id]=xe,Qi(xe),xe.id},Ps=function(f){for(var w=0,U=ke.length;w<U;w++){var K=ai.call(this,f[w][0],f[w][1],f[w][2],ke[w][3],!0);b===!1&&(v[K]=!0);var fe=f[w][0].split("-");Qi({id:K,fontName:fe[0],fontStyle:fe[1]||""})}gt.publish("addFonts",{fonts:At,dictionary:Ct})},cr=function(f){return f.foo=function(){try{return f.apply(this,arguments)}catch(K){var w=K.stack||"";~w.indexOf(" at ")&&(w=w.split(" at ")[1]);var U="Error in function "+w.split(`
`)[0].split("<")[0]+": "+K.message;if(!Xe.console)throw new Error(U);Xe.console.error(U,K),Xe.alert&&alert(U)}},f.foo.bar=f,f.foo},ea=function(f,w){var U,K,fe,xe,_e,Oe,We,ot,it;if(fe=(w=w||{}).sourceEncoding||"Unicode",_e=w.outputEncoding,(w.autoencode||_e)&&At[st].metadata&&At[st].metadata[fe]&&At[st].metadata[fe].encoding&&(xe=At[st].metadata[fe].encoding,!_e&&At[st].encoding&&(_e=At[st].encoding),!_e&&xe.codePages&&(_e=xe.codePages[0]),typeof _e=="string"&&(_e=xe[_e]),_e)){for(We=!1,Oe=[],U=0,K=f.length;U<K;U++)(ot=_e[f.charCodeAt(U)])?Oe.push(String.fromCharCode(ot)):Oe.push(f[U]),Oe[U].charCodeAt(0)>>8&&(We=!0);f=Oe.join("")}for(U=f.length;We===void 0&&U!==0;)f.charCodeAt(U-1)>>8&&(We=!0),U--;if(!We)return f;for(Oe=w.noBOM?[]:[254,255],U=0,K=f.length;U<K;U++){if((it=(ot=f.charCodeAt(U))>>8)>>8)throw new Error("Character at position "+U+" of string '"+f+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");Oe.push(it),Oe.push(ot-(it<<8))}return String.fromCharCode.apply(void 0,Oe)},Cn=d.__private__.pdfEscape=d.pdfEscape=function(f,w){return ea(f,w).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},Ma=d.__private__.beginPage=function(f){Se[++Mt]=[],tt[Mt]={objId:0,contentsObjId:0,userUnit:Number(l),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(f[0]),topRightY:Number(f[1])}},Ao(Mt),ye(Se[B])},xo=function(f,w){var U,K,fe;switch(n=w||n,typeof f=="string"&&(U=A(f.toLowerCase()),Array.isArray(U)&&(K=U[0],fe=U[1])),Array.isArray(f)&&(K=f[0]*je,fe=f[1]*je),isNaN(K)&&(K=o[0],fe=o[1]),(K>14400||fe>14400)&&(kt.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),K=Math.min(14400,K),fe=Math.min(14400,fe)),o=[K,fe],n.substr(0,1)){case"l":fe>K&&(o=[fe,K]);break;case"p":K>fe&&(o=[fe,K])}Ma(o),ko(Da),W(ur),Ua!==0&&W(Ua+" J"),za!==0&&W(za+" j"),gt.publish("addPage",{pageNumber:Mt})},_s=function(f){f>0&&f<=Mt&&(Se.splice(f,1),tt.splice(f,1),Mt--,B>Mt&&(B=Mt),this.setPage(B))},Ao=function(f){f>0&&f<=Mt&&(B=f)},ks=d.__private__.getNumberOfPages=d.getNumberOfPages=function(){return Se.length-1},So=function(f,w,U){var K,fe=void 0;return U=U||{},f=f!==void 0?f:At[st].fontName,w=w!==void 0?w:At[st].fontStyle,K=f.toLowerCase(),Ct[K]!==void 0&&Ct[K][w]!==void 0?fe=Ct[K][w]:Ct[f]!==void 0&&Ct[f][w]!==void 0?fe=Ct[f][w]:U.disableWarning===!1&&kt.warn("Unable to look up font label for font '"+f+"', '"+w+"'. Refer to getFontList() for available fonts."),fe||U.noFallback||(fe=Ct.times[w])==null&&(fe=Ct.times.normal),fe},Cs=d.__private__.putInfo=function(){var f=Wt(),w=function(K){return K};for(var U in g!==null&&(w=pn.encryptor(f,0)),W("<<"),W("/Producer ("+Cn(w("jsPDF "+Ke.version))+")"),$e)$e.hasOwnProperty(U)&&$e[U]&&W("/"+U.substr(0,1).toUpperCase()+U.substr(1)+" ("+Cn(w($e[U]))+")");W("/CreationDate ("+Cn(w(X))+")"),W(">>"),W("endobj")},Ra=d.__private__.putCatalog=function(f){var w=(f=f||{}).rootDictionaryObjId||lr;switch(Wt(),W("<<"),W("/Type /Catalog"),W("/Pages "+w+" 0 R"),Fe||(Fe="fullwidth"),Fe){case"fullwidth":W("/OpenAction [3 0 R /FitH null]");break;case"fullheight":W("/OpenAction [3 0 R /FitV null]");break;case"fullpage":W("/OpenAction [3 0 R /Fit]");break;case"original":W("/OpenAction [3 0 R /XYZ null null 1]");break;default:var U=""+Fe;U.substr(U.length-1)==="%"&&(Fe=parseInt(Fe)/100),typeof Fe=="number"&&W("/OpenAction [3 0 R /XYZ null null "+re(Fe)+"]")}switch(rt||(rt="continuous"),rt){case"continuous":W("/PageLayout /OneColumn");break;case"single":W("/PageLayout /SinglePage");break;case"two":case"twoleft":W("/PageLayout /TwoColumnLeft");break;case"tworight":W("/PageLayout /TwoColumnRight")}Qe&&W("/PageMode /"+Qe),gt.publish("putCatalog"),W(">>"),W("endobj")},Es=d.__private__.putTrailer=function(){W("trailer"),W("<<"),W("/Size "+(H+1)),W("/Root "+H+" 0 R"),W("/Info "+(H-1)+" 0 R"),g!==null&&W("/Encrypt "+pn.oid+" 0 R"),W("/ID [ <"+ne+"> <"+ne+"> ]"),W(">>")},Is=d.__private__.putHeader=function(){W("%PDF-"+_),W("%ºß¬à")},Ts=d.__private__.putXRef=function(){var f="0000000000";W("xref"),W("0 "+(H+1)),W("0000000000 65535 f ");for(var w=1;w<=H;w++)typeof G[w]=="function"?W((f+G[w]()).slice(-10)+" 00000 n "):G[w]!==void 0?W((f+G[w]).slice(-10)+" 00000 n "):W("0000000000 00000 n ")},Dr=d.__private__.buildDocument=function(){ze(),ye(ee),gt.publish("buildDocument"),Is(),Fr(),Ti(),Or(),g!==null&&ni(),Cs(),Ra();var f=oe;return Ts(),Es(),W("startxref"),W(""+f),W("%%EOF"),ye(Se[B]),ee.join(`
`)},ta=d.__private__.getBlob=function(f){return new Blob([qe(f)],{type:"application/pdf"})},na=d.output=d.__private__.output=cr(function(f,w){switch(typeof(w=w||{})=="string"?w={filename:w}:w.filename=w.filename||"generated.pdf",f){case void 0:return Dr();case"save":d.save(w.filename);break;case"arraybuffer":return qe(Dr());case"blob":return ta(Dr());case"bloburi":case"bloburl":if(Xe.URL!==void 0&&typeof Xe.URL.createObjectURL=="function")return Xe.URL&&Xe.URL.createObjectURL(ta(Dr()))||void 0;kt.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var U="",K=Dr();try{U=dc(K)}catch{U=dc(unescape(encodeURIComponent(K)))}return"data:application/pdf;filename="+w.filename+";base64,"+U;case"pdfobjectnewwindow":if(Object.prototype.toString.call(Xe)==="[object Window]"){var fe="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",xe=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';w.pdfObjectUrl&&(fe=w.pdfObjectUrl,xe="");var _e='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+fe+'"'+xe+'><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(w)+");<\/script></body></html>",Oe=Xe.open();return Oe!==null&&Oe.document.write(_e),Oe}throw new Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if(Object.prototype.toString.call(Xe)==="[object Window]"){var We='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(w.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+w.filename+'" width="500px" height="400px" /></body></html>',ot=Xe.open();if(ot!==null){ot.document.write(We);var it=this;ot.document.documentElement.querySelector("#pdfViewer").onload=function(){ot.document.title=w.filename,ot.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(it.output("bloburl"))}}return ot}throw new Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if(Object.prototype.toString.call(Xe)!=="[object Window]")throw new Error("The option dataurlnewwindow just works in a browser-environment.");var mt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",w)+'"></iframe></body></html>',Ft=Xe.open();if(Ft!==null&&(Ft.document.write(mt),Ft.document.title=w.filename),Ft||typeof safari>"u")return Ft;break;case"datauri":case"dataurl":return Xe.document.location.href=this.output("datauristring",w);default:return null}}),No=function(f){return Array.isArray(Mr)===!0&&Mr.indexOf(f)>-1};switch(r){case"pt":je=1;break;case"mm":je=72/25.4;break;case"cm":je=72/2.54;break;case"in":je=72;break;case"px":je=No("px_scaling")==1?.75:96/72;break;case"pc":case"em":je=12;break;case"ex":je=6;break;default:if(typeof r!="number")throw new Error("Invalid unit: "+r);je=r}var pn=null;me(),Q();var js=function(f){return g!==null?pn.encryptor(f,0):function(w){return w}},Lo=d.__private__.getPageInfo=d.getPageInfo=function(f){if(isNaN(f)||f%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:tt[f].objId,pageNumber:f,pageContext:tt[f]}},nt=d.__private__.getPageInfoByObjId=function(f){if(isNaN(f)||f%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var w in tt)if(tt[w].objId===f)break;return Lo(w)},Bs=d.__private__.getCurrentPageInfo=d.getCurrentPageInfo=function(){return{objId:tt[B].objId,pageNumber:B,pageContext:tt[B]}};d.addPage=function(){return xo.apply(this,arguments),this},d.setPage=function(){return Ao.apply(this,arguments),ye.call(this,Se[B]),this},d.insertPage=function(f){return this.addPage(),this.movePage(B,f),this},d.movePage=function(f,w){var U,K;if(f>w){U=Se[f],K=tt[f];for(var fe=f;fe>w;fe--)Se[fe]=Se[fe-1],tt[fe]=tt[fe-1];Se[w]=U,tt[w]=K,this.setPage(w)}else if(f<w){U=Se[f],K=tt[f];for(var xe=f;xe<w;xe++)Se[xe]=Se[xe+1],tt[xe]=tt[xe+1];Se[w]=U,tt[w]=K,this.setPage(w)}return this},d.deletePage=function(){return _s.apply(this,arguments),this},d.__private__.text=d.text=function(f,w,U,K,fe){var xe,_e,Oe,We,ot,it,mt,Ft,qt,Xt=(K=K||{}).scope||this;if(typeof f=="number"&&typeof w=="number"&&(typeof U=="string"||Array.isArray(U))){var gn=U;U=w,w=f,f=gn}if(arguments[3]instanceof Ve?(F("The transform parameter of text() with a Matrix value"),qt=fe):(Oe=arguments[4],We=arguments[5],Lt(mt=arguments[3])==="object"&&mt!==null||(typeof Oe=="string"&&(We=Oe,Oe=null),typeof mt=="string"&&(We=mt,mt=null),typeof mt=="number"&&(Oe=mt,mt=null),K={flags:mt,angle:Oe,align:We})),isNaN(w)||isNaN(U)||f==null)throw new Error("Invalid arguments passed to jsPDF.text");if(f.length===0)return Xt;var an="",fr=!1,Hn=typeof K.lineHeightFactor=="number"?K.lineHeightFactor:Bi,Nr=Xt.internal.scaleFactor;function Bo(Et){return Et=Et.split("	").join(Array(K.TabLen||9).join(" ")),Cn(Et,mt)}function Ya(Et){for(var It,Gt=Et.concat(),tn=[],Wr=Gt.length;Wr--;)typeof(It=Gt.shift())=="string"?tn.push(It):Array.isArray(Et)&&(It.length===1||It[1]===void 0&&It[2]===void 0)?tn.push(It[0]):tn.push([It[0],It[1],It[2]]);return tn}function $a(Et,It){var Gt;if(typeof Et=="string")Gt=It(Et)[0];else if(Array.isArray(Et)){for(var tn,Wr,no=Et.concat(),ba=[],Do=no.length;Do--;)typeof(tn=no.shift())=="string"?ba.push(It(tn)[0]):Array.isArray(tn)&&typeof tn[0]=="string"&&(Wr=It(tn[0],tn[1],tn[2]),ba.push([Wr[0],Wr[1],Wr[2]]));Gt=ba}return Gt}var la=!1,Ja=!0;if(typeof f=="string")la=!0;else if(Array.isArray(f)){var Ka=f.concat();_e=[];for(var ca,un=Ka.length;un--;)(typeof(ca=Ka.shift())!="string"||Array.isArray(ca)&&typeof ca[0]!="string")&&(Ja=!1);la=Ja}if(la===!1)throw new Error('Type of text must be string or Array. "'+f+'" is not recognized.');typeof f=="string"&&(f=f.match(/[\r?\n]/)?f.split(/\r\n|\r|\n/g):[f]);var ua=Ie/Xt.internal.scaleFactor,fa=ua*(Hn-1);switch(K.baseline){case"bottom":U-=fa;break;case"top":U+=ua-fa;break;case"hanging":U+=ua-2*fa;break;case"middle":U+=ua/2-fa}if((it=K.maxWidth||0)>0&&(typeof f=="string"?f=Xt.splitTextToSize(f,it):Object.prototype.toString.call(f)==="[object Array]"&&(f=f.reduce(function(Et,It){return Et.concat(Xt.splitTextToSize(It,it))},[]))),xe={text:f,x:w,y:U,options:K,mutex:{pdfEscape:Cn,activeFontKey:st,fonts:At,activeFontSize:Ie}},gt.publish("preProcessText",xe),f=xe.text,Oe=(K=xe.options).angle,!(qt instanceof Ve)&&Oe&&typeof Oe=="number"){Oe*=Math.PI/180,K.rotationDirection===0&&(Oe=-Oe),q===j.ADVANCED&&(Oe=-Oe);var ha=Math.cos(Oe),Xa=Math.sin(Oe);qt=new Ve(ha,Xa,-Xa,ha,0,0)}else Oe&&Oe instanceof Ve&&(qt=Oe);q!==j.ADVANCED||qt||(qt=Xn),(ot=K.charSpace||aa)!==void 0&&(an+=$(k(ot))+` Tc
`,this.setCharSpace(this.getCharSpace()||0)),(Ft=K.horizontalScale)!==void 0&&(an+=$(100*Ft)+` Tz
`),K.lang;var En=-1,Ws=K.renderingMode!==void 0?K.renderingMode:K.stroke,Za=Xt.internal.getCurrentPageInfo().pageContext;switch(Ws){case 0:case!1:case"fill":En=0;break;case 1:case!0:case"stroke":En=1;break;case 2:case"fillThenStroke":En=2;break;case 3:case"invisible":En=3;break;case 4:case"fillAndAddForClipping":En=4;break;case 5:case"strokeAndAddPathForClipping":En=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":En=6;break;case 7:case"addToPathForClipping":En=7}var Mo=Za.usedRenderingMode!==void 0?Za.usedRenderingMode:-1;En!==-1?an+=En+` Tr
`:Mo!==-1&&(an+=`0 Tr
`),En!==-1&&(Za.usedRenderingMode=En),We=K.align||"left";var Qn,da=Ie*Hn,Ro=Xt.internal.pageSize.getWidth(),Fo=At[st];ot=K.charSpace||aa,it=K.maxWidth||0,mt=Object.assign({autoencode:!0,noBOM:!0},K.flags);var ci=[],Fi=function(Et){return Xt.getStringUnitWidth(Et,{font:Fo,charSpace:ot,fontSize:Ie,doKerning:!1})*Ie/Nr};if(Object.prototype.toString.call(f)==="[object Array]"){var In;_e=Ya(f),We!=="left"&&(Qn=_e.map(Fi));var Sn,ui=0;if(We==="right"){w-=Qn[0],f=[],un=_e.length;for(var Ur=0;Ur<un;Ur++)Ur===0?(Sn=Sr(w),In=qr(U)):(Sn=k(ui-Qn[Ur]),In=-da),f.push([_e[Ur],Sn,In]),ui=Qn[Ur]}else if(We==="center"){w-=Qn[0]/2,f=[],un=_e.length;for(var zr=0;zr<un;zr++)zr===0?(Sn=Sr(w),In=qr(U)):(Sn=k((ui-Qn[zr])/2),In=-da),f.push([_e[zr],Sn,In]),ui=Qn[zr]}else if(We==="left"){f=[],un=_e.length;for(var pa=0;pa<un;pa++)f.push(_e[pa])}else if(We==="justify"&&Fo.encoding==="Identity-H"){f=[],un=_e.length,it=it!==0?it:Ro;for(var Hr=0,Vt=0;Vt<un;Vt++)if(In=Vt===0?qr(U):-da,Sn=Vt===0?Sr(w):Hr,Vt<un-1){var Qa=k((it-Qn[Vt])/(_e[Vt].split(" ").length-1)),Nn=_e[Vt].split(" ");f.push([Nn[0]+" ",Sn,In]),Hr=0;for(var er=1;er<Nn.length;er++){var ga=(Fi(Nn[er-1]+" "+Nn[er])-Fi(Nn[er]))*Nr+Qa;er==Nn.length-1?f.push([Nn[er],ga,0]):f.push([Nn[er]+" ",ga,0]),Hr-=ga}}else f.push([_e[Vt],Sn,In]);f.push(["",Hr,0])}else{if(We!=="justify")throw new Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');for(f=[],un=_e.length,it=it!==0?it:Ro,Vt=0;Vt<un;Vt++)In=Vt===0?qr(U):-da,Sn=Vt===0?Sr(w):0,Vt<un-1?ci.push($(k((it-Qn[Vt])/(_e[Vt].split(" ").length-1)))):ci.push(0),f.push([_e[Vt],Sn,In])}}var Oo=typeof K.R2L=="boolean"?K.R2L:He;Oo===!0&&(f=$a(f,function(Et,It,Gt){return[Et.split("").reverse().join(""),It,Gt]})),xe={text:f,x:w,y:U,options:K,mutex:{pdfEscape:Cn,activeFontKey:st,fonts:At,activeFontSize:Ie}},gt.publish("postProcessText",xe),f=xe.text,fr=xe.mutex.isHex||!1;var eo=At[st].encoding;eo!=="WinAnsiEncoding"&&eo!=="StandardEncoding"||(f=$a(f,function(Et,It,Gt){return[Bo(Et),It,Gt]})),_e=Ya(f),f=[];for(var Oi,Di,fi,qi=0,ma=1,Ui=Array.isArray(_e[0])?ma:qi,hi="",to=function(Et,It,Gt){var tn="";return Gt instanceof Ve?(Gt=typeof K.angle=="number"?On(Gt,new Ve(1,0,0,1,Et,It)):On(new Ve(1,0,0,1,Et,It),Gt),q===j.ADVANCED&&(Gt=On(new Ve(1,0,0,-1,0,0),Gt)),tn=Gt.join(" ")+` Tm
`):tn=$(Et)+" "+$(It)+` Td
`,tn},tr=0;tr<_e.length;tr++){switch(hi="",Ui){case ma:fi=(fr?"<":"(")+_e[tr][0]+(fr?">":")"),Oi=parseFloat(_e[tr][1]),Di=parseFloat(_e[tr][2]);break;case qi:fi=(fr?"<":"(")+_e[tr]+(fr?">":")"),Oi=Sr(w),Di=qr(U)}ci!==void 0&&ci[tr]!==void 0&&(hi=ci[tr]+` Tw
`),tr===0?f.push(hi+to(Oi,Di,qt)+fi):Ui===qi?f.push(hi+fi):Ui===ma&&f.push(hi+to(Oi,Di,qt)+fi)}f=Ui===qi?f.join(` Tj
T* `):f.join(` Tj
`),f+=` Tj
`;var nr=`BT
/`;return nr+=st+" "+Ie+` Tf
`,nr+=$(Ie*Hn)+` TL
`,nr+=Mi+`
`,nr+=an,nr+=f,W(nr+="ET"),v[st]=!0,Xt};var Ms=d.__private__.clip=d.clip=function(f){return W(f==="evenodd"?"W*":"W"),this};d.clipEvenOdd=function(){return Ms("evenodd")},d.__private__.discardPath=d.discardPath=function(){return W("n"),this};var Ar=d.__private__.isValidStyle=function(f){var w=!1;return[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(f)!==-1&&(w=!0),w};d.__private__.setDefaultPathOperation=d.setDefaultPathOperation=function(f){return Ar(f)&&(p=f),this};var Po=d.__private__.getStyle=d.getStyle=function(f){var w=p;switch(f){case"D":case"S":w="S";break;case"F":w="f";break;case"FD":case"DF":w="B";break;case"f":case"f*":case"B":case"B*":w=f}return w},_o=d.close=function(){return W("h"),this};d.stroke=function(){return W("S"),this},d.fill=function(f){return ra("f",f),this},d.fillEvenOdd=function(f){return ra("f*",f),this},d.fillStroke=function(f){return ra("B",f),this},d.fillStrokeEvenOdd=function(f){return ra("B*",f),this};var ra=function(f,w){Lt(w)==="object"?Fs(w,f):W(f)},Fa=function(f){f===null||q===j.ADVANCED&&f===void 0||(f=Po(f),W(f))};function Rs(f,w,U,K,fe){var xe=new Ca(w||this.boundingBox,U||this.xStep,K||this.yStep,this.gState,fe||this.matrix);xe.stream=this.stream;var _e=f+"$$"+this.cloneIndex+++"$$";return dn(_e,xe),xe}var Fs=function(f,w){var U=Br[f.key],K=vt[U];if(K instanceof Gi)W("q"),W(Os(w)),K.gState&&d.setGState(K.gState),W(f.matrix.toString()+" cm"),W("/"+U+" sh"),W("Q");else if(K instanceof Ca){var fe=new Ve(1,0,0,-1,0,li());f.matrix&&(fe=fe.multiply(f.matrix||Xn),U=Rs.call(K,f.key,f.boundingBox,f.xStep,f.yStep,fe).id),W("q"),W("/Pattern cs"),W("/"+U+" scn"),K.gState&&d.setGState(K.gState),W(w),W("Q")}},Os=function(f){switch(f){case"f":case"F":return"W n";case"f*":return"W* n";case"B":return"W S";case"B*":return"W* S";case"S":return"W S";case"n":return"W n"}},Oa=d.moveTo=function(f,w){return W($(k(f))+" "+$(C(w))+" m"),this},ji=d.lineTo=function(f,w){return W($(k(f))+" "+$(C(w))+" l"),this},oi=d.curveTo=function(f,w,U,K,fe,xe){return W([$(k(f)),$(C(w)),$(k(U)),$(C(K)),$(k(fe)),$(C(xe)),"c"].join(" ")),this};d.__private__.line=d.line=function(f,w,U,K,fe){if(isNaN(f)||isNaN(w)||isNaN(U)||isNaN(K)||!Ar(fe))throw new Error("Invalid arguments passed to jsPDF.line");return q===j.COMPAT?this.lines([[U-f,K-w]],f,w,[1,1],fe||"S"):this.lines([[U-f,K-w]],f,w,[1,1]).stroke()},d.__private__.lines=d.lines=function(f,w,U,K,fe,xe){var _e,Oe,We,ot,it,mt,Ft,qt,Xt,gn,an,fr;if(typeof f=="number"&&(fr=U,U=w,w=f,f=fr),K=K||[1,1],xe=xe||!1,isNaN(w)||isNaN(U)||!Array.isArray(f)||!Array.isArray(K)||!Ar(fe)||typeof xe!="boolean")throw new Error("Invalid arguments passed to jsPDF.lines");for(Oa(w,U),_e=K[0],Oe=K[1],ot=f.length,gn=w,an=U,We=0;We<ot;We++)(it=f[We]).length===2?(gn=it[0]*_e+gn,an=it[1]*Oe+an,ji(gn,an)):(mt=it[0]*_e+gn,Ft=it[1]*Oe+an,qt=it[2]*_e+gn,Xt=it[3]*Oe+an,gn=it[4]*_e+gn,an=it[5]*Oe+an,oi(mt,Ft,qt,Xt,gn,an));return xe&&_o(),Fa(fe),this},d.path=function(f){for(var w=0;w<f.length;w++){var U=f[w],K=U.c;switch(U.op){case"m":Oa(K[0],K[1]);break;case"l":ji(K[0],K[1]);break;case"c":oi.apply(this,K);break;case"h":_o()}}return this},d.__private__.rect=d.rect=function(f,w,U,K,fe){if(isNaN(f)||isNaN(w)||isNaN(U)||isNaN(K)||!Ar(fe))throw new Error("Invalid arguments passed to jsPDF.rect");return q===j.COMPAT&&(K=-K),W([$(k(f)),$(C(w)),$(k(U)),$(k(K)),"re"].join(" ")),Fa(fe),this},d.__private__.triangle=d.triangle=function(f,w,U,K,fe,xe,_e){if(isNaN(f)||isNaN(w)||isNaN(U)||isNaN(K)||isNaN(fe)||isNaN(xe)||!Ar(_e))throw new Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[U-f,K-w],[fe-U,xe-K],[f-fe,w-xe]],f,w,[1,1],_e,!0),this},d.__private__.roundedRect=d.roundedRect=function(f,w,U,K,fe,xe,_e){if(isNaN(f)||isNaN(w)||isNaN(U)||isNaN(K)||isNaN(fe)||isNaN(xe)||!Ar(_e))throw new Error("Invalid arguments passed to jsPDF.roundedRect");var Oe=4/3*(Math.SQRT2-1);return fe=Math.min(fe,.5*U),xe=Math.min(xe,.5*K),this.lines([[U-2*fe,0],[fe*Oe,0,fe,xe-xe*Oe,fe,xe],[0,K-2*xe],[0,xe*Oe,-fe*Oe,xe,-fe,xe],[2*fe-U,0],[-fe*Oe,0,-fe,-xe*Oe,-fe,-xe],[0,2*xe-K],[0,-xe*Oe,fe*Oe,-xe,fe,-xe]],f+fe,w,[1,1],_e,!0),this},d.__private__.ellipse=d.ellipse=function(f,w,U,K,fe){if(isNaN(f)||isNaN(w)||isNaN(U)||isNaN(K)||!Ar(fe))throw new Error("Invalid arguments passed to jsPDF.ellipse");var xe=4/3*(Math.SQRT2-1)*U,_e=4/3*(Math.SQRT2-1)*K;return Oa(f+U,w),oi(f+U,w-_e,f+xe,w-K,f,w-K),oi(f-xe,w-K,f-U,w-_e,f-U,w),oi(f-U,w+_e,f-xe,w+K,f,w+K),oi(f+xe,w+K,f+U,w+_e,f+U,w),Fa(fe),this},d.__private__.circle=d.circle=function(f,w,U,K){if(isNaN(f)||isNaN(w)||isNaN(U)||!Ar(K))throw new Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(f,w,U,U,K)},d.setFont=function(f,w,U){return U&&(w=ae(w,U)),st=So(f,w,{disableWarning:!1}),this};var Ds=d.__private__.getFont=d.getFont=function(){return At[So.apply(d,arguments)]};d.__private__.getFontList=d.getFontList=function(){var f,w,U={};for(f in Ct)if(Ct.hasOwnProperty(f))for(w in U[f]=[],Ct[f])Ct[f].hasOwnProperty(w)&&U[f].push(w);return U},d.addFont=function(f,w,U,K,fe){var xe=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&xe.indexOf(arguments[3])!==-1?fe=arguments[3]:arguments[3]&&xe.indexOf(arguments[3])==-1&&(U=ae(U,K)),fe=fe||"Identity-H",ai.call(this,f,w,U,fe)};var Bi,Da=t.lineWidth||.200025,ia=d.__private__.getLineWidth=d.getLineWidth=function(){return Da},ko=d.__private__.setLineWidth=d.setLineWidth=function(f){return Da=f,W($(k(f))+" w"),this};d.__private__.setLineDash=Ke.API.setLineDash=Ke.API.setLineDashPattern=function(f,w){if(f=f||[],w=w||0,isNaN(w)||!Array.isArray(f))throw new Error("Invalid arguments passed to jsPDF.setLineDash");return f=f.map(function(U){return $(k(U))}).join(" "),w=$(k(w)),W("["+f+"] "+w+" d"),this};var Co=d.__private__.getLineHeight=d.getLineHeight=function(){return Ie*Bi};d.__private__.getLineHeight=d.getLineHeight=function(){return Ie*Bi};var Eo=d.__private__.setLineHeightFactor=d.setLineHeightFactor=function(f){return typeof(f=f||1.15)=="number"&&(Bi=f),this},Io=d.__private__.getLineHeightFactor=d.getLineHeightFactor=function(){return Bi};Eo(t.lineHeight);var Sr=d.__private__.getHorizontalCoordinate=function(f){return k(f)},qr=d.__private__.getVerticalCoordinate=function(f){return q===j.ADVANCED?f:tt[B].mediaBox.topRightY-tt[B].mediaBox.bottomLeftY-k(f)},qs=d.__private__.getHorizontalCoordinateString=d.getHorizontalCoordinateString=function(f){return $(Sr(f))},si=d.__private__.getVerticalCoordinateString=d.getVerticalCoordinateString=function(f){return $(qr(f))},ur=t.strokeColor||"0 G";d.__private__.getStrokeColor=d.getDrawColor=function(){return qn(ur)},d.__private__.setStrokeColor=d.setDrawColor=function(f,w,U,K){return ur=Un({ch1:f,ch2:w,ch3:U,ch4:K,pdfColorType:"draw",precision:2}),W(ur),this};var qa=t.fillColor||"0 g";d.__private__.getFillColor=d.getFillColor=function(){return qn(qa)},d.__private__.setFillColor=d.setFillColor=function(f,w,U,K){return qa=Un({ch1:f,ch2:w,ch3:U,ch4:K,pdfColorType:"fill",precision:2}),W(qa),this};var Mi=t.textColor||"0 g",Us=d.__private__.getTextColor=d.getTextColor=function(){return qn(Mi)};d.__private__.setTextColor=d.setTextColor=function(f,w,U,K){return Mi=Un({ch1:f,ch2:w,ch3:U,ch4:K,pdfColorType:"text",precision:3}),this};var aa=t.charSpace,zs=d.__private__.getCharSpace=d.getCharSpace=function(){return parseFloat(aa||0)};d.__private__.setCharSpace=d.setCharSpace=function(f){if(isNaN(f))throw new Error("Invalid argument passed to jsPDF.setCharSpace");return aa=f,this};var Ua=0;d.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},d.__private__.setLineCap=d.setLineCap=function(f){var w=d.CapJoinStyles[f];if(w===void 0)throw new Error("Line cap style of '"+f+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Ua=w,W(w+" J"),this};var za=0;d.__private__.setLineJoin=d.setLineJoin=function(f){var w=d.CapJoinStyles[f];if(w===void 0)throw new Error("Line join style of '"+f+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return za=w,W(w+" j"),this},d.__private__.setLineMiterLimit=d.__private__.setMiterLimit=d.setLineMiterLimit=d.setMiterLimit=function(f){if(f=f||0,isNaN(f))throw new Error("Invalid argument passed to jsPDF.setLineMiterLimit");return W($(k(f))+" M"),this},d.GState=bs,d.setGState=function(f){(f=typeof f=="string"?Bt[Fn[f]]:To(null,f)).equals(sr)||(W("/"+f.id+" gs"),sr=f)};var To=function(f,w){if(!f||!Fn[f]){var U=!1;for(var K in Bt)if(Bt.hasOwnProperty(K)&&Bt[K].equals(w)){U=!0;break}if(U)w=Bt[K];else{var fe="GS"+(Object.keys(Bt).length+1).toString(10);Bt[fe]=w,w.id=fe}return f&&(Fn[f]=w.id),gt.publish("addGState",w),w}};d.addGState=function(f,w){return To(f,w),this},d.saveGraphicsState=function(){return W("q"),$n.push({key:st,size:Ie,color:Mi}),this},d.restoreGraphicsState=function(){W("Q");var f=$n.pop();return st=f.key,Ie=f.size,Mi=f.color,sr=null,this},d.setCurrentTransformationMatrix=function(f){return W(f.toString()+" cm"),this},d.comment=function(f){return W("#"+f),this};var oa=function(f,w){var U=f||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return U},set:function(xe){isNaN(xe)||(U=parseFloat(xe))}});var K=w||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return K},set:function(xe){isNaN(xe)||(K=parseFloat(xe))}});var fe="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return fe},set:function(xe){fe=xe.toString()}}),this},Ha=function(f,w,U,K){oa.call(this,f,w),this.type="rect";var fe=U||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return fe},set:function(_e){isNaN(_e)||(fe=parseFloat(_e))}});var xe=K||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return xe},set:function(_e){isNaN(_e)||(xe=parseFloat(_e))}}),this},Wa=function(){this.page=Mt,this.currentPage=B,this.pages=Se.slice(0),this.pagesContext=tt.slice(0),this.x=rn,this.y=ht,this.matrix=Rn,this.width=Ri(B),this.height=li(B),this.outputDestination=Ce,this.id="",this.objectNumber=-1};Wa.prototype.restore=function(){Mt=this.page,B=this.currentPage,tt=this.pagesContext,Se=this.pages,rn=this.x,ht=this.y,Rn=this.matrix,Va(B,this.width),Ga(B,this.height),Ce=this.outputDestination};var jo=function(f,w,U,K,fe){Kn.push(new Wa),Mt=B=0,Se=[],rn=f,ht=w,Rn=fe,Ma([U,K])},Hs=function(f){if(Jn[f])Kn.pop().restore();else{var w=new Wa,U="Xo"+(Object.keys(Ht).length+1).toString(10);w.id=U,Jn[f]=U,Ht[U]=w,gt.publish("addFormObject",w),Kn.pop().restore()}};for(var sa in d.beginFormObject=function(f,w,U,K,fe){return jo(f,w,U,K,fe),this},d.endFormObject=function(f){return Hs(f),this},d.doFormObject=function(f,w){var U=Ht[Jn[f]];return W("q"),W(w.toString()+" cm"),W("/"+U.id+" Do"),W("Q"),this},d.getFormObject=function(f){var w=Ht[Jn[f]];return{x:w.x,y:w.y,width:w.width,height:w.height,matrix:w.matrix}},d.save=function(f,w){return f=f||"generated.pdf",(w=w||{}).returnPromise=w.returnPromise||!1,w.returnPromise===!1?(Vi(ta(Dr()),f),typeof Vi.unload=="function"&&Xe.setTimeout&&setTimeout(Vi.unload,911),this):new Promise(function(U,K){try{var fe=Vi(ta(Dr()),f);typeof Vi.unload=="function"&&Xe.setTimeout&&setTimeout(Vi.unload,911),U(fe)}catch(xe){K(xe.message)}})},Ke.API)Ke.API.hasOwnProperty(sa)&&(sa==="events"&&Ke.API.events.length?function(f,w){var U,K,fe;for(fe=w.length-1;fe!==-1;fe--)U=w[fe][0],K=w[fe][1],f.subscribe.apply(f,[U].concat(typeof K=="function"?[K]:K))}(gt,Ke.API.events):d[sa]=Ke.API[sa]);var Ri=d.getPageWidth=function(f){return(tt[f=f||B].mediaBox.topRightX-tt[f].mediaBox.bottomLeftX)/je},Va=d.setPageWidth=function(f,w){tt[f].mediaBox.topRightX=w*je+tt[f].mediaBox.bottomLeftX},li=d.getPageHeight=function(f){return(tt[f=f||B].mediaBox.topRightY-tt[f].mediaBox.bottomLeftY)/je},Ga=d.setPageHeight=function(f,w){tt[f].mediaBox.topRightY=w*je+tt[f].mediaBox.bottomLeftY};return d.internal={pdfEscape:Cn,getStyle:Po,getFont:Ds,getFontSize:Te,getCharSpace:zs,getTextColor:Us,getLineHeight:Co,getLineHeightFactor:Io,getLineWidth:ia,write:Ze,getHorizontalCoordinate:Sr,getVerticalCoordinate:qr,getCoordinateString:qs,getVerticalCoordinateString:si,collections:{},newObject:Wt,newAdditionalObject:Rr,newObjectDeferred:Dt,newObjectDeferredBegin:xn,getFilters:zn,putStream:Pn,events:gt,scaleFactor:je,pageSize:{getWidth:function(){return Ri(B)},setWidth:function(f){Va(B,f)},getHeight:function(){return li(B)},setHeight:function(f){Ga(B,f)}},encryptionOptions:g,encryption:pn,getEncryptor:js,output:na,getNumberOfPages:ks,pages:Se,out:W,f2:re,f3:N,getPageInfo:Lo,getPageInfoByObjId:nt,getCurrentPageInfo:Bs,getPDFVersion:S,Point:oa,Rectangle:Ha,Matrix:Ve,hasHotfix:No},Object.defineProperty(d.internal.pageSize,"width",{get:function(){return Ri(B)},set:function(f){Va(B,f)},enumerable:!0,configurable:!0}),Object.defineProperty(d.internal.pageSize,"height",{get:function(){return li(B)},set:function(f){Ga(B,f)},enumerable:!0,configurable:!0}),Ps.call(d,ke),st="F1",xo(o,n),gt.publish("initialized"),d}_a.prototype.lsbFirstWord=function(t){return String.fromCharCode(t>>0&255,t>>8&255,t>>16&255,t>>24&255)},_a.prototype.toHexString=function(t){return t.split("").map(function(e){return("0"+(255&e.charCodeAt(0)).toString(16)).slice(-2)}).join("")},_a.prototype.hexToBytes=function(t){for(var e=[],n=0;n<t.length;n+=2)e.push(String.fromCharCode(parseInt(t.substr(n,2),16)));return e.join("")},_a.prototype.processOwnerPassword=function(t,e){return gc(pc(e).substr(0,5),t)},_a.prototype.encryptor=function(t,e){var n=pc(this.encryptionKey+String.fromCharCode(255&t,t>>8&255,t>>16&255,255&e,e>>8&255)).substr(0,10);return function(r){return gc(n,r)}},bs.prototype.equals=function(t){var e,n="id,objectNumber,equals";if(!t||Lt(t)!==Lt(this))return!1;var r=0;for(e in this)if(!(n.indexOf(e)>=0)){if(this.hasOwnProperty(e)&&!t.hasOwnProperty(e)||this[e]!==t[e])return!1;r++}for(e in t)t.hasOwnProperty(e)&&n.indexOf(e)<0&&r--;return r===0},Ke.API={events:[]},Ke.version="3.0.1";var zt=Ke.API,Tc=1,Ki=function(t){return t.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},Sa=function(t){return t.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},at=function(t){return t.toFixed(2)},xi=function(t){return t.toFixed(5)};zt.__acroform__={};var Mn=function(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t},Hu=function(t){return t*Tc},Cr=function(t){var e=new yh,n=Ue.internal.getHeight(t)||0,r=Ue.internal.getWidth(t)||0;return e.BBox=[0,0,Number(at(r)),Number(at(n))],e},D2=zt.__acroform__.setBit=function(t,e){if(t=t||0,e=e||0,isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return t|=1<<e},q2=zt.__acroform__.clearBit=function(t,e){if(t=t||0,e=e||0,isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return t&=~(1<<e)},U2=zt.__acroform__.getBit=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return(t&1<<e)==0?0:1},Yt=zt.__acroform__.getBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return U2(t,e-1)},$t=zt.__acroform__.setBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return D2(t,e-1)},Jt=zt.__acroform__.clearBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return q2(t,e-1)},z2=zt.__acroform__.calculateCoordinates=function(t,e){var n=e.internal.getHorizontalCoordinate,r=e.internal.getVerticalCoordinate,o=t[0],i=t[1],s=t[2],l=t[3],u={};return u.lowerLeft_X=n(o)||0,u.lowerLeft_Y=r(i+l)||0,u.upperRight_X=n(o+s)||0,u.upperRight_Y=r(i)||0,[Number(at(u.lowerLeft_X)),Number(at(u.lowerLeft_Y)),Number(at(u.upperRight_X)),Number(at(u.upperRight_Y))]},H2=function(t){if(t.appearanceStreamContent)return t.appearanceStreamContent;if(t.V||t.DV){var e=[],n=t._V||t.DV,r=mc(t,n),o=t.scope.internal.getFont(t.fontName,t.fontStyle).id;e.push("/Tx BMC"),e.push("q"),e.push("BT"),e.push(t.scope.__private__.encodeColorString(t.color)),e.push("/"+o+" "+at(r.fontSize)+" Tf"),e.push("1 0 0 1 0 0 Tm"),e.push(r.text),e.push("ET"),e.push("Q"),e.push("EMC");var i=Cr(t);return i.scope=t.scope,i.stream=e.join(`
`),i}},mc=function(t,e){var n=t.fontSize===0?t.maxFontSize:t.fontSize,r={text:"",fontSize:""},o=(e=(e=e.substr(0,1)=="("?e.substr(1):e).substr(e.length-1)==")"?e.substr(0,e.length-1):e).split(" ");o=t.multiline?o.map(function(N){return N.split(`
`)}):o.map(function(N){return[N]});var i=n,s=Ue.internal.getHeight(t)||0;s=s<0?-s:s;var l=Ue.internal.getWidth(t)||0;l=l<0?-l:l;var u=function(N,k,O){if(N+1<o.length){var C=k+" "+o[N+1][0];return ss(C,t,O).width<=l-4}return!1};i++;e:for(;i>0;){e="",i--;var p,g,b=ss("3",t,i).height,v=t.multiline?s-i:(s-b)/2,d=v+=2,_=0,S=0,T=0;if(i<=0){e=`(...) Tj
`,e+="% Width of Text: "+ss(e,t,i=12).width+", FieldWidth:"+l+`
`;break}for(var A="",j=0,q=0;q<o.length;q++)if(o.hasOwnProperty(q)){var Y=!1;if(o[q].length!==1&&T!==o[q].length-1){if((b+2)*(j+2)+2>s)continue e;A+=o[q][T],Y=!0,S=q,q--}else{A=(A+=o[q][T]+" ").substr(A.length-1)==" "?A.substr(0,A.length-1):A;var le=parseInt(q),ae=u(le,A,i),$=q>=o.length-1;if(ae&&!$){A+=" ",T=0;continue}if(ae||$){if($)S=le;else if(t.multiline&&(b+2)*(j+2)+2>s)continue e}else{if(!t.multiline||(b+2)*(j+2)+2>s)continue e;S=le}}for(var F="",J=_;J<=S;J++){var re=o[J];if(t.multiline){if(J===S){F+=re[T]+" ",T=(T+1)%re.length;continue}if(J===_){F+=re[re.length-1]+" ";continue}}F+=re[0]+" "}switch(F=F.substr(F.length-1)==" "?F.substr(0,F.length-1):F,g=ss(F,t,i).width,t.textAlign){case"right":p=l-g-2;break;case"center":p=(l-g)/2;break;case"left":default:p=2}e+=at(p)+" "+at(d)+` Td
`,e+="("+Ki(F)+`) Tj
`,e+=-at(p)+` 0 Td
`,d=-(i+2),g=0,_=Y?S:S+1,j++,A=""}break}return r.text=e,r.fontSize=i,r},ss=function(t,e,n){var r=e.scope.internal.getFont(e.fontName,e.fontStyle),o=e.scope.getStringUnitWidth(t,{font:r,fontSize:parseFloat(n),charSpace:0})*parseFloat(n);return{height:e.scope.getStringUnitWidth("3",{font:r,fontSize:parseFloat(n),charSpace:0})*parseFloat(n)*1.5,width:o}},W2={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},V2=function(t,e){var n={type:"reference",object:t};e.internal.getPageInfo(t.page).pageContext.annotations.find(function(r){return r.type===n.type&&r.object===n.object})===void 0&&e.internal.getPageInfo(t.page).pageContext.annotations.push(n)},G2=function(t,e){for(var n in t)if(t.hasOwnProperty(n)){var r=n,o=t[n];e.internal.newObjectDeferredBegin(o.objId,!0),Lt(o)==="object"&&typeof o.putStream=="function"&&o.putStream(),delete t[r]}},Y2=function(t,e){if(e.scope=t,t.internal!==void 0&&(t.internal.acroformPlugin===void 0||t.internal.acroformPlugin.isInitialized===!1)){if(yr.FieldNum=0,t.internal.acroformPlugin=JSON.parse(JSON.stringify(W2)),t.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("Exception while creating AcroformDictionary");Tc=t.internal.scaleFactor,t.internal.acroformPlugin.acroFormDictionaryRoot=new wh,t.internal.acroformPlugin.acroFormDictionaryRoot.scope=t,t.internal.acroformPlugin.acroFormDictionaryRoot._eventID=t.internal.events.subscribe("postPutResources",function(){(function(n){n.internal.events.unsubscribe(n.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete n.internal.acroformPlugin.acroFormDictionaryRoot._eventID,n.internal.acroformPlugin.printedOut=!0})(t)}),t.internal.events.subscribe("buildDocument",function(){(function(n){n.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var r=n.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var o in r)if(r.hasOwnProperty(o)){var i=r[o];i.objId=void 0,i.hasAnnotation&&V2(i,n)}})(t)}),t.internal.events.subscribe("putCatalog",function(){(function(n){if(n.internal.acroformPlugin.acroFormDictionaryRoot===void 0)throw new Error("putCatalogCallback: Root missing.");n.internal.write("/AcroForm "+n.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")})(t)}),t.internal.events.subscribe("postPutPages",function(n){(function(r,o){var i=!r;for(var s in r||(o.internal.newObjectDeferredBegin(o.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),o.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),r=r||o.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(r.hasOwnProperty(s)){var l=r[s],u=[],p=l.Rect;if(l.Rect&&(l.Rect=z2(l.Rect,o)),o.internal.newObjectDeferredBegin(l.objId,!0),l.DA=Ue.createDefaultAppearanceStream(l),Lt(l)==="object"&&typeof l.getKeyValueListForStream=="function"&&(u=l.getKeyValueListForStream()),l.Rect=p,l.hasAppearanceStream&&!l.appearanceStreamContent){var g=H2(l);u.push({key:"AP",value:"<</N "+g+">>"}),o.internal.acroformPlugin.xForms.push(g)}if(l.appearanceStreamContent){var b="";for(var v in l.appearanceStreamContent)if(l.appearanceStreamContent.hasOwnProperty(v)){var d=l.appearanceStreamContent[v];if(b+="/"+v+" ",b+="<<",Object.keys(d).length>=1||Array.isArray(d)){for(var s in d)if(d.hasOwnProperty(s)){var _=d[s];typeof _=="function"&&(_=_.call(o,l)),b+="/"+s+" "+_+" ",o.internal.acroformPlugin.xForms.indexOf(_)>=0||o.internal.acroformPlugin.xForms.push(_)}}else typeof(_=d)=="function"&&(_=_.call(o,l)),b+="/"+s+" "+_,o.internal.acroformPlugin.xForms.indexOf(_)>=0||o.internal.acroformPlugin.xForms.push(_);b+=">>"}u.push({key:"AP",value:`<<
`+b+">>"})}o.internal.putStream({additionalKeyValues:u,objectId:l.objId}),o.internal.out("endobj")}i&&G2(o.internal.acroformPlugin.xForms,o)})(n,t)}),t.internal.acroformPlugin.isInitialized=!0}},vh=zt.__acroform__.arrayToPdfArray=function(t,e,n){var r=function(s){return s};if(Array.isArray(t)){for(var o="[",i=0;i<t.length;i++)switch(i!==0&&(o+=" "),Lt(t[i])){case"boolean":case"number":case"object":o+=t[i].toString();break;case"string":t[i].substr(0,1)!=="/"?(e!==void 0&&n&&(r=n.internal.getEncryptor(e)),o+="("+Ki(r(t[i].toString()))+")"):o+=t[i].toString()}return o+="]"}throw new Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},Pl=function(t,e,n){var r=function(o){return o};return e!==void 0&&n&&(r=n.internal.getEncryptor(e)),(t=t||"").toString(),t="("+Ki(r(t))+")"},Er=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(this._objId===void 0){if(this.scope===void 0)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(t){this._objId=t}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};Er.prototype.toString=function(){return this.objId+" 0 R"},Er.prototype.putStream=function(){var t=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:t,objectId:this.objId}),this.scope.internal.out("endobj")},Er.prototype.getKeyValueListForStream=function(){var t=[],e=Object.getOwnPropertyNames(this).filter(function(i){return i!="content"&&i!="appearanceStreamContent"&&i!="scope"&&i!="objId"&&i.substring(0,1)!="_"});for(var n in e)if(Object.getOwnPropertyDescriptor(this,e[n]).configurable===!1){var r=e[n],o=this[r];o&&(Array.isArray(o)?t.push({key:r,value:vh(o,this.objId,this.scope)}):o instanceof Er?(o.scope=this.scope,t.push({key:r,value:o.objId+" 0 R"})):typeof o!="function"&&t.push({key:r,value:o}))}return t};var yh=function(){Er.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var t,e=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return e},set:function(n){e=n}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(n){t=n.trim()},get:function(){return t||null}})};Mn(yh,Er);var wh=function(){Er.call(this);var t,e=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return e.length>0?e:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return e}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(t){var n=function(r){return r};return this.scope&&(n=this.scope.internal.getEncryptor(this.objId)),"("+Ki(n(t))+")"}},set:function(n){t=n}})};Mn(wh,Er);var yr=function t(){Er.call(this);var e=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return e},set:function(A){if(isNaN(A))throw new Error('Invalid value "'+A+'" for attribute F supplied.');e=A}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return!!Yt(e,3)},set:function(A){A?this.F=$t(e,3):this.F=Jt(e,3)}});var n=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return n},set:function(A){if(isNaN(A))throw new Error('Invalid value "'+A+'" for attribute Ff supplied.');n=A}});var r=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(r.length!==0)return r},set:function(A){r=A!==void 0?A:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[0])?0:r[0]},set:function(A){r[0]=A}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[1])?0:r[1]},set:function(A){r[1]=A}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[2])?0:r[2]},set:function(A){r[2]=A}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[3])?0:r[3]},set:function(A){r[3]=A}});var o="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return o},set:function(A){switch(A){case"/Btn":case"/Tx":case"/Ch":case"/Sig":o=A;break;default:throw new Error('Invalid value "'+A+'" for attribute FT supplied.')}}});var i=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!i||i.length<1){if(this instanceof vs)return;i="FieldObject"+t.FieldNum++}var A=function(j){return j};return this.scope&&(A=this.scope.internal.getEncryptor(this.objId)),"("+Ki(A(i))+")"},set:function(A){i=A.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return i},set:function(A){i=A}});var s="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return s},set:function(A){s=A}});var l="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return l},set:function(A){l=A}});var u=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return u},set:function(A){u=A}});var p=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return p===void 0?50/Tc:p},set:function(A){p=A}});var g="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return g},set:function(A){g=A}});var b="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!b||this instanceof vs||this instanceof Ji))return Pl(b,this.objId,this.scope)},set:function(A){A=A.toString(),b=A}});var v=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(v)return this instanceof ln?v:Pl(v,this.objId,this.scope)},set:function(A){A=A.toString(),v=this instanceof ln?A:A.substr(0,1)==="("?Sa(A.substr(1,A.length-2)):Sa(A)}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof ln?Sa(v.substr(1,v.length-1)):v},set:function(A){A=A.toString(),v=this instanceof ln?"/"+A:A}});var d=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(d)return d},set:function(A){this.V=A}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(d)return this instanceof ln?d:Pl(d,this.objId,this.scope)},set:function(A){A=A.toString(),d=this instanceof ln?A:A.substr(0,1)==="("?Sa(A.substr(1,A.length-2)):Sa(A)}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof ln?Sa(d.substr(1,d.length-1)):d},set:function(A){A=A.toString(),d=this instanceof ln?"/"+A:A}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var _,S=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return S},set:function(A){A=!!A,S=A}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(_)return _},set:function(A){_=A}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,1)},set:function(A){A?this.Ff=$t(this.Ff,1):this.Ff=Jt(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,2)},set:function(A){A?this.Ff=$t(this.Ff,2):this.Ff=Jt(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,3)},set:function(A){A?this.Ff=$t(this.Ff,3):this.Ff=Jt(this.Ff,3)}});var T=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(T!==null)return T},set:function(A){if([0,1,2].indexOf(A)===-1)throw new Error('Invalid value "'+A+'" for attribute Q supplied.');T=A}}),Object.defineProperty(this,"textAlign",{get:function(){var A;switch(T){case 0:default:A="left";break;case 1:A="center";break;case 2:A="right"}return A},configurable:!0,enumerable:!0,set:function(A){switch(A){case"right":case 2:T=2;break;case"center":case 1:T=1;break;case"left":case 0:default:T=0}}})};Mn(yr,Er);var Ia=function(){yr.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var t=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return t},set:function(n){t=n}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return t},set:function(n){t=n}});var e=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return vh(e,this.objId,this.scope)},set:function(n){var r,o;o=[],typeof(r=n)=="string"&&(o=function(i,s,l){l||(l=1);for(var u,p=[];u=s.exec(i);)p.push(u[l]);return p}(r,/\((.*?)\)/g)),e=o}}),this.getOptions=function(){return e},this.setOptions=function(n){e=n,this.sort&&e.sort()},this.addOption=function(n){n=(n=n||"").toString(),e.push(n),this.sort&&e.sort()},this.removeOption=function(n,r){for(r=r||!1,n=(n=n||"").toString();e.indexOf(n)!==-1&&(e.splice(e.indexOf(n),1),r!==!1););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,18)},set:function(n){n?this.Ff=$t(this.Ff,18):this.Ff=Jt(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,19)},set:function(n){this.combo===!0&&(n?this.Ff=$t(this.Ff,19):this.Ff=Jt(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,20)},set:function(n){n?(this.Ff=$t(this.Ff,20),e.sort()):this.Ff=Jt(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,22)},set:function(n){n?this.Ff=$t(this.Ff,22):this.Ff=Jt(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,23)},set:function(n){n?this.Ff=$t(this.Ff,23):this.Ff=Jt(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,27)},set:function(n){n?this.Ff=$t(this.Ff,27):this.Ff=Jt(this.Ff,27)}}),this.hasAppearanceStream=!1};Mn(Ia,yr);var Ta=function(){Ia.call(this),this.fontName="helvetica",this.combo=!1};Mn(Ta,Ia);var ja=function(){Ta.call(this),this.combo=!0};Mn(ja,Ta);var fs=function(){ja.call(this),this.edit=!0};Mn(fs,ja);var ln=function(){yr.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,15)},set:function(n){n?this.Ff=$t(this.Ff,15):this.Ff=Jt(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,16)},set:function(n){n?this.Ff=$t(this.Ff,16):this.Ff=Jt(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,17)},set:function(n){n?this.Ff=$t(this.Ff,17):this.Ff=Jt(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,26)},set:function(n){n?this.Ff=$t(this.Ff,26):this.Ff=Jt(this.Ff,26)}});var t,e={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var n=function(i){return i};if(this.scope&&(n=this.scope.internal.getEncryptor(this.objId)),Object.keys(e).length!==0){var r,o=[];for(r in o.push("<<"),e)o.push("/"+r+" ("+Ki(n(e[r]))+")");return o.push(">>"),o.join(`
`)}},set:function(n){Lt(n)==="object"&&(e=n)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return e.CA||""},set:function(n){typeof n=="string"&&(e.CA=n)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return t},set:function(n){t=n}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return t.substr(1,t.length-1)},set:function(n){t="/"+n}})};Mn(ln,yr);var hs=function(){ln.call(this),this.pushButton=!0};Mn(hs,ln);var Ba=function(){ln.call(this),this.radio=!0,this.pushButton=!1;var t=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=e!==void 0?e:[]}})};Mn(Ba,ln);var vs=function(){var t,e;yr.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return t},set:function(o){t=o}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return e},set:function(o){e=o}});var n,r={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var o=function(l){return l};this.scope&&(o=this.scope.internal.getEncryptor(this.objId));var i,s=[];for(i in s.push("<<"),r)s.push("/"+i+" ("+Ki(o(r[i]))+")");return s.push(">>"),s.join(`
`)},set:function(o){Lt(o)==="object"&&(r=o)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return r.CA||""},set:function(o){typeof o=="string"&&(r.CA=o)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return n},set:function(o){n=o}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return n.substr(1,n.length-1)},set:function(o){n="/"+o}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=Ue.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};Mn(vs,yr),Ba.prototype.setAppearance=function(t){if(!("createAppearanceStream"in t)||!("getCA"in t))throw new Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var e in this.Kids)if(this.Kids.hasOwnProperty(e)){var n=this.Kids[e];n.appearanceStreamContent=t.createAppearanceStream(n.optionName),n.caption=t.getCA()}},Ba.prototype.createOption=function(t){var e=new vs;return e.Parent=this,e.optionName=t,this.Kids.push(e),$2.call(this.scope,e),e};var ds=function(){ln.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=Ue.CheckBox.createAppearanceStream()};Mn(ds,ln);var Ji=function(){yr.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,13)},set:function(e){e?this.Ff=$t(this.Ff,13):this.Ff=Jt(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,21)},set:function(e){e?this.Ff=$t(this.Ff,21):this.Ff=Jt(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,23)},set:function(e){e?this.Ff=$t(this.Ff,23):this.Ff=Jt(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,24)},set:function(e){e?this.Ff=$t(this.Ff,24):this.Ff=Jt(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,25)},set:function(e){e?this.Ff=$t(this.Ff,25):this.Ff=Jt(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,26)},set:function(e){e?this.Ff=$t(this.Ff,26):this.Ff=Jt(this.Ff,26)}});var t=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return t},set:function(e){Number.isInteger(e)&&(t=e)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};Mn(Ji,yr);var ps=function(){Ji.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return!!Yt(this.Ff,14)},set:function(t){t?this.Ff=$t(this.Ff,14):this.Ff=Jt(this.Ff,14)}}),this.password=!0};Mn(ps,Ji);var Ue={CheckBox:{createAppearanceStream:function(){return{N:{On:Ue.CheckBox.YesNormal},D:{On:Ue.CheckBox.YesPushDown,Off:Ue.CheckBox.OffPushDown}}},YesPushDown:function(t){var e=Cr(t);e.scope=t.scope;var n=[],r=t.scope.internal.getFont(t.fontName,t.fontStyle).id,o=t.scope.__private__.encodeColorString(t.color),i=mc(t,t.caption);return n.push("0.749023 g"),n.push("0 0 "+at(Ue.internal.getWidth(t))+" "+at(Ue.internal.getHeight(t))+" re"),n.push("f"),n.push("BMC"),n.push("q"),n.push("0 0 1 rg"),n.push("/"+r+" "+at(i.fontSize)+" Tf "+o),n.push("BT"),n.push(i.text),n.push("ET"),n.push("Q"),n.push("EMC"),e.stream=n.join(`
`),e},YesNormal:function(t){var e=Cr(t);e.scope=t.scope;var n=t.scope.internal.getFont(t.fontName,t.fontStyle).id,r=t.scope.__private__.encodeColorString(t.color),o=[],i=Ue.internal.getHeight(t),s=Ue.internal.getWidth(t),l=mc(t,t.caption);return o.push("1 g"),o.push("0 0 "+at(s)+" "+at(i)+" re"),o.push("f"),o.push("q"),o.push("0 0 1 rg"),o.push("0 0 "+at(s-1)+" "+at(i-1)+" re"),o.push("W"),o.push("n"),o.push("0 g"),o.push("BT"),o.push("/"+n+" "+at(l.fontSize)+" Tf "+r),o.push(l.text),o.push("ET"),o.push("Q"),e.stream=o.join(`
`),e},OffPushDown:function(t){var e=Cr(t);e.scope=t.scope;var n=[];return n.push("0.749023 g"),n.push("0 0 "+at(Ue.internal.getWidth(t))+" "+at(Ue.internal.getHeight(t))+" re"),n.push("f"),e.stream=n.join(`
`),e}},RadioButton:{Circle:{createAppearanceStream:function(t){var e={D:{Off:Ue.RadioButton.Circle.OffPushDown},N:{}};return e.N[t]=Ue.RadioButton.Circle.YesNormal,e.D[t]=Ue.RadioButton.Circle.YesPushDown,e},getCA:function(){return"l"},YesNormal:function(t){var e=Cr(t);e.scope=t.scope;var n=[],r=Ue.internal.getWidth(t)<=Ue.internal.getHeight(t)?Ue.internal.getWidth(t)/4:Ue.internal.getHeight(t)/4;r=Number((.9*r).toFixed(5));var o=Ue.internal.Bezier_C,i=Number((r*o).toFixed(5));return n.push("q"),n.push("1 0 0 1 "+xi(Ue.internal.getWidth(t)/2)+" "+xi(Ue.internal.getHeight(t)/2)+" cm"),n.push(r+" 0 m"),n.push(r+" "+i+" "+i+" "+r+" 0 "+r+" c"),n.push("-"+i+" "+r+" -"+r+" "+i+" -"+r+" 0 c"),n.push("-"+r+" -"+i+" -"+i+" -"+r+" 0 -"+r+" c"),n.push(i+" -"+r+" "+r+" -"+i+" "+r+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join(`
`),e},YesPushDown:function(t){var e=Cr(t);e.scope=t.scope;var n=[],r=Ue.internal.getWidth(t)<=Ue.internal.getHeight(t)?Ue.internal.getWidth(t)/4:Ue.internal.getHeight(t)/4;r=Number((.9*r).toFixed(5));var o=Number((2*r).toFixed(5)),i=Number((o*Ue.internal.Bezier_C).toFixed(5)),s=Number((r*Ue.internal.Bezier_C).toFixed(5));return n.push("0.749023 g"),n.push("q"),n.push("1 0 0 1 "+xi(Ue.internal.getWidth(t)/2)+" "+xi(Ue.internal.getHeight(t)/2)+" cm"),n.push(o+" 0 m"),n.push(o+" "+i+" "+i+" "+o+" 0 "+o+" c"),n.push("-"+i+" "+o+" -"+o+" "+i+" -"+o+" 0 c"),n.push("-"+o+" -"+i+" -"+i+" -"+o+" 0 -"+o+" c"),n.push(i+" -"+o+" "+o+" -"+i+" "+o+" 0 c"),n.push("f"),n.push("Q"),n.push("0 g"),n.push("q"),n.push("1 0 0 1 "+xi(Ue.internal.getWidth(t)/2)+" "+xi(Ue.internal.getHeight(t)/2)+" cm"),n.push(r+" 0 m"),n.push(r+" "+s+" "+s+" "+r+" 0 "+r+" c"),n.push("-"+s+" "+r+" -"+r+" "+s+" -"+r+" 0 c"),n.push("-"+r+" -"+s+" -"+s+" -"+r+" 0 -"+r+" c"),n.push(s+" -"+r+" "+r+" -"+s+" "+r+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join(`
`),e},OffPushDown:function(t){var e=Cr(t);e.scope=t.scope;var n=[],r=Ue.internal.getWidth(t)<=Ue.internal.getHeight(t)?Ue.internal.getWidth(t)/4:Ue.internal.getHeight(t)/4;r=Number((.9*r).toFixed(5));var o=Number((2*r).toFixed(5)),i=Number((o*Ue.internal.Bezier_C).toFixed(5));return n.push("0.749023 g"),n.push("q"),n.push("1 0 0 1 "+xi(Ue.internal.getWidth(t)/2)+" "+xi(Ue.internal.getHeight(t)/2)+" cm"),n.push(o+" 0 m"),n.push(o+" "+i+" "+i+" "+o+" 0 "+o+" c"),n.push("-"+i+" "+o+" -"+o+" "+i+" -"+o+" 0 c"),n.push("-"+o+" -"+i+" -"+i+" -"+o+" 0 -"+o+" c"),n.push(i+" -"+o+" "+o+" -"+i+" "+o+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join(`
`),e}},Cross:{createAppearanceStream:function(t){var e={D:{Off:Ue.RadioButton.Cross.OffPushDown},N:{}};return e.N[t]=Ue.RadioButton.Cross.YesNormal,e.D[t]=Ue.RadioButton.Cross.YesPushDown,e},getCA:function(){return"8"},YesNormal:function(t){var e=Cr(t);e.scope=t.scope;var n=[],r=Ue.internal.calculateCross(t);return n.push("q"),n.push("1 1 "+at(Ue.internal.getWidth(t)-2)+" "+at(Ue.internal.getHeight(t)-2)+" re"),n.push("W"),n.push("n"),n.push(at(r.x1.x)+" "+at(r.x1.y)+" m"),n.push(at(r.x2.x)+" "+at(r.x2.y)+" l"),n.push(at(r.x4.x)+" "+at(r.x4.y)+" m"),n.push(at(r.x3.x)+" "+at(r.x3.y)+" l"),n.push("s"),n.push("Q"),e.stream=n.join(`
`),e},YesPushDown:function(t){var e=Cr(t);e.scope=t.scope;var n=Ue.internal.calculateCross(t),r=[];return r.push("0.749023 g"),r.push("0 0 "+at(Ue.internal.getWidth(t))+" "+at(Ue.internal.getHeight(t))+" re"),r.push("f"),r.push("q"),r.push("1 1 "+at(Ue.internal.getWidth(t)-2)+" "+at(Ue.internal.getHeight(t)-2)+" re"),r.push("W"),r.push("n"),r.push(at(n.x1.x)+" "+at(n.x1.y)+" m"),r.push(at(n.x2.x)+" "+at(n.x2.y)+" l"),r.push(at(n.x4.x)+" "+at(n.x4.y)+" m"),r.push(at(n.x3.x)+" "+at(n.x3.y)+" l"),r.push("s"),r.push("Q"),e.stream=r.join(`
`),e},OffPushDown:function(t){var e=Cr(t);e.scope=t.scope;var n=[];return n.push("0.749023 g"),n.push("0 0 "+at(Ue.internal.getWidth(t))+" "+at(Ue.internal.getHeight(t))+" re"),n.push("f"),e.stream=n.join(`
`),e}}},createDefaultAppearanceStream:function(t){var e=t.scope.internal.getFont(t.fontName,t.fontStyle).id,n=t.scope.__private__.encodeColorString(t.color);return"/"+e+" "+t.fontSize+" Tf "+n}};Ue.internal={Bezier_C:.551915024494,calculateCross:function(t){var e=Ue.internal.getWidth(t),n=Ue.internal.getHeight(t),r=Math.min(e,n);return{x1:{x:(e-r)/2,y:(n-r)/2+r},x2:{x:(e-r)/2+r,y:(n-r)/2},x3:{x:(e-r)/2,y:(n-r)/2},x4:{x:(e-r)/2+r,y:(n-r)/2+r}}}},Ue.internal.getWidth=function(t){var e=0;return Lt(t)==="object"&&(e=Hu(t.Rect[2])),e},Ue.internal.getHeight=function(t){var e=0;return Lt(t)==="object"&&(e=Hu(t.Rect[3])),e};var $2=zt.addField=function(t){if(Y2(this,t),!(t instanceof yr))throw new Error("Invalid argument passed to jsPDF.addField.");var e;return(e=t).scope.internal.acroformPlugin.printedOut&&(e.scope.internal.acroformPlugin.printedOut=!1,e.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),e.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(e),t.page=t.scope.internal.getCurrentPageInfo().pageNumber,this};zt.AcroFormChoiceField=Ia,zt.AcroFormListBox=Ta,zt.AcroFormComboBox=ja,zt.AcroFormEditBox=fs,zt.AcroFormButton=ln,zt.AcroFormPushButton=hs,zt.AcroFormRadioButton=Ba,zt.AcroFormCheckBox=ds,zt.AcroFormTextField=Ji,zt.AcroFormPasswordField=ps,zt.AcroFormAppearance=Ue,zt.AcroForm={ChoiceField:Ia,ListBox:Ta,ComboBox:ja,EditBox:fs,Button:ln,PushButton:hs,RadioButton:Ba,CheckBox:ds,TextField:Ji,PasswordField:ps,Appearance:Ue},Ke.AcroForm={ChoiceField:Ia,ListBox:Ta,ComboBox:ja,EditBox:fs,Button:ln,PushButton:hs,RadioButton:Ba,CheckBox:ds,TextField:Ji,PasswordField:ps,Appearance:Ue};Ke.AcroForm;function xh(t){return t.reduce(function(e,n,r){return e[n]=r,e},{})}(function(t){t.__addimage__={};var e="UNKNOWN",n={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},r=t.__addimage__.getImageFileTypeByImageData=function(N,k){var O,C,X,ne,ue,Q=e;if((k=k||e)==="RGBA"||N.data!==void 0&&N.data instanceof Uint8ClampedArray&&"height"in N&&"width"in N)return"RGBA";if(ae(N))for(ue in n)for(X=n[ue],O=0;O<X.length;O+=1){for(ne=!0,C=0;C<X[O].length;C+=1)if(X[O][C]!==void 0&&X[O][C]!==N[C]){ne=!1;break}if(ne===!0){Q=ue;break}}else for(ue in n)for(X=n[ue],O=0;O<X.length;O+=1){for(ne=!0,C=0;C<X[O].length;C+=1)if(X[O][C]!==void 0&&X[O][C]!==N.charCodeAt(C)){ne=!1;break}if(ne===!0){Q=ue;break}}return Q===e&&k!==e&&(Q=k),Q},o=function N(k){for(var O=this.internal.write,C=this.internal.putStream,X=(0,this.internal.getFilters)();X.indexOf("FlateEncode")!==-1;)X.splice(X.indexOf("FlateEncode"),1);k.objectId=this.internal.newObject();var ne=[];if(ne.push({key:"Type",value:"/XObject"}),ne.push({key:"Subtype",value:"/Image"}),ne.push({key:"Width",value:k.width}),ne.push({key:"Height",value:k.height}),k.colorSpace===T.INDEXED?ne.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(k.palette.length/3-1)+" "+("sMask"in k&&k.sMask!==void 0?k.objectId+2:k.objectId+1)+" 0 R]"}):(ne.push({key:"ColorSpace",value:"/"+k.colorSpace}),k.colorSpace===T.DEVICE_CMYK&&ne.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),ne.push({key:"BitsPerComponent",value:k.bitsPerComponent}),"decodeParameters"in k&&k.decodeParameters!==void 0&&ne.push({key:"DecodeParms",value:"<<"+k.decodeParameters+">>"}),"transparency"in k&&Array.isArray(k.transparency)){for(var ue="",Q=0,he=k.transparency.length;Q<he;Q++)ue+=k.transparency[Q]+" "+k.transparency[Q]+" ";ne.push({key:"Mask",value:"["+ue+"]"})}k.sMask!==void 0&&ne.push({key:"SMask",value:k.objectId+1+" 0 R"});var V=k.filter!==void 0?["/"+k.filter]:void 0;if(C({data:k.data,additionalKeyValues:ne,alreadyAppliedFilters:V,objectId:k.objectId}),O("endobj"),"sMask"in k&&k.sMask!==void 0){var me="/Predictor "+k.predictor+" /Colors 1 /BitsPerComponent "+k.bitsPerComponent+" /Columns "+k.width,x={width:k.width,height:k.height,colorSpace:"DeviceGray",bitsPerComponent:k.bitsPerComponent,decodeParameters:me,data:k.sMask};"filter"in k&&(x.filter=k.filter),N.call(this,x)}if(k.colorSpace===T.INDEXED){var B=this.internal.newObject();C({data:F(new Uint8Array(k.palette)),objectId:B}),O("endobj")}},i=function(){var N=this.internal.collections.addImage_images;for(var k in N)o.call(this,N[k])},s=function(){var N,k=this.internal.collections.addImage_images,O=this.internal.write;for(var C in k)O("/I"+(N=k[C]).index,N.objectId,"0","R")},l=function(){this.internal.collections.addImage_images||(this.internal.collections.addImage_images={},this.internal.events.subscribe("putResources",i),this.internal.events.subscribe("putXobjectDict",s))},u=function(){var N=this.internal.collections.addImage_images;return l.call(this),N},p=function(){return Object.keys(this.internal.collections.addImage_images).length},g=function(N){return typeof t["process"+N.toUpperCase()]=="function"},b=function(N){return Lt(N)==="object"&&N.nodeType===1},v=function(N,k){if(N.nodeName==="IMG"&&N.hasAttribute("src")){var O=""+N.getAttribute("src");if(O.indexOf("data:image/")===0)return bo(unescape(O).split("base64,").pop());var C=t.loadFile(O,!0);if(C!==void 0)return C}if(N.nodeName==="CANVAS"){if(N.width===0||N.height===0)throw new Error("Given canvas must have data. Canvas width: "+N.width+", height: "+N.height);var X;switch(k){case"PNG":X="image/png";break;case"WEBP":X="image/webp";break;case"JPEG":case"JPG":default:X="image/jpeg"}return bo(N.toDataURL(X,1).split("base64,").pop())}},d=function(N){var k=this.internal.collections.addImage_images;if(k){for(var O in k)if(N===k[O].alias)return k[O]}},_=function(N,k,O){return N||k||(N=-96,k=-96),N<0&&(N=-1*O.width*72/N/this.internal.scaleFactor),k<0&&(k=-1*O.height*72/k/this.internal.scaleFactor),N===0&&(N=k*O.width/O.height),k===0&&(k=N*O.height/O.width),[N,k]},S=function(N,k,O,C,X,ne){var ue=_.call(this,O,C,X),Q=this.internal.getCoordinateString,he=this.internal.getVerticalCoordinateString,V=u.call(this);if(O=ue[0],C=ue[1],V[X.index]=X,ne){ne*=Math.PI/180;var me=Math.cos(ne),x=Math.sin(ne),B=function(z){return z.toFixed(4)},M=[B(me),B(x),B(-1*x),B(me),0,0,"cm"]}this.internal.write("q"),ne?(this.internal.write([1,"0","0",1,Q(N),he(k+C),"cm"].join(" ")),this.internal.write(M.join(" ")),this.internal.write([Q(O),"0","0",Q(C),"0","0","cm"].join(" "))):this.internal.write([Q(O),"0","0",Q(C),Q(N),he(k+C),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write([1,0,0,-1,0,0,"cm"].join(" ")),this.internal.write("/I"+X.index+" Do"),this.internal.write("Q")},T=t.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};t.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var A=t.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},j=t.__addimage__.sHashCode=function(N){var k,O,C=0;if(typeof N=="string")for(O=N.length,k=0;k<O;k++)C=(C<<5)-C+N.charCodeAt(k),C|=0;else if(ae(N))for(O=N.byteLength/2,k=0;k<O;k++)C=(C<<5)-C+N[k],C|=0;return C},q=t.__addimage__.validateStringAsBase64=function(N){(N=N||"").toString().trim();var k=!0;return N.length===0&&(k=!1),N.length%4!=0&&(k=!1),/^[A-Za-z0-9+/]+$/.test(N.substr(0,N.length-2))===!1&&(k=!1),/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(N.substr(-2))===!1&&(k=!1),k},Y=t.__addimage__.extractImageFromDataUrl=function(N){if(N==null||!(N=N.trim()).startsWith("data:"))return null;var k=N.indexOf(",");return k<0?null:N.substring(0,k).trim().endsWith("base64")?N.substring(k+1):null},le=t.__addimage__.supportsArrayBuffer=function(){return typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"};t.__addimage__.isArrayBuffer=function(N){return le()&&N instanceof ArrayBuffer};var ae=t.__addimage__.isArrayBufferView=function(N){return le()&&typeof Uint32Array<"u"&&(N instanceof Int8Array||N instanceof Uint8Array||typeof Uint8ClampedArray<"u"&&N instanceof Uint8ClampedArray||N instanceof Int16Array||N instanceof Uint16Array||N instanceof Int32Array||N instanceof Uint32Array||N instanceof Float32Array||N instanceof Float64Array)},$=t.__addimage__.binaryStringToUint8Array=function(N){for(var k=N.length,O=new Uint8Array(k),C=0;C<k;C++)O[C]=N.charCodeAt(C);return O},F=t.__addimage__.arrayBufferToBinaryString=function(N){for(var k="",O=ae(N)?N:new Uint8Array(N),C=0;C<O.length;C+=8192)k+=String.fromCharCode.apply(null,O.subarray(C,C+8192));return k};t.addImage=function(){var N,k,O,C,X,ne,ue,Q,he;if(typeof arguments[1]=="number"?(k=e,O=arguments[1],C=arguments[2],X=arguments[3],ne=arguments[4],ue=arguments[5],Q=arguments[6],he=arguments[7]):(k=arguments[1],O=arguments[2],C=arguments[3],X=arguments[4],ne=arguments[5],ue=arguments[6],Q=arguments[7],he=arguments[8]),Lt(N=arguments[0])==="object"&&!b(N)&&"imageData"in N){var V=N;N=V.imageData,k=V.format||k||e,O=V.x||O||0,C=V.y||C||0,X=V.w||V.width||X,ne=V.h||V.height||ne,ue=V.alias||ue,Q=V.compression||Q,he=V.rotation||V.angle||he}var me=this.internal.getFilters();if(Q===void 0&&me.indexOf("FlateEncode")!==-1&&(Q="SLOW"),isNaN(O)||isNaN(C))throw new Error("Invalid coordinates passed to jsPDF.addImage");l.call(this);var x=J.call(this,N,k,ue,Q);return S.call(this,O,C,X,ne,x,he),this};var J=function(N,k,O,C){var X,ne,ue;if(typeof N=="string"&&r(N)===e){N=unescape(N);var Q=re(N,!1);(Q!==""||(Q=t.loadFile(N,!0))!==void 0)&&(N=Q)}if(b(N)&&(N=v(N,k)),k=r(N,k),!g(k))throw new Error("addImage does not support files of type '"+k+"', please ensure that a plugin for '"+k+"' support is added.");if(((ue=O)==null||ue.length===0)&&(O=function(he){return typeof he=="string"||ae(he)?j(he):ae(he.data)?j(he.data):null}(N)),(X=d.call(this,O))||(le()&&(N instanceof Uint8Array||k==="RGBA"||(ne=N,N=$(N))),X=this["process"+k.toUpperCase()](N,p.call(this),O,function(he){return he&&typeof he=="string"&&(he=he.toUpperCase()),he in t.image_compression?he:A.NONE}(C),ne)),!X)throw new Error("An unknown error occurred whilst processing the image.");return X},re=t.__addimage__.convertBase64ToBinaryString=function(N,k){k=typeof k!="boolean"||k;var O,C="";if(typeof N=="string"){var X;O=(X=Y(N))!==null&&X!==void 0?X:N;try{C=bo(O)}catch(ne){if(k)throw q(O)?new Error("atob-Error in jsPDF.convertBase64ToBinaryString "+ne.message):new Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return C};t.getImageProperties=function(N){var k,O,C="";if(b(N)&&(N=v(N)),typeof N=="string"&&r(N)===e&&((C=re(N,!1))===""&&(C=t.loadFile(N)||""),N=C),O=r(N),!g(O))throw new Error("addImage does not support files of type '"+O+"', please ensure that a plugin for '"+O+"' support is added.");if(!le()||N instanceof Uint8Array||(N=$(N)),!(k=this["process"+O.toUpperCase()](N)))throw new Error("An unknown error occurred whilst processing the image");return k.fileType=O,k}})(Ke.API),function(t){var e=function(n){if(n!==void 0&&n!="")return!0};Ke.API.events.push(["addPage",function(n){this.internal.getPageInfo(n.pageNumber).pageContext.annotations=[]}]),t.events.push(["putPage",function(n){for(var r,o,i,s=this.internal.getCoordinateString,l=this.internal.getVerticalCoordinateString,u=this.internal.getPageInfoByObjId(n.objId),p=n.pageContext.annotations,g=!1,b=0;b<p.length&&!g;b++)switch((r=p[b]).type){case"link":(e(r.options.url)||e(r.options.pageNumber))&&(g=!0);break;case"reference":case"text":case"freetext":g=!0}if(g!=0){this.internal.write("/Annots [");for(var v=0;v<p.length;v++){r=p[v];var d=this.internal.pdfEscape,_=this.internal.getEncryptor(n.objId);switch(r.type){case"reference":this.internal.write(" "+r.object.objId+" 0 R ");break;case"text":var S=this.internal.newAdditionalObject(),T=this.internal.newAdditionalObject(),A=this.internal.getEncryptor(S.objId),j=r.title||"Note";i="<</Type /Annot /Subtype /Text "+(o="/Rect ["+s(r.bounds.x)+" "+l(r.bounds.y+r.bounds.h)+" "+s(r.bounds.x+r.bounds.w)+" "+l(r.bounds.y)+"] ")+"/Contents ("+d(A(r.contents))+")",i+=" /Popup "+T.objId+" 0 R",i+=" /P "+u.objId+" 0 R",i+=" /T ("+d(A(j))+") >>",S.content=i;var q=S.objId+" 0 R";i="<</Type /Annot /Subtype /Popup "+(o="/Rect ["+s(r.bounds.x+30)+" "+l(r.bounds.y+r.bounds.h)+" "+s(r.bounds.x+r.bounds.w+30)+" "+l(r.bounds.y)+"] ")+" /Parent "+q,r.open&&(i+=" /Open true"),i+=" >>",T.content=i,this.internal.write(S.objId,"0 R",T.objId,"0 R");break;case"freetext":o="/Rect ["+s(r.bounds.x)+" "+l(r.bounds.y)+" "+s(r.bounds.x+r.bounds.w)+" "+l(r.bounds.y+r.bounds.h)+"] ";var Y=r.color||"#000000";i="<</Type /Annot /Subtype /FreeText "+o+"/Contents ("+d(_(r.contents))+")",i+=" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+Y+")",i+=" /Border [0 0 0]",i+=" >>",this.internal.write(i);break;case"link":if(r.options.name){var le=this.annotations._nameMap[r.options.name];r.options.pageNumber=le.page,r.options.top=le.y}else r.options.top||(r.options.top=0);if(o="/Rect ["+r.finalBounds.x+" "+r.finalBounds.y+" "+r.finalBounds.w+" "+r.finalBounds.h+"] ",i="",r.options.url)i="<</Type /Annot /Subtype /Link "+o+"/Border [0 0 0] /A <</S /URI /URI ("+d(_(r.options.url))+") >>";else if(r.options.pageNumber)switch(i="<</Type /Annot /Subtype /Link "+o+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(r.options.pageNumber).objId+" 0 R",r.options.magFactor=r.options.magFactor||"XYZ",r.options.magFactor){case"Fit":i+=" /Fit]";break;case"FitH":i+=" /FitH "+r.options.top+"]";break;case"FitV":r.options.left=r.options.left||0,i+=" /FitV "+r.options.left+"]";break;case"XYZ":default:var ae=l(r.options.top);r.options.left=r.options.left||0,r.options.zoom===void 0&&(r.options.zoom=0),i+=" /XYZ "+r.options.left+" "+ae+" "+r.options.zoom+"]"}i!=""&&(i+=" >>",this.internal.write(i))}}this.internal.write("]")}}]),t.createAnnotation=function(n){var r=this.internal.getCurrentPageInfo();switch(n.type){case"link":this.link(n.bounds.x,n.bounds.y,n.bounds.w,n.bounds.h,n);break;case"text":case"freetext":r.pageContext.annotations.push(n)}},t.link=function(n,r,o,i,s){var l=this.internal.getCurrentPageInfo(),u=this.internal.getCoordinateString,p=this.internal.getVerticalCoordinateString;l.pageContext.annotations.push({finalBounds:{x:u(n),y:p(r),w:u(n+o),h:p(r+i)},options:s,type:"link"})},t.textWithLink=function(n,r,o,i){var s,l,u=this.getTextWidth(n),p=this.internal.getLineHeight()/this.internal.scaleFactor;if(i.maxWidth!==void 0){l=i.maxWidth;var g=this.splitTextToSize(n,l).length;s=Math.ceil(p*g)}else l=u,s=p;return this.text(n,r,o,i),o+=.2*p,i.align==="center"&&(r-=u/2),i.align==="right"&&(r-=u),this.link(r,o-p,l,s,i),u},t.getTextWidth=function(n){var r=this.internal.getFontSize();return this.getStringUnitWidth(n)*r/this.internal.scaleFactor}}(Ke.API),function(t){var e={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},n={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},r={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},o=[1570,1571,1573,1575];t.__arabicParser__={};var i=t.__arabicParser__.isInArabicSubstitutionA=function(S){return e[S.charCodeAt(0)]!==void 0},s=t.__arabicParser__.isArabicLetter=function(S){return typeof S=="string"&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(S)},l=t.__arabicParser__.isArabicEndLetter=function(S){return s(S)&&i(S)&&e[S.charCodeAt(0)].length<=2},u=t.__arabicParser__.isArabicAlfLetter=function(S){return s(S)&&o.indexOf(S.charCodeAt(0))>=0};t.__arabicParser__.arabicLetterHasIsolatedForm=function(S){return s(S)&&i(S)&&e[S.charCodeAt(0)].length>=1};var p=t.__arabicParser__.arabicLetterHasFinalForm=function(S){return s(S)&&i(S)&&e[S.charCodeAt(0)].length>=2};t.__arabicParser__.arabicLetterHasInitialForm=function(S){return s(S)&&i(S)&&e[S.charCodeAt(0)].length>=3};var g=t.__arabicParser__.arabicLetterHasMedialForm=function(S){return s(S)&&i(S)&&e[S.charCodeAt(0)].length==4},b=t.__arabicParser__.resolveLigatures=function(S){var T=0,A=n,j="",q=0;for(T=0;T<S.length;T+=1)A[S.charCodeAt(T)]!==void 0?(q++,typeof(A=A[S.charCodeAt(T)])=="number"&&(j+=String.fromCharCode(A),A=n,q=0),T===S.length-1&&(A=n,j+=S.charAt(T-(q-1)),T-=q-1,q=0)):(A=n,j+=S.charAt(T-q),T-=q,q=0);return j};t.__arabicParser__.isArabicDiacritic=function(S){return S!==void 0&&r[S.charCodeAt(0)]!==void 0};var v=t.__arabicParser__.getCorrectForm=function(S,T,A){return s(S)?i(S)===!1?-1:!p(S)||!s(T)&&!s(A)||!s(A)&&l(T)||l(S)&&!s(T)||l(S)&&u(T)||l(S)&&l(T)?0:g(S)&&s(T)&&!l(T)&&s(A)&&p(A)?3:l(S)||!s(A)?1:2:-1},d=function(S){var T=0,A=0,j=0,q="",Y="",le="",ae=(S=S||"").split("\\s+"),$=[];for(T=0;T<ae.length;T+=1){for($.push(""),A=0;A<ae[T].length;A+=1)q=ae[T][A],Y=ae[T][A-1],le=ae[T][A+1],s(q)?(j=v(q,Y,le),$[T]+=j!==-1?String.fromCharCode(e[q.charCodeAt(0)][j]):q):$[T]+=q;$[T]=b($[T])}return $.join(" ")},_=t.__arabicParser__.processArabic=t.processArabic=function(){var S,T=typeof arguments[0]=="string"?arguments[0]:arguments[0].text,A=[];if(Array.isArray(T)){var j=0;for(A=[],j=0;j<T.length;j+=1)Array.isArray(T[j])?A.push([d(T[j][0]),T[j][1],T[j][2]]):A.push([d(T[j])]);S=A}else S=d(T);return typeof arguments[0]=="string"?S:(arguments[0].text=S,arguments[0])};t.events.push(["preProcessText",_])}(Ke.API),Ke.API.autoPrint=function(t){var e;switch((t=t||{}).variant=t.variant||"non-conform",t.variant){case"javascript":this.addJS("print({});");break;case"non-conform":default:this.internal.events.subscribe("postPutResources",function(){e=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+e+" 0 R")})}return this},function(t){var e=function(){var n=void 0;Object.defineProperty(this,"pdf",{get:function(){return n},set:function(l){n=l}});var r=150;Object.defineProperty(this,"width",{get:function(){return r},set:function(l){r=isNaN(l)||Number.isInteger(l)===!1||l<0?150:l,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=r+1)}});var o=300;Object.defineProperty(this,"height",{get:function(){return o},set:function(l){o=isNaN(l)||Number.isInteger(l)===!1||l<0?300:l,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=o+1)}});var i=[];Object.defineProperty(this,"childNodes",{get:function(){return i},set:function(l){i=l}});var s={};Object.defineProperty(this,"style",{get:function(){return s},set:function(l){s=l}}),Object.defineProperty(this,"parentNode",{})};e.prototype.getContext=function(n,r){var o;if((n=n||"2d")!=="2d")return null;for(o in r)this.pdf.context2d.hasOwnProperty(o)&&(this.pdf.context2d[o]=r[o]);return this.pdf.context2d._canvas=this,this.pdf.context2d},e.prototype.toDataURL=function(){throw new Error("toDataURL is not implemented.")},t.events.push(["initialized",function(){this.canvas=new e,this.canvas.pdf=this}])}(Ke.API),function(t){var e={left:0,top:0,bottom:0,right:0},n=!1,r=function(){this.internal.__cell__===void 0&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},e),this.internal.__cell__.margins.width=this.getPageWidth(),o.call(this))},o=function(){this.internal.__cell__.lastCell=new i,this.internal.__cell__.pages=1},i=function(){var u=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return u},set:function(S){u=S}});var p=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return p},set:function(S){p=S}});var g=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return g},set:function(S){g=S}});var b=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return b},set:function(S){b=S}});var v=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return v},set:function(S){v=S}});var d=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return d},set:function(S){d=S}});var _=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return _},set:function(S){_=S}}),this};i.prototype.clone=function(){return new i(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},i.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},t.setHeaderFunction=function(u){return r.call(this),this.internal.__cell__.headerFunction=typeof u=="function"?u:void 0,this},t.getTextDimensions=function(u,p){r.call(this);var g=(p=p||{}).fontSize||this.getFontSize(),b=p.font||this.getFont(),v=p.scaleFactor||this.internal.scaleFactor,d=0,_=0,S=0,T=this;if(!Array.isArray(u)&&typeof u!="string"){if(typeof u!="number")throw new Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");u=String(u)}var A=p.maxWidth;A>0?typeof u=="string"?u=this.splitTextToSize(u,A):Object.prototype.toString.call(u)==="[object Array]"&&(u=u.reduce(function(q,Y){return q.concat(T.splitTextToSize(Y,A))},[])):u=Array.isArray(u)?u:[u];for(var j=0;j<u.length;j++)d<(S=this.getStringUnitWidth(u[j],{font:b})*g)&&(d=S);return d!==0&&(_=u.length),{w:d/=v,h:Math.max((_*g*this.getLineHeightFactor()-g*(this.getLineHeightFactor()-1))/v,0)}},t.cellAddPage=function(){r.call(this),this.addPage();var u=this.internal.__cell__.margins||e;return this.internal.__cell__.lastCell=new i(u.left,u.top,void 0,void 0),this.internal.__cell__.pages+=1,this};var s=t.cell=function(){var u;u=arguments[0]instanceof i?arguments[0]:new i(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),r.call(this);var p=this.internal.__cell__.lastCell,g=this.internal.__cell__.padding,b=this.internal.__cell__.margins||e,v=this.internal.__cell__.tableHeaderRow,d=this.internal.__cell__.printHeaders;return p.lineNumber!==void 0&&(p.lineNumber===u.lineNumber?(u.x=(p.x||0)+(p.width||0),u.y=p.y||0):p.y+p.height+u.height+b.bottom>this.getPageHeight()?(this.cellAddPage(),u.y=b.top,d&&v&&(this.printHeaderRow(u.lineNumber,!0),u.y+=v[0].height)):u.y=p.y+p.height||u.y),u.text[0]!==void 0&&(this.rect(u.x,u.y,u.width,u.height,n===!0?"FD":void 0),u.align==="right"?this.text(u.text,u.x+u.width-g,u.y+g,{align:"right",baseline:"top"}):u.align==="center"?this.text(u.text,u.x+u.width/2,u.y+g,{align:"center",baseline:"top",maxWidth:u.width-g-g}):this.text(u.text,u.x+g,u.y+g,{align:"left",baseline:"top",maxWidth:u.width-g-g})),this.internal.__cell__.lastCell=u,this};t.table=function(u,p,g,b,v){if(r.call(this),!g)throw new Error("No data for PDF table.");var d,_,S,T,A=[],j=[],q=[],Y={},le={},ae=[],$=[],F=(v=v||{}).autoSize||!1,J=v.printHeaders!==!1,re=v.css&&v.css["font-size"]!==void 0?16*v.css["font-size"]:v.fontSize||12,N=v.margins||Object.assign({width:this.getPageWidth()},e),k=typeof v.padding=="number"?v.padding:3,O=v.headerBackgroundColor||"#c8c8c8",C=v.headerTextColor||"#000";if(o.call(this),this.internal.__cell__.printHeaders=J,this.internal.__cell__.margins=N,this.internal.__cell__.table_font_size=re,this.internal.__cell__.padding=k,this.internal.__cell__.headerBackgroundColor=O,this.internal.__cell__.headerTextColor=C,this.setFontSize(re),b==null)j=A=Object.keys(g[0]),q=A.map(function(){return"left"});else if(Array.isArray(b)&&Lt(b[0])==="object")for(A=b.map(function(V){return V.name}),j=b.map(function(V){return V.prompt||V.name||""}),q=b.map(function(V){return V.align||"left"}),d=0;d<b.length;d+=1)le[b[d].name]=b[d].width*(19.049976/25.4);else Array.isArray(b)&&typeof b[0]=="string"&&(j=A=b,q=A.map(function(){return"left"}));if(F||Array.isArray(b)&&typeof b[0]=="string")for(d=0;d<A.length;d+=1){for(Y[T=A[d]]=g.map(function(V){return V[T]}),this.setFont(void 0,"bold"),ae.push(this.getTextDimensions(j[d],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),_=Y[T],this.setFont(void 0,"normal"),S=0;S<_.length;S+=1)ae.push(this.getTextDimensions(_[S],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);le[T]=Math.max.apply(null,ae)+k+k,ae=[]}if(J){var X={};for(d=0;d<A.length;d+=1)X[A[d]]={},X[A[d]].text=j[d],X[A[d]].align=q[d];var ne=l.call(this,X,le);$=A.map(function(V){return new i(u,p,le[V],ne,X[V].text,void 0,X[V].align)}),this.setTableHeaderRow($),this.printHeaderRow(1,!1)}var ue=b.reduce(function(V,me){return V[me.name]=me.align,V},{});for(d=0;d<g.length;d+=1){"rowStart"in v&&v.rowStart instanceof Function&&v.rowStart({row:d,data:g[d]},this);var Q=l.call(this,g[d],le);for(S=0;S<A.length;S+=1){var he=g[d][A[S]];"cellStart"in v&&v.cellStart instanceof Function&&v.cellStart({row:d,col:S,data:he},this),s.call(this,new i(u,p,le[A[S]],Q,he,d+2,ue[A[S]]))}}return this.internal.__cell__.table_x=u,this.internal.__cell__.table_y=p,this};var l=function(u,p){var g=this.internal.__cell__.padding,b=this.internal.__cell__.table_font_size,v=this.internal.scaleFactor;return Object.keys(u).map(function(d){var _=u[d];return this.splitTextToSize(_.hasOwnProperty("text")?_.text:_,p[d]-g-g)},this).map(function(d){return this.getLineHeightFactor()*d.length*b/v+g+g},this).reduce(function(d,_){return Math.max(d,_)},0)};t.setTableHeaderRow=function(u){r.call(this),this.internal.__cell__.tableHeaderRow=u},t.printHeaderRow=function(u,p){if(r.call(this),!this.internal.__cell__.tableHeaderRow)throw new Error("Property tableHeaderRow does not exist.");var g;if(n=!0,typeof this.internal.__cell__.headerFunction=="function"){var b=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new i(b[0],b[1],b[2],b[3],void 0,-1)}this.setFont(void 0,"bold");for(var v=[],d=0;d<this.internal.__cell__.tableHeaderRow.length;d+=1){g=this.internal.__cell__.tableHeaderRow[d].clone(),p&&(g.y=this.internal.__cell__.margins.top||0,v.push(g)),g.lineNumber=u;var _=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),s.call(this,g),this.setTextColor(_)}v.length>0&&this.setTableHeaderRow(v),this.setFont(void 0,"normal"),n=!1}}(Ke.API);var Ah={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},Sh=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],bc=xh(Sh),Nh=[100,200,300,400,500,600,700,800,900],J2=xh(Nh);function vc(t){var e=t.family.replace(/"|'/g,"").toLowerCase(),n=function(i){return Ah[i=i||"normal"]?i:"normal"}(t.style),r=function(i){if(!i)return 400;if(typeof i=="number")return i>=100&&i<=900&&i%100==0?i:400;if(/^\d00$/.test(i))return parseInt(i);switch(i){case"bold":return 700;case"normal":default:return 400}}(t.weight),o=function(i){return typeof bc[i=i||"normal"]=="number"?i:"normal"}(t.stretch);return{family:e,style:n,weight:r,stretch:o,src:t.src||[],ref:t.ref||{name:e,style:[o,n,r].join(" ")}}}function Wu(t,e,n,r){var o;for(o=n;o>=0&&o<e.length;o+=r)if(t[e[o]])return t[e[o]];for(o=n;o>=0&&o<e.length;o-=r)if(t[e[o]])return t[e[o]]}var K2={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},Vu={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};function Gu(t){return[t.stretch,t.style,t.weight,t.family].join(" ")}function X2(t,e,n){for(var r=(n=n||{}).defaultFontFamily||"times",o=Object.assign({},K2,n.genericFontFamilies||{}),i=null,s=null,l=0;l<e.length;++l)if(o[(i=vc(e[l])).family]&&(i.family=o[i.family]),t.hasOwnProperty(i.family)){s=t[i.family];break}if(!(s=s||t[r]))throw new Error("Could not find a font-family for the rule '"+Gu(i)+"' and default family '"+r+"'.");if(s=function(u,p){if(p[u])return p[u];var g=bc[u],b=g<=bc.normal?-1:1,v=Wu(p,Sh,g,b);if(!v)throw new Error("Could not find a matching font-stretch value for "+u);return v}(i.stretch,s),s=function(u,p){if(p[u])return p[u];for(var g=Ah[u],b=0;b<g.length;++b)if(p[g[b]])return p[g[b]];throw new Error("Could not find a matching font-style for "+u)}(i.style,s),!(s=function(u,p){if(p[u])return p[u];if(u===400&&p[500])return p[500];if(u===500&&p[400])return p[400];var g=J2[u],b=Wu(p,Nh,g,u<400?-1:1);if(!b)throw new Error("Could not find a matching font-weight for value "+u);return b}(i.weight,s)))throw new Error("Failed to resolve a font for the rule '"+Gu(i)+"'.");return s}function Yu(t){return t.trimLeft()}function Z2(t,e){for(var n=0;n<t.length;){if(t.charAt(n)===e)return[t.substring(0,n),t.substring(n+1)];n+=1}return null}function Q2(t){var e=t.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return e===null?null:[e[0],t.substring(e[0].length)]}var ls,$u,Ju,_l=["times"];(function(t){var e,n,r,o,i,s,l,u,p,g=function(x){return x=x||{},this.isStrokeTransparent=x.isStrokeTransparent||!1,this.strokeOpacity=x.strokeOpacity||1,this.strokeStyle=x.strokeStyle||"#000000",this.fillStyle=x.fillStyle||"#000000",this.isFillTransparent=x.isFillTransparent||!1,this.fillOpacity=x.fillOpacity||1,this.font=x.font||"10px sans-serif",this.textBaseline=x.textBaseline||"alphabetic",this.textAlign=x.textAlign||"left",this.lineWidth=x.lineWidth||1,this.lineJoin=x.lineJoin||"miter",this.lineCap=x.lineCap||"butt",this.path=x.path||[],this.transform=x.transform!==void 0?x.transform.clone():new u,this.globalCompositeOperation=x.globalCompositeOperation||"normal",this.globalAlpha=x.globalAlpha||1,this.clip_path=x.clip_path||[],this.currentPoint=x.currentPoint||new s,this.miterLimit=x.miterLimit||10,this.lastPoint=x.lastPoint||new s,this.lineDashOffset=x.lineDashOffset||0,this.lineDash=x.lineDash||[],this.margin=x.margin||[0,0,0,0],this.prevPageLastElemOffset=x.prevPageLastElemOffset||0,this.ignoreClearRect=typeof x.ignoreClearRect!="boolean"||x.ignoreClearRect,this};t.events.push(["initialized",function(){this.context2d=new b(this),e=this.internal.f2,n=this.internal.getCoordinateString,r=this.internal.getVerticalCoordinateString,o=this.internal.getHorizontalCoordinate,i=this.internal.getVerticalCoordinate,s=this.internal.Point,l=this.internal.Rectangle,u=this.internal.Matrix,p=new g}]);var b=function(x){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}});var B=x;Object.defineProperty(this,"pdf",{get:function(){return B}});var M=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return M},set:function(ye){M=!!ye}});var z=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return z},set:function(ye){z=!!ye}});var H=0;Object.defineProperty(this,"posX",{get:function(){return H},set:function(ye){isNaN(ye)||(H=ye)}});var G=0;Object.defineProperty(this,"posY",{get:function(){return G},set:function(ye){isNaN(ye)||(G=ye)}}),Object.defineProperty(this,"margin",{get:function(){return p.margin},set:function(ye){var W;typeof ye=="number"?W=[ye,ye,ye,ye]:((W=new Array(4))[0]=ye[0],W[1]=ye.length>=2?ye[1]:W[0],W[2]=ye.length>=3?ye[2]:W[0],W[3]=ye.length>=4?ye[3]:W[1]),p.margin=W}});var ee=!1;Object.defineProperty(this,"autoPaging",{get:function(){return ee},set:function(ye){ee=ye}});var oe=0;Object.defineProperty(this,"lastBreak",{get:function(){return oe},set:function(ye){oe=ye}});var ve=[];Object.defineProperty(this,"pageBreaks",{get:function(){return ve},set:function(ye){ve=ye}}),Object.defineProperty(this,"ctx",{get:function(){return p},set:function(ye){ye instanceof g&&(p=ye)}}),Object.defineProperty(this,"path",{get:function(){return p.path},set:function(ye){p.path=ye}});var Se=[];Object.defineProperty(this,"ctxStack",{get:function(){return Se},set:function(ye){Se=ye}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(ye){var W;W=v(ye),this.ctx.fillStyle=W.style,this.ctx.isFillTransparent=W.a===0,this.ctx.fillOpacity=W.a,this.pdf.setFillColor(W.r,W.g,W.b,{a:W.a}),this.pdf.setTextColor(W.r,W.g,W.b,{a:W.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(ye){var W=v(ye);this.ctx.strokeStyle=W.style,this.ctx.isStrokeTransparent=W.a===0,this.ctx.strokeOpacity=W.a,W.a===0?this.pdf.setDrawColor(255,255,255):(W.a,this.pdf.setDrawColor(W.r,W.g,W.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(ye){["butt","round","square"].indexOf(ye)!==-1&&(this.ctx.lineCap=ye,this.pdf.setLineCap(ye))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(ye){isNaN(ye)||(this.ctx.lineWidth=ye,this.pdf.setLineWidth(ye))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(ye){["bevel","round","miter"].indexOf(ye)!==-1&&(this.ctx.lineJoin=ye,this.pdf.setLineJoin(ye))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(ye){isNaN(ye)||(this.ctx.miterLimit=ye,this.pdf.setMiterLimit(ye))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(ye){this.ctx.textBaseline=ye}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(ye){["right","end","center","left","start"].indexOf(ye)!==-1&&(this.ctx.textAlign=ye)}});var be=null;function Ce(ye,W){if(be===null){var Ze=function(qe){var ke=[];return Object.keys(qe).forEach(function(Ie){qe[Ie].forEach(function(Fe){var Te=null;switch(Fe){case"bold":Te={family:Ie,weight:"bold"};break;case"italic":Te={family:Ie,style:"italic"};break;case"bolditalic":Te={family:Ie,weight:"bold",style:"italic"};break;case"":case"normal":Te={family:Ie}}Te!==null&&(Te.ref={name:Ie,style:Fe},ke.push(Te))})}),ke}(ye.getFontList());be=function(qe){for(var ke={},Ie=0;Ie<qe.length;++Ie){var Fe=vc(qe[Ie]),Te=Fe.family,He=Fe.stretch,Qe=Fe.style,et=Fe.weight;ke[Te]=ke[Te]||{},ke[Te][He]=ke[Te][He]||{},ke[Te][He][Qe]=ke[Te][He][Qe]||{},ke[Te][He][Qe][et]=Fe}return ke}(Ze.concat(W))}return be}var ze=null;Object.defineProperty(this,"fontFaces",{get:function(){return ze},set:function(ye){be=null,ze=ye}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(ye){var W;if(this.ctx.font=ye,(W=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(ye))!==null){var Ze=W[1];W[2];var qe=W[3],ke=W[4];W[5];var Ie=W[6],Fe=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(ke)[2];ke=Math.floor(Fe==="px"?parseFloat(ke)*this.pdf.internal.scaleFactor:Fe==="em"?parseFloat(ke)*this.pdf.getFontSize():parseFloat(ke)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(ke);var Te=function($e){var st,je,rn=[],ht=$e.trim();if(ht==="")return _l;if(ht in Vu)return[Vu[ht]];for(;ht!=="";){switch(je=null,st=(ht=Yu(ht)).charAt(0)){case'"':case"'":je=Z2(ht.substring(1),st);break;default:je=Q2(ht)}if(je===null||(rn.push(je[0]),(ht=Yu(je[1]))!==""&&ht.charAt(0)!==","))return _l;ht=ht.replace(/^,/,"")}return rn}(Ie);if(this.fontFaces){var He=X2(Ce(this.pdf,this.fontFaces),Te.map(function($e){return{family:$e,stretch:"normal",weight:qe,style:Ze}}));this.pdf.setFont(He.ref.name,He.ref.style)}else{var Qe="";(qe==="bold"||parseInt(qe,10)>=700||Ze==="bold")&&(Qe="bold"),Ze==="italic"&&(Qe+="italic"),Qe.length===0&&(Qe="normal");for(var et="",rt={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},dt=0;dt<Te.length;dt++){if(this.pdf.internal.getFont(Te[dt],Qe,{noFallback:!0,disableWarning:!0})!==void 0){et=Te[dt];break}if(Qe==="bolditalic"&&this.pdf.internal.getFont(Te[dt],"bold",{noFallback:!0,disableWarning:!0})!==void 0)et=Te[dt],Qe="bold";else if(this.pdf.internal.getFont(Te[dt],"normal",{noFallback:!0,disableWarning:!0})!==void 0){et=Te[dt],Qe="normal";break}}if(et===""){for(var yt=0;yt<Te.length;yt++)if(rt[Te[yt]]){et=rt[Te[yt]];break}}et=et===""?"Times":et,this.pdf.setFont(et,Qe)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(ye){this.ctx.globalCompositeOperation=ye}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(ye){this.ctx.globalAlpha=ye}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(ye){this.ctx.lineDashOffset=ye,me.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(ye){this.ctx.lineDash=ye,me.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(ye){this.ctx.ignoreClearRect=!!ye}})};b.prototype.setLineDash=function(x){this.lineDash=x},b.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},b.prototype.fill=function(){Y.call(this,"fill",!1)},b.prototype.stroke=function(){Y.call(this,"stroke",!1)},b.prototype.beginPath=function(){this.path=[{type:"begin"}]},b.prototype.moveTo=function(x,B){if(isNaN(x)||isNaN(B))throw kt.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.moveTo");var M=this.ctx.transform.applyToPoint(new s(x,B));this.path.push({type:"mt",x:M.x,y:M.y}),this.ctx.lastPoint=new s(x,B)},b.prototype.closePath=function(){var x=new s(0,0),B=0;for(B=this.path.length-1;B!==-1;B--)if(this.path[B].type==="begin"&&Lt(this.path[B+1])==="object"&&typeof this.path[B+1].x=="number"){x=new s(this.path[B+1].x,this.path[B+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new s(x.x,x.y)},b.prototype.lineTo=function(x,B){if(isNaN(x)||isNaN(B))throw kt.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.lineTo");var M=this.ctx.transform.applyToPoint(new s(x,B));this.path.push({type:"lt",x:M.x,y:M.y}),this.ctx.lastPoint=new s(M.x,M.y)},b.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),Y.call(this,null,!0)},b.prototype.quadraticCurveTo=function(x,B,M,z){if(isNaN(M)||isNaN(z)||isNaN(x)||isNaN(B))throw kt.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var H=this.ctx.transform.applyToPoint(new s(M,z)),G=this.ctx.transform.applyToPoint(new s(x,B));this.path.push({type:"qct",x1:G.x,y1:G.y,x:H.x,y:H.y}),this.ctx.lastPoint=new s(H.x,H.y)},b.prototype.bezierCurveTo=function(x,B,M,z,H,G){if(isNaN(H)||isNaN(G)||isNaN(x)||isNaN(B)||isNaN(M)||isNaN(z))throw kt.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var ee=this.ctx.transform.applyToPoint(new s(H,G)),oe=this.ctx.transform.applyToPoint(new s(x,B)),ve=this.ctx.transform.applyToPoint(new s(M,z));this.path.push({type:"bct",x1:oe.x,y1:oe.y,x2:ve.x,y2:ve.y,x:ee.x,y:ee.y}),this.ctx.lastPoint=new s(ee.x,ee.y)},b.prototype.arc=function(x,B,M,z,H,G){if(isNaN(x)||isNaN(B)||isNaN(M)||isNaN(z)||isNaN(H))throw kt.error("jsPDF.context2d.arc: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.arc");if(G=!!G,!this.ctx.transform.isIdentity){var ee=this.ctx.transform.applyToPoint(new s(x,B));x=ee.x,B=ee.y;var oe=this.ctx.transform.applyToPoint(new s(0,M)),ve=this.ctx.transform.applyToPoint(new s(0,0));M=Math.sqrt(Math.pow(oe.x-ve.x,2)+Math.pow(oe.y-ve.y,2))}Math.abs(H-z)>=2*Math.PI&&(z=0,H=2*Math.PI),this.path.push({type:"arc",x,y:B,radius:M,startAngle:z,endAngle:H,counterclockwise:G})},b.prototype.arcTo=function(x,B,M,z,H){throw new Error("arcTo not implemented.")},b.prototype.rect=function(x,B,M,z){if(isNaN(x)||isNaN(B)||isNaN(M)||isNaN(z))throw kt.error("jsPDF.context2d.rect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(x,B),this.lineTo(x+M,B),this.lineTo(x+M,B+z),this.lineTo(x,B+z),this.lineTo(x,B),this.lineTo(x+M,B),this.lineTo(x,B)},b.prototype.fillRect=function(x,B,M,z){if(isNaN(x)||isNaN(B)||isNaN(M)||isNaN(z))throw kt.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!d.call(this)){var H={};this.lineCap!=="butt"&&(H.lineCap=this.lineCap,this.lineCap="butt"),this.lineJoin!=="miter"&&(H.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(x,B,M,z),this.fill(),H.hasOwnProperty("lineCap")&&(this.lineCap=H.lineCap),H.hasOwnProperty("lineJoin")&&(this.lineJoin=H.lineJoin)}},b.prototype.strokeRect=function(x,B,M,z){if(isNaN(x)||isNaN(B)||isNaN(M)||isNaN(z))throw kt.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeRect");_.call(this)||(this.beginPath(),this.rect(x,B,M,z),this.stroke())},b.prototype.clearRect=function(x,B,M,z){if(isNaN(x)||isNaN(B)||isNaN(M)||isNaN(z))throw kt.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(x,B,M,z))},b.prototype.save=function(x){x=typeof x!="boolean"||x;for(var B=this.pdf.internal.getCurrentPageInfo().pageNumber,M=0;M<this.pdf.internal.getNumberOfPages();M++)this.pdf.setPage(M+1),this.pdf.internal.out("q");if(this.pdf.setPage(B),x){this.ctx.fontSize=this.pdf.internal.getFontSize();var z=new g(this.ctx);this.ctxStack.push(this.ctx),this.ctx=z}},b.prototype.restore=function(x){x=typeof x!="boolean"||x;for(var B=this.pdf.internal.getCurrentPageInfo().pageNumber,M=0;M<this.pdf.internal.getNumberOfPages();M++)this.pdf.setPage(M+1),this.pdf.internal.out("Q");this.pdf.setPage(B),x&&this.ctxStack.length!==0&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},b.prototype.toDataURL=function(){throw new Error("toDataUrl not implemented.")};var v=function(x){var B,M,z,H;if(x.isCanvasGradient===!0&&(x=x.getColor()),!x)return{r:0,g:0,b:0,a:0,style:x};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(x))B=0,M=0,z=0,H=0;else{var G=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(x);if(G!==null)B=parseInt(G[1]),M=parseInt(G[2]),z=parseInt(G[3]),H=1;else if((G=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(x))!==null)B=parseInt(G[1]),M=parseInt(G[2]),z=parseInt(G[3]),H=parseFloat(G[4]);else{if(H=1,typeof x=="string"&&x.charAt(0)!=="#"){var ee=new gh(x);x=ee.ok?ee.toHex():"#000000"}x.length===4?(B=x.substring(1,2),B+=B,M=x.substring(2,3),M+=M,z=x.substring(3,4),z+=z):(B=x.substring(1,3),M=x.substring(3,5),z=x.substring(5,7)),B=parseInt(B,16),M=parseInt(M,16),z=parseInt(z,16)}}return{r:B,g:M,b:z,a:H,style:x}},d=function(){return this.ctx.isFillTransparent||this.globalAlpha==0},_=function(){return!!(this.ctx.isStrokeTransparent||this.globalAlpha==0)};b.prototype.fillText=function(x,B,M,z){if(isNaN(B)||isNaN(M)||typeof x!="string")throw kt.error("jsPDF.context2d.fillText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillText");if(z=isNaN(z)?void 0:z,!d.call(this)){var H=Q(this.ctx.transform.rotation),G=this.ctx.transform.scaleX;k.call(this,{text:x,x:B,y:M,scale:G,angle:H,align:this.textAlign,maxWidth:z})}},b.prototype.strokeText=function(x,B,M,z){if(isNaN(B)||isNaN(M)||typeof x!="string")throw kt.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!_.call(this)){z=isNaN(z)?void 0:z;var H=Q(this.ctx.transform.rotation),G=this.ctx.transform.scaleX;k.call(this,{text:x,x:B,y:M,scale:G,renderingMode:"stroke",angle:H,align:this.textAlign,maxWidth:z})}},b.prototype.measureText=function(x){if(typeof x!="string")throw kt.error("jsPDF.context2d.measureText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.measureText");var B=this.pdf,M=this.pdf.internal.scaleFactor,z=B.internal.getFontSize(),H=B.getStringUnitWidth(x)*z/B.internal.scaleFactor,G=function(ee){var oe=(ee=ee||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return oe}}),this};return new G({width:H*=Math.round(96*M/72*1e4)/1e4})},b.prototype.scale=function(x,B){if(isNaN(x)||isNaN(B))throw kt.error("jsPDF.context2d.scale: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.scale");var M=new u(x,0,0,B,0,0);this.ctx.transform=this.ctx.transform.multiply(M)},b.prototype.rotate=function(x){if(isNaN(x))throw kt.error("jsPDF.context2d.rotate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rotate");var B=new u(Math.cos(x),Math.sin(x),-Math.sin(x),Math.cos(x),0,0);this.ctx.transform=this.ctx.transform.multiply(B)},b.prototype.translate=function(x,B){if(isNaN(x)||isNaN(B))throw kt.error("jsPDF.context2d.translate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.translate");var M=new u(1,0,0,1,x,B);this.ctx.transform=this.ctx.transform.multiply(M)},b.prototype.transform=function(x,B,M,z,H,G){if(isNaN(x)||isNaN(B)||isNaN(M)||isNaN(z)||isNaN(H)||isNaN(G))throw kt.error("jsPDF.context2d.transform: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.transform");var ee=new u(x,B,M,z,H,G);this.ctx.transform=this.ctx.transform.multiply(ee)},b.prototype.setTransform=function(x,B,M,z,H,G){x=isNaN(x)?1:x,B=isNaN(B)?0:B,M=isNaN(M)?0:M,z=isNaN(z)?1:z,H=isNaN(H)?0:H,G=isNaN(G)?0:G,this.ctx.transform=new u(x,B,M,z,H,G)};var S=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};b.prototype.drawImage=function(x,B,M,z,H,G,ee,oe,ve){var Se=this.pdf.getImageProperties(x),be=1,Ce=1,ze=1,ye=1;z!==void 0&&oe!==void 0&&(ze=oe/z,ye=ve/H,be=Se.width/z*oe/z,Ce=Se.height/H*ve/H),G===void 0&&(G=B,ee=M,B=0,M=0),z!==void 0&&oe===void 0&&(oe=z,ve=H),z===void 0&&oe===void 0&&(oe=Se.width,ve=Se.height);for(var W,Ze=this.ctx.transform.decompose(),qe=Q(Ze.rotate.shx),ke=new u,Ie=(ke=(ke=(ke=ke.multiply(Ze.translate)).multiply(Ze.skew)).multiply(Ze.scale)).applyToRectangle(new l(G-B*ze,ee-M*ye,z*be,H*Ce)),Fe=T.call(this,Ie),Te=[],He=0;He<Fe.length;He+=1)Te.indexOf(Fe[He])===-1&&Te.push(Fe[He]);if(q(Te),this.autoPaging)for(var Qe=Te[0],et=Te[Te.length-1],rt=Qe;rt<et+1;rt++){this.pdf.setPage(rt);var dt=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],yt=rt===1?this.posY+this.margin[0]:this.margin[0],$e=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],st=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],je=rt===1?0:$e+(rt-2)*st;if(this.ctx.clip_path.length!==0){var rn=this.path;W=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=j(W,this.posX+this.margin[3],-je+yt+this.ctx.prevPageLastElemOffset),le.call(this,"fill",!0),this.path=rn}var ht=JSON.parse(JSON.stringify(Ie));ht=j([ht],this.posX+this.margin[3],-je+yt+this.ctx.prevPageLastElemOffset)[0];var Rn=(rt>Qe||rt<et)&&S.call(this);Rn&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],dt,st,null).clip().discardPath()),this.pdf.addImage(x,"JPEG",ht.x,ht.y,ht.w,ht.h,null,null,qe),Rn&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(x,"JPEG",Ie.x,Ie.y,Ie.w,Ie.h,null,null,qe)};var T=function(x,B,M){var z=[];B=B||this.pdf.internal.pageSize.width,M=M||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var H=this.posY+this.ctx.prevPageLastElemOffset;switch(x.type){default:case"mt":case"lt":z.push(Math.floor((x.y+H)/M)+1);break;case"arc":z.push(Math.floor((x.y+H-x.radius)/M)+1),z.push(Math.floor((x.y+H+x.radius)/M)+1);break;case"qct":var G=he(this.ctx.lastPoint.x,this.ctx.lastPoint.y,x.x1,x.y1,x.x,x.y);z.push(Math.floor((G.y+H)/M)+1),z.push(Math.floor((G.y+G.h+H)/M)+1);break;case"bct":var ee=V(this.ctx.lastPoint.x,this.ctx.lastPoint.y,x.x1,x.y1,x.x2,x.y2,x.x,x.y);z.push(Math.floor((ee.y+H)/M)+1),z.push(Math.floor((ee.y+ee.h+H)/M)+1);break;case"rect":z.push(Math.floor((x.y+H)/M)+1),z.push(Math.floor((x.y+x.h+H)/M)+1)}for(var oe=0;oe<z.length;oe+=1)for(;this.pdf.internal.getNumberOfPages()<z[oe];)A.call(this);return z},A=function(){var x=this.fillStyle,B=this.strokeStyle,M=this.font,z=this.lineCap,H=this.lineWidth,G=this.lineJoin;this.pdf.addPage(),this.fillStyle=x,this.strokeStyle=B,this.font=M,this.lineCap=z,this.lineWidth=H,this.lineJoin=G},j=function(x,B,M){for(var z=0;z<x.length;z++)switch(x[z].type){case"bct":x[z].x2+=B,x[z].y2+=M;case"qct":x[z].x1+=B,x[z].y1+=M;case"mt":case"lt":case"arc":default:x[z].x+=B,x[z].y+=M}return x},q=function(x){return x.sort(function(B,M){return B-M})},Y=function(x,B){for(var M,z,H=this.fillStyle,G=this.strokeStyle,ee=this.lineCap,oe=this.lineWidth,ve=Math.abs(oe*this.ctx.transform.scaleX),Se=this.lineJoin,be=JSON.parse(JSON.stringify(this.path)),Ce=JSON.parse(JSON.stringify(this.path)),ze=[],ye=0;ye<Ce.length;ye++)if(Ce[ye].x!==void 0)for(var W=T.call(this,Ce[ye]),Ze=0;Ze<W.length;Ze+=1)ze.indexOf(W[Ze])===-1&&ze.push(W[Ze]);for(var qe=0;qe<ze.length;qe++)for(;this.pdf.internal.getNumberOfPages()<ze[qe];)A.call(this);if(q(ze),this.autoPaging)for(var ke=ze[0],Ie=ze[ze.length-1],Fe=ke;Fe<Ie+1;Fe++){this.pdf.setPage(Fe),this.fillStyle=H,this.strokeStyle=G,this.lineCap=ee,this.lineWidth=ve,this.lineJoin=Se;var Te=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],He=Fe===1?this.posY+this.margin[0]:this.margin[0],Qe=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],et=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],rt=Fe===1?0:Qe+(Fe-2)*et;if(this.ctx.clip_path.length!==0){var dt=this.path;M=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=j(M,this.posX+this.margin[3],-rt+He+this.ctx.prevPageLastElemOffset),le.call(this,x,!0),this.path=dt}if(z=JSON.parse(JSON.stringify(be)),this.path=j(z,this.posX+this.margin[3],-rt+He+this.ctx.prevPageLastElemOffset),B===!1||Fe===0){var yt=(Fe>ke||Fe<Ie)&&S.call(this);yt&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],Te,et,null).clip().discardPath()),le.call(this,x,B),yt&&this.pdf.restoreGraphicsState()}this.lineWidth=oe}else this.lineWidth=ve,le.call(this,x,B),this.lineWidth=oe;this.path=be},le=function(x,B){if((x!=="stroke"||B||!_.call(this))&&(x==="stroke"||B||!d.call(this))){for(var M,z,H=[],G=this.path,ee=0;ee<G.length;ee++){var oe=G[ee];switch(oe.type){case"begin":H.push({begin:!0});break;case"close":H.push({close:!0});break;case"mt":H.push({start:oe,deltas:[],abs:[]});break;case"lt":var ve=H.length;if(G[ee-1]&&!isNaN(G[ee-1].x)&&(M=[oe.x-G[ee-1].x,oe.y-G[ee-1].y],ve>0)){for(;ve>=0;ve--)if(H[ve-1].close!==!0&&H[ve-1].begin!==!0){H[ve-1].deltas.push(M),H[ve-1].abs.push(oe);break}}break;case"bct":M=[oe.x1-G[ee-1].x,oe.y1-G[ee-1].y,oe.x2-G[ee-1].x,oe.y2-G[ee-1].y,oe.x-G[ee-1].x,oe.y-G[ee-1].y],H[H.length-1].deltas.push(M);break;case"qct":var Se=G[ee-1].x+2/3*(oe.x1-G[ee-1].x),be=G[ee-1].y+2/3*(oe.y1-G[ee-1].y),Ce=oe.x+2/3*(oe.x1-oe.x),ze=oe.y+2/3*(oe.y1-oe.y),ye=oe.x,W=oe.y;M=[Se-G[ee-1].x,be-G[ee-1].y,Ce-G[ee-1].x,ze-G[ee-1].y,ye-G[ee-1].x,W-G[ee-1].y],H[H.length-1].deltas.push(M);break;case"arc":H.push({deltas:[],abs:[],arc:!0}),Array.isArray(H[H.length-1].abs)&&H[H.length-1].abs.push(oe)}}z=B?null:x==="stroke"?"stroke":"fill";for(var Ze=!1,qe=0;qe<H.length;qe++)if(H[qe].arc)for(var ke=H[qe].abs,Ie=0;Ie<ke.length;Ie++){var Fe=ke[Ie];Fe.type==="arc"?F.call(this,Fe.x,Fe.y,Fe.radius,Fe.startAngle,Fe.endAngle,Fe.counterclockwise,void 0,B,!Ze):O.call(this,Fe.x,Fe.y),Ze=!0}else if(H[qe].close===!0)this.pdf.internal.out("h"),Ze=!1;else if(H[qe].begin!==!0){var Te=H[qe].start.x,He=H[qe].start.y;C.call(this,H[qe].deltas,Te,He),Ze=!0}z&&J.call(this,z),B&&re.call(this)}},ae=function(x){var B=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,M=B*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return x-M;case"top":return x+B-M;case"hanging":return x+B-2*M;case"middle":return x+B/2-M;case"ideographic":return x;case"alphabetic":default:return x}},$=function(x){return x+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};b.prototype.createLinearGradient=function(){var x=function(){};return x.colorStops=[],x.addColorStop=function(B,M){this.colorStops.push([B,M])},x.getColor=function(){return this.colorStops.length===0?"#000000":this.colorStops[0][1]},x.isCanvasGradient=!0,x},b.prototype.createPattern=function(){return this.createLinearGradient()},b.prototype.createRadialGradient=function(){return this.createLinearGradient()};var F=function(x,B,M,z,H,G,ee,oe,ve){for(var Se=ne.call(this,M,z,H,G),be=0;be<Se.length;be++){var Ce=Se[be];be===0&&(ve?N.call(this,Ce.x1+x,Ce.y1+B):O.call(this,Ce.x1+x,Ce.y1+B)),X.call(this,x,B,Ce.x2,Ce.y2,Ce.x3,Ce.y3,Ce.x4,Ce.y4)}oe?re.call(this):J.call(this,ee)},J=function(x){switch(x){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},re=function(){this.pdf.clip(),this.pdf.discardPath()},N=function(x,B){this.pdf.internal.out(n(x)+" "+r(B)+" m")},k=function(x){var B;switch(x.align){case"right":case"end":B="right";break;case"center":B="center";break;case"left":case"start":default:B="left"}var M=this.pdf.getTextDimensions(x.text),z=ae.call(this,x.y),H=$.call(this,z)-M.h,G=this.ctx.transform.applyToPoint(new s(x.x,z)),ee=this.ctx.transform.decompose(),oe=new u;oe=(oe=(oe=oe.multiply(ee.translate)).multiply(ee.skew)).multiply(ee.scale);for(var ve,Se,be,Ce=this.ctx.transform.applyToRectangle(new l(x.x,z,M.w,M.h)),ze=oe.applyToRectangle(new l(x.x,H,M.w,M.h)),ye=T.call(this,ze),W=[],Ze=0;Ze<ye.length;Ze+=1)W.indexOf(ye[Ze])===-1&&W.push(ye[Ze]);if(q(W),this.autoPaging)for(var qe=W[0],ke=W[W.length-1],Ie=qe;Ie<ke+1;Ie++){this.pdf.setPage(Ie);var Fe=Ie===1?this.posY+this.margin[0]:this.margin[0],Te=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],He=this.pdf.internal.pageSize.height-this.margin[2],Qe=He-this.margin[0],et=this.pdf.internal.pageSize.width-this.margin[1],rt=et-this.margin[3],dt=Ie===1?0:Te+(Ie-2)*Qe;if(this.ctx.clip_path.length!==0){var yt=this.path;ve=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=j(ve,this.posX+this.margin[3],-1*dt+Fe),le.call(this,"fill",!0),this.path=yt}var $e=j([JSON.parse(JSON.stringify(ze))],this.posX+this.margin[3],-dt+Fe+this.ctx.prevPageLastElemOffset)[0];x.scale>=.01&&(Se=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Se*x.scale),be=this.lineWidth,this.lineWidth=be*x.scale);var st=this.autoPaging!=="text";if(st||$e.y+$e.h<=He){if(st||$e.y>=Fe&&$e.x<=et){var je=st?x.text:this.pdf.splitTextToSize(x.text,x.maxWidth||et-$e.x)[0],rn=j([JSON.parse(JSON.stringify(Ce))],this.posX+this.margin[3],-dt+Fe+this.ctx.prevPageLastElemOffset)[0],ht=st&&(Ie>qe||Ie<ke)&&S.call(this);ht&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],rt,Qe,null).clip().discardPath()),this.pdf.text(je,rn.x,rn.y,{angle:x.angle,align:B,renderingMode:x.renderingMode}),ht&&this.pdf.restoreGraphicsState()}}else $e.y<He&&(this.ctx.prevPageLastElemOffset+=He-$e.y);x.scale>=.01&&(this.pdf.setFontSize(Se),this.lineWidth=be)}else x.scale>=.01&&(Se=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Se*x.scale),be=this.lineWidth,this.lineWidth=be*x.scale),this.pdf.text(x.text,G.x+this.posX,G.y+this.posY,{angle:x.angle,align:B,renderingMode:x.renderingMode,maxWidth:x.maxWidth}),x.scale>=.01&&(this.pdf.setFontSize(Se),this.lineWidth=be)},O=function(x,B,M,z){M=M||0,z=z||0,this.pdf.internal.out(n(x+M)+" "+r(B+z)+" l")},C=function(x,B,M){return this.pdf.lines(x,B,M,null,null)},X=function(x,B,M,z,H,G,ee,oe){this.pdf.internal.out([e(o(M+x)),e(i(z+B)),e(o(H+x)),e(i(G+B)),e(o(ee+x)),e(i(oe+B)),"c"].join(" "))},ne=function(x,B,M,z){for(var H=2*Math.PI,G=Math.PI/2;B>M;)B-=H;var ee=Math.abs(M-B);ee<H&&z&&(ee=H-ee);for(var oe=[],ve=z?-1:1,Se=B;ee>1e-5;){var be=Se+ve*Math.min(ee,G);oe.push(ue.call(this,x,Se,be)),ee-=Math.abs(be-Se),Se=be}return oe},ue=function(x,B,M){var z=(M-B)/2,H=x*Math.cos(z),G=x*Math.sin(z),ee=H,oe=-G,ve=ee*ee+oe*oe,Se=ve+ee*H+oe*G,be=4/3*(Math.sqrt(2*ve*Se)-Se)/(ee*G-oe*H),Ce=ee-be*oe,ze=oe+be*ee,ye=Ce,W=-ze,Ze=z+B,qe=Math.cos(Ze),ke=Math.sin(Ze);return{x1:x*Math.cos(B),y1:x*Math.sin(B),x2:Ce*qe-ze*ke,y2:Ce*ke+ze*qe,x3:ye*qe-W*ke,y3:ye*ke+W*qe,x4:x*Math.cos(M),y4:x*Math.sin(M)}},Q=function(x){return 180*x/Math.PI},he=function(x,B,M,z,H,G){var ee=x+.5*(M-x),oe=B+.5*(z-B),ve=H+.5*(M-H),Se=G+.5*(z-G),be=Math.min(x,H,ee,ve),Ce=Math.max(x,H,ee,ve),ze=Math.min(B,G,oe,Se),ye=Math.max(B,G,oe,Se);return new l(be,ze,Ce-be,ye-ze)},V=function(x,B,M,z,H,G,ee,oe){var ve,Se,be,Ce,ze,ye,W,Ze,qe,ke,Ie,Fe,Te,He,Qe=M-x,et=z-B,rt=H-M,dt=G-z,yt=ee-H,$e=oe-G;for(Se=0;Se<41;Se++)qe=(W=(be=x+(ve=Se/40)*Qe)+ve*((ze=M+ve*rt)-be))+ve*(ze+ve*(H+ve*yt-ze)-W),ke=(Ze=(Ce=B+ve*et)+ve*((ye=z+ve*dt)-Ce))+ve*(ye+ve*(G+ve*$e-ye)-Ze),Se==0?(Ie=qe,Fe=ke,Te=qe,He=ke):(Ie=Math.min(Ie,qe),Fe=Math.min(Fe,ke),Te=Math.max(Te,qe),He=Math.max(He,ke));return new l(Math.round(Ie),Math.round(Fe),Math.round(Te-Ie),Math.round(He-Fe))},me=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var x,B,M=(x=this.ctx.lineDash,B=this.ctx.lineDashOffset,JSON.stringify({lineDash:x,lineDashOffset:B}));this.prevLineDash!==M&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=M)}}})(Ke.API),function(t){var e=function(i){var s,l,u,p,g,b,v,d,_,S;for(l=[],u=0,p=(i+=s="\0\0\0\0".slice(i.length%4||4)).length;p>u;u+=4)(g=(i.charCodeAt(u)<<24)+(i.charCodeAt(u+1)<<16)+(i.charCodeAt(u+2)<<8)+i.charCodeAt(u+3))!==0?(b=(g=((g=((g=((g=(g-(S=g%85))/85)-(_=g%85))/85)-(d=g%85))/85)-(v=g%85))/85)%85,l.push(b+33,v+33,d+33,_+33,S+33)):l.push(122);return function(T,A){for(var j=A;j>0;j--)T.pop()}(l,s.length),String.fromCharCode.apply(String,l)+"~>"},n=function(i){var s,l,u,p,g,b=String,v="length",d=255,_="charCodeAt",S="slice",T="replace";for(i[S](-2),i=i[S](0,-2)[T](/\s/g,"")[T]("z","!!!!!"),u=[],p=0,g=(i+=s="uuuuu"[S](i[v]%5||5))[v];g>p;p+=5)l=52200625*(i[_](p)-33)+614125*(i[_](p+1)-33)+7225*(i[_](p+2)-33)+85*(i[_](p+3)-33)+(i[_](p+4)-33),u.push(d&l>>24,d&l>>16,d&l>>8,d&l);return function(A,j){for(var q=j;q>0;q--)A.pop()}(u,s[v]),b.fromCharCode.apply(b,u)},r=function(i){var s=new RegExp(/^([0-9A-Fa-f]{2})+$/);if((i=i.replace(/\s/g,"")).indexOf(">")!==-1&&(i=i.substr(0,i.indexOf(">"))),i.length%2&&(i+="0"),s.test(i)===!1)return"";for(var l="",u=0;u<i.length;u+=2)l+=String.fromCharCode("0x"+(i[u]+i[u+1]));return l},o=function(i){for(var s=new Uint8Array(i.length),l=i.length;l--;)s[l]=i.charCodeAt(l);return i=(s=hc(s)).reduce(function(u,p){return u+String.fromCharCode(p)},"")};t.processDataByFilters=function(i,s){var l=0,u=i||"",p=[];for(typeof(s=s||[])=="string"&&(s=[s]),l=0;l<s.length;l+=1)switch(s[l]){case"ASCII85Decode":case"/ASCII85Decode":u=n(u),p.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":u=e(u),p.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":u=r(u),p.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":u=u.split("").map(function(g){return("0"+g.charCodeAt().toString(16)).slice(-2)}).join("")+">",p.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":u=o(u),p.push("/FlateDecode");break;default:throw new Error('The filter: "'+s[l]+'" is not implemented')}return{data:u,reverseChain:p.reverse().join(" ")}}}(Ke.API),function(t){t.loadFile=function(e,n,r){return function(o,i,s){i=i!==!1,s=typeof s=="function"?s:function(){};var l=void 0;try{l=function(u,p,g){var b=new XMLHttpRequest,v=0,d=function(_){var S=_.length,T=[],A=String.fromCharCode;for(v=0;v<S;v+=1)T.push(A(255&_.charCodeAt(v)));return T.join("")};if(b.open("GET",u,!p),b.overrideMimeType("text/plain; charset=x-user-defined"),p===!1&&(b.onload=function(){b.status===200?g(d(this.responseText)):g(void 0)}),b.send(null),p&&b.status===200)return d(b.responseText)}(o,i,s)}catch{}return l}(e,n,r)},t.loadImageFile=t.loadFile}(Ke.API),function(t){function e(){return(Xe.html2canvas?Promise.resolve(Xe.html2canvas):sc(()=>import("./html2canvas.esm-CBrSDip1.js"),[])).catch(function(s){return Promise.reject(new Error("Could not load html2canvas: "+s))}).then(function(s){return s.default?s.default:s})}function n(){return(Xe.DOMPurify?Promise.resolve(Xe.DOMPurify):sc(()=>import("./purify.es-CQJ0hv7W.js"),[])).catch(function(s){return Promise.reject(new Error("Could not load dompurify: "+s))}).then(function(s){return s.default?s.default:s})}var r=function(s){var l=Lt(s);return l==="undefined"?"undefined":l==="string"||s instanceof String?"string":l==="number"||s instanceof Number?"number":l==="function"||s instanceof Function?"function":s&&s.constructor===Array?"array":s&&s.nodeType===1?"element":l==="object"?"object":"unknown"},o=function(s,l){var u=document.createElement(s);for(var p in l.className&&(u.className=l.className),l.innerHTML&&l.dompurify&&(u.innerHTML=l.dompurify.sanitize(l.innerHTML)),l.style)u.style[p]=l.style[p];return u},i=function s(l){var u=Object.assign(s.convert(Promise.resolve()),JSON.parse(JSON.stringify(s.template))),p=s.convert(Promise.resolve(),u);return p=(p=p.setProgress(1,s,1,[s])).set(l)};(i.prototype=Object.create(Promise.prototype)).constructor=i,i.convert=function(s,l){return s.__proto__=l||i.prototype,s},i.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},i.prototype.from=function(s,l){return this.then(function(){switch(l=l||function(u){switch(r(u)){case"string":return"string";case"element":return u.nodeName.toLowerCase()==="canvas"?"canvas":"element";default:return"unknown"}}(s)){case"string":return this.then(n).then(function(u){return this.set({src:o("div",{innerHTML:s,dompurify:u})})});case"element":return this.set({src:s});case"canvas":return this.set({canvas:s});case"img":return this.set({img:s});default:return this.error("Unknown source type.")}})},i.prototype.to=function(s){switch(s){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},i.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var s={position:"relative",display:"inline-block",width:(typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},l=function u(p,g){for(var b=p.nodeType===3?document.createTextNode(p.nodeValue):p.cloneNode(!1),v=p.firstChild;v;v=v.nextSibling)g!==!0&&v.nodeType===1&&v.nodeName==="SCRIPT"||b.appendChild(u(v,g));return p.nodeType===1&&(p.nodeName==="CANVAS"?(b.width=p.width,b.height=p.height,b.getContext("2d").drawImage(p,0,0)):p.nodeName!=="TEXTAREA"&&p.nodeName!=="SELECT"||(b.value=p.value),b.addEventListener("load",function(){b.scrollTop=p.scrollTop,b.scrollLeft=p.scrollLeft},!0)),b}(this.prop.src,this.opt.html2canvas.javascriptEnabled);l.tagName==="BODY"&&(s.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=o("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=o("div",{className:"html2pdf__container",style:s}),this.prop.container.appendChild(l),this.prop.container.firstChild.appendChild(o("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},i.prototype.toCanvas=function(){var s=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(s).then(e).then(function(l){var u=Object.assign({},this.opt.html2canvas);return delete u.onrendered,l(this.prop.container,u)}).then(function(l){(this.opt.html2canvas.onrendered||function(){})(l),this.prop.canvas=l,document.body.removeChild(this.prop.overlay)})},i.prototype.toContext2d=function(){var s=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(s).then(e).then(function(l){var u=this.opt.jsPDF,p=this.opt.fontFaces,g=typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,b=Object.assign({async:!0,allowTaint:!0,scale:g,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete b.onrendered,u.context2d.autoPaging=this.opt.autoPaging===void 0||this.opt.autoPaging,u.context2d.posX=this.opt.x,u.context2d.posY=this.opt.y,u.context2d.margin=this.opt.margin,u.context2d.fontFaces=p,p)for(var v=0;v<p.length;++v){var d=p[v],_=d.src.find(function(S){return S.format==="truetype"});_&&u.addFont(_.url,d.ref.name,d.ref.style)}return b.windowHeight=b.windowHeight||0,b.windowHeight=b.windowHeight==0?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):b.windowHeight,u.context2d.save(!0),l(this.prop.container,b)}).then(function(l){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(l),this.prop.canvas=l,document.body.removeChild(this.prop.overlay)})},i.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var s=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=s})},i.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},i.prototype.output=function(s,l,u){return(u=u||"pdf").toLowerCase()==="img"||u.toLowerCase()==="image"?this.outputImg(s,l):this.outputPdf(s,l)},i.prototype.outputPdf=function(s,l){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(s,l)})},i.prototype.outputImg=function(s){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(s){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+s+'" is not supported.'}})},i.prototype.save=function(s){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(s?{filename:s}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},i.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},i.prototype.set=function(s){if(r(s)!=="object")return this;var l=Object.keys(s||{}).map(function(u){if(u in i.template.prop)return function(){this.prop[u]=s[u]};switch(u){case"margin":return this.setMargin.bind(this,s.margin);case"jsPDF":return function(){return this.opt.jsPDF=s.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,s.pageSize);default:return function(){this.opt[u]=s[u]}}},this);return this.then(function(){return this.thenList(l)})},i.prototype.get=function(s,l){return this.then(function(){var u=s in i.template.prop?this.prop[s]:this.opt[s];return l?l(u):u})},i.prototype.setMargin=function(s){return this.then(function(){switch(r(s)){case"number":s=[s,s,s,s];case"array":if(s.length===2&&(s=[s[0],s[1],s[0],s[1]]),s.length===4)break;default:return this.error("Invalid margin array.")}this.opt.margin=s}).then(this.setPageSize)},i.prototype.setPageSize=function(s){function l(u,p){return Math.floor(u*p/72*96)}return this.then(function(){(s=s||Ke.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(s.inner={width:s.width-this.opt.margin[1]-this.opt.margin[3],height:s.height-this.opt.margin[0]-this.opt.margin[2]},s.inner.px={width:l(s.inner.width,s.k),height:l(s.inner.height,s.k)},s.inner.ratio=s.inner.height/s.inner.width),this.prop.pageSize=s})},i.prototype.setProgress=function(s,l,u,p){return s!=null&&(this.progress.val=s),l!=null&&(this.progress.state=l),u!=null&&(this.progress.n=u),p!=null&&(this.progress.stack=p),this.progress.ratio=this.progress.val/this.progress.state,this},i.prototype.updateProgress=function(s,l,u,p){return this.setProgress(s?this.progress.val+s:null,l||null,u?this.progress.n+u:null,p?this.progress.stack.concat(p):null)},i.prototype.then=function(s,l){var u=this;return this.thenCore(s,l,function(p,g){return u.updateProgress(null,null,1,[p]),Promise.prototype.then.call(this,function(b){return u.updateProgress(null,p),b}).then(p,g).then(function(b){return u.updateProgress(1),b})})},i.prototype.thenCore=function(s,l,u){u=u||Promise.prototype.then,s&&(s=s.bind(this)),l&&(l=l.bind(this));var p=Promise.toString().indexOf("[native code]")!==-1&&Promise.name==="Promise"?this:i.convert(Object.assign({},this),Promise.prototype),g=u.call(p,s,l);return i.convert(g,this.__proto__)},i.prototype.thenExternal=function(s,l){return Promise.prototype.then.call(this,s,l)},i.prototype.thenList=function(s){var l=this;return s.forEach(function(u){l=l.thenCore(u)}),l},i.prototype.catch=function(s){s&&(s=s.bind(this));var l=Promise.prototype.catch.call(this,s);return i.convert(l,this)},i.prototype.catchExternal=function(s){return Promise.prototype.catch.call(this,s)},i.prototype.error=function(s){return this.then(function(){throw new Error(s)})},i.prototype.using=i.prototype.set,i.prototype.saveAs=i.prototype.save,i.prototype.export=i.prototype.output,i.prototype.run=i.prototype.then,Ke.getPageSize=function(s,l,u){if(Lt(s)==="object"){var p=s;s=p.orientation,l=p.unit||l,u=p.format||u}l=l||"mm",u=u||"a4",s=(""+(s||"P")).toLowerCase();var g,b=(""+u).toLowerCase(),v={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(l){case"pt":g=1;break;case"mm":g=72/25.4;break;case"cm":g=72/2.54;break;case"in":g=72;break;case"px":g=.75;break;case"pc":case"em":g=12;break;case"ex":g=6;break;default:throw"Invalid unit: "+l}var d,_=0,S=0;if(v.hasOwnProperty(b))_=v[b][1]/g,S=v[b][0]/g;else try{_=u[1],S=u[0]}catch{throw new Error("Invalid format: "+u)}if(s==="p"||s==="portrait")s="p",S>_&&(d=S,S=_,_=d);else{if(s!=="l"&&s!=="landscape")throw"Invalid orientation: "+s;s="l",_>S&&(d=S,S=_,_=d)}return{width:S,height:_,unit:l,k:g,orientation:s}},t.html=function(s,l){(l=l||{}).callback=l.callback||function(){},l.html2canvas=l.html2canvas||{},l.html2canvas.canvas=l.html2canvas.canvas||this.canvas,l.jsPDF=l.jsPDF||this,l.fontFaces=l.fontFaces?l.fontFaces.map(vc):null;var u=new i(l);return l.worker?u:u.from(s).doCallback()}}(Ke.API),Ke.API.addJS=function(t){return Ju=t,this.internal.events.subscribe("postPutResources",function(){ls=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(ls+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),$u=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+Ju+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){ls!==void 0&&$u!==void 0&&this.internal.out("/Names <</JavaScript "+ls+" 0 R>>")}),this},function(t){var e;t.events.push(["postPutResources",function(){var n=this,r=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var o=n.outline.render().split(/\r\n/),i=0;i<o.length;i++){var s=o[i],l=r.exec(s);if(l!=null){var u=l[1];n.internal.newObjectDeferredBegin(u,!1)}n.internal.write(s)}if(this.outline.createNamedDestinations){var p=this.internal.pages.length,g=[];for(i=0;i<p;i++){var b=n.internal.newObject();g.push(b);var v=n.internal.getPageInfo(i+1);n.internal.write("<< /D["+v.objId+" 0 R /XYZ null null null]>> endobj")}var d=n.internal.newObject();for(n.internal.write("<< /Names [ "),i=0;i<g.length;i++)n.internal.write("(page_"+(i+1)+")"+g[i]+" 0 R");n.internal.write(" ] >>","endobj"),e=n.internal.newObject(),n.internal.write("<< /Dests "+d+" 0 R"),n.internal.write(">>","endobj")}}]),t.events.push(["putCatalog",function(){this.outline.root.children.length>0&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+e+" 0 R"))}]),t.events.push(["initialized",function(){var n=this;n.outline={createNamedDestinations:!1,root:{children:[]}},n.outline.add=function(r,o,i){var s={title:o,options:i,children:[]};return r==null&&(r=this.root),r.children.push(s),s},n.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=n,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},n.outline.genIds_r=function(r){r.id=n.internal.newObjectDeferred();for(var o=0;o<r.children.length;o++)this.genIds_r(r.children[o])},n.outline.renderRoot=function(r){this.objStart(r),this.line("/Type /Outlines"),r.children.length>0&&(this.line("/First "+this.makeRef(r.children[0])),this.line("/Last "+this.makeRef(r.children[r.children.length-1]))),this.line("/Count "+this.count_r({count:0},r)),this.objEnd()},n.outline.renderItems=function(r){for(var o=this.ctx.pdf.internal.getVerticalCoordinateString,i=0;i<r.children.length;i++){var s=r.children[i];this.objStart(s),this.line("/Title "+this.makeString(s.title)),this.line("/Parent "+this.makeRef(r)),i>0&&this.line("/Prev "+this.makeRef(r.children[i-1])),i<r.children.length-1&&this.line("/Next "+this.makeRef(r.children[i+1])),s.children.length>0&&(this.line("/First "+this.makeRef(s.children[0])),this.line("/Last "+this.makeRef(s.children[s.children.length-1])));var l=this.count=this.count_r({count:0},s);if(l>0&&this.line("/Count "+l),s.options&&s.options.pageNumber){var u=n.internal.getPageInfo(s.options.pageNumber);this.line("/Dest ["+u.objId+" 0 R /XYZ 0 "+o(0)+" 0]")}this.objEnd()}for(var p=0;p<r.children.length;p++)this.renderItems(r.children[p])},n.outline.line=function(r){this.ctx.val+=r+`\r
`},n.outline.makeRef=function(r){return r.id+" 0 R"},n.outline.makeString=function(r){return"("+n.internal.pdfEscape(r)+")"},n.outline.objStart=function(r){this.ctx.val+=`\r
`+r.id+` 0 obj\r
<<\r
`},n.outline.objEnd=function(){this.ctx.val+=`>> \r
endobj\r
`},n.outline.count_r=function(r,o){for(var i=0;i<o.children.length;i++)r.count++,this.count_r(r,o.children[i]);return r.count}}])}(Ke.API),function(t){var e=[192,193,194,195,196,197,198,199];t.processJPEG=function(n,r,o,i,s,l){var u,p=this.decode.DCT_DECODE,g=null;if(typeof n=="string"||this.__addimage__.isArrayBuffer(n)||this.__addimage__.isArrayBufferView(n)){switch(n=s||n,n=this.__addimage__.isArrayBuffer(n)?new Uint8Array(n):n,(u=function(b){for(var v,d=256*b.charCodeAt(4)+b.charCodeAt(5),_=b.length,S={width:0,height:0,numcomponents:1},T=4;T<_;T+=2){if(T+=d,e.indexOf(b.charCodeAt(T+1))!==-1){v=256*b.charCodeAt(T+5)+b.charCodeAt(T+6),S={width:256*b.charCodeAt(T+7)+b.charCodeAt(T+8),height:v,numcomponents:b.charCodeAt(T+9)};break}d=256*b.charCodeAt(T+2)+b.charCodeAt(T+3)}return S}(n=this.__addimage__.isArrayBufferView(n)?this.__addimage__.arrayBufferToBinaryString(n):n)).numcomponents){case 1:l=this.color_spaces.DEVICE_GRAY;break;case 4:l=this.color_spaces.DEVICE_CMYK;break;case 3:l=this.color_spaces.DEVICE_RGB}g={data:n,width:u.width,height:u.height,colorSpace:l,bitsPerComponent:8,filter:p,index:r,alias:o}}return g}}(Ke.API);var Na,cs,Ku,Xu,Zu,e5=function(){var t,e,n;function r(i){var s,l,u,p,g,b,v,d,_,S,T,A,j,q;for(this.data=i,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},b=null;;){switch(s=this.readUInt32(),_=(function(){var Y,le;for(le=[],Y=0;Y<4;++Y)le.push(String.fromCharCode(this.data[this.pos++]));return le}).call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(s);break;case"fcTL":b&&this.animation.frames.push(b),this.pos+=4,b={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},g=this.readUInt16(),p=this.readUInt16()||100,b.delay=1e3*g/p,b.disposeOp=this.data[this.pos++],b.blendOp=this.data[this.pos++],b.data=[];break;case"IDAT":case"fdAT":for(_==="fdAT"&&(this.pos+=4,s-=4),i=(b!=null?b.data:void 0)||this.imgData,A=0;0<=s?A<s:A>s;0<=s?++A:--A)i.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(u=this.palette.length/3,this.transparency.indexed=this.read(s),this.transparency.indexed.length>u)throw new Error("More transparent colors than palette size");if((S=u-this.transparency.indexed.length)>0)for(j=0;0<=S?j<S:j>S;0<=S?++j:--j)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(s)[0];break;case 2:this.transparency.rgb=this.read(s)}break;case"tEXt":v=(T=this.read(s)).indexOf(0),d=String.fromCharCode.apply(String,T.slice(0,v)),this.text[d]=String.fromCharCode.apply(String,T.slice(v+1));break;case"IEND":return b&&this.animation.frames.push(b),this.colors=(function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}).call(this),this.hasAlphaChannel=(q=this.colorType)===4||q===6,l=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*l,this.colorSpace=(function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}).call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=s}if(this.pos+=4,this.pos>this.data.length)throw new Error("Incomplete or corrupt PNG file")}}r.prototype.read=function(i){var s,l;for(l=[],s=0;0<=i?s<i:s>i;0<=i?++s:--s)l.push(this.data[this.pos++]);return l},r.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},r.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},r.prototype.decodePixels=function(i){var s=this.pixelBitlength/8,l=new Uint8Array(this.width*this.height*s),u=0,p=this;if(i==null&&(i=this.imgData),i.length===0)return new Uint8Array(0);function g(b,v,d,_){var S,T,A,j,q,Y,le,ae,$,F,J,re,N,k,O,C,X,ne,ue,Q,he,V=Math.ceil((p.width-b)/d),me=Math.ceil((p.height-v)/_),x=p.width==V&&p.height==me;for(k=s*V,re=x?l:new Uint8Array(k*me),Y=i.length,N=0,T=0;N<me&&u<Y;){switch(i[u++]){case 0:for(j=X=0;X<k;j=X+=1)re[T++]=i[u++];break;case 1:for(j=ne=0;ne<k;j=ne+=1)S=i[u++],q=j<s?0:re[T-s],re[T++]=(S+q)%256;break;case 2:for(j=ue=0;ue<k;j=ue+=1)S=i[u++],A=(j-j%s)/s,O=N&&re[(N-1)*k+A*s+j%s],re[T++]=(O+S)%256;break;case 3:for(j=Q=0;Q<k;j=Q+=1)S=i[u++],A=(j-j%s)/s,q=j<s?0:re[T-s],O=N&&re[(N-1)*k+A*s+j%s],re[T++]=(S+Math.floor((q+O)/2))%256;break;case 4:for(j=he=0;he<k;j=he+=1)S=i[u++],A=(j-j%s)/s,q=j<s?0:re[T-s],N===0?O=C=0:(O=re[(N-1)*k+A*s+j%s],C=A&&re[(N-1)*k+(A-1)*s+j%s]),le=q+O-C,ae=Math.abs(le-q),F=Math.abs(le-O),J=Math.abs(le-C),$=ae<=F&&ae<=J?q:F<=J?O:C,re[T++]=(S+$)%256;break;default:throw new Error("Invalid filter algorithm: "+i[u-1])}if(!x){var B=((v+N*_)*p.width+b)*s,M=N*k;for(j=0;j<V;j+=1){for(var z=0;z<s;z+=1)l[B++]=re[M++];B+=(d-1)*s}}N++}}return i=T2(i),p.interlaceMethod==1?(g(0,0,8,8),g(4,0,8,8),g(0,4,4,8),g(2,0,4,4),g(0,2,2,4),g(1,0,2,2),g(0,1,1,2)):g(0,0,1,1),l},r.prototype.decodePalette=function(){var i,s,l,u,p,g,b,v,d;for(l=this.palette,g=this.transparency.indexed||[],p=new Uint8Array((g.length||0)+l.length),u=0,i=0,s=b=0,v=l.length;b<v;s=b+=3)p[u++]=l[s],p[u++]=l[s+1],p[u++]=l[s+2],p[u++]=(d=g[i++])!=null?d:255;return p},r.prototype.copyToImageData=function(i,s){var l,u,p,g,b,v,d,_,S,T,A;if(u=this.colors,S=null,l=this.hasAlphaChannel,this.palette.length&&(S=(A=this._decodedPalette)!=null?A:this._decodedPalette=this.decodePalette(),u=4,l=!0),_=(p=i.data||i).length,b=S||s,g=v=0,u===1)for(;g<_;)d=S?4*s[g/4]:v,T=b[d++],p[g++]=T,p[g++]=T,p[g++]=T,p[g++]=l?b[d++]:255,v=d;else for(;g<_;)d=S?4*s[g/4]:v,p[g++]=b[d++],p[g++]=b[d++],p[g++]=b[d++],p[g++]=l?b[d++]:255,v=d},r.prototype.decode=function(){var i;return i=new Uint8Array(this.width*this.height*4),this.copyToImageData(i,this.decodePixels()),i};var o=function(){if(Object.prototype.toString.call(Xe)==="[object Window]"){try{e=Xe.document.createElement("canvas"),n=e.getContext("2d")}catch{return!1}return!0}return!1};return o(),t=function(i){var s;if(o()===!0)return n.width=i.width,n.height=i.height,n.clearRect(0,0,i.width,i.height),n.putImageData(i,0,0),(s=new Image).src=e.toDataURL(),s;throw new Error("This method requires a Browser with Canvas-capability.")},r.prototype.decodeFrames=function(i){var s,l,u,p,g,b,v,d;if(this.animation){for(d=[],l=g=0,b=(v=this.animation.frames).length;g<b;l=++g)s=v[l],u=i.createImageData(s.width,s.height),p=this.decodePixels(new Uint8Array(s.data)),this.copyToImageData(u,p),s.imageData=u,d.push(s.image=t(u));return d}},r.prototype.renderFrame=function(i,s){var l,u,p;return l=(u=this.animation.frames)[s],p=u[s-1],s===0&&i.clearRect(0,0,this.width,this.height),(p!=null?p.disposeOp:void 0)===1?i.clearRect(p.xOffset,p.yOffset,p.width,p.height):(p!=null?p.disposeOp:void 0)===2&&i.putImageData(p.imageData,p.xOffset,p.yOffset),l.blendOp===0&&i.clearRect(l.xOffset,l.yOffset,l.width,l.height),i.drawImage(l.image,l.xOffset,l.yOffset)},r.prototype.animate=function(i){var s,l,u,p,g,b,v=this;return l=0,b=this.animation,p=b.numFrames,u=b.frames,g=b.numPlays,(s=function(){var d,_;if(d=l++%p,_=u[d],v.renderFrame(i,d),p>1&&l/p<g)return v.animation._timeout=setTimeout(s,_.delay)})()},r.prototype.stopAnimation=function(){var i;return clearTimeout((i=this.animation)!=null?i._timeout:void 0)},r.prototype.render=function(i){var s,l;return i._png&&i._png.stopAnimation(),i._png=this,i.width=this.width,i.height=this.height,s=i.getContext("2d"),this.animation?(this.decodeFrames(s),this.animate(s)):(l=s.createImageData(this.width,this.height),this.copyToImageData(l,this.decodePixels()),s.putImageData(l,0,0))},r}();/**
 * @license
 *
 * Copyright (c) 2014 James Robb, https://github.com/jamesbrobb
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 *//**
 * @license
 * (c) Dean McNamee <<EMAIL>>, 2013.
 *
 * https://github.com/deanm/omggif
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 *
 * omggif is a JavaScript implementation of a GIF 89a encoder and decoder,
 * including animation and compression.  It does not rely on any specific
 * underlying system, so should run in the browser, Node, or Plask.
 */function t5(t){var e=0;if(t[e++]!==71||t[e++]!==73||t[e++]!==70||t[e++]!==56||(t[e++]+1&253)!=56||t[e++]!==97)throw new Error("Invalid GIF 87a/89a header.");var n=t[e++]|t[e++]<<8,r=t[e++]|t[e++]<<8,o=t[e++],i=o>>7,s=1<<(7&o)+1;t[e++],t[e++];var l=null,u=null;i&&(l=e,u=s,e+=3*s);var p=!0,g=[],b=0,v=null,d=0,_=null;for(this.width=n,this.height=r;p&&e<t.length;)switch(t[e++]){case 33:switch(t[e++]){case 255:if(t[e]!==11||t[e+1]==78&&t[e+2]==69&&t[e+3]==84&&t[e+4]==83&&t[e+5]==67&&t[e+6]==65&&t[e+7]==80&&t[e+8]==69&&t[e+9]==50&&t[e+10]==46&&t[e+11]==48&&t[e+12]==3&&t[e+13]==1&&t[e+16]==0)e+=14,_=t[e++]|t[e++]<<8,e++;else for(e+=12;;){if(!((N=t[e++])>=0))throw Error("Invalid block size");if(N===0)break;e+=N}break;case 249:if(t[e++]!==4||t[e+4]!==0)throw new Error("Invalid graphics extension block.");var S=t[e++];b=t[e++]|t[e++]<<8,v=t[e++],(1&S)==0&&(v=null),d=S>>2&7,e++;break;case 254:for(;;){if(!((N=t[e++])>=0))throw Error("Invalid block size");if(N===0)break;e+=N}break;default:throw new Error("Unknown graphic control label: 0x"+t[e-1].toString(16))}break;case 44:var T=t[e++]|t[e++]<<8,A=t[e++]|t[e++]<<8,j=t[e++]|t[e++]<<8,q=t[e++]|t[e++]<<8,Y=t[e++],le=Y>>6&1,ae=1<<(7&Y)+1,$=l,F=u,J=!1;Y>>7&&(J=!0,$=e,F=ae,e+=3*ae);var re=e;for(e++;;){var N;if(!((N=t[e++])>=0))throw Error("Invalid block size");if(N===0)break;e+=N}g.push({x:T,y:A,width:j,height:q,has_local_palette:J,palette_offset:$,palette_size:F,data_offset:re,data_length:e-re,transparent_index:v,interlaced:!!le,delay:b,disposal:d});break;case 59:p=!1;break;default:throw new Error("Unknown gif block: 0x"+t[e-1].toString(16))}this.numFrames=function(){return g.length},this.loopCount=function(){return _},this.frameInfo=function(k){if(k<0||k>=g.length)throw new Error("Frame index out of range.");return g[k]},this.decodeAndBlitFrameBGRA=function(k,O){var C=this.frameInfo(k),X=C.width*C.height,ne=new Uint8Array(X);Qu(t,C.data_offset,ne,X);var ue=C.palette_offset,Q=C.transparent_index;Q===null&&(Q=256);var he=C.width,V=n-he,me=he,x=4*(C.y*n+C.x),B=4*((C.y+C.height)*n+C.x),M=x,z=4*V;C.interlaced===!0&&(z+=4*n*7);for(var H=8,G=0,ee=ne.length;G<ee;++G){var oe=ne[G];if(me===0&&(me=he,(M+=z)>=B&&(z=4*V+4*n*(H-1),M=x+(he+V)*(H<<1),H>>=1)),oe===Q)M+=4;else{var ve=t[ue+3*oe],Se=t[ue+3*oe+1],be=t[ue+3*oe+2];O[M++]=be,O[M++]=Se,O[M++]=ve,O[M++]=255}--me}},this.decodeAndBlitFrameRGBA=function(k,O){var C=this.frameInfo(k),X=C.width*C.height,ne=new Uint8Array(X);Qu(t,C.data_offset,ne,X);var ue=C.palette_offset,Q=C.transparent_index;Q===null&&(Q=256);var he=C.width,V=n-he,me=he,x=4*(C.y*n+C.x),B=4*((C.y+C.height)*n+C.x),M=x,z=4*V;C.interlaced===!0&&(z+=4*n*7);for(var H=8,G=0,ee=ne.length;G<ee;++G){var oe=ne[G];if(me===0&&(me=he,(M+=z)>=B&&(z=4*V+4*n*(H-1),M=x+(he+V)*(H<<1),H>>=1)),oe===Q)M+=4;else{var ve=t[ue+3*oe],Se=t[ue+3*oe+1],be=t[ue+3*oe+2];O[M++]=ve,O[M++]=Se,O[M++]=be,O[M++]=255}--me}}}function Qu(t,e,n,r){for(var o=t[e++],i=1<<o,s=i+1,l=s+1,u=o+1,p=(1<<u)-1,g=0,b=0,v=0,d=t[e++],_=new Int32Array(4096),S=null;;){for(;g<16&&d!==0;)b|=t[e++]<<g,g+=8,d===1?d=t[e++]:--d;if(g<u)break;var T=b&p;if(b>>=u,g-=u,T!==i){if(T===s)break;for(var A=T<l?T:S,j=0,q=A;q>i;)q=_[q]>>8,++j;var Y=q;if(v+j+(A!==T?1:0)>r)return void kt.log("Warning, gif stream longer than expected.");n[v++]=Y;var le=v+=j;for(A!==T&&(n[v++]=Y),q=A;j--;)q=_[q],n[--le]=255&q,q>>=8;S!==null&&l<4096&&(_[l++]=S<<8|Y,l>=p+1&&u<12&&(++u,p=p<<1|1)),S=T}else l=s+1,p=(1<<(u=o+1))-1,S=null}return v!==r&&kt.log("Warning, gif stream shorter than expected."),n}/**
 * @license
  Copyright (c) 2008, Adobe Systems Incorporated
  All rights reserved.

  Redistribution and use in source and binary forms, with or without 
  modification, are permitted provided that the following conditions are
  met:

  * Redistributions of source code must retain the above copyright notice, 
    this list of conditions and the following disclaimer.
  
  * Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the 
    documentation and/or other materials provided with the distribution.
  
  * Neither the name of Adobe Systems Incorporated nor the names of its 
    contributors may be used to endorse or promote products derived from 
    this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
  IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/function kl(t){var e,n,r,o,i,s=Math.floor,l=new Array(64),u=new Array(64),p=new Array(64),g=new Array(64),b=new Array(65535),v=new Array(65535),d=new Array(64),_=new Array(64),S=[],T=0,A=7,j=new Array(64),q=new Array(64),Y=new Array(64),le=new Array(256),ae=new Array(2048),$=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],F=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],J=[0,1,2,3,4,5,6,7,8,9,10,11],re=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],N=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],k=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],O=[0,1,2,3,4,5,6,7,8,9,10,11],C=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],X=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function ne(x,B){for(var M=0,z=0,H=new Array,G=1;G<=16;G++){for(var ee=1;ee<=x[G];ee++)H[B[z]]=[],H[B[z]][0]=M,H[B[z]][1]=G,z++,M++;M*=2}return H}function ue(x){for(var B=x[0],M=x[1]-1;M>=0;)B&1<<M&&(T|=1<<A),M--,--A<0&&(T==255?(Q(255),Q(0)):Q(T),A=7,T=0)}function Q(x){S.push(x)}function he(x){Q(x>>8&255),Q(255&x)}function V(x,B,M,z,H){for(var G,ee=H[0],oe=H[240],ve=function(ke,Ie){var Fe,Te,He,Qe,et,rt,dt,yt,$e,st,je=0;for($e=0;$e<8;++$e){Fe=ke[je],Te=ke[je+1],He=ke[je+2],Qe=ke[je+3],et=ke[je+4],rt=ke[je+5],dt=ke[je+6];var rn=Fe+(yt=ke[je+7]),ht=Fe-yt,Rn=Te+dt,At=Te-dt,Ct=He+rt,$n=He-rt,vt=Qe+et,Br=Qe-et,Bt=rn+vt,Fn=rn-vt,sr=Rn+Ct,Mt=Rn-Ct;ke[je]=Bt+sr,ke[je+4]=Bt-sr;var tt=.707106781*(Mt+Fn);ke[je+2]=Fn+tt,ke[je+6]=Fn-tt;var gt=.382683433*((Bt=Br+$n)-(Mt=At+ht)),Mr=.5411961*Bt+gt,Ht=1.306562965*Mt+gt,Jn=.707106781*(sr=$n+At),Kn=ht+Jn,Ve=ht-Jn;ke[je+5]=Ve+Mr,ke[je+3]=Ve-Mr,ke[je+1]=Kn+Ht,ke[je+7]=Kn-Ht,je+=8}for(je=0,$e=0;$e<8;++$e){Fe=ke[je],Te=ke[je+8],He=ke[je+16],Qe=ke[je+24],et=ke[je+32],rt=ke[je+40],dt=ke[je+48];var On=Fe+(yt=ke[je+56]),Xn=Fe-yt,dn=Te+dt,Wt=Te-dt,Dt=He+rt,xn=He-rt,Rr=Qe+et,lr=Qe-et,Dn=On+Rr,qn=On-Rr,Un=dn+Dt,zn=dn-Dt;ke[je]=Dn+Un,ke[je+32]=Dn-Un;var Pn=.707106781*(zn+qn);ke[je+16]=qn+Pn,ke[je+48]=qn-Pn;var Zn=.382683433*((Dn=lr+xn)-(zn=Wt+Xn)),Fr=.5411961*Dn+Zn,Qr=1.306562965*zn+Zn,ei=.707106781*(Un=xn+Wt),ti=Xn+ei,Me=Xn-ei;ke[je+40]=Me+Fr,ke[je+24]=Me-Fr,ke[je+8]=ti+Qr,ke[je+56]=ti-Qr,je++}for($e=0;$e<64;++$e)st=ke[$e]*Ie[$e],d[$e]=st>0?st+.5|0:st-.5|0;return d}(x,B),Se=0;Se<64;++Se)_[$[Se]]=ve[Se];var be=_[0]-M;M=_[0],be==0?ue(z[0]):(ue(z[v[G=32767+be]]),ue(b[G]));for(var Ce=63;Ce>0&&_[Ce]==0;)Ce--;if(Ce==0)return ue(ee),M;for(var ze,ye=1;ye<=Ce;){for(var W=ye;_[ye]==0&&ye<=Ce;)++ye;var Ze=ye-W;if(Ze>=16){ze=Ze>>4;for(var qe=1;qe<=ze;++qe)ue(oe);Ze&=15}G=32767+_[ye],ue(H[(Ze<<4)+v[G]]),ue(b[G]),ye++}return Ce!=63&&ue(ee),M}function me(x){x=Math.min(Math.max(x,1),100),i!=x&&(function(B){for(var M=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],z=0;z<64;z++){var H=s((M[z]*B+50)/100);H=Math.min(Math.max(H,1),255),l[$[z]]=H}for(var G=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],ee=0;ee<64;ee++){var oe=s((G[ee]*B+50)/100);oe=Math.min(Math.max(oe,1),255),u[$[ee]]=oe}for(var ve=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],Se=0,be=0;be<8;be++)for(var Ce=0;Ce<8;Ce++)p[Se]=1/(l[$[Se]]*ve[be]*ve[Ce]*8),g[Se]=1/(u[$[Se]]*ve[be]*ve[Ce]*8),Se++}(x<50?Math.floor(5e3/x):Math.floor(200-2*x)),i=x)}this.encode=function(x,B){B&&me(B),S=new Array,T=0,A=7,he(65496),he(65504),he(16),Q(74),Q(70),Q(73),Q(70),Q(0),Q(1),Q(1),Q(0),he(1),he(1),Q(0),Q(0),function(){he(65499),he(132),Q(0);for(var Te=0;Te<64;Te++)Q(l[Te]);Q(1);for(var He=0;He<64;He++)Q(u[He])}(),function(Te,He){he(65472),he(17),Q(8),he(He),he(Te),Q(3),Q(1),Q(17),Q(0),Q(2),Q(17),Q(1),Q(3),Q(17),Q(1)}(x.width,x.height),function(){he(65476),he(418),Q(0);for(var Te=0;Te<16;Te++)Q(F[Te+1]);for(var He=0;He<=11;He++)Q(J[He]);Q(16);for(var Qe=0;Qe<16;Qe++)Q(re[Qe+1]);for(var et=0;et<=161;et++)Q(N[et]);Q(1);for(var rt=0;rt<16;rt++)Q(k[rt+1]);for(var dt=0;dt<=11;dt++)Q(O[dt]);Q(17);for(var yt=0;yt<16;yt++)Q(C[yt+1]);for(var $e=0;$e<=161;$e++)Q(X[$e])}(),he(65498),he(12),Q(3),Q(1),Q(0),Q(2),Q(17),Q(3),Q(17),Q(0),Q(63),Q(0);var M=0,z=0,H=0;T=0,A=7,this.encode.displayName="_encode_";for(var G,ee,oe,ve,Se,be,Ce,ze,ye,W=x.data,Ze=x.width,qe=x.height,ke=4*Ze,Ie=0;Ie<qe;){for(G=0;G<ke;){for(Se=ke*Ie+G,Ce=-1,ze=0,ye=0;ye<64;ye++)be=Se+(ze=ye>>3)*ke+(Ce=4*(7&ye)),Ie+ze>=qe&&(be-=ke*(Ie+1+ze-qe)),G+Ce>=ke&&(be-=G+Ce-ke+4),ee=W[be++],oe=W[be++],ve=W[be++],j[ye]=(ae[ee]+ae[oe+256>>0]+ae[ve+512>>0]>>16)-128,q[ye]=(ae[ee+768>>0]+ae[oe+1024>>0]+ae[ve+1280>>0]>>16)-128,Y[ye]=(ae[ee+1280>>0]+ae[oe+1536>>0]+ae[ve+1792>>0]>>16)-128;M=V(j,p,M,e,r),z=V(q,g,z,n,o),H=V(Y,g,H,n,o),G+=32}Ie+=8}if(A>=0){var Fe=[];Fe[1]=A+1,Fe[0]=(1<<A+1)-1,ue(Fe)}return he(65497),new Uint8Array(S)},t=t||50,function(){for(var x=String.fromCharCode,B=0;B<256;B++)le[B]=x(B)}(),e=ne(F,J),n=ne(k,O),r=ne(re,N),o=ne(C,X),function(){for(var x=1,B=2,M=1;M<=15;M++){for(var z=x;z<B;z++)v[32767+z]=M,b[32767+z]=[],b[32767+z][1]=M,b[32767+z][0]=z;for(var H=-(B-1);H<=-x;H++)v[32767+H]=M,b[32767+H]=[],b[32767+H][1]=M,b[32767+H][0]=B-1+H;x<<=1,B<<=1}}(),function(){for(var x=0;x<256;x++)ae[x]=19595*x,ae[x+256>>0]=38470*x,ae[x+512>>0]=7471*x+32768,ae[x+768>>0]=-11059*x,ae[x+1024>>0]=-21709*x,ae[x+1280>>0]=32768*x+8421375,ae[x+1536>>0]=-27439*x,ae[x+1792>>0]=-5329*x}(),me(t)}/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */function gr(t,e){if(this.pos=0,this.buffer=t,this.datav=new DataView(t.buffer),this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,["BM","BA","CI","CP","IC","PT"].indexOf(this.flag)===-1)throw new Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function ef(t){function e(F){if(!F)throw Error("assert :P")}function n(F,J,re){for(var N=0;4>N;N++)if(F[J+N]!=re.charCodeAt(N))return!0;return!1}function r(F,J,re,N,k){for(var O=0;O<k;O++)F[J+O]=re[N+O]}function o(F,J,re,N){for(var k=0;k<N;k++)F[J+k]=re}function i(F){return new Int32Array(F)}function s(F,J){for(var re=[],N=0;N<F;N++)re.push(new J);return re}function l(F,J){var re=[];return function N(k,O,C){for(var X=C[O],ne=0;ne<X&&(k.push(C.length>O+1?[]:new J),!(C.length<O+1));ne++)N(k[ne],O+1,C)}(re,0,F),re}var u=function(){var F=this;function J(a,c){for(var h=1<<c-1>>>0;a&h;)h>>>=1;return h?(a&h-1)+h:a}function re(a,c,h,m,y){e(!(m%h));do a[c+(m-=h)]=y;while(0<m)}function N(a,c,h,m,y){if(e(2328>=y),512>=y)var L=i(512);else if((L=i(y))==null)return 0;return function(P,E,I,D,Z,pe){var ge,se,Le=E,we=1<<I,te=i(16),ie=i(16);for(e(Z!=0),e(D!=null),e(P!=null),e(0<I),se=0;se<Z;++se){if(15<D[se])return 0;++te[D[se]]}if(te[0]==Z)return 0;for(ie[1]=0,ge=1;15>ge;++ge){if(te[ge]>1<<ge)return 0;ie[ge+1]=ie[ge]+te[ge]}for(se=0;se<Z;++se)ge=D[se],0<D[se]&&(pe[ie[ge]++]=se);if(ie[15]==1)return(D=new k).g=0,D.value=pe[0],re(P,Le,1,we,D),we;var Ae,Pe=-1,Ne=we-1,De=0,Be=1,Ge=1,Re=1<<I;for(se=0,ge=1,Z=2;ge<=I;++ge,Z<<=1){if(Be+=Ge<<=1,0>(Ge-=te[ge]))return 0;for(;0<te[ge];--te[ge])(D=new k).g=ge,D.value=pe[se++],re(P,Le+De,Z,Re,D),De=J(De,ge)}for(ge=I+1,Z=2;15>=ge;++ge,Z<<=1){if(Be+=Ge<<=1,0>(Ge-=te[ge]))return 0;for(;0<te[ge];--te[ge]){if(D=new k,(De&Ne)!=Pe){for(Le+=Re,Ae=1<<(Pe=ge)-I;15>Pe&&!(0>=(Ae-=te[Pe]));)++Pe,Ae<<=1;we+=Re=1<<(Ae=Pe-I),P[E+(Pe=De&Ne)].g=Ae+I,P[E+Pe].value=Le-E-Pe}D.g=ge-I,D.value=pe[se++],re(P,Le+(De>>I),Z,Re,D),De=J(De,ge)}}return Be!=2*ie[15]-1?0:we}(a,c,h,m,y,L)}function k(){this.value=this.g=0}function O(){this.value=this.g=0}function C(){this.G=s(5,k),this.H=i(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=s(un,O)}function X(a,c,h,m){e(a!=null),e(c!=null),e(2147483648>m),a.Ca=254,a.I=0,a.b=-8,a.Ka=0,a.oa=c,a.pa=h,a.Jd=c,a.Yc=h+m,a.Zc=4<=m?h+m-4+1:h,G(a)}function ne(a,c){for(var h=0;0<c--;)h|=oe(a,128)<<c;return h}function ue(a,c){var h=ne(a,c);return ee(a)?-h:h}function Q(a,c,h,m){var y,L=0;for(e(a!=null),e(c!=null),e(4294967288>m),a.Sb=m,a.Ra=0,a.u=0,a.h=0,4<m&&(m=4),y=0;y<m;++y)L+=c[h+y]<<8*y;a.Ra=L,a.bb=m,a.oa=c,a.pa=h}function he(a){for(;8<=a.u&&a.bb<a.Sb;)a.Ra>>>=8,a.Ra+=a.oa[a.pa+a.bb]<<ha-8>>>0,++a.bb,a.u-=8;M(a)&&(a.h=1,a.u=0)}function V(a,c){if(e(0<=c),!a.h&&c<=fa){var h=B(a)&ua[c];return a.u+=c,he(a),h}return a.h=1,a.u=0}function me(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function x(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function B(a){return a.Ra>>>(a.u&ha-1)>>>0}function M(a){return e(a.bb<=a.Sb),a.h||a.bb==a.Sb&&a.u>ha}function z(a,c){a.u=c,a.h=M(a)}function H(a){a.u>=Xa&&(e(a.u>=Xa),he(a))}function G(a){e(a!=null&&a.oa!=null),a.pa<a.Zc?(a.I=(a.oa[a.pa++]|a.I<<8)>>>0,a.b+=8):(e(a!=null&&a.oa!=null),a.pa<a.Yc?(a.b+=8,a.I=a.oa[a.pa++]|a.I<<8):a.Ka?a.b=0:(a.I<<=8,a.b+=8,a.Ka=1))}function ee(a){return ne(a,1)}function oe(a,c){var h=a.Ca;0>a.b&&G(a);var m=a.b,y=h*c>>>8,L=(a.I>>>m>y)+0;for(L?(h-=y,a.I-=y+1<<m>>>0):h=y+1,m=h,y=0;256<=m;)y+=8,m>>=8;return m=7^y+En[m],a.b-=m,a.Ca=(h<<m)-1,L}function ve(a,c,h){a[c+0]=h>>24&255,a[c+1]=h>>16&255,a[c+2]=h>>8&255,a[c+3]=h>>0&255}function Se(a,c){return a[c+0]<<0|a[c+1]<<8}function be(a,c){return Se(a,c)|a[c+2]<<16}function Ce(a,c){return Se(a,c)|Se(a,c+2)<<16}function ze(a,c){var h=1<<c;return e(a!=null),e(0<c),a.X=i(h),a.X==null?0:(a.Mb=32-c,a.Xa=c,1)}function ye(a,c){e(a!=null),e(c!=null),e(a.Xa==c.Xa),r(c.X,0,a.X,0,1<<c.Xa)}function W(){this.X=[],this.Xa=this.Mb=0}function Ze(a,c,h,m){e(h!=null),e(m!=null);var y=h[0],L=m[0];return y==0&&(y=(a*L+c/2)/c),L==0&&(L=(c*y+a/2)/a),0>=y||0>=L?0:(h[0]=y,m[0]=L,1)}function qe(a,c){return a+(1<<c)-1>>>c}function ke(a,c){return((4278255360&a)+(4278255360&c)>>>0&4278255360)+((16711935&a)+(16711935&c)>>>0&16711935)>>>0}function Ie(a,c){F[c]=function(h,m,y,L,P,E,I){var D;for(D=0;D<P;++D){var Z=F[a](E[I+D-1],y,L+D);E[I+D]=ke(h[m+D],Z)}}}function Fe(){this.ud=this.hd=this.jd=0}function Te(a,c){return((4278124286&(a^c))>>>1)+(a&c)>>>0}function He(a){return 0<=a&&256>a?a:0>a?0:255<a?255:void 0}function Qe(a,c){return He(a+(a-c+.5>>1))}function et(a,c,h){return Math.abs(c-h)-Math.abs(a-h)}function rt(a,c,h,m,y,L,P){for(m=L[P-1],h=0;h<y;++h)L[P+h]=m=ke(a[c+h],m)}function dt(a,c,h,m,y){var L;for(L=0;L<h;++L){var P=a[c+L],E=P>>8&255,I=16711935&(I=(I=16711935&P)+((E<<16)+E));m[y+L]=(4278255360&P)+I>>>0}}function yt(a,c){c.jd=a>>0&255,c.hd=a>>8&255,c.ud=a>>16&255}function $e(a,c,h,m,y,L){var P;for(P=0;P<m;++P){var E=c[h+P],I=E>>>8,D=E,Z=255&(Z=(Z=E>>>16)+((a.jd<<24>>24)*(I<<24>>24)>>>5));D=255&(D=(D=D+((a.hd<<24>>24)*(I<<24>>24)>>>5))+((a.ud<<24>>24)*(Z<<24>>24)>>>5)),y[L+P]=(4278255360&E)+(Z<<16)+D}}function st(a,c,h,m,y){F[c]=function(L,P,E,I,D,Z,pe,ge,se){for(I=pe;I<ge;++I)for(pe=0;pe<se;++pe)D[Z++]=y(E[m(L[P++])])},F[a]=function(L,P,E,I,D,Z,pe){var ge=8>>L.b,se=L.Ea,Le=L.K[0],we=L.w;if(8>ge)for(L=(1<<L.b)-1,we=(1<<ge)-1;P<E;++P){var te,ie=0;for(te=0;te<se;++te)te&L||(ie=m(I[D++])),Z[pe++]=y(Le[ie&we]),ie>>=ge}else F["VP8LMapColor"+h](I,D,Le,we,Z,pe,P,E,se)}}function je(a,c,h,m,y){for(h=c+h;c<h;){var L=a[c++];m[y++]=L>>16&255,m[y++]=L>>8&255,m[y++]=L>>0&255}}function rn(a,c,h,m,y){for(h=c+h;c<h;){var L=a[c++];m[y++]=L>>16&255,m[y++]=L>>8&255,m[y++]=L>>0&255,m[y++]=L>>24&255}}function ht(a,c,h,m,y){for(h=c+h;c<h;){var L=(P=a[c++])>>16&240|P>>12&15,P=P>>0&240|P>>28&15;m[y++]=L,m[y++]=P}}function Rn(a,c,h,m,y){for(h=c+h;c<h;){var L=(P=a[c++])>>16&248|P>>13&7,P=P>>5&224|P>>3&31;m[y++]=L,m[y++]=P}}function At(a,c,h,m,y){for(h=c+h;c<h;){var L=a[c++];m[y++]=L>>0&255,m[y++]=L>>8&255,m[y++]=L>>16&255}}function Ct(a,c,h,m,y,L){if(L==0)for(h=c+h;c<h;)ve(m,((L=a[c++])[0]>>24|L[1]>>8&65280|L[2]<<8&16711680|L[3]<<24)>>>0),y+=32;else r(m,y,a,c,h)}function $n(a,c){F[c][0]=F[a+"0"],F[c][1]=F[a+"1"],F[c][2]=F[a+"2"],F[c][3]=F[a+"3"],F[c][4]=F[a+"4"],F[c][5]=F[a+"5"],F[c][6]=F[a+"6"],F[c][7]=F[a+"7"],F[c][8]=F[a+"8"],F[c][9]=F[a+"9"],F[c][10]=F[a+"10"],F[c][11]=F[a+"11"],F[c][12]=F[a+"12"],F[c][13]=F[a+"13"],F[c][14]=F[a+"0"],F[c][15]=F[a+"0"]}function vt(a){return a==Ys||a==$s||a==Vo||a==Js}function Br(){this.eb=[],this.size=this.A=this.fb=0}function Bt(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function Fn(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new Br,this.f.kb=new Bt,this.sd=null}function sr(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function Mt(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function tt(a){return alert("todo:WebPSamplerProcessPlane"),a.T}function gt(a,c){var h=a.T,m=c.ba.f.RGBA,y=m.eb,L=m.fb+a.ka*m.A,P=ir[c.ba.S],E=a.y,I=a.O,D=a.f,Z=a.N,pe=a.ea,ge=a.W,se=c.cc,Le=c.dc,we=c.Mc,te=c.Nc,ie=a.ka,Ae=a.ka+a.T,Pe=a.U,Ne=Pe+1>>1;for(ie==0?P(E,I,null,null,D,Z,pe,ge,D,Z,pe,ge,y,L,null,null,Pe):(P(c.ec,c.fc,E,I,se,Le,we,te,D,Z,pe,ge,y,L-m.A,y,L,Pe),++h);ie+2<Ae;ie+=2)se=D,Le=Z,we=pe,te=ge,Z+=a.Rc,ge+=a.Rc,L+=2*m.A,P(E,(I+=2*a.fa)-a.fa,E,I,se,Le,we,te,D,Z,pe,ge,y,L-m.A,y,L,Pe);return I+=a.fa,a.j+Ae<a.o?(r(c.ec,c.fc,E,I,Pe),r(c.cc,c.dc,D,Z,Ne),r(c.Mc,c.Nc,pe,ge,Ne),h--):1&Ae||P(E,I,null,null,D,Z,pe,ge,D,Z,pe,ge,y,L+m.A,null,null,Pe),h}function Mr(a,c,h){var m=a.F,y=[a.J];if(m!=null){var L=a.U,P=c.ba.S,E=P==Wo||P==Vo;c=c.ba.f.RGBA;var I=[0],D=a.ka;I[0]=a.T,a.Kb&&(D==0?--I[0]:(--D,y[0]-=a.width),a.j+a.ka+a.T==a.o&&(I[0]=a.o-a.j-D));var Z=c.eb;D=c.fb+D*c.A,a=It(m,y[0],a.width,L,I,Z,D+(E?0:3),c.A),e(h==I),a&&vt(P)&&nr(Z,D,E,L,I,c.A)}return 0}function Ht(a){var c=a.ma,h=c.ba.S,m=11>h,y=h==zo||h==Ho||h==Wo||h==Gs||h==12||vt(h);if(c.memory=null,c.Ib=null,c.Jb=null,c.Nd=null,!Ja(c.Oa,a,y?11:12))return 0;if(y&&vt(h)&&_e(),a.da)alert("todo:use_scaling");else{if(m){if(c.Ib=tt,a.Kb){if(h=a.U+1>>1,c.memory=i(a.U+2*h),c.memory==null)return 0;c.ec=c.memory,c.fc=0,c.cc=c.ec,c.dc=c.fc+a.U,c.Mc=c.cc,c.Nc=c.dc+h,c.Ib=gt,_e()}}else alert("todo:EmitYUV");y&&(c.Jb=Mr,m&&fe())}if(m&&!Hc){for(a=0;256>a;++a)Jh[a]=89858*(a-128)+Yo>>Go,Zh[a]=-22014*(a-128)+Yo,Xh[a]=-45773*(a-128),Kh[a]=113618*(a-128)+Yo>>Go;for(a=io;a<Zs;++a)c=76283*(a-16)+Yo>>Go,Qh[a-io]=Hn(c,255),ed[a-io]=Hn(c+8>>4,15);Hc=1}return 1}function Jn(a){var c=a.ma,h=a.U,m=a.T;return e(!(1&a.ka)),0>=h||0>=m?0:(h=c.Ib(a,c),c.Jb!=null&&c.Jb(a,c,h),c.Dc+=h,1)}function Kn(a){a.ma.memory=null}function Ve(a,c,h,m){return V(a,8)!=47?0:(c[0]=V(a,14)+1,h[0]=V(a,14)+1,m[0]=V(a,1),V(a,3)!=0?0:!a.h)}function On(a,c){if(4>a)return a+1;var h=a-2>>1;return(2+(1&a)<<h)+V(c,h)+1}function Xn(a,c){return 120<c?c-120:1<=(h=((h=Mh[c-1])>>4)*a+(8-(15&h)))?h:1;var h}function dn(a,c,h){var m=B(h),y=a[c+=255&m].g-8;return 0<y&&(z(h,h.u+8),m=B(h),c+=a[c].value,c+=m&(1<<y)-1),z(h,h.u+a[c].g),a[c].value}function Wt(a,c,h){return h.g+=a.g,h.value+=a.value<<c>>>0,e(8>=h.g),a.g}function Dt(a,c,h){var m=a.xc;return e((c=m==0?0:a.vc[a.md*(h>>m)+(c>>m)])<a.Wb),a.Ya[c]}function xn(a,c,h,m){var y=a.ab,L=a.c*c,P=a.C;c=P+c;var E=h,I=m;for(m=a.Ta,h=a.Ua;0<y--;){var D=a.gc[y],Z=P,pe=c,ge=E,se=I,Le=(I=m,E=h,D.Ea);switch(e(Z<pe),e(pe<=D.nc),D.hc){case 2:Mo(ge,se,(pe-Z)*Le,I,E);break;case 0:var we=Z,te=pe,ie=I,Ae=E,Pe=(Re=D).Ea;we==0&&(Ws(ge,se,null,null,1,ie,Ae),rt(ge,se+1,0,0,Pe-1,ie,Ae+1),se+=Pe,Ae+=Pe,++we);for(var Ne=1<<Re.b,De=Ne-1,Be=qe(Pe,Re.b),Ge=Re.K,Re=Re.w+(we>>Re.b)*Be;we<te;){var wt=Ge,St=Re,bt=1;for(Za(ge,se,ie,Ae-Pe,1,ie,Ae);bt<Pe;){var pt=(bt&~De)+Ne;pt>Pe&&(pt=Pe),(0,ci[wt[St++]>>8&15])(ge,se+ +bt,ie,Ae+bt-Pe,pt-bt,ie,Ae+bt),bt=pt}se+=Pe,Ae+=Pe,++we&De||(Re+=Be)}pe!=D.nc&&r(I,E-Le,I,E+(pe-Z-1)*Le,Le);break;case 1:for(Le=ge,te=se,Pe=(ge=D.Ea)-(Ae=ge&~(ie=(se=1<<D.b)-1)),we=qe(ge,D.b),Ne=D.K,D=D.w+(Z>>D.b)*we;Z<pe;){for(De=Ne,Be=D,Ge=new Fe,Re=te+Ae,wt=te+ge;te<Re;)yt(De[Be++],Ge),Fi(Ge,Le,te,se,I,E),te+=se,E+=se;te<wt&&(yt(De[Be++],Ge),Fi(Ge,Le,te,Pe,I,E),te+=Pe,E+=Pe),++Z&ie||(D+=we)}break;case 3:if(ge==I&&se==E&&0<D.b){for(te=I,ge=Le=E+(pe-Z)*Le-(Ae=(pe-Z)*qe(D.Ea,D.b)),se=I,ie=E,we=[],Ae=(Pe=Ae)-1;0<=Ae;--Ae)we[Ae]=se[ie+Ae];for(Ae=Pe-1;0<=Ae;--Ae)te[ge+Ae]=we[Ae];Qn(D,Z,pe,I,Le,I,E)}else Qn(D,Z,pe,ge,se,I,E)}E=m,I=h}I!=h&&r(m,h,E,I,L)}function Rr(a,c){var h=a.V,m=a.Ba+a.c*a.C,y=c-a.C;if(e(c<=a.l.o),e(16>=y),0<y){var L=a.l,P=a.Ta,E=a.Ua,I=L.width;if(xn(a,y,h,m),y=E=[E],e((h=a.C)<(m=c)),e(L.v<L.va),m>L.o&&(m=L.o),h<L.j){var D=L.j-h;h=L.j,y[0]+=D*I}if(h>=m?h=0:(y[0]+=4*L.v,L.ka=h-L.j,L.U=L.va-L.v,L.T=m-h,h=1),h){if(E=E[0],11>(h=a.ca).S){var Z=h.f.RGBA,pe=(m=h.S,y=L.U,L=L.T,D=Z.eb,Z.A),ge=L;for(Z=Z.fb+a.Ma*Z.A;0<ge--;){var se=P,Le=E,we=y,te=D,ie=Z;switch(m){case Uo:In(se,Le,we,te,ie);break;case zo:Sn(se,Le,we,te,ie);break;case Ys:Sn(se,Le,we,te,ie),nr(te,ie,0,we,1,0);break;case Bc:zr(se,Le,we,te,ie);break;case Ho:Ct(se,Le,we,te,ie,1);break;case $s:Ct(se,Le,we,te,ie,1),nr(te,ie,0,we,1,0);break;case Wo:Ct(se,Le,we,te,ie,0);break;case Vo:Ct(se,Le,we,te,ie,0),nr(te,ie,1,we,1,0);break;case Gs:ui(se,Le,we,te,ie);break;case Js:ui(se,Le,we,te,ie),Et(te,ie,we,1,0);break;case Mc:Ur(se,Le,we,te,ie);break;default:e(0)}E+=I,Z+=pe}a.Ma+=L}else alert("todo:EmitRescaledRowsYUVA");e(a.Ma<=h.height)}}a.C=c,e(a.C<=a.i)}function lr(a){var c;if(0<a.ua)return 0;for(c=0;c<a.Wb;++c){var h=a.Ya[c].G,m=a.Ya[c].H;if(0<h[1][m[1]+0].g||0<h[2][m[2]+0].g||0<h[3][m[3]+0].g)return 0}return 1}function Dn(a,c,h,m,y,L){if(a.Z!=0){var P=a.qd,E=a.rd;for(e(pi[a.Z]!=null);c<h;++c)pi[a.Z](P,E,m,y,m,y,L),P=m,E=y,y+=L;a.qd=P,a.rd=E}}function qn(a,c){var h=a.l.ma,m=h.Z==0||h.Z==1?a.l.j:a.C;if(m=a.C<m?m:a.C,e(c<=a.l.o),c>m){var y=a.l.width,L=h.ca,P=h.tb+y*m,E=a.V,I=a.Ba+a.c*m,D=a.gc;e(a.ab==1),e(D[0].hc==3),Ro(D[0],m,c,E,I,L,P),Dn(h,m,c,L,P,y)}a.C=a.Ma=c}function Un(a,c,h,m,y,L,P){var E=a.$/m,I=a.$%m,D=a.m,Z=a.s,pe=h+a.$,ge=pe;y=h+m*y;var se=h+m*L,Le=280+Z.ua,we=a.Pb?E:16777216,te=0<Z.ua?Z.Wa:null,ie=Z.wc,Ae=pe<se?Dt(Z,I,E):null;e(a.C<L),e(se<=y);var Pe=!1;e:for(;;){for(;Pe||pe<se;){var Ne=0;if(E>=we){var De=pe-h;e((we=a).Pb),we.wd=we.m,we.xd=De,0<we.s.ua&&ye(we.s.Wa,we.s.vb),we=E+Fh}if(I&ie||(Ae=Dt(Z,I,E)),e(Ae!=null),Ae.Qb&&(c[pe]=Ae.qb,Pe=!0),!Pe)if(H(D),Ae.jc){Ne=D,De=c;var Be=pe,Ge=Ae.pd[B(Ne)&un-1];e(Ae.jc),256>Ge.g?(z(Ne,Ne.u+Ge.g),De[Be]=Ge.value,Ne=0):(z(Ne,Ne.u+Ge.g-256),e(256<=Ge.value),Ne=Ge.value),Ne==0&&(Pe=!0)}else Ne=dn(Ae.G[0],Ae.H[0],D);if(D.h)break;if(Pe||256>Ne){if(!Pe)if(Ae.nd)c[pe]=(Ae.qb|Ne<<8)>>>0;else{if(H(D),Pe=dn(Ae.G[1],Ae.H[1],D),H(D),De=dn(Ae.G[2],Ae.H[2],D),Be=dn(Ae.G[3],Ae.H[3],D),D.h)break;c[pe]=(Be<<24|Pe<<16|Ne<<8|De)>>>0}if(Pe=!1,++pe,++I>=m&&(I=0,++E,P!=null&&E<=L&&!(E%16)&&P(a,E),te!=null))for(;ge<pe;)Ne=c[ge++],te.X[(506832829*Ne&**********)>>>te.Mb]=Ne}else if(280>Ne){if(Ne=On(Ne-256,D),De=dn(Ae.G[4],Ae.H[4],D),H(D),De=Xn(m,De=On(De,D)),D.h)break;if(pe-h<De||y-pe<Ne)break e;for(Be=0;Be<Ne;++Be)c[pe+Be]=c[pe+Be-De];for(pe+=Ne,I+=Ne;I>=m;)I-=m,++E,P!=null&&E<=L&&!(E%16)&&P(a,E);if(e(pe<=y),I&ie&&(Ae=Dt(Z,I,E)),te!=null)for(;ge<pe;)Ne=c[ge++],te.X[(506832829*Ne&**********)>>>te.Mb]=Ne}else{if(!(Ne<Le))break e;for(Pe=Ne-280,e(te!=null);ge<pe;)Ne=c[ge++],te.X[(506832829*Ne&**********)>>>te.Mb]=Ne;Ne=pe,e(!(Pe>>>(De=te).Xa)),c[Ne]=De.X[Pe],Pe=!0}Pe||e(D.h==M(D))}if(a.Pb&&D.h&&pe<y)e(a.m.h),a.a=5,a.m=a.wd,a.$=a.xd,0<a.s.ua&&ye(a.s.vb,a.s.Wa);else{if(D.h)break e;P!=null&&P(a,E>L?L:E),a.a=0,a.$=pe-h}return 1}return a.a=3,0}function zn(a){e(a!=null),a.vc=null,a.yc=null,a.Ya=null;var c=a.Wa;c!=null&&(c.X=null),a.vb=null,e(a!=null)}function Pn(){var a=new Hs;return a==null?null:(a.a=0,a.xb=Oc,$n("Predictor","VP8LPredictors"),$n("Predictor","VP8LPredictors_C"),$n("PredictorAdd","VP8LPredictorsAdd"),$n("PredictorAdd","VP8LPredictorsAdd_C"),Mo=dt,Fi=$e,In=je,Sn=rn,ui=ht,Ur=Rn,zr=At,F.VP8LMapColor32b=da,F.VP8LMapColor8b=Fo,a)}function Zn(a,c,h,m,y){var L=1,P=[a],E=[c],I=m.m,D=m.s,Z=null,pe=0;e:for(;;){if(h)for(;L&&V(I,1);){var ge=P,se=E,Le=m,we=1,te=Le.m,ie=Le.gc[Le.ab],Ae=V(te,2);if(Le.Oc&1<<Ae)L=0;else{switch(Le.Oc|=1<<Ae,ie.hc=Ae,ie.Ea=ge[0],ie.nc=se[0],ie.K=[null],++Le.ab,e(4>=Le.ab),Ae){case 0:case 1:ie.b=V(te,3)+2,we=Zn(qe(ie.Ea,ie.b),qe(ie.nc,ie.b),0,Le,ie.K),ie.K=ie.K[0];break;case 3:var Pe,Ne=V(te,8)+1,De=16<Ne?0:4<Ne?1:2<Ne?2:3;if(ge[0]=qe(ie.Ea,De),ie.b=De,Pe=we=Zn(Ne,1,0,Le,ie.K)){var Be,Ge=Ne,Re=ie,wt=1<<(8>>Re.b),St=i(wt);if(St==null)Pe=0;else{var bt=Re.K[0],pt=Re.w;for(St[0]=Re.K[0][0],Be=1;Be<1*Ge;++Be)St[Be]=ke(bt[pt+Be],St[Be-1]);for(;Be<4*wt;++Be)St[Be]=0;Re.K[0]=null,Re.K[0]=St,Pe=1}}we=Pe;break;case 2:break;default:e(0)}L=we}}if(P=P[0],E=E[0],L&&V(I,1)&&!(L=1<=(pe=V(I,4))&&11>=pe)){m.a=3;break e}var _t;if(_t=L)t:{var Pt,lt,Zt,Tn=m,Qt=P,jn=E,Nt=pe,Vn=h,Gn=Tn.m,on=Tn.s,fn=[null],Ln=1,ar=0,Lr=Rh[Nt];n:for(;;){if(Vn&&V(Gn,1)){var sn=V(Gn,3)+2,Gr=qe(Qt,sn),zi=qe(jn,sn),va=Gr*zi;if(!Zn(Gr,zi,0,Tn,fn))break n;for(fn=fn[0],on.xc=sn,Pt=0;Pt<va;++Pt){var gi=fn[Pt]>>8&65535;fn[Pt]=gi,gi>=Ln&&(Ln=gi+1)}}if(Gn.h)break n;for(lt=0;5>lt;++lt){var Ut=Rc[lt];!lt&&0<Nt&&(Ut+=1<<Nt),ar<Ut&&(ar=Ut)}var Qs=s(Ln*Lr,k),Gc=Ln,Yc=s(Gc,C);if(Yc==null)var Jo=null;else e(65536>=Gc),Jo=Yc;var ao=i(ar);if(Jo==null||ao==null||Qs==null){Tn.a=1;break n}var Ko=Qs;for(Pt=Zt=0;Pt<Ln;++Pt){var dr=Jo[Pt],ya=dr.G,wa=dr.H,$c=0,Xo=1,Jc=0;for(lt=0;5>lt;++lt){Ut=Rc[lt],ya[lt]=Ko,wa[lt]=Zt,!lt&&0<Nt&&(Ut+=1<<Nt);i:{var Zo,el=Ut,Qo=Tn,oo=ao,rd=Ko,id=Zt,tl=0,mi=Qo.m,ad=V(mi,1);if(o(oo,0,0,el),ad){var od=V(mi,1)+1,sd=V(mi,1),Kc=V(mi,sd==0?1:8);oo[Kc]=1,od==2&&(oo[Kc=V(mi,8)]=1);var es=1}else{var Xc=i(19),Zc=V(mi,4)+4;if(19<Zc){Qo.a=3;var ts=0;break i}for(Zo=0;Zo<Zc;++Zo)Xc[Bh[Zo]]=V(mi,3);var nl=void 0,so=void 0,Qc=Qo,ld=Xc,ns=el,eu=oo,rl=0,bi=Qc.m,tu=8,nu=s(128,k);r:for(;N(nu,0,7,ld,19);){if(V(bi,1)){var cd=2+2*V(bi,3);if((nl=2+V(bi,cd))>ns)break r}else nl=ns;for(so=0;so<ns&&nl--;){H(bi);var ru=nu[0+(127&B(bi))];z(bi,bi.u+ru.g);var xa=ru.value;if(16>xa)eu[so++]=xa,xa!=0&&(tu=xa);else{var ud=xa==16,iu=xa-16,fd=Th[iu],au=V(bi,Ih[iu])+fd;if(so+au>ns)break r;for(var hd=ud?tu:0;0<au--;)eu[so++]=hd}}rl=1;break r}rl||(Qc.a=3),es=rl}(es=es&&!mi.h)&&(tl=N(rd,id,8,oo,el)),es&&tl!=0?ts=tl:(Qo.a=3,ts=0)}if(ts==0)break n;if(Xo&&jh[lt]==1&&(Xo=Ko[Zt].g==0),$c+=Ko[Zt].g,Zt+=ts,3>=lt){var lo,il=ao[0];for(lo=1;lo<Ut;++lo)ao[lo]>il&&(il=ao[lo]);Jc+=il}}if(dr.nd=Xo,dr.Qb=0,Xo&&(dr.qb=(ya[3][wa[3]+0].value<<24|ya[1][wa[1]+0].value<<16|ya[2][wa[2]+0].value)>>>0,$c==0&&256>ya[0][wa[0]+0].value&&(dr.Qb=1,dr.qb+=ya[0][wa[0]+0].value<<8)),dr.jc=!dr.Qb&&6>Jc,dr.jc){var rs,Yr=dr;for(rs=0;rs<un;++rs){var vi=rs,yi=Yr.pd[vi],is=Yr.G[0][Yr.H[0]+vi];256<=is.value?(yi.g=is.g+256,yi.value=is.value):(yi.g=0,yi.value=0,vi>>=Wt(is,8,yi),vi>>=Wt(Yr.G[1][Yr.H[1]+vi],16,yi),vi>>=Wt(Yr.G[2][Yr.H[2]+vi],0,yi),Wt(Yr.G[3][Yr.H[3]+vi],24,yi))}}}on.vc=fn,on.Wb=Ln,on.Ya=Jo,on.yc=Qs,_t=1;break t}_t=0}if(!(L=_t)){m.a=3;break e}if(0<pe){if(D.ua=1<<pe,!ze(D.Wa,pe)){m.a=1,L=0;break e}}else D.ua=0;var al=m,ou=P,dd=E,ol=al.s,sl=ol.xc;if(al.c=ou,al.i=dd,ol.md=qe(ou,sl),ol.wc=sl==0?-1:(1<<sl)-1,h){m.xb=Wh;break e}if((Z=i(P*E))==null){m.a=1,L=0;break e}L=(L=Un(m,Z,0,P,E,E,null))&&!I.h;break e}return L?(y!=null?y[0]=Z:(e(Z==null),e(h)),m.$=0,h||zn(D)):zn(D),L}function Fr(a,c){var h=a.c*a.i,m=h+c+16*c;return e(a.c<=c),a.V=i(m),a.V==null?(a.Ta=null,a.Ua=0,a.a=1,0):(a.Ta=a.V,a.Ua=a.Ba+h+c,1)}function Qr(a,c){var h=a.C,m=c-h,y=a.V,L=a.Ba+a.c*h;for(e(c<=a.l.o);0<m;){var P=16<m?16:m,E=a.l.ma,I=a.l.width,D=I*P,Z=E.ca,pe=E.tb+I*h,ge=a.Ta,se=a.Ua;xn(a,P,y,L),Gt(ge,se,Z,pe,D),Dn(E,h,h+P,Z,pe,I),m-=P,y+=P*a.c,h+=P}e(h==c),a.C=a.Ma=c}function ei(){this.ub=this.yd=this.td=this.Rb=0}function ti(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function Me(){this.Fb=this.Bb=this.Cb=0,this.Zb=i(4),this.Lb=i(4)}function ft(){this.Yb=function(){var a=[];return function c(h,m,y){for(var L=y[m],P=0;P<L&&(h.push(y.length>m+1?[]:0),!(y.length<m+1));P++)c(h[P],m+1,y)}(a,0,[3,11]),a}()}function Kt(){this.jb=i(3),this.Wc=l([4,8],ft),this.Xc=l([4,17],ft)}function en(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new i(4),this.od=new i(4)}function cn(){this.ld=this.La=this.dd=this.tc=0}function _n(){this.Na=this.la=0}function xr(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function An(){this.ad=i(384),this.Za=0,this.Ob=i(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function ni(){this.uc=this.M=this.Nb=0,this.wa=Array(new cn),this.Y=0,this.ya=Array(new An),this.aa=0,this.l=new kn}function ri(){this.y=i(16),this.f=i(8),this.ea=i(8)}function Ii(){this.cb=this.a=0,this.sc="",this.m=new me,this.Od=new ei,this.Kc=new ti,this.ed=new en,this.Qa=new Me,this.Ic=this.$c=this.Aa=0,this.D=new ni,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=s(8,me),this.ia=0,this.pb=s(4,xr),this.Pa=new Kt,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new ri),this.Hd=0,this.rb=Array(new _n),this.sb=0,this.wa=Array(new cn),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new An),this.L=this.aa=0,this.gd=l([4,2],cn),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function kn(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function ii(){var a=new Ii;return a!=null&&(a.a=0,a.sc="OK",a.cb=0,a.Xb=0,ro||(ro=Qi)),a}function Tt(a,c,h){return a.a==0&&(a.a=c,a.sc=h,a.cb=0),0}function Or(a,c,h){return 3<=h&&a[c+0]==157&&a[c+1]==1&&a[c+2]==42}function Ti(a,c){if(a==null)return 0;if(a.a=0,a.sc="OK",c==null)return Tt(a,2,"null VP8Io passed to VP8GetHeaders()");var h=c.data,m=c.w,y=c.ha;if(4>y)return Tt(a,7,"Truncated header.");var L=h[m+0]|h[m+1]<<8|h[m+2]<<16,P=a.Od;if(P.Rb=!(1&L),P.td=L>>1&7,P.yd=L>>4&1,P.ub=L>>5,3<P.td)return Tt(a,3,"Incorrect keyframe parameters.");if(!P.yd)return Tt(a,4,"Frame not displayable.");m+=3,y-=3;var E=a.Kc;if(P.Rb){if(7>y)return Tt(a,7,"cannot parse picture header");if(!Or(h,m,y))return Tt(a,3,"Bad code word");E.c=16383&(h[m+4]<<8|h[m+3]),E.Td=h[m+4]>>6,E.i=16383&(h[m+6]<<8|h[m+5]),E.Ud=h[m+6]>>6,m+=7,y-=7,a.za=E.c+15>>4,a.Ub=E.i+15>>4,c.width=E.c,c.height=E.i,c.Da=0,c.j=0,c.v=0,c.va=c.width,c.o=c.height,c.da=0,c.ib=c.width,c.hb=c.height,c.U=c.width,c.T=c.height,o((L=a.Pa).jb,0,255,L.jb.length),e((L=a.Qa)!=null),L.Cb=0,L.Bb=0,L.Fb=1,o(L.Zb,0,0,L.Zb.length),o(L.Lb,0,0,L.Lb)}if(P.ub>y)return Tt(a,7,"bad partition length");X(L=a.m,h,m,P.ub),m+=P.ub,y-=P.ub,P.Rb&&(E.Ld=ee(L),E.Kd=ee(L)),E=a.Qa;var I,D=a.Pa;if(e(L!=null),e(E!=null),E.Cb=ee(L),E.Cb){if(E.Bb=ee(L),ee(L)){for(E.Fb=ee(L),I=0;4>I;++I)E.Zb[I]=ee(L)?ue(L,7):0;for(I=0;4>I;++I)E.Lb[I]=ee(L)?ue(L,6):0}if(E.Bb)for(I=0;3>I;++I)D.jb[I]=ee(L)?ne(L,8):255}else E.Bb=0;if(L.Ka)return Tt(a,3,"cannot parse segment header");if((E=a.ed).zd=ee(L),E.Tb=ne(L,6),E.wb=ne(L,3),E.Pc=ee(L),E.Pc&&ee(L)){for(D=0;4>D;++D)ee(L)&&(E.vd[D]=ue(L,6));for(D=0;4>D;++D)ee(L)&&(E.od[D]=ue(L,6))}if(a.L=E.Tb==0?0:E.zd?1:2,L.Ka)return Tt(a,3,"cannot parse filter header");var Z=y;if(y=I=m,m=I+Z,E=Z,a.Xb=(1<<ne(a.m,2))-1,Z<3*(D=a.Xb))h=7;else{for(I+=3*D,E-=3*D,Z=0;Z<D;++Z){var pe=h[y+0]|h[y+1]<<8|h[y+2]<<16;pe>E&&(pe=E),X(a.Jc[+Z],h,I,pe),I+=pe,E-=pe,y+=3}X(a.Jc[+D],h,I,E),h=I<m?0:5}if(h!=0)return Tt(a,h,"cannot parse partitions");for(h=ne(I=a.m,7),y=ee(I)?ue(I,4):0,m=ee(I)?ue(I,4):0,E=ee(I)?ue(I,4):0,D=ee(I)?ue(I,4):0,I=ee(I)?ue(I,4):0,Z=a.Qa,pe=0;4>pe;++pe){if(Z.Cb){var ge=Z.Zb[pe];Z.Fb||(ge+=h)}else{if(0<pe){a.pb[pe]=a.pb[0];continue}ge=h}var se=a.pb[pe];se.Sc[0]=Ks[Hn(ge+y,127)],se.Sc[1]=Xs[Hn(ge+0,127)],se.Eb[0]=2*Ks[Hn(ge+m,127)],se.Eb[1]=101581*Xs[Hn(ge+E,127)]>>16,8>se.Eb[1]&&(se.Eb[1]=8),se.Qc[0]=Ks[Hn(ge+D,117)],se.Qc[1]=Xs[Hn(ge+I,127)],se.lc=ge+I}if(!P.Rb)return Tt(a,4,"Not a key frame.");for(ee(L),P=a.Pa,h=0;4>h;++h){for(y=0;8>y;++y)for(m=0;3>m;++m)for(E=0;11>E;++E)D=oe(L,zh[h][y][m][E])?ne(L,8):qh[h][y][m][E],P.Wc[h][y].Yb[m][E]=D;for(y=0;17>y;++y)P.Xc[h][y]=P.Wc[h][Hh[y]]}return a.kc=ee(L),a.kc&&(a.Bd=ne(L,8)),a.cb=1}function Qi(a,c,h,m,y,L,P){var E=c[y].Yb[h];for(h=0;16>y;++y){if(!oe(a,E[h+0]))return y;for(;!oe(a,E[h+1]);)if(E=c[++y].Yb[0],h=0,y==16)return 16;var I=c[y+1].Yb;if(oe(a,E[h+2])){var D=a,Z=0;if(oe(D,(ge=E)[(pe=h)+3]))if(oe(D,ge[pe+6])){for(E=0,pe=2*(Z=oe(D,ge[pe+8]))+(ge=oe(D,ge[pe+9+Z])),Z=0,ge=Oh[pe];ge[E];++E)Z+=Z+oe(D,ge[E]);Z+=3+(8<<pe)}else oe(D,ge[pe+7])?(Z=7+2*oe(D,165),Z+=oe(D,145)):Z=5+oe(D,159);else Z=oe(D,ge[pe+4])?3+oe(D,ge[pe+5]):2;E=I[2]}else Z=1,E=I[1];I=P+Dh[y],0>(D=a).b&&G(D);var pe,ge=D.b,se=(pe=D.Ca>>1)-(D.I>>ge)>>31;--D.b,D.Ca+=se,D.Ca|=1,D.I-=(pe+1&se)<<ge,L[I]=((Z^se)-se)*m[(0<y)+0]}return 16}function ai(a){var c=a.rb[a.sb-1];c.la=0,c.Na=0,o(a.zc,0,0,a.zc.length),a.ja=0}function Ps(a,c){if(a==null)return 0;if(c==null)return Tt(a,2,"NULL VP8Io parameter in VP8Decode().");if(!a.cb&&!Ti(a,c))return 0;if(e(a.cb),c.ac==null||c.ac(c)){c.ob&&(a.L=0);var h=$o[a.L];if(a.L==2?(a.yb=0,a.zb=0):(a.yb=c.v-h>>4,a.zb=c.j-h>>4,0>a.yb&&(a.yb=0),0>a.zb&&(a.zb=0)),a.Va=c.o+15+h>>4,a.Hb=c.va+15+h>>4,a.Hb>a.za&&(a.Hb=a.za),a.Va>a.Ub&&(a.Va=a.Ub),0<a.L){var m=a.ed;for(h=0;4>h;++h){var y;if(a.Qa.Cb){var L=a.Qa.Lb[h];a.Qa.Fb||(L+=m.Tb)}else L=m.Tb;for(y=0;1>=y;++y){var P=a.gd[h][y],E=L;if(m.Pc&&(E+=m.vd[0],y&&(E+=m.od[0])),0<(E=0>E?0:63<E?63:E)){var I=E;0<m.wb&&(I=4<m.wb?I>>2:I>>1)>9-m.wb&&(I=9-m.wb),1>I&&(I=1),P.dd=I,P.tc=2*E+I,P.ld=40<=E?2:15<=E?1:0}else P.tc=0;P.La=y}}}h=0}else Tt(a,6,"Frame setup failed"),h=a.a;if(h=h==0){if(h){a.$c=0,0<a.Aa||(a.Ic=nd);e:{h=a.Ic,m=4*(I=a.za);var D=32*I,Z=I+1,pe=0<a.L?I*(0<a.Aa?2:1):0,ge=(a.Aa==2?2:1)*I;if((P=m+832+(y=3*(16*h+$o[a.L])/2*D)+(L=a.Fa!=null&&0<a.Fa.length?a.Kc.c*a.Kc.i:0))!=P)h=0;else{if(P>a.Vb){if(a.Vb=0,a.Ec=i(P),a.Fc=0,a.Ec==null){h=Tt(a,1,"no memory during frame initialization.");break e}a.Vb=P}P=a.Ec,E=a.Fc,a.Ac=P,a.Bc=E,E+=m,a.Gd=s(D,ri),a.Hd=0,a.rb=s(Z+1,_n),a.sb=1,a.wa=pe?s(pe,cn):null,a.Y=0,a.D.Nb=0,a.D.wa=a.wa,a.D.Y=a.Y,0<a.Aa&&(a.D.Y+=I),e(!0),a.oc=P,a.pc=E,E+=832,a.ya=s(ge,An),a.aa=0,a.D.ya=a.ya,a.D.aa=a.aa,a.Aa==2&&(a.D.aa+=I),a.R=16*I,a.B=8*I,I=(D=$o[a.L])*a.R,D=D/2*a.B,a.sa=P,a.ta=E+I,a.qa=a.sa,a.ra=a.ta+16*h*a.R+D,a.Ha=a.qa,a.Ia=a.ra+8*h*a.B+D,a.$c=0,E+=y,a.mb=L?P:null,a.nb=L?E:null,e(E+L<=a.Fc+a.Vb),ai(a),o(a.Ac,a.Bc,0,m),h=1}}if(h){if(c.ka=0,c.y=a.sa,c.O=a.ta,c.f=a.qa,c.N=a.ra,c.ea=a.Ha,c.Vd=a.Ia,c.fa=a.R,c.Rc=a.B,c.F=null,c.J=0,!Do){for(h=-255;255>=h;++h)tn[255+h]=0>h?-h:h;for(h=-1020;1020>=h;++h)Wr[1020+h]=-128>h?-128:127<h?127:h;for(h=-112;112>=h;++h)no[112+h]=-16>h?-16:15<h?15:h;for(h=-255;510>=h;++h)ba[255+h]=0>h?0:255<h?255:h;Do=1}pa=Cs,Hr=_s,Qa=Ao,Nn=ks,er=So,Vt=xo,ga=qa,Oo=Mi,eo=zs,Oi=Ua,Di=Us,fi=aa,qi=za,ma=To,Ui=Io,hi=Sr,to=qr,tr=qs,hr[0]=Ar,hr[1]=Es,hr[2]=Bs,hr[3]=Ms,hr[4]=Po,hr[5]=ra,hr[6]=_o,hr[7]=Fa,hr[8]=Fs,hr[9]=Rs,di[0]=No,di[1]=Ts,di[2]=Dr,di[3]=ta,di[4]=pn,di[5]=js,di[6]=Lo,Vr[0]=oi,Vr[1]=Is,Vr[2]=Os,Vr[3]=Oa,Vr[4]=Bi,Vr[5]=Ds,Vr[6]=Da,h=1}else h=0}h&&(h=function(se,Le){for(se.M=0;se.M<se.Va;++se.M){var we,te=se.Jc[se.M&se.Xb],ie=se.m,Ae=se;for(we=0;we<Ae.za;++we){var Pe=ie,Ne=Ae,De=Ne.Ac,Be=Ne.Bc+4*we,Ge=Ne.zc,Re=Ne.ya[Ne.aa+we];if(Ne.Qa.Bb?Re.$b=oe(Pe,Ne.Pa.jb[0])?2+oe(Pe,Ne.Pa.jb[2]):oe(Pe,Ne.Pa.jb[1]):Re.$b=0,Ne.kc&&(Re.Ad=oe(Pe,Ne.Bd)),Re.Za=!oe(Pe,145)+0,Re.Za){var wt=Re.Ob,St=0;for(Ne=0;4>Ne;++Ne){var bt,pt=Ge[0+Ne];for(bt=0;4>bt;++bt){pt=Uh[De[Be+bt]][pt];for(var _t=Fc[oe(Pe,pt[0])];0<_t;)_t=Fc[2*_t+oe(Pe,pt[_t])];pt=-_t,De[Be+bt]=pt}r(wt,St,De,Be,4),St+=4,Ge[0+Ne]=pt}}else pt=oe(Pe,156)?oe(Pe,128)?1:3:oe(Pe,163)?2:0,Re.Ob[0]=pt,o(De,Be,pt,4),o(Ge,0,pt,4);Re.Dd=oe(Pe,142)?oe(Pe,114)?oe(Pe,183)?1:3:2:0}if(Ae.m.Ka)return Tt(se,7,"Premature end-of-partition0 encountered.");for(;se.ja<se.za;++se.ja){if(Ae=te,Pe=(ie=se).rb[ie.sb-1],De=ie.rb[ie.sb+ie.ja],we=ie.ya[ie.aa+ie.ja],Be=ie.kc?we.Ad:0)Pe.la=De.la=0,we.Za||(Pe.Na=De.Na=0),we.Hc=0,we.Gc=0,we.ia=0;else{var Pt,lt;if(Pe=De,De=Ae,Be=ie.Pa.Xc,Ge=ie.ya[ie.aa+ie.ja],Re=ie.pb[Ge.$b],Ne=Ge.ad,wt=0,St=ie.rb[ie.sb-1],pt=bt=0,o(Ne,wt,0,384),Ge.Za)var Zt=0,Tn=Be[3];else{_t=i(16);var Qt=Pe.Na+St.Na;if(Qt=ro(De,Be[1],Qt,Re.Eb,0,_t,0),Pe.Na=St.Na=(0<Qt)+0,1<Qt)pa(_t,0,Ne,wt);else{var jn=_t[0]+3>>3;for(_t=0;256>_t;_t+=16)Ne[wt+_t]=jn}Zt=1,Tn=Be[0]}var Nt=15&Pe.la,Vn=15&St.la;for(_t=0;4>_t;++_t){var Gn=1&Vn;for(jn=lt=0;4>jn;++jn)Nt=Nt>>1|(Gn=(Qt=ro(De,Tn,Qt=Gn+(1&Nt),Re.Sc,Zt,Ne,wt))>Zt)<<7,lt=lt<<2|(3<Qt?3:1<Qt?2:Ne[wt+0]!=0),wt+=16;Nt>>=4,Vn=Vn>>1|Gn<<7,bt=(bt<<8|lt)>>>0}for(Tn=Nt,Zt=Vn>>4,Pt=0;4>Pt;Pt+=2){for(lt=0,Nt=Pe.la>>4+Pt,Vn=St.la>>4+Pt,_t=0;2>_t;++_t){for(Gn=1&Vn,jn=0;2>jn;++jn)Qt=Gn+(1&Nt),Nt=Nt>>1|(Gn=0<(Qt=ro(De,Be[2],Qt,Re.Qc,0,Ne,wt)))<<3,lt=lt<<2|(3<Qt?3:1<Qt?2:Ne[wt+0]!=0),wt+=16;Nt>>=2,Vn=Vn>>1|Gn<<5}pt|=lt<<4*Pt,Tn|=Nt<<4<<Pt,Zt|=(240&Vn)<<Pt}Pe.la=Tn,St.la=Zt,Ge.Hc=bt,Ge.Gc=pt,Ge.ia=43690&pt?0:Re.ia,Be=!(bt|pt)}if(0<ie.L&&(ie.wa[ie.Y+ie.ja]=ie.gd[we.$b][we.Za],ie.wa[ie.Y+ie.ja].La|=!Be),Ae.Ka)return Tt(se,7,"Premature end-of-file encountered.")}if(ai(se),ie=Le,Ae=1,we=(te=se).D,Pe=0<te.L&&te.M>=te.zb&&te.M<=te.Va,te.Aa==0)e:{if(we.M=te.M,we.uc=Pe,$a(te,we),Ae=1,we=(lt=te.D).Nb,Pe=(pt=$o[te.L])*te.R,De=pt/2*te.B,_t=16*we*te.R,jn=8*we*te.B,Be=te.sa,Ge=te.ta-Pe+_t,Re=te.qa,Ne=te.ra-De+jn,wt=te.Ha,St=te.Ia-De+jn,Vn=(Nt=lt.M)==0,bt=Nt>=te.Va-1,te.Aa==2&&$a(te,lt),lt.uc)for(Gn=(Qt=te).D.M,e(Qt.D.uc),lt=Qt.yb;lt<Qt.Hb;++lt){Zt=lt,Tn=Gn;var on=(fn=(Ut=Qt).D).Nb;Pt=Ut.R;var fn=fn.wa[fn.Y+Zt],Ln=Ut.sa,ar=Ut.ta+16*on*Pt+16*Zt,Lr=fn.dd,sn=fn.tc;if(sn!=0)if(e(3<=sn),Ut.L==1)0<Zt&&hi(Ln,ar,Pt,sn+4),fn.La&&tr(Ln,ar,Pt,sn),0<Tn&&Ui(Ln,ar,Pt,sn+4),fn.La&&to(Ln,ar,Pt,sn);else{var Gr=Ut.B,zi=Ut.qa,va=Ut.ra+8*on*Gr+8*Zt,gi=Ut.Ha,Ut=Ut.Ia+8*on*Gr+8*Zt;on=fn.ld,0<Zt&&(Oo(Ln,ar,Pt,sn+4,Lr,on),Oi(zi,va,gi,Ut,Gr,sn+4,Lr,on)),fn.La&&(fi(Ln,ar,Pt,sn,Lr,on),ma(zi,va,gi,Ut,Gr,sn,Lr,on)),0<Tn&&(ga(Ln,ar,Pt,sn+4,Lr,on),eo(zi,va,gi,Ut,Gr,sn+4,Lr,on)),fn.La&&(Di(Ln,ar,Pt,sn,Lr,on),qi(zi,va,gi,Ut,Gr,sn,Lr,on))}}if(te.ia&&alert("todo:DitherRow"),ie.put!=null){if(lt=16*Nt,Nt=16*(Nt+1),Vn?(ie.y=te.sa,ie.O=te.ta+_t,ie.f=te.qa,ie.N=te.ra+jn,ie.ea=te.Ha,ie.W=te.Ia+jn):(lt-=pt,ie.y=Be,ie.O=Ge,ie.f=Re,ie.N=Ne,ie.ea=wt,ie.W=St),bt||(Nt-=pt),Nt>ie.o&&(Nt=ie.o),ie.F=null,ie.J=null,te.Fa!=null&&0<te.Fa.length&&lt<Nt&&(ie.J=Ga(te,ie,lt,Nt-lt),ie.F=te.mb,ie.F==null&&ie.F.length==0)){Ae=Tt(te,3,"Could not decode alpha data.");break e}lt<ie.j&&(pt=ie.j-lt,lt=ie.j,e(!(1&pt)),ie.O+=te.R*pt,ie.N+=te.B*(pt>>1),ie.W+=te.B*(pt>>1),ie.F!=null&&(ie.J+=ie.width*pt)),lt<Nt&&(ie.O+=ie.v,ie.N+=ie.v>>1,ie.W+=ie.v>>1,ie.F!=null&&(ie.J+=ie.v),ie.ka=lt-ie.j,ie.U=ie.va-ie.v,ie.T=Nt-lt,Ae=ie.put(ie))}we+1!=te.Ic||bt||(r(te.sa,te.ta-Pe,Be,Ge+16*te.R,Pe),r(te.qa,te.ra-De,Re,Ne+8*te.B,De),r(te.Ha,te.Ia-De,wt,St+8*te.B,De))}if(!Ae)return Tt(se,6,"Output aborted.")}return 1}(a,c)),c.bc!=null&&c.bc(c),h&=1}return h?(a.cb=0,h):0}function cr(a,c,h,m,y){y=a[c+h+32*m]+(y>>3),a[c+h+32*m]=-256&y?0>y?0:255:y}function ea(a,c,h,m,y,L){cr(a,c,0,h,m+y),cr(a,c,1,h,m+L),cr(a,c,2,h,m-L),cr(a,c,3,h,m-y)}function Cn(a){return(20091*a>>16)+a}function Ma(a,c,h,m){var y,L=0,P=i(16);for(y=0;4>y;++y){var E=a[c+0]+a[c+8],I=a[c+0]-a[c+8],D=(35468*a[c+4]>>16)-Cn(a[c+12]),Z=Cn(a[c+4])+(35468*a[c+12]>>16);P[L+0]=E+Z,P[L+1]=I+D,P[L+2]=I-D,P[L+3]=E-Z,L+=4,c++}for(y=L=0;4>y;++y)E=(a=P[L+0]+4)+P[L+8],I=a-P[L+8],D=(35468*P[L+4]>>16)-Cn(P[L+12]),cr(h,m,0,0,E+(Z=Cn(P[L+4])+(35468*P[L+12]>>16))),cr(h,m,1,0,I+D),cr(h,m,2,0,I-D),cr(h,m,3,0,E-Z),L++,m+=32}function xo(a,c,h,m){var y=a[c+0]+4,L=35468*a[c+4]>>16,P=Cn(a[c+4]),E=35468*a[c+1]>>16;ea(h,m,0,y+P,a=Cn(a[c+1]),E),ea(h,m,1,y+L,a,E),ea(h,m,2,y-L,a,E),ea(h,m,3,y-P,a,E)}function _s(a,c,h,m,y){Ma(a,c,h,m),y&&Ma(a,c+16,h,m+4)}function Ao(a,c,h,m){Hr(a,c+0,h,m,1),Hr(a,c+32,h,m+128,1)}function ks(a,c,h,m){var y;for(a=a[c+0]+4,y=0;4>y;++y)for(c=0;4>c;++c)cr(h,m,c,y,a)}function So(a,c,h,m){a[c+0]&&Nn(a,c+0,h,m),a[c+16]&&Nn(a,c+16,h,m+4),a[c+32]&&Nn(a,c+32,h,m+128),a[c+48]&&Nn(a,c+48,h,m+128+4)}function Cs(a,c,h,m){var y,L=i(16);for(y=0;4>y;++y){var P=a[c+0+y]+a[c+12+y],E=a[c+4+y]+a[c+8+y],I=a[c+4+y]-a[c+8+y],D=a[c+0+y]-a[c+12+y];L[0+y]=P+E,L[8+y]=P-E,L[4+y]=D+I,L[12+y]=D-I}for(y=0;4>y;++y)P=(a=L[0+4*y]+3)+L[3+4*y],E=L[1+4*y]+L[2+4*y],I=L[1+4*y]-L[2+4*y],D=a-L[3+4*y],h[m+0]=P+E>>3,h[m+16]=D+I>>3,h[m+32]=P-E>>3,h[m+48]=D-I>>3,m+=64}function Ra(a,c,h){var m,y=c-32,L=Wn,P=255-a[y-1];for(m=0;m<h;++m){var E,I=L,D=P+a[c-1];for(E=0;E<h;++E)a[c+E]=I[D+a[y+E]];c+=32}}function Es(a,c){Ra(a,c,4)}function Is(a,c){Ra(a,c,8)}function Ts(a,c){Ra(a,c,16)}function Dr(a,c){var h;for(h=0;16>h;++h)r(a,c+32*h,a,c-32,16)}function ta(a,c){var h;for(h=16;0<h;--h)o(a,c,a[c-1],16),c+=32}function na(a,c,h){var m;for(m=0;16>m;++m)o(c,h+32*m,a,16)}function No(a,c){var h,m=16;for(h=0;16>h;++h)m+=a[c-1+32*h]+a[c+h-32];na(m>>5,a,c)}function pn(a,c){var h,m=8;for(h=0;16>h;++h)m+=a[c-1+32*h];na(m>>4,a,c)}function js(a,c){var h,m=8;for(h=0;16>h;++h)m+=a[c+h-32];na(m>>4,a,c)}function Lo(a,c){na(128,a,c)}function nt(a,c,h){return a+2*c+h+2>>2}function Bs(a,c){var h,m=c-32;for(m=new Uint8Array([nt(a[m-1],a[m+0],a[m+1]),nt(a[m+0],a[m+1],a[m+2]),nt(a[m+1],a[m+2],a[m+3]),nt(a[m+2],a[m+3],a[m+4])]),h=0;4>h;++h)r(a,c+32*h,m,0,m.length)}function Ms(a,c){var h=a[c-1],m=a[c-1+32],y=a[c-1+64],L=a[c-1+96];ve(a,c+0,16843009*nt(a[c-1-32],h,m)),ve(a,c+32,16843009*nt(h,m,y)),ve(a,c+64,16843009*nt(m,y,L)),ve(a,c+96,16843009*nt(y,L,L))}function Ar(a,c){var h,m=4;for(h=0;4>h;++h)m+=a[c+h-32]+a[c-1+32*h];for(m>>=3,h=0;4>h;++h)o(a,c+32*h,m,4)}function Po(a,c){var h=a[c-1+0],m=a[c-1+32],y=a[c-1+64],L=a[c-1-32],P=a[c+0-32],E=a[c+1-32],I=a[c+2-32],D=a[c+3-32];a[c+0+96]=nt(m,y,a[c-1+96]),a[c+1+96]=a[c+0+64]=nt(h,m,y),a[c+2+96]=a[c+1+64]=a[c+0+32]=nt(L,h,m),a[c+3+96]=a[c+2+64]=a[c+1+32]=a[c+0+0]=nt(P,L,h),a[c+3+64]=a[c+2+32]=a[c+1+0]=nt(E,P,L),a[c+3+32]=a[c+2+0]=nt(I,E,P),a[c+3+0]=nt(D,I,E)}function _o(a,c){var h=a[c+1-32],m=a[c+2-32],y=a[c+3-32],L=a[c+4-32],P=a[c+5-32],E=a[c+6-32],I=a[c+7-32];a[c+0+0]=nt(a[c+0-32],h,m),a[c+1+0]=a[c+0+32]=nt(h,m,y),a[c+2+0]=a[c+1+32]=a[c+0+64]=nt(m,y,L),a[c+3+0]=a[c+2+32]=a[c+1+64]=a[c+0+96]=nt(y,L,P),a[c+3+32]=a[c+2+64]=a[c+1+96]=nt(L,P,E),a[c+3+64]=a[c+2+96]=nt(P,E,I),a[c+3+96]=nt(E,I,I)}function ra(a,c){var h=a[c-1+0],m=a[c-1+32],y=a[c-1+64],L=a[c-1-32],P=a[c+0-32],E=a[c+1-32],I=a[c+2-32],D=a[c+3-32];a[c+0+0]=a[c+1+64]=L+P+1>>1,a[c+1+0]=a[c+2+64]=P+E+1>>1,a[c+2+0]=a[c+3+64]=E+I+1>>1,a[c+3+0]=I+D+1>>1,a[c+0+96]=nt(y,m,h),a[c+0+64]=nt(m,h,L),a[c+0+32]=a[c+1+96]=nt(h,L,P),a[c+1+32]=a[c+2+96]=nt(L,P,E),a[c+2+32]=a[c+3+96]=nt(P,E,I),a[c+3+32]=nt(E,I,D)}function Fa(a,c){var h=a[c+0-32],m=a[c+1-32],y=a[c+2-32],L=a[c+3-32],P=a[c+4-32],E=a[c+5-32],I=a[c+6-32],D=a[c+7-32];a[c+0+0]=h+m+1>>1,a[c+1+0]=a[c+0+64]=m+y+1>>1,a[c+2+0]=a[c+1+64]=y+L+1>>1,a[c+3+0]=a[c+2+64]=L+P+1>>1,a[c+0+32]=nt(h,m,y),a[c+1+32]=a[c+0+96]=nt(m,y,L),a[c+2+32]=a[c+1+96]=nt(y,L,P),a[c+3+32]=a[c+2+96]=nt(L,P,E),a[c+3+64]=nt(P,E,I),a[c+3+96]=nt(E,I,D)}function Rs(a,c){var h=a[c-1+0],m=a[c-1+32],y=a[c-1+64],L=a[c-1+96];a[c+0+0]=h+m+1>>1,a[c+2+0]=a[c+0+32]=m+y+1>>1,a[c+2+32]=a[c+0+64]=y+L+1>>1,a[c+1+0]=nt(h,m,y),a[c+3+0]=a[c+1+32]=nt(m,y,L),a[c+3+32]=a[c+1+64]=nt(y,L,L),a[c+3+64]=a[c+2+64]=a[c+0+96]=a[c+1+96]=a[c+2+96]=a[c+3+96]=L}function Fs(a,c){var h=a[c-1+0],m=a[c-1+32],y=a[c-1+64],L=a[c-1+96],P=a[c-1-32],E=a[c+0-32],I=a[c+1-32],D=a[c+2-32];a[c+0+0]=a[c+2+32]=h+P+1>>1,a[c+0+32]=a[c+2+64]=m+h+1>>1,a[c+0+64]=a[c+2+96]=y+m+1>>1,a[c+0+96]=L+y+1>>1,a[c+3+0]=nt(E,I,D),a[c+2+0]=nt(P,E,I),a[c+1+0]=a[c+3+32]=nt(h,P,E),a[c+1+32]=a[c+3+64]=nt(m,h,P),a[c+1+64]=a[c+3+96]=nt(y,m,h),a[c+1+96]=nt(L,y,m)}function Os(a,c){var h;for(h=0;8>h;++h)r(a,c+32*h,a,c-32,8)}function Oa(a,c){var h;for(h=0;8>h;++h)o(a,c,a[c-1],8),c+=32}function ji(a,c,h){var m;for(m=0;8>m;++m)o(c,h+32*m,a,8)}function oi(a,c){var h,m=8;for(h=0;8>h;++h)m+=a[c+h-32]+a[c-1+32*h];ji(m>>4,a,c)}function Ds(a,c){var h,m=4;for(h=0;8>h;++h)m+=a[c+h-32];ji(m>>3,a,c)}function Bi(a,c){var h,m=4;for(h=0;8>h;++h)m+=a[c-1+32*h];ji(m>>3,a,c)}function Da(a,c){ji(128,a,c)}function ia(a,c,h){var m=a[c-h],y=a[c+0],L=3*(y-m)+Vs[1020+a[c-2*h]-a[c+h]],P=qo[112+(L+4>>3)];a[c-h]=Wn[255+m+qo[112+(L+3>>3)]],a[c+0]=Wn[255+y-P]}function ko(a,c,h,m){var y=a[c+0],L=a[c+h];return rr[255+a[c-2*h]-a[c-h]]>m||rr[255+L-y]>m}function Co(a,c,h,m){return 4*rr[255+a[c-h]-a[c+0]]+rr[255+a[c-2*h]-a[c+h]]<=m}function Eo(a,c,h,m,y){var L=a[c-3*h],P=a[c-2*h],E=a[c-h],I=a[c+0],D=a[c+h],Z=a[c+2*h],pe=a[c+3*h];return 4*rr[255+E-I]+rr[255+P-D]>m?0:rr[255+a[c-4*h]-L]<=y&&rr[255+L-P]<=y&&rr[255+P-E]<=y&&rr[255+pe-Z]<=y&&rr[255+Z-D]<=y&&rr[255+D-I]<=y}function Io(a,c,h,m){var y=2*m+1;for(m=0;16>m;++m)Co(a,c+m,h,y)&&ia(a,c+m,h)}function Sr(a,c,h,m){var y=2*m+1;for(m=0;16>m;++m)Co(a,c+m*h,1,y)&&ia(a,c+m*h,1)}function qr(a,c,h,m){var y;for(y=3;0<y;--y)Io(a,c+=4*h,h,m)}function qs(a,c,h,m){var y;for(y=3;0<y;--y)Sr(a,c+=4,h,m)}function si(a,c,h,m,y,L,P,E){for(L=2*L+1;0<y--;){if(Eo(a,c,h,L,P))if(ko(a,c,h,E))ia(a,c,h);else{var I=a,D=c,Z=h,pe=I[D-2*Z],ge=I[D-Z],se=I[D+0],Le=I[D+Z],we=I[D+2*Z],te=27*(Ae=Vs[1020+3*(se-ge)+Vs[1020+pe-Le]])+63>>7,ie=18*Ae+63>>7,Ae=9*Ae+63>>7;I[D-3*Z]=Wn[255+I[D-3*Z]+Ae],I[D-2*Z]=Wn[255+pe+ie],I[D-Z]=Wn[255+ge+te],I[D+0]=Wn[255+se-te],I[D+Z]=Wn[255+Le-ie],I[D+2*Z]=Wn[255+we-Ae]}c+=m}}function ur(a,c,h,m,y,L,P,E){for(L=2*L+1;0<y--;){if(Eo(a,c,h,L,P))if(ko(a,c,h,E))ia(a,c,h);else{var I=a,D=c,Z=h,pe=I[D-Z],ge=I[D+0],se=I[D+Z],Le=qo[112+((we=3*(ge-pe))+4>>3)],we=qo[112+(we+3>>3)],te=Le+1>>1;I[D-2*Z]=Wn[255+I[D-2*Z]+te],I[D-Z]=Wn[255+pe+we],I[D+0]=Wn[255+ge-Le],I[D+Z]=Wn[255+se-te]}c+=m}}function qa(a,c,h,m,y,L){si(a,c,h,1,16,m,y,L)}function Mi(a,c,h,m,y,L){si(a,c,1,h,16,m,y,L)}function Us(a,c,h,m,y,L){var P;for(P=3;0<P;--P)ur(a,c+=4*h,h,1,16,m,y,L)}function aa(a,c,h,m,y,L){var P;for(P=3;0<P;--P)ur(a,c+=4,1,h,16,m,y,L)}function zs(a,c,h,m,y,L,P,E){si(a,c,y,1,8,L,P,E),si(h,m,y,1,8,L,P,E)}function Ua(a,c,h,m,y,L,P,E){si(a,c,1,y,8,L,P,E),si(h,m,1,y,8,L,P,E)}function za(a,c,h,m,y,L,P,E){ur(a,c+4*y,y,1,8,L,P,E),ur(h,m+4*y,y,1,8,L,P,E)}function To(a,c,h,m,y,L,P,E){ur(a,c+4,1,y,8,L,P,E),ur(h,m+4,1,y,8,L,P,E)}function oa(){this.ba=new Fn,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new Mt,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function Ha(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function Wa(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function jo(){this.ua=0,this.Wa=new W,this.vb=new W,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new C,this.yc=new k}function Hs(){this.xb=this.a=0,this.l=new kn,this.ca=new Fn,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new x,this.Pb=0,this.wd=new x,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new jo,this.ab=0,this.gc=s(4,Wa),this.Oc=0}function sa(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new kn,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function Ri(a,c,h,m,y,L,P){for(a=a==null?0:a[c+0],c=0;c<P;++c)y[L+c]=a+h[m+c]&255,a=y[L+c]}function Va(a,c,h,m,y,L,P){var E;if(a==null)Ri(null,null,h,m,y,L,P);else for(E=0;E<P;++E)y[L+E]=a[c+E]+h[m+E]&255}function li(a,c,h,m,y,L,P){if(a==null)Ri(null,null,h,m,y,L,P);else{var E,I=a[c+0],D=I,Z=I;for(E=0;E<P;++E)D=Z+(I=a[c+E])-D,Z=h[m+E]+(-256&D?0>D?0:255:D)&255,D=I,y[L+E]=Z}}function Ga(a,c,h,m){var y=c.width,L=c.o;if(e(a!=null&&c!=null),0>h||0>=m||h+m>L)return null;if(!a.Cc){if(a.ga==null){var P;if(a.ga=new sa,(P=a.ga==null)||(P=c.width*c.o,e(a.Gb.length==0),a.Gb=i(P),a.Uc=0,a.Gb==null?P=0:(a.mb=a.Gb,a.nb=a.Uc,a.rc=null,P=1),P=!P),!P){P=a.ga;var E=a.Fa,I=a.P,D=a.qc,Z=a.mb,pe=a.nb,ge=I+1,se=D-1,Le=P.l;if(e(E!=null&&Z!=null&&c!=null),pi[0]=null,pi[1]=Ri,pi[2]=Va,pi[3]=li,P.ca=Z,P.tb=pe,P.c=c.width,P.i=c.height,e(0<P.c&&0<P.i),1>=D)c=0;else if(P.$a=E[I+0]>>0&3,P.Z=E[I+0]>>2&3,P.Lc=E[I+0]>>4&3,I=E[I+0]>>6&3,0>P.$a||1<P.$a||4<=P.Z||1<P.Lc||I)c=0;else if(Le.put=Jn,Le.ac=Ht,Le.bc=Kn,Le.ma=P,Le.width=c.width,Le.height=c.height,Le.Da=c.Da,Le.v=c.v,Le.va=c.va,Le.j=c.j,Le.o=c.o,P.$a)e:{e(P.$a==1),c=Pn();t:for(;;){if(c==null){c=0;break e}if(e(P!=null),P.mc=c,c.c=P.c,c.i=P.i,c.l=P.l,c.l.ma=P,c.l.width=P.c,c.l.height=P.i,c.a=0,Q(c.m,E,ge,se),!Zn(P.c,P.i,1,c,null)||(c.ab==1&&c.gc[0].hc==3&&lr(c.s)?(P.ic=1,E=c.c*c.i,c.Ta=null,c.Ua=0,c.V=i(E),c.Ba=0,c.V==null?(c.a=1,c=0):c=1):(P.ic=0,c=Fr(c,P.c)),!c))break t;c=1;break e}P.mc=null,c=0}else c=se>=P.c*P.i;P=!c}if(P)return null;a.ga.Lc!=1?a.Ga=0:m=L-h}e(a.ga!=null),e(h+m<=L);e:{if(c=(E=a.ga).c,L=E.l.o,E.$a==0){if(ge=a.rc,se=a.Vc,Le=a.Fa,I=a.P+1+h*c,D=a.mb,Z=a.nb+h*c,e(I<=a.P+a.qc),E.Z!=0)for(e(pi[E.Z]!=null),P=0;P<m;++P)pi[E.Z](ge,se,Le,I,D,Z,c),ge=D,se=Z,Z+=c,I+=c;else for(P=0;P<m;++P)r(D,Z,Le,I,c),ge=D,se=Z,Z+=c,I+=c;a.rc=ge,a.Vc=se}else{if(e(E.mc!=null),c=h+m,e((P=E.mc)!=null),e(c<=P.i),P.C>=c)c=1;else if(E.ic||fe(),E.ic){E=P.V,ge=P.Ba,se=P.c;var we=P.i,te=(Le=1,I=P.$/se,D=P.$%se,Z=P.m,pe=P.s,P.$),ie=se*we,Ae=se*c,Pe=pe.wc,Ne=te<Ae?Dt(pe,D,I):null;e(te<=ie),e(c<=we),e(lr(pe));t:for(;;){for(;!Z.h&&te<Ae;){if(D&Pe||(Ne=Dt(pe,D,I)),e(Ne!=null),H(Z),256>(we=dn(Ne.G[0],Ne.H[0],Z)))E[ge+te]=we,++te,++D>=se&&(D=0,++I<=c&&!(I%16)&&qn(P,I));else{if(!(280>we)){Le=0;break t}we=On(we-256,Z);var De,Be=dn(Ne.G[4],Ne.H[4],Z);if(H(Z),!(te>=(Be=Xn(se,Be=On(Be,Z)))&&ie-te>=we)){Le=0;break t}for(De=0;De<we;++De)E[ge+te+De]=E[ge+te+De-Be];for(te+=we,D+=we;D>=se;)D-=se,++I<=c&&!(I%16)&&qn(P,I);te<Ae&&D&Pe&&(Ne=Dt(pe,D,I))}e(Z.h==M(Z))}qn(P,I>c?c:I);break t}!Le||Z.h&&te<ie?(Le=0,P.a=Z.h?5:3):P.$=te,c=Le}else c=Un(P,P.V,P.Ba,P.c,P.i,c,Qr);if(!c){m=0;break e}}h+m>=L&&(a.Cc=1),m=1}if(!m)return null;if(a.Cc&&((m=a.ga)!=null&&(m.mc=null),a.ga=null,0<a.Ga))return alert("todo:WebPDequantizeLevels"),null}return a.nb+h*y}function f(a,c,h,m,y,L){for(;0<y--;){var P,E=a,I=c+(h?1:0),D=a,Z=c+(h?0:3);for(P=0;P<m;++P){var pe=D[Z+4*P];pe!=255&&(pe*=32897,E[I+4*P+0]=E[I+4*P+0]*pe>>23,E[I+4*P+1]=E[I+4*P+1]*pe>>23,E[I+4*P+2]=E[I+4*P+2]*pe>>23)}c+=L}}function w(a,c,h,m,y){for(;0<m--;){var L;for(L=0;L<h;++L){var P=a[c+2*L+0],E=15&(D=a[c+2*L+1]),I=4369*E,D=(240&D|D>>4)*I>>16;a[c+2*L+0]=(240&P|P>>4)*I>>16&240|(15&P|P<<4)*I>>16>>4&15,a[c+2*L+1]=240&D|E}c+=y}}function U(a,c,h,m,y,L,P,E){var I,D,Z=255;for(D=0;D<y;++D){for(I=0;I<m;++I){var pe=a[c+I];L[P+4*I]=pe,Z&=pe}c+=h,P+=E}return Z!=255}function K(a,c,h,m,y){var L;for(L=0;L<y;++L)h[m+L]=a[c+L]>>8}function fe(){nr=f,Et=w,It=U,Gt=K}function xe(a,c,h){F[a]=function(m,y,L,P,E,I,D,Z,pe,ge,se,Le,we,te,ie,Ae,Pe){var Ne,De=Pe-1>>1,Be=E[I+0]|D[Z+0]<<16,Ge=pe[ge+0]|se[Le+0]<<16;e(m!=null);var Re=3*Be+Ge+131074>>2;for(c(m[y+0],255&Re,Re>>16,we,te),L!=null&&(Re=3*Ge+Be+131074>>2,c(L[P+0],255&Re,Re>>16,ie,Ae)),Ne=1;Ne<=De;++Ne){var wt=E[I+Ne]|D[Z+Ne]<<16,St=pe[ge+Ne]|se[Le+Ne]<<16,bt=Be+wt+Ge+St+524296,pt=bt+2*(wt+Ge)>>3;Re=pt+Be>>1,Be=(bt=bt+2*(Be+St)>>3)+wt>>1,c(m[y+2*Ne-1],255&Re,Re>>16,we,te+(2*Ne-1)*h),c(m[y+2*Ne-0],255&Be,Be>>16,we,te+(2*Ne-0)*h),L!=null&&(Re=bt+Ge>>1,Be=pt+St>>1,c(L[P+2*Ne-1],255&Re,Re>>16,ie,Ae+(2*Ne-1)*h),c(L[P+2*Ne+0],255&Be,Be>>16,ie,Ae+(2*Ne+0)*h)),Be=wt,Ge=St}1&Pe||(Re=3*Be+Ge+131074>>2,c(m[y+Pe-1],255&Re,Re>>16,we,te+(Pe-1)*h),L!=null&&(Re=3*Ge+Be+131074>>2,c(L[P+Pe-1],255&Re,Re>>16,ie,Ae+(Pe-1)*h)))}}function _e(){ir[Uo]=Vh,ir[zo]=Dc,ir[Bc]=Gh,ir[Ho]=qc,ir[Wo]=Uc,ir[Gs]=zc,ir[Mc]=Yh,ir[Ys]=Dc,ir[$s]=qc,ir[Vo]=Uc,ir[Js]=zc}function Oe(a){return a&-16384?0>a?0:255:a>>$h}function We(a,c){return Oe((19077*a>>8)+(26149*c>>8)-14234)}function ot(a,c,h){return Oe((19077*a>>8)-(6419*c>>8)-(13320*h>>8)+8708)}function it(a,c){return Oe((19077*a>>8)+(33050*c>>8)-17685)}function mt(a,c,h,m,y){m[y+0]=We(a,h),m[y+1]=ot(a,c,h),m[y+2]=it(a,c)}function Ft(a,c,h,m,y){m[y+0]=it(a,c),m[y+1]=ot(a,c,h),m[y+2]=We(a,h)}function qt(a,c,h,m,y){var L=ot(a,c,h);c=L<<3&224|it(a,c)>>3,m[y+0]=248&We(a,h)|L>>5,m[y+1]=c}function Xt(a,c,h,m,y){var L=240&it(a,c)|15;m[y+0]=240&We(a,h)|ot(a,c,h)>>4,m[y+1]=L}function gn(a,c,h,m,y){m[y+0]=255,mt(a,c,h,m,y+1)}function an(a,c,h,m,y){Ft(a,c,h,m,y),m[y+3]=255}function fr(a,c,h,m,y){mt(a,c,h,m,y),m[y+3]=255}function Hn(a,c){return 0>a?0:a>c?c:a}function Nr(a,c,h){F[a]=function(m,y,L,P,E,I,D,Z,pe){for(var ge=Z+(-2&pe)*h;Z!=ge;)c(m[y+0],L[P+0],E[I+0],D,Z),c(m[y+1],L[P+0],E[I+0],D,Z+h),y+=2,++P,++I,Z+=2*h;1&pe&&c(m[y+0],L[P+0],E[I+0],D,Z)}}function Bo(a,c,h){return h==0?a==0?c==0?6:5:c==0?4:0:h}function Ya(a,c,h,m,y){switch(a>>>30){case 3:Hr(c,h,m,y,0);break;case 2:Vt(c,h,m,y);break;case 1:Nn(c,h,m,y)}}function $a(a,c){var h,m,y=c.M,L=c.Nb,P=a.oc,E=a.pc+40,I=a.oc,D=a.pc+584,Z=a.oc,pe=a.pc+600;for(h=0;16>h;++h)P[E+32*h-1]=129;for(h=0;8>h;++h)I[D+32*h-1]=129,Z[pe+32*h-1]=129;for(0<y?P[E-1-32]=I[D-1-32]=Z[pe-1-32]=129:(o(P,E-32-1,127,21),o(I,D-32-1,127,9),o(Z,pe-32-1,127,9)),m=0;m<a.za;++m){var ge=c.ya[c.aa+m];if(0<m){for(h=-1;16>h;++h)r(P,E+32*h-4,P,E+32*h+12,4);for(h=-1;8>h;++h)r(I,D+32*h-4,I,D+32*h+4,4),r(Z,pe+32*h-4,Z,pe+32*h+4,4)}var se=a.Gd,Le=a.Hd+m,we=ge.ad,te=ge.Hc;if(0<y&&(r(P,E-32,se[Le].y,0,16),r(I,D-32,se[Le].f,0,8),r(Z,pe-32,se[Le].ea,0,8)),ge.Za){var ie=P,Ae=E-32+16;for(0<y&&(m>=a.za-1?o(ie,Ae,se[Le].y[15],4):r(ie,Ae,se[Le+1].y,0,4)),h=0;4>h;h++)ie[Ae+128+h]=ie[Ae+256+h]=ie[Ae+384+h]=ie[Ae+0+h];for(h=0;16>h;++h,te<<=2)ie=P,Ae=E+Wc[h],hr[ge.Ob[h]](ie,Ae),Ya(te,we,16*+h,ie,Ae)}else if(ie=Bo(m,y,ge.Ob[0]),di[ie](P,E),te!=0)for(h=0;16>h;++h,te<<=2)Ya(te,we,16*+h,P,E+Wc[h]);for(h=ge.Gc,ie=Bo(m,y,ge.Dd),Vr[ie](I,D),Vr[ie](Z,pe),te=we,ie=I,Ae=D,255&(ge=h>>0)&&(170&ge?Qa(te,256,ie,Ae):er(te,256,ie,Ae)),ge=Z,te=pe,255&(h>>=8)&&(170&h?Qa(we,320,ge,te):er(we,320,ge,te)),y<a.Ub-1&&(r(se[Le].y,0,P,E+480,16),r(se[Le].f,0,I,D+224,8),r(se[Le].ea,0,Z,pe+224,8)),h=8*L*a.B,se=a.sa,Le=a.ta+16*m+16*L*a.R,we=a.qa,ge=a.ra+8*m+h,te=a.Ha,ie=a.Ia+8*m+h,h=0;16>h;++h)r(se,Le+h*a.R,P,E+32*h,16);for(h=0;8>h;++h)r(we,ge+h*a.B,I,D+32*h,8),r(te,ie+h*a.B,Z,pe+32*h,8)}}function la(a,c,h,m,y,L,P,E,I){var D=[0],Z=[0],pe=0,ge=I!=null?I.kd:0,se=I??new Ha;if(a==null||12>h)return 7;se.data=a,se.w=c,se.ha=h,c=[c],h=[h],se.gb=[se.gb];e:{var Le=c,we=h,te=se.gb;if(e(a!=null),e(we!=null),e(te!=null),te[0]=0,12<=we[0]&&!n(a,Le[0],"RIFF")){if(n(a,Le[0]+8,"WEBP")){te=3;break e}var ie=Ce(a,Le[0]+4);if(12>ie||4294967286<ie){te=3;break e}if(ge&&ie>we[0]-8){te=7;break e}te[0]=ie,Le[0]+=12,we[0]-=12}te=0}if(te!=0)return te;for(ie=0<se.gb[0],h=h[0];;){e:{var Ae=a;we=c,te=h;var Pe=D,Ne=Z,De=Le=[0];if((Re=pe=[pe])[0]=0,8>te[0])te=7;else{if(!n(Ae,we[0],"VP8X")){if(Ce(Ae,we[0]+4)!=10){te=3;break e}if(18>te[0]){te=7;break e}var Be=Ce(Ae,we[0]+8),Ge=1+be(Ae,we[0]+12);if(2147483648<=Ge*(Ae=1+be(Ae,we[0]+15))){te=3;break e}De!=null&&(De[0]=Be),Pe!=null&&(Pe[0]=Ge),Ne!=null&&(Ne[0]=Ae),we[0]+=18,te[0]-=18,Re[0]=1}te=0}}if(pe=pe[0],Le=Le[0],te!=0)return te;if(we=!!(2&Le),!ie&&pe)return 3;if(L!=null&&(L[0]=!!(16&Le)),P!=null&&(P[0]=we),E!=null&&(E[0]=0),P=D[0],Le=Z[0],pe&&we&&I==null){te=0;break}if(4>h){te=7;break}if(ie&&pe||!ie&&!pe&&!n(a,c[0],"ALPH")){h=[h],se.na=[se.na],se.P=[se.P],se.Sa=[se.Sa];e:{Be=a,te=c,ie=h;var Re=se.gb;Pe=se.na,Ne=se.P,De=se.Sa,Ge=22,e(Be!=null),e(ie!=null),Ae=te[0];var wt=ie[0];for(e(Pe!=null),e(De!=null),Pe[0]=null,Ne[0]=null,De[0]=0;;){if(te[0]=Ae,ie[0]=wt,8>wt){te=7;break e}var St=Ce(Be,Ae+4);if(4294967286<St){te=3;break e}var bt=8+St+1&-2;if(Ge+=bt,0<Re&&Ge>Re){te=3;break e}if(!n(Be,Ae,"VP8 ")||!n(Be,Ae,"VP8L")){te=0;break e}if(wt[0]<bt){te=7;break e}n(Be,Ae,"ALPH")||(Pe[0]=Be,Ne[0]=Ae+8,De[0]=St),Ae+=bt,wt-=bt}}if(h=h[0],se.na=se.na[0],se.P=se.P[0],se.Sa=se.Sa[0],te!=0)break}h=[h],se.Ja=[se.Ja],se.xa=[se.xa];e:if(Re=a,te=c,ie=h,Pe=se.gb[0],Ne=se.Ja,De=se.xa,Be=te[0],Ae=!n(Re,Be,"VP8 "),Ge=!n(Re,Be,"VP8L"),e(Re!=null),e(ie!=null),e(Ne!=null),e(De!=null),8>ie[0])te=7;else{if(Ae||Ge){if(Re=Ce(Re,Be+4),12<=Pe&&Re>Pe-12){te=3;break e}if(ge&&Re>ie[0]-8){te=7;break e}Ne[0]=Re,te[0]+=8,ie[0]-=8,De[0]=Ge}else De[0]=5<=ie[0]&&Re[Be+0]==47&&!(Re[Be+4]>>5),Ne[0]=ie[0];te=0}if(h=h[0],se.Ja=se.Ja[0],se.xa=se.xa[0],c=c[0],te!=0)break;if(4294967286<se.Ja)return 3;if(E==null||we||(E[0]=se.xa?2:1),P=[P],Le=[Le],se.xa){if(5>h){te=7;break}E=P,ge=Le,we=L,a==null||5>h?a=0:5<=h&&a[c+0]==47&&!(a[c+4]>>5)?(ie=[0],Re=[0],Pe=[0],Q(Ne=new x,a,c,h),Ve(Ne,ie,Re,Pe)?(E!=null&&(E[0]=ie[0]),ge!=null&&(ge[0]=Re[0]),we!=null&&(we[0]=Pe[0]),a=1):a=0):a=0}else{if(10>h){te=7;break}E=Le,a==null||10>h||!Or(a,c+3,h-3)?a=0:(ge=a[c+0]|a[c+1]<<8|a[c+2]<<16,we=16383&(a[c+7]<<8|a[c+6]),a=16383&(a[c+9]<<8|a[c+8]),1&ge||3<(ge>>1&7)||!(ge>>4&1)||ge>>5>=se.Ja||!we||!a?a=0:(P&&(P[0]=we),E&&(E[0]=a),a=1))}if(!a||(P=P[0],Le=Le[0],pe&&(D[0]!=P||Z[0]!=Le)))return 3;I!=null&&(I[0]=se,I.offset=c-I.w,e(4294967286>c-I.w),e(I.offset==I.ha-h));break}return te==0||te==7&&pe&&I==null?(L!=null&&(L[0]|=se.na!=null&&0<se.na.length),m!=null&&(m[0]=P),y!=null&&(y[0]=Le),0):te}function Ja(a,c,h){var m=c.width,y=c.height,L=0,P=0,E=m,I=y;if(c.Da=a!=null&&0<a.Da,c.Da&&(E=a.cd,I=a.bd,L=a.v,P=a.j,11>h||(L&=-2,P&=-2),0>L||0>P||0>=E||0>=I||L+E>m||P+I>y))return 0;if(c.v=L,c.j=P,c.va=L+E,c.o=P+I,c.U=E,c.T=I,c.da=a!=null&&0<a.da,c.da){if(!Ze(E,I,h=[a.ib],L=[a.hb]))return 0;c.ib=h[0],c.hb=L[0]}return c.ob=a!=null&&a.ob,c.Kb=a==null||!a.Sd,c.da&&(c.ob=c.ib<3*m/4&&c.hb<3*y/4,c.Kb=0),1}function Ka(a){if(a==null)return 2;if(11>a.S){var c=a.f.RGBA;c.fb+=(a.height-1)*c.A,c.A=-c.A}else c=a.f.kb,a=a.height,c.O+=(a-1)*c.fa,c.fa=-c.fa,c.N+=(a-1>>1)*c.Ab,c.Ab=-c.Ab,c.W+=(a-1>>1)*c.Db,c.Db=-c.Db,c.F!=null&&(c.J+=(a-1)*c.lb,c.lb=-c.lb);return 0}function ca(a,c,h,m){if(m==null||0>=a||0>=c)return 2;if(h!=null){if(h.Da){var y=h.cd,L=h.bd,P=-2&h.v,E=-2&h.j;if(0>P||0>E||0>=y||0>=L||P+y>a||E+L>c)return 2;a=y,c=L}if(h.da){if(!Ze(a,c,y=[h.ib],L=[h.hb]))return 2;a=y[0],c=L[0]}}m.width=a,m.height=c;e:{var I=m.width,D=m.height;if(a=m.S,0>=I||0>=D||!(a>=Uo&&13>a))a=2;else{if(0>=m.Rd&&m.sd==null){P=L=y=c=0;var Z=(E=I*Vc[a])*D;if(11>a||(L=(D+1)/2*(c=(I+1)/2),a==12&&(P=(y=I)*D)),(D=i(Z+2*L+P))==null){a=1;break e}m.sd=D,11>a?((I=m.f.RGBA).eb=D,I.fb=0,I.A=E,I.size=Z):((I=m.f.kb).y=D,I.O=0,I.fa=E,I.Fd=Z,I.f=D,I.N=0+Z,I.Ab=c,I.Cd=L,I.ea=D,I.W=0+Z+L,I.Db=c,I.Ed=L,a==12&&(I.F=D,I.J=0+Z+2*L),I.Tc=P,I.lb=y)}if(c=1,y=m.S,L=m.width,P=m.height,y>=Uo&&13>y)if(11>y)a=m.f.RGBA,c&=(E=Math.abs(a.A))*(P-1)+L<=a.size,c&=E>=L*Vc[y],c&=a.eb!=null;else{a=m.f.kb,E=(L+1)/2,Z=(P+1)/2,I=Math.abs(a.fa),D=Math.abs(a.Ab);var pe=Math.abs(a.Db),ge=Math.abs(a.lb),se=ge*(P-1)+L;c&=I*(P-1)+L<=a.Fd,c&=D*(Z-1)+E<=a.Cd,c=(c&=pe*(Z-1)+E<=a.Ed)&I>=L&D>=E&pe>=E,c&=a.y!=null,c&=a.f!=null,c&=a.ea!=null,y==12&&(c&=ge>=L,c&=se<=a.Tc,c&=a.F!=null)}else c=0;a=c?0:2}}return a!=0||h!=null&&h.fd&&(a=Ka(m)),a}var un=64,ua=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,16777215],fa=24,ha=32,Xa=8,En=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];Ie("Predictor0","PredictorAdd0"),F.Predictor0=function(){return **********},F.Predictor1=function(a){return a},F.Predictor2=function(a,c,h){return c[h+0]},F.Predictor3=function(a,c,h){return c[h+1]},F.Predictor4=function(a,c,h){return c[h-1]},F.Predictor5=function(a,c,h){return Te(Te(a,c[h+1]),c[h+0])},F.Predictor6=function(a,c,h){return Te(a,c[h-1])},F.Predictor7=function(a,c,h){return Te(a,c[h+0])},F.Predictor8=function(a,c,h){return Te(c[h-1],c[h+0])},F.Predictor9=function(a,c,h){return Te(c[h+0],c[h+1])},F.Predictor10=function(a,c,h){return Te(Te(a,c[h-1]),Te(c[h+0],c[h+1]))},F.Predictor11=function(a,c,h){var m=c[h+0];return 0>=et(m>>24&255,a>>24&255,(c=c[h-1])>>24&255)+et(m>>16&255,a>>16&255,c>>16&255)+et(m>>8&255,a>>8&255,c>>8&255)+et(255&m,255&a,255&c)?m:a},F.Predictor12=function(a,c,h){var m=c[h+0];return(He((a>>24&255)+(m>>24&255)-((c=c[h-1])>>24&255))<<24|He((a>>16&255)+(m>>16&255)-(c>>16&255))<<16|He((a>>8&255)+(m>>8&255)-(c>>8&255))<<8|He((255&a)+(255&m)-(255&c)))>>>0},F.Predictor13=function(a,c,h){var m=c[h-1];return(Qe((a=Te(a,c[h+0]))>>24&255,m>>24&255)<<24|Qe(a>>16&255,m>>16&255)<<16|Qe(a>>8&255,m>>8&255)<<8|Qe(a>>0&255,m>>0&255))>>>0};var Ws=F.PredictorAdd0;F.PredictorAdd1=rt,Ie("Predictor2","PredictorAdd2"),Ie("Predictor3","PredictorAdd3"),Ie("Predictor4","PredictorAdd4"),Ie("Predictor5","PredictorAdd5"),Ie("Predictor6","PredictorAdd6"),Ie("Predictor7","PredictorAdd7"),Ie("Predictor8","PredictorAdd8"),Ie("Predictor9","PredictorAdd9"),Ie("Predictor10","PredictorAdd10"),Ie("Predictor11","PredictorAdd11"),Ie("Predictor12","PredictorAdd12"),Ie("Predictor13","PredictorAdd13");var Za=F.PredictorAdd2;st("ColorIndexInverseTransform","MapARGB","32b",function(a){return a>>8&255},function(a){return a}),st("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",function(a){return a},function(a){return a>>8&255});var Mo,Qn=F.ColorIndexInverseTransform,da=F.MapARGB,Ro=F.VP8LColorIndexInverseTransformAlpha,Fo=F.MapAlpha,ci=F.VP8LPredictorsAdd=[];ci.length=16,(F.VP8LPredictors=[]).length=16,(F.VP8LPredictorsAdd_C=[]).length=16,(F.VP8LPredictors_C=[]).length=16;var Fi,In,Sn,ui,Ur,zr,pa,Hr,Vt,Qa,Nn,er,ga,Oo,eo,Oi,Di,fi,qi,ma,Ui,hi,to,tr,nr,Et,It,Gt,tn=i(511),Wr=i(2041),no=i(225),ba=i(767),Do=0,Vs=Wr,qo=no,Wn=ba,rr=tn,Uo=0,zo=1,Bc=2,Ho=3,Wo=4,Gs=5,Mc=6,Ys=7,$s=8,Vo=9,Js=10,Ih=[2,3,7],Th=[3,3,11],Rc=[280,256,256,256,40],jh=[0,1,1,1,0],Bh=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],Mh=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],Rh=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],Fh=8,Ks=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],Xs=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],ro=null,Oh=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],Dh=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],Fc=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],qh=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],Uh=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],zh=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],Hh=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],di=[],hr=[],Vr=[],Wh=1,Oc=2,pi=[],ir=[];xe("UpsampleRgbLinePair",mt,3),xe("UpsampleBgrLinePair",Ft,3),xe("UpsampleRgbaLinePair",fr,4),xe("UpsampleBgraLinePair",an,4),xe("UpsampleArgbLinePair",gn,4),xe("UpsampleRgba4444LinePair",Xt,2),xe("UpsampleRgb565LinePair",qt,2);var Vh=F.UpsampleRgbLinePair,Gh=F.UpsampleBgrLinePair,Dc=F.UpsampleRgbaLinePair,qc=F.UpsampleBgraLinePair,Uc=F.UpsampleArgbLinePair,zc=F.UpsampleRgba4444LinePair,Yh=F.UpsampleRgb565LinePair,Go=16,Yo=1<<Go-1,io=-227,Zs=482,$h=6,Hc=0,Jh=i(256),Kh=i(256),Xh=i(256),Zh=i(256),Qh=i(Zs-io),ed=i(Zs-io);Nr("YuvToRgbRow",mt,3),Nr("YuvToBgrRow",Ft,3),Nr("YuvToRgbaRow",fr,4),Nr("YuvToBgraRow",an,4),Nr("YuvToArgbRow",gn,4),Nr("YuvToRgba4444Row",Xt,2),Nr("YuvToRgb565Row",qt,2);var Wc=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],$o=[0,2,8],td=[8,7,6,4,4,2,2,2,1,1,1,1],nd=1;this.WebPDecodeRGBA=function(a,c,h,m,y){var L=zo,P=new oa,E=new Fn;P.ba=E,E.S=L,E.width=[E.width],E.height=[E.height];var I=E.width,D=E.height,Z=new sr;if(Z==null||a==null)var pe=2;else e(Z!=null),pe=la(a,c,h,Z.width,Z.height,Z.Pd,Z.Qd,Z.format,null);if(pe!=0?I=0:(I!=null&&(I[0]=Z.width[0]),D!=null&&(D[0]=Z.height[0]),I=1),I){E.width=E.width[0],E.height=E.height[0],m!=null&&(m[0]=E.width),y!=null&&(y[0]=E.height);e:{if(m=new kn,(y=new Ha).data=a,y.w=c,y.ha=h,y.kd=1,c=[0],e(y!=null),((a=la(y.data,y.w,y.ha,null,null,null,c,null,y))==0||a==7)&&c[0]&&(a=4),(c=a)==0){if(e(P!=null),m.data=y.data,m.w=y.w+y.offset,m.ha=y.ha-y.offset,m.put=Jn,m.ac=Ht,m.bc=Kn,m.ma=P,y.xa){if((a=Pn())==null){P=1;break e}if(function(ge,se){var Le=[0],we=[0],te=[0];t:for(;;){if(ge==null)return 0;if(se==null)return ge.a=2,0;if(ge.l=se,ge.a=0,Q(ge.m,se.data,se.w,se.ha),!Ve(ge.m,Le,we,te)){ge.a=3;break t}if(ge.xb=Oc,se.width=Le[0],se.height=we[0],!Zn(Le[0],we[0],1,ge,null))break t;return 1}return e(ge.a!=0),0}(a,m)){if(m=(c=ca(m.width,m.height,P.Oa,P.ba))==0){t:{m=a;n:for(;;){if(m==null){m=0;break t}if(e(m.s.yc!=null),e(m.s.Ya!=null),e(0<m.s.Wb),e((h=m.l)!=null),e((y=h.ma)!=null),m.xb!=0){if(m.ca=y.ba,m.tb=y.tb,e(m.ca!=null),!Ja(y.Oa,h,Ho)){m.a=2;break n}if(!Fr(m,h.width)||h.da)break n;if((h.da||vt(m.ca.S))&&fe(),11>m.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),m.ca.f.kb.F!=null&&fe()),m.Pb&&0<m.s.ua&&m.s.vb.X==null&&!ze(m.s.vb,m.s.Wa.Xa)){m.a=1;break n}m.xb=0}if(!Un(m,m.V,m.Ba,m.c,m.i,h.o,Rr))break n;y.Dc=m.Ma,m=1;break t}e(m.a!=0),m=0}m=!m}m&&(c=a.a)}else c=a.a}else{if((a=new ii)==null){P=1;break e}if(a.Fa=y.na,a.P=y.P,a.qc=y.Sa,Ti(a,m)){if((c=ca(m.width,m.height,P.Oa,P.ba))==0){if(a.Aa=0,h=P.Oa,e((y=a)!=null),h!=null){if(0<(I=0>(I=h.Md)?0:100<I?255:255*I/100)){for(D=Z=0;4>D;++D)12>(pe=y.pb[D]).lc&&(pe.ia=I*td[0>pe.lc?0:pe.lc]>>3),Z|=pe.ia;Z&&(alert("todo:VP8InitRandom"),y.ia=1)}y.Ga=h.Id,100<y.Ga?y.Ga=100:0>y.Ga&&(y.Ga=0)}Ps(a,m)||(c=a.a)}}else c=a.a}c==0&&P.Oa!=null&&P.Oa.fd&&(c=Ka(P.ba))}P=c}L=P!=0?null:11>L?E.f.RGBA.eb:E.f.kb.y}else L=null;return L};var Vc=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function p(F,J){for(var re="",N=0;N<4;N++)re+=String.fromCharCode(F[J++]);return re}function g(F,J){return(F[J+0]<<0|F[J+1]<<8|F[J+2]<<16)>>>0}function b(F,J){return(F[J+0]<<0|F[J+1]<<8|F[J+2]<<16|F[J+3]<<24)>>>0}new u;var v=[0],d=[0],_=[],S=new u,T=t,A=function(F,J){var re={},N=0,k=!1,O=0,C=0;if(re.frames=[],!function(B,M,z,H){for(var G=0;G<H;G++)if(B[M+G]!=z.charCodeAt(G))return!0;return!1}(F,J,"RIFF",4)){for(b(F,J+=4),J+=8;J<F.length;){var X=p(F,J),ne=b(F,J+=4);J+=4;var ue=ne+(1&ne);switch(X){case"VP8 ":case"VP8L":re.frames[N]===void 0&&(re.frames[N]={}),(V=re.frames[N]).src_off=k?C:J-8,V.src_size=O+ne+8,N++,k&&(k=!1,O=0,C=0);break;case"VP8X":(V=re.header={}).feature_flags=F[J];var Q=J+4;V.canvas_width=1+g(F,Q),Q+=3,V.canvas_height=1+g(F,Q),Q+=3;break;case"ALPH":k=!0,O=ue+8,C=J-8;break;case"ANIM":(V=re.header).bgcolor=b(F,J),Q=J+4,V.loop_count=(me=F)[(x=Q)+0]<<0|me[x+1]<<8,Q+=2;break;case"ANMF":var he,V;(V=re.frames[N]={}).offset_x=2*g(F,J),J+=3,V.offset_y=2*g(F,J),J+=3,V.width=1+g(F,J),J+=3,V.height=1+g(F,J),J+=3,V.duration=g(F,J),J+=3,he=F[J++],V.dispose=1&he,V.blend=he>>1&1}X!="ANMF"&&(J+=ue)}var me,x;return re}}(T,0);A.response=T,A.rgbaoutput=!0,A.dataurl=!1;var j=A.header?A.header:null,q=A.frames?A.frames:null;if(j){j.loop_counter=j.loop_count,v=[j.canvas_height],d=[j.canvas_width];for(var Y=0;Y<q.length&&q[Y].blend!=0;Y++);}var le=q[0],ae=S.WebPDecodeRGBA(T,le.src_off,le.src_size,d,v);le.rgba=ae,le.imgwidth=d[0],le.imgheight=v[0];for(var $=0;$<d[0]*v[0]*4;$++)_[$]=ae[$];return this.width=d,this.height=v,this.data=_,this}(function(t){var e=function(){return typeof hc=="function"},n=function(v,d,_,S){var T=4,A=s;switch(S){case t.image_compression.FAST:T=1,A=i;break;case t.image_compression.MEDIUM:T=6,A=l;break;case t.image_compression.SLOW:T=9,A=u}v=r(v,d,_,A);var j=hc(v,{level:T});return t.__addimage__.arrayBufferToBinaryString(j)},r=function(v,d,_,S){for(var T,A,j,q=v.length/d,Y=new Uint8Array(v.length+q),le=g(),ae=0;ae<q;ae+=1){if(j=ae*d,T=v.subarray(j,j+d),S)Y.set(S(T,_,A),j+ae);else{for(var $,F=le.length,J=[];$<F;$+=1)J[$]=le[$](T,_,A);var re=b(J.concat());Y.set(J[re],j+ae)}A=T}return Y},o=function(v){var d=Array.apply([],v);return d.unshift(0),d},i=function(v,d){var _,S=[],T=v.length;S[0]=1;for(var A=0;A<T;A+=1)_=v[A-d]||0,S[A+1]=v[A]-_+256&255;return S},s=function(v,d,_){var S,T=[],A=v.length;T[0]=2;for(var j=0;j<A;j+=1)S=_&&_[j]||0,T[j+1]=v[j]-S+256&255;return T},l=function(v,d,_){var S,T,A=[],j=v.length;A[0]=3;for(var q=0;q<j;q+=1)S=v[q-d]||0,T=_&&_[q]||0,A[q+1]=v[q]+256-(S+T>>>1)&255;return A},u=function(v,d,_){var S,T,A,j,q=[],Y=v.length;q[0]=4;for(var le=0;le<Y;le+=1)S=v[le-d]||0,T=_&&_[le]||0,A=_&&_[le-d]||0,j=p(S,T,A),q[le+1]=v[le]-j+256&255;return q},p=function(v,d,_){if(v===d&&d===_)return v;var S=Math.abs(d-_),T=Math.abs(v-_),A=Math.abs(v+d-_-_);return S<=T&&S<=A?v:T<=A?d:_},g=function(){return[o,i,s,l,u]},b=function(v){var d=v.map(function(_){return _.reduce(function(S,T){return S+Math.abs(T)},0)});return d.indexOf(Math.min.apply(null,d))};t.processPNG=function(v,d,_,S){var T,A,j,q,Y,le,ae,$,F,J,re,N,k,O,C,X=this.decode.FLATE_DECODE,ne="";if(this.__addimage__.isArrayBuffer(v)&&(v=new Uint8Array(v)),this.__addimage__.isArrayBufferView(v)){if(v=(j=new e5(v)).imgData,A=j.bits,T=j.colorSpace,Y=j.colors,[4,6].indexOf(j.colorType)!==-1){if(j.bits===8){F=($=j.pixelBitlength==32?new Uint32Array(j.decodePixels().buffer):j.pixelBitlength==16?new Uint16Array(j.decodePixels().buffer):new Uint8Array(j.decodePixels().buffer)).length,re=new Uint8Array(F*j.colors),J=new Uint8Array(F);var ue,Q=j.pixelBitlength-j.bits;for(O=0,C=0;O<F;O++){for(k=$[O],ue=0;ue<Q;)re[C++]=k>>>ue&255,ue+=j.bits;J[O]=k>>>ue&255}}if(j.bits===16){F=($=new Uint32Array(j.decodePixels().buffer)).length,re=new Uint8Array(F*(32/j.pixelBitlength)*j.colors),J=new Uint8Array(F*(32/j.pixelBitlength)),N=j.colors>1,O=0,C=0;for(var he=0;O<F;)k=$[O++],re[C++]=k>>>0&255,N&&(re[C++]=k>>>16&255,k=$[O++],re[C++]=k>>>0&255),J[he++]=k>>>16&255;A=8}S!==t.image_compression.NONE&&e()?(v=n(re,j.width*j.colors,j.colors,S),ae=n(J,j.width,1,S)):(v=re,ae=J,X=void 0)}if(j.colorType===3&&(T=this.color_spaces.INDEXED,le=j.palette,j.transparency.indexed)){var V=j.transparency.indexed,me=0;for(O=0,F=V.length;O<F;++O)me+=V[O];if((me/=255)===F-1&&V.indexOf(0)!==-1)q=[V.indexOf(0)];else if(me!==F){for($=j.decodePixels(),J=new Uint8Array($.length),O=0,F=$.length;O<F;O++)J[O]=V[$[O]];ae=n(J,j.width,1)}}var x=function(B){var M;switch(B){case t.image_compression.FAST:M=11;break;case t.image_compression.MEDIUM:M=13;break;case t.image_compression.SLOW:M=14;break;default:M=12}return M}(S);return X===this.decode.FLATE_DECODE&&(ne="/Predictor "+x+" "),ne+="/Colors "+Y+" /BitsPerComponent "+A+" /Columns "+j.width,(this.__addimage__.isArrayBuffer(v)||this.__addimage__.isArrayBufferView(v))&&(v=this.__addimage__.arrayBufferToBinaryString(v)),(ae&&this.__addimage__.isArrayBuffer(ae)||this.__addimage__.isArrayBufferView(ae))&&(ae=this.__addimage__.arrayBufferToBinaryString(ae)),{alias:_,data:v,index:d,filter:X,decodeParameters:ne,transparency:q,palette:le,sMask:ae,predictor:x,width:j.width,height:j.height,bitsPerComponent:A,colorSpace:T}}}})(Ke.API),function(t){t.processGIF89A=function(e,n,r,o){var i=new t5(e),s=i.width,l=i.height,u=[];i.decodeAndBlitFrameRGBA(0,u);var p={data:u,width:s,height:l},g=new kl(100).encode(p,100);return t.processJPEG.call(this,g,n,r,o)},t.processGIF87A=t.processGIF89A}(Ke.API),gr.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.bitPP===16&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var t=this.colors===0?1<<this.bitPP:this.colors;this.palette=new Array(t);for(var e=0;e<t;e++){var n=this.datav.getUint8(this.pos++,!0),r=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0);this.palette[e]={red:o,green:r,blue:n,quad:i}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},gr.prototype.parseBGR=function(){this.pos=this.offset;try{var t="bit"+this.bitPP,e=this.width*this.height*4;this.data=new Uint8Array(e),this[t]()}catch(n){kt.log("bit decode error:"+n)}},gr.prototype.bit1=function(){var t,e=Math.ceil(this.width/8),n=e%4;for(t=this.height-1;t>=0;t--){for(var r=this.bottom_up?t:this.height-1-t,o=0;o<e;o++)for(var i=this.datav.getUint8(this.pos++,!0),s=r*this.width*4+8*o*4,l=0;l<8&&8*o+l<this.width;l++){var u=this.palette[i>>7-l&1];this.data[s+4*l]=u.blue,this.data[s+4*l+1]=u.green,this.data[s+4*l+2]=u.red,this.data[s+4*l+3]=255}n!==0&&(this.pos+=4-n)}},gr.prototype.bit4=function(){for(var t=Math.ceil(this.width/2),e=t%4,n=this.height-1;n>=0;n--){for(var r=this.bottom_up?n:this.height-1-n,o=0;o<t;o++){var i=this.datav.getUint8(this.pos++,!0),s=r*this.width*4+2*o*4,l=i>>4,u=15&i,p=this.palette[l];if(this.data[s]=p.blue,this.data[s+1]=p.green,this.data[s+2]=p.red,this.data[s+3]=255,2*o+1>=this.width)break;p=this.palette[u],this.data[s+4]=p.blue,this.data[s+4+1]=p.green,this.data[s+4+2]=p.red,this.data[s+4+3]=255}e!==0&&(this.pos+=4-e)}},gr.prototype.bit8=function(){for(var t=this.width%4,e=this.height-1;e>=0;e--){for(var n=this.bottom_up?e:this.height-1-e,r=0;r<this.width;r++){var o=this.datav.getUint8(this.pos++,!0),i=n*this.width*4+4*r;if(o<this.palette.length){var s=this.palette[o];this.data[i]=s.red,this.data[i+1]=s.green,this.data[i+2]=s.blue,this.data[i+3]=255}else this.data[i]=255,this.data[i+1]=255,this.data[i+2]=255,this.data[i+3]=255}t!==0&&(this.pos+=4-t)}},gr.prototype.bit15=function(){for(var t=this.width%3,e=parseInt("11111",2),n=this.height-1;n>=0;n--){for(var r=this.bottom_up?n:this.height-1-n,o=0;o<this.width;o++){var i=this.datav.getUint16(this.pos,!0);this.pos+=2;var s=(i&e)/e*255|0,l=(i>>5&e)/e*255|0,u=(i>>10&e)/e*255|0,p=i>>15?255:0,g=r*this.width*4+4*o;this.data[g]=u,this.data[g+1]=l,this.data[g+2]=s,this.data[g+3]=p}this.pos+=t}},gr.prototype.bit16=function(){for(var t=this.width%3,e=parseInt("11111",2),n=parseInt("111111",2),r=this.height-1;r>=0;r--){for(var o=this.bottom_up?r:this.height-1-r,i=0;i<this.width;i++){var s=this.datav.getUint16(this.pos,!0);this.pos+=2;var l=(s&e)/e*255|0,u=(s>>5&n)/n*255|0,p=(s>>11)/e*255|0,g=o*this.width*4+4*i;this.data[g]=p,this.data[g+1]=u,this.data[g+2]=l,this.data[g+3]=255}this.pos+=t}},gr.prototype.bit24=function(){for(var t=this.height-1;t>=0;t--){for(var e=this.bottom_up?t:this.height-1-t,n=0;n<this.width;n++){var r=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),s=e*this.width*4+4*n;this.data[s]=i,this.data[s+1]=o,this.data[s+2]=r,this.data[s+3]=255}this.pos+=this.width%4}},gr.prototype.bit32=function(){for(var t=this.height-1;t>=0;t--)for(var e=this.bottom_up?t:this.height-1-t,n=0;n<this.width;n++){var r=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),s=this.datav.getUint8(this.pos++,!0),l=e*this.width*4+4*n;this.data[l]=i,this.data[l+1]=o,this.data[l+2]=r,this.data[l+3]=s}},gr.prototype.getData=function(){return this.data},function(t){t.processBMP=function(e,n,r,o){var i=new gr(e,!1),s=i.width,l=i.height,u={data:i.getData(),width:s,height:l},p=new kl(100).encode(u,100);return t.processJPEG.call(this,p,n,r,o)}}(Ke.API),ef.prototype.getData=function(){return this.data},function(t){t.processWEBP=function(e,n,r,o){var i=new ef(e),s=i.width,l=i.height,u={data:i.getData(),width:s,height:l},p=new kl(100).encode(u,100);return t.processJPEG.call(this,p,n,r,o)}}(Ke.API),Ke.API.processRGBA=function(t,e,n){for(var r=t.data,o=r.length,i=new Uint8Array(o/4*3),s=new Uint8Array(o/4),l=0,u=0,p=0;p<o;p+=4){var g=r[p],b=r[p+1],v=r[p+2],d=r[p+3];i[l++]=g,i[l++]=b,i[l++]=v,s[u++]=d}var _=this.__addimage__.arrayBufferToBinaryString(i);return{alpha:this.__addimage__.arrayBufferToBinaryString(s),data:_,index:e,alias:n,colorSpace:"DeviceRGB",bitsPerComponent:8,width:t.width,height:t.height}},Ke.API.setLanguage=function(t){return this.internal.languageSettings===void 0&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),{af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"}[t]!==void 0&&(this.internal.languageSettings.languageCode=t,this.internal.languageSettings.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},Na=Ke.API,cs=Na.getCharWidthsArray=function(t,e){var n,r,o=(e=e||{}).font||this.internal.getFont(),i=e.fontSize||this.internal.getFontSize(),s=e.charSpace||this.internal.getCharSpace(),l=e.widths?e.widths:o.metadata.Unicode.widths,u=l.fof?l.fof:1,p=e.kerning?e.kerning:o.metadata.Unicode.kerning,g=p.fof?p.fof:1,b=e.doKerning!==!1,v=0,d=t.length,_=0,S=l[0]||u,T=[];for(n=0;n<d;n++)r=t.charCodeAt(n),typeof o.metadata.widthOfString=="function"?T.push((o.metadata.widthOfGlyph(o.metadata.characterToGlyph(r))+s*(1e3/i)||0)/1e3):(v=b&&Lt(p[r])==="object"&&!isNaN(parseInt(p[r][_],10))?p[r][_]/g:0,T.push((l[r]||S)/u+v)),_=r;return T},Ku=Na.getStringUnitWidth=function(t,e){var n=(e=e||{}).fontSize||this.internal.getFontSize(),r=e.font||this.internal.getFont(),o=e.charSpace||this.internal.getCharSpace();return Na.processArabic&&(t=Na.processArabic(t)),typeof r.metadata.widthOfString=="function"?r.metadata.widthOfString(t,n,o)/n:cs.apply(this,arguments).reduce(function(i,s){return i+s},0)},Xu=function(t,e,n,r){for(var o=[],i=0,s=t.length,l=0;i!==s&&l+e[i]<n;)l+=e[i],i++;o.push(t.slice(0,i));var u=i;for(l=0;i!==s;)l+e[i]>r&&(o.push(t.slice(u,i)),l=0,u=i),l+=e[i],i++;return u!==i&&o.push(t.slice(u,i)),o},Zu=function(t,e,n){n||(n={});var r,o,i,s,l,u,p,g=[],b=[g],v=n.textIndent||0,d=0,_=0,S=t.split(" "),T=cs.apply(this,[" ",n])[0];if(u=n.lineIndent===-1?S[0].length+2:n.lineIndent||0){var A=Array(u).join(" "),j=[];S.map(function(Y){(Y=Y.split(/\s*\n/)).length>1?j=j.concat(Y.map(function(le,ae){return(ae&&le.length?`
`:"")+le})):j.push(Y[0])}),S=j,u=Ku.apply(this,[A,n])}for(i=0,s=S.length;i<s;i++){var q=0;if(r=S[i],u&&r[0]==`
`&&(r=r.substr(1),q=1),v+d+(_=(o=cs.apply(this,[r,n])).reduce(function(Y,le){return Y+le},0))>e||q){if(_>e){for(l=Xu.apply(this,[r,o,e-(v+d),e]),g.push(l.shift()),g=[l.pop()];l.length;)b.push([l.shift()]);_=o.slice(r.length-(g[0]?g[0].length:0)).reduce(function(Y,le){return Y+le},0)}else g=[r];b.push(g),v=_+u,d=T}else g.push(r),v+=d+_,d=T}return p=u?function(Y,le){return(le?A:"")+Y.join(" ")}:function(Y){return Y.join(" ")},b.map(p)},Na.splitTextToSize=function(t,e,n){var r,o=(n=n||{}).fontSize||this.internal.getFontSize(),i=(function(g){if(g.widths&&g.kerning)return{widths:g.widths,kerning:g.kerning};var b=this.internal.getFont(g.fontName,g.fontStyle);return b.metadata.Unicode?{widths:b.metadata.Unicode.widths||{0:1},kerning:b.metadata.Unicode.kerning||{}}:{font:b.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}).call(this,n);r=Array.isArray(t)?t:String(t).split(/\r?\n/);var s=1*this.internal.scaleFactor*e/o;i.textIndent=n.textIndent?1*n.textIndent*this.internal.scaleFactor/o:0,i.lineIndent=n.lineIndent;var l,u,p=[];for(l=0,u=r.length;l<u;l++)p=p.concat(Zu.apply(this,[r[l],s,i]));return p},function(t){t.__fontmetrics__=t.__fontmetrics__||{};for(var e="klmnopqrstuvwxyz",n={},r={},o=0;o<e.length;o++)n[e[o]]="**********abcdef"[o],r["**********abcdef"[o]]=e[o];var i=function(b){return"0x"+parseInt(b,10).toString(16)},s=t.__fontmetrics__.compress=function(b){var v,d,_,S,T=["{"];for(var A in b){if(v=b[A],isNaN(parseInt(A,10))?d="'"+A+"'":(A=parseInt(A,10),d=(d=i(A).slice(2)).slice(0,-1)+r[d.slice(-1)]),typeof v=="number")v<0?(_=i(v).slice(3),S="-"):(_=i(v).slice(2),S=""),_=S+_.slice(0,-1)+r[_.slice(-1)];else{if(Lt(v)!=="object")throw new Error("Don't know what to do with value type "+Lt(v)+".");_=s(v)}T.push(d+_)}return T.push("}"),T.join("")},l=t.__fontmetrics__.uncompress=function(b){if(typeof b!="string")throw new Error("Invalid argument passed to uncompress.");for(var v,d,_,S,T={},A=1,j=T,q=[],Y="",le="",ae=b.length-1,$=1;$<ae;$+=1)(S=b[$])=="'"?v?(_=v.join(""),v=void 0):v=[]:v?v.push(S):S=="{"?(q.push([j,_]),j={},_=void 0):S=="}"?((d=q.pop())[0][d[1]]=j,_=void 0,j=d[0]):S=="-"?A=-1:_===void 0?n.hasOwnProperty(S)?(Y+=n[S],_=parseInt(Y,16)*A,A=1,Y=""):Y+=S:n.hasOwnProperty(S)?(le+=n[S],j[_]=parseInt(le,16)*A,A=1,_=void 0,le=""):le+=S;return T},u={codePages:["WinAnsiEncoding"],WinAnsiEncoding:l("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},p={Unicode:{Courier:u,"Courier-Bold":u,"Courier-BoldOblique":u,"Courier-Oblique":u,Helvetica:u,"Helvetica-Bold":u,"Helvetica-BoldOblique":u,"Helvetica-Oblique":u,"Times-Roman":u,"Times-Bold":u,"Times-BoldItalic":u,"Times-Italic":u}},g={Unicode:{"Courier-Oblique":l("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":l("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":l("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:l("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":l("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":l("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:l("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:l("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":l("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:l("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":l("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":l("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":l("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":l("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};t.events.push(["addFont",function(b){var v=b.font,d=g.Unicode[v.postScriptName];d&&(v.metadata.Unicode={},v.metadata.Unicode.widths=d.widths,v.metadata.Unicode.kerning=d.kerning);var _=p.Unicode[v.postScriptName];_&&(v.metadata.Unicode.encoding=_,v.encoding=_.codePages[0])}])}(Ke.API),function(t){var e=function(n){for(var r=n.length,o=new Uint8Array(r),i=0;i<r;i++)o[i]=n.charCodeAt(i);return o};t.API.events.push(["addFont",function(n){var r=void 0,o=n.font,i=n.instance;if(!o.isStandardFont){if(i===void 0)throw new Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+o.postScriptName+"').");if(typeof(r=i.existsFileInVFS(o.postScriptName)===!1?i.loadFile(o.postScriptName):i.getFileFromVFS(o.postScriptName))!="string")throw new Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+o.postScriptName+"').");(function(s,l){l=/^\x00\x01\x00\x00/.test(l)?e(l):e(bo(l)),s.metadata=t.API.TTFFont.open(l),s.metadata.Unicode=s.metadata.Unicode||{encoding:{},kerning:{},widths:[]},s.metadata.glyIdsUsed=[0]})(o,r)}}])}(Ke),function(t){function e(){return(Xe.canvg?Promise.resolve(Xe.canvg):sc(()=>import("./index.es-DFQKMsfX.js"),__vite__mapDeps([0,1,2,3,4,5,6,7]))).catch(function(n){return Promise.reject(new Error("Could not load canvg: "+n))}).then(function(n){return n.default?n.default:n})}Ke.API.addSvgAsImage=function(n,r,o,i,s,l,u,p){if(isNaN(r)||isNaN(o))throw kt.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(i)||isNaN(s))throw kt.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),new Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var g=document.createElement("canvas");g.width=i,g.height=s;var b=g.getContext("2d");b.fillStyle="#fff",b.fillRect(0,0,g.width,g.height);var v={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},d=this;return e().then(function(_){return _.fromString(b,n,v)},function(){return Promise.reject(new Error("Could not load canvg."))}).then(function(_){return _.render(v)}).then(function(){d.addImage(g.toDataURL("image/jpeg",1),r,o,i,s,u,p)})}}(),Ke.API.putTotalPages=function(t){var e,n=0;parseInt(this.internal.getFont().id.substr(1),10)<15?(e=new RegExp(t,"g"),n=this.internal.getNumberOfPages()):(e=new RegExp(this.pdfEscape16(t,this.internal.getFont()),"g"),n=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var r=1;r<=this.internal.getNumberOfPages();r++)for(var o=0;o<this.internal.pages[r].length;o++)this.internal.pages[r][o]=this.internal.pages[r][o].replace(e,n);return this},Ke.API.viewerPreferences=function(t,e){var n;t=t||{},e=e||!1;var r,o,i,s={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},l=Object.keys(s),u=[],p=0,g=0,b=0;function v(_,S){var T,A=!1;for(T=0;T<_.length;T+=1)_[T]===S&&(A=!0);return A}if(this.internal.viewerpreferences===void 0&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(s)),this.internal.viewerpreferences.isSubscribed=!1),n=this.internal.viewerpreferences.configuration,t==="reset"||e===!0){var d=l.length;for(b=0;b<d;b+=1)n[l[b]].value=n[l[b]].defaultValue,n[l[b]].explicitSet=!1}if(Lt(t)==="object"){for(o in t)if(i=t[o],v(l,o)&&i!==void 0){if(n[o].type==="boolean"&&typeof i=="boolean")n[o].value=i;else if(n[o].type==="name"&&v(n[o].valueSet,i))n[o].value=i;else if(n[o].type==="integer"&&Number.isInteger(i))n[o].value=i;else if(n[o].type==="array"){for(p=0;p<i.length;p+=1)if(r=!0,i[p].length===1&&typeof i[p][0]=="number")u.push(String(i[p]-1));else if(i[p].length>1){for(g=0;g<i[p].length;g+=1)typeof i[p][g]!="number"&&(r=!1);r===!0&&u.push([i[p][0]-1,i[p][1]-1].join(" "))}n[o].value="["+u.join(" ")+"]"}else n[o].value=n[o].defaultValue;n[o].explicitSet=!0}}return this.internal.viewerpreferences.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){var _,S=[];for(_ in n)n[_].explicitSet===!0&&(n[_].type==="name"?S.push("/"+_+" /"+n[_].value):S.push("/"+_+" "+n[_].value));S.length!==0&&this.internal.write(`/ViewerPreferences
<<
`+S.join(`
`)+`
>>`)}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=n,this},function(t){var e=function(){var r='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',o=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),i=unescape(encodeURIComponent(r)),s=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),l=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),u=unescape(encodeURIComponent("</x:xmpmeta>")),p=i.length+s.length+l.length+o.length+u.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+p+" >>"),this.internal.write("stream"),this.internal.write(o+i+s+l+u),this.internal.write("endstream"),this.internal.write("endobj")},n=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")};t.addMetadata=function(r,o){return this.internal.__metadata__===void 0&&(this.internal.__metadata__={metadata:r,namespaceuri:o||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",n),this.internal.events.subscribe("postPutResources",e)),this}}(Ke.API),function(t){var e=t.API,n=e.pdfEscape16=function(i,s){for(var l,u=s.metadata.Unicode.widths,p=["","0","00","000","0000"],g=[""],b=0,v=i.length;b<v;++b){if(l=s.metadata.characterToGlyph(i.charCodeAt(b)),s.metadata.glyIdsUsed.push(l),s.metadata.toUnicode[l]=i.charCodeAt(b),u.indexOf(l)==-1&&(u.push(l),u.push([parseInt(s.metadata.widthOfGlyph(l),10)])),l=="0")return g.join("");l=l.toString(16),g.push(p[4-l.length],l)}return g.join("")},r=function(i){var s,l,u,p,g,b,v;for(g=`/CIDInit /ProcSet findresource begin
12 dict begin
begincmap
/CIDSystemInfo <<
  /Registry (Adobe)
  /Ordering (UCS)
  /Supplement 0
>> def
/CMapName /Adobe-Identity-UCS def
/CMapType 2 def
1 begincodespacerange
<0000><ffff>
endcodespacerange`,u=[],b=0,v=(l=Object.keys(i).sort(function(d,_){return d-_})).length;b<v;b++)s=l[b],u.length>=100&&(g+=`
`+u.length+` beginbfchar
`+u.join(`
`)+`
endbfchar`,u=[]),i[s]!==void 0&&i[s]!==null&&typeof i[s].toString=="function"&&(p=("0000"+i[s].toString(16)).slice(-4),s=("0000"+(+s).toString(16)).slice(-4),u.push("<"+s+"><"+p+">"));return u.length&&(g+=`
`+u.length+` beginbfchar
`+u.join(`
`)+`
endbfchar
`),g+=`endcmap
CMapName currentdict /CMap defineresource pop
end
end`};e.events.push(["putFont",function(i){(function(s){var l=s.font,u=s.out,p=s.newObject,g=s.putStream;if(l.metadata instanceof t.API.TTFFont&&l.encoding==="Identity-H"){for(var b=l.metadata.Unicode.widths,v=l.metadata.subset.encode(l.metadata.glyIdsUsed,1),d="",_=0;_<v.length;_++)d+=String.fromCharCode(v[_]);var S=p();g({data:d,addLength1:!0,objectId:S}),u("endobj");var T=p();g({data:r(l.metadata.toUnicode),addLength1:!0,objectId:T}),u("endobj");var A=p();u("<<"),u("/Type /FontDescriptor"),u("/FontName /"+ka(l.fontName)),u("/FontFile2 "+S+" 0 R"),u("/FontBBox "+t.API.PDFObject.convert(l.metadata.bbox)),u("/Flags "+l.metadata.flags),u("/StemV "+l.metadata.stemV),u("/ItalicAngle "+l.metadata.italicAngle),u("/Ascent "+l.metadata.ascender),u("/Descent "+l.metadata.decender),u("/CapHeight "+l.metadata.capHeight),u(">>"),u("endobj");var j=p();u("<<"),u("/Type /Font"),u("/BaseFont /"+ka(l.fontName)),u("/FontDescriptor "+A+" 0 R"),u("/W "+t.API.PDFObject.convert(b)),u("/CIDToGIDMap /Identity"),u("/DW 1000"),u("/Subtype /CIDFontType2"),u("/CIDSystemInfo"),u("<<"),u("/Supplement 0"),u("/Registry (Adobe)"),u("/Ordering ("+l.encoding+")"),u(">>"),u(">>"),u("endobj"),l.objectNumber=p(),u("<<"),u("/Type /Font"),u("/Subtype /Type0"),u("/ToUnicode "+T+" 0 R"),u("/BaseFont /"+ka(l.fontName)),u("/Encoding /"+l.encoding),u("/DescendantFonts ["+j+" 0 R]"),u(">>"),u("endobj"),l.isAlreadyPutted=!0}})(i)}]),e.events.push(["putFont",function(i){(function(s){var l=s.font,u=s.out,p=s.newObject,g=s.putStream;if(l.metadata instanceof t.API.TTFFont&&l.encoding==="WinAnsiEncoding"){for(var b=l.metadata.rawData,v="",d=0;d<b.length;d++)v+=String.fromCharCode(b[d]);var _=p();g({data:v,addLength1:!0,objectId:_}),u("endobj");var S=p();g({data:r(l.metadata.toUnicode),addLength1:!0,objectId:S}),u("endobj");var T=p();u("<<"),u("/Descent "+l.metadata.decender),u("/CapHeight "+l.metadata.capHeight),u("/StemV "+l.metadata.stemV),u("/Type /FontDescriptor"),u("/FontFile2 "+_+" 0 R"),u("/Flags 96"),u("/FontBBox "+t.API.PDFObject.convert(l.metadata.bbox)),u("/FontName /"+ka(l.fontName)),u("/ItalicAngle "+l.metadata.italicAngle),u("/Ascent "+l.metadata.ascender),u(">>"),u("endobj"),l.objectNumber=p();for(var A=0;A<l.metadata.hmtx.widths.length;A++)l.metadata.hmtx.widths[A]=parseInt(l.metadata.hmtx.widths[A]*(1e3/l.metadata.head.unitsPerEm));u("<</Subtype/TrueType/Type/Font/ToUnicode "+S+" 0 R/BaseFont/"+ka(l.fontName)+"/FontDescriptor "+T+" 0 R/Encoding/"+l.encoding+" /FirstChar 29 /LastChar 255 /Widths "+t.API.PDFObject.convert(l.metadata.hmtx.widths)+">>"),u("endobj"),l.isAlreadyPutted=!0}})(i)}]);var o=function(i){var s,l=i.text||"",u=i.x,p=i.y,g=i.options||{},b=i.mutex||{},v=b.pdfEscape,d=b.activeFontKey,_=b.fonts,S=d,T="",A=0,j="",q=_[S].encoding;if(_[S].encoding!=="Identity-H")return{text:l,x:u,y:p,options:g,mutex:b};for(j=l,S=d,Array.isArray(l)&&(j=l[0]),A=0;A<j.length;A+=1)_[S].metadata.hasOwnProperty("cmap")&&(s=_[S].metadata.cmap.unicode.codeMap[j[A].charCodeAt(0)]),s||j[A].charCodeAt(0)<256&&_[S].metadata.hasOwnProperty("Unicode")?T+=j[A]:T+="";var Y="";return parseInt(S.slice(1))<14||q==="WinAnsiEncoding"?Y=v(T,S).split("").map(function(le){return le.charCodeAt(0).toString(16)}).join(""):q==="Identity-H"&&(Y=n(T,_[S])),b.isHex=!0,{text:Y,x:u,y:p,options:g,mutex:b}};e.events.push(["postProcessText",function(i){var s=i.text||"",l=[],u={text:s,x:i.x,y:i.y,options:i.options,mutex:i.mutex};if(Array.isArray(s)){var p=0;for(p=0;p<s.length;p+=1)Array.isArray(s[p])&&s[p].length===3?l.push([o(Object.assign({},u,{text:s[p][0]})).text,s[p][1],s[p][2]]):l.push(o(Object.assign({},u,{text:s[p]})).text);i.text=l}else i.text=o(Object.assign({},u,{text:s})).text}])}(Ke),function(t){var e=function(){return this.internal.vFS===void 0&&(this.internal.vFS={}),!0};t.existsFileInVFS=function(n){return e.call(this),this.internal.vFS[n]!==void 0},t.addFileToVFS=function(n,r){return e.call(this),this.internal.vFS[n]=r,this},t.getFileFromVFS=function(n){return e.call(this),this.internal.vFS[n]!==void 0?this.internal.vFS[n]:null}}(Ke.API),function(t){t.__bidiEngine__=t.prototype.__bidiEngine__=function(r){var o,i,s,l,u,p,g,b=e,v=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],d=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],_={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},S={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},T=["(",")","(","<",">","<","[","]","[","{","}","{","«","»","«","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],A=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),j=!1,q=0;this.__bidiEngine__={};var Y=function(N){var k=N.charCodeAt(),O=k>>8,C=S[O];return C!==void 0?b[256*C+(255&k)]:O===252||O===253?"AL":A.test(O)?"L":O===8?"R":"N"},le=function(N){for(var k,O=0;O<N.length;O++){if((k=Y(N.charAt(O)))==="L")return!1;if(k==="R")return!0}return!1},ae=function(N,k,O,C){var X,ne,ue,Q,he=k[C];switch(he){case"L":case"R":j=!1;break;case"N":case"AN":break;case"EN":j&&(he="AN");break;case"AL":j=!0,he="R";break;case"WS":he="N";break;case"CS":C<1||C+1>=k.length||(X=O[C-1])!=="EN"&&X!=="AN"||(ne=k[C+1])!=="EN"&&ne!=="AN"?he="N":j&&(ne="AN"),he=ne===X?ne:"N";break;case"ES":he=(X=C>0?O[C-1]:"B")==="EN"&&C+1<k.length&&k[C+1]==="EN"?"EN":"N";break;case"ET":if(C>0&&O[C-1]==="EN"){he="EN";break}if(j){he="N";break}for(ue=C+1,Q=k.length;ue<Q&&k[ue]==="ET";)ue++;he=ue<Q&&k[ue]==="EN"?"EN":"N";break;case"NSM":if(s&&!l){for(Q=k.length,ue=C+1;ue<Q&&k[ue]==="NSM";)ue++;if(ue<Q){var V=N[C],me=V>=1425&&V<=2303||V===64286;if(X=k[ue],me&&(X==="R"||X==="AL")){he="R";break}}}he=C<1||(X=k[C-1])==="B"?"N":O[C-1];break;case"B":j=!1,o=!0,he=q;break;case"S":i=!0,he="N";break;case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":j=!1;break;case"BN":he="N"}return he},$=function(N,k,O){var C=N.split("");return O&&F(C,O,{hiLevel:q}),C.reverse(),k&&k.reverse(),C.join("")},F=function(N,k,O){var C,X,ne,ue,Q,he=-1,V=N.length,me=0,x=[],B=q?d:v,M=[];for(j=!1,o=!1,i=!1,X=0;X<V;X++)M[X]=Y(N[X]);for(ne=0;ne<V;ne++){if(Q=me,x[ne]=ae(N,M,x,ne),C=240&(me=B[Q][_[x[ne]]]),me&=15,k[ne]=ue=B[me][5],C>0)if(C===16){for(X=he;X<ne;X++)k[X]=1;he=-1}else he=-1;if(B[me][6])he===-1&&(he=ne);else if(he>-1){for(X=he;X<ne;X++)k[X]=ue;he=-1}M[ne]==="B"&&(k[ne]=0),O.hiLevel|=ue}i&&function(z,H,G){for(var ee=0;ee<G;ee++)if(z[ee]==="S"){H[ee]=q;for(var oe=ee-1;oe>=0&&z[oe]==="WS";oe--)H[oe]=q}}(M,k,V)},J=function(N,k,O,C,X){if(!(X.hiLevel<N)){if(N===1&&q===1&&!o)return k.reverse(),void(O&&O.reverse());for(var ne,ue,Q,he,V=k.length,me=0;me<V;){if(C[me]>=N){for(Q=me+1;Q<V&&C[Q]>=N;)Q++;for(he=me,ue=Q-1;he<ue;he++,ue--)ne=k[he],k[he]=k[ue],k[ue]=ne,O&&(ne=O[he],O[he]=O[ue],O[ue]=ne);me=Q}me++}}},re=function(N,k,O){var C=N.split(""),X={hiLevel:q};return O||(O=[]),F(C,O,X),function(ne,ue,Q){if(Q.hiLevel!==0&&g)for(var he,V=0;V<ne.length;V++)ue[V]===1&&(he=T.indexOf(ne[V]))>=0&&(ne[V]=T[he+1])}(C,O,X),J(2,C,k,O,X),J(1,C,k,O,X),C.join("")};return this.__bidiEngine__.doBidiReorder=function(N,k,O){if(function(X,ne){if(ne)for(var ue=0;ue<X.length;ue++)ne[ue]=ue;l===void 0&&(l=le(X)),p===void 0&&(p=le(X))}(N,k),s||!u||p)if(s&&u&&l^p)q=l?1:0,N=$(N,k,O);else if(!s&&u&&p)q=l?1:0,N=re(N,k,O),N=$(N,k);else if(!s||l||u||p){if(s&&!u&&l^p)N=$(N,k),l?(q=0,N=re(N,k,O)):(q=1,N=re(N,k,O),N=$(N,k));else if(s&&l&&!u&&p)q=1,N=re(N,k,O),N=$(N,k);else if(!s&&!u&&l^p){var C=g;l?(q=1,N=re(N,k,O),q=0,g=!1,N=re(N,k,O),g=C):(q=0,N=re(N,k,O),N=$(N,k),q=1,g=!1,N=re(N,k,O),g=C,N=$(N,k))}}else q=0,N=re(N,k,O);else q=l?1:0,N=re(N,k,O);return N},this.__bidiEngine__.setOptions=function(N){N&&(s=N.isInputVisual,u=N.isOutputVisual,l=N.isInputRtl,p=N.isOutputRtl,g=N.isSymmetricSwapping)},this.__bidiEngine__.setOptions(r),this.__bidiEngine__};var e=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],n=new t.__bidiEngine__({isInputVisual:!0});t.API.events.push(["postProcessText",function(r){var o=r.text;r.x,r.y;var i=r.options||{};r.mutex,i.lang;var s=[];if(i.isInputVisual=typeof i.isInputVisual!="boolean"||i.isInputVisual,n.setOptions(i),Object.prototype.toString.call(o)==="[object Array]"){var l=0;for(s=[],l=0;l<o.length;l+=1)Object.prototype.toString.call(o[l])==="[object Array]"?s.push([n.doBidiReorder(o[l][0]),o[l][1],o[l][2]]):s.push([n.doBidiReorder(o[l])]);r.text=s}else r.text=n.doBidiReorder(o);n.setOptions({isInputVisual:!0})}])}(Ke),Ke.API.TTFFont=function(){function t(e){var n;if(this.rawData=e,n=this.contents=new Ei(e),this.contents.pos=4,n.readString(4)==="ttcf")throw new Error("TTCF not supported.");n.pos=0,this.parse(),this.subset=new m5(this),this.registerTTF()}return t.open=function(e){return new t(e)},t.prototype.parse=function(){return this.directory=new n5(this.contents),this.head=new i5(this),this.name=new c5(this),this.cmap=new Lh(this),this.toUnicode={},this.hhea=new a5(this),this.maxp=new u5(this),this.hmtx=new f5(this),this.post=new s5(this),this.os2=new o5(this),this.loca=new g5(this),this.glyf=new h5(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},t.prototype.registerTTF=function(){var e,n,r,o,i;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=(function(){var s,l,u,p;for(p=[],s=0,l=(u=this.bbox).length;s<l;s++)e=u[s],p.push(Math.round(e*this.scaleFactor));return p}).call(this),this.stemV=0,this.post.exists?(r=255&(o=this.post.italic_angle),(32768&(n=o>>16))!=0&&(n=-(1+(65535^n))),this.italicAngle=+(n+"."+r)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=(i=this.familyClass)===1||i===2||i===3||i===4||i===5||i===7,this.isScript=this.familyClass===10,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),this.italicAngle!==0&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw new Error("No unicode cmap for font")},t.prototype.characterToGlyph=function(e){var n;return((n=this.cmap.unicode)!=null?n.codeMap[e]:void 0)||0},t.prototype.widthOfGlyph=function(e){var n;return n=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(e).advance*n},t.prototype.widthOfString=function(e,n,r){var o,i,s,l;for(s=0,i=0,l=(e=""+e).length;0<=l?i<l:i>l;i=0<=l?++i:--i)o=e.charCodeAt(i),s+=this.widthOfGlyph(this.characterToGlyph(o))+r*(1e3/n)||0;return s*(n/1e3)},t.prototype.lineHeight=function(e,n){var r;return n==null&&(n=!1),r=n?this.lineGap:0,(this.ascender+r-this.decender)/1e3*e},t}();var wr,Ei=function(){function t(e){this.data=e??[],this.pos=0,this.length=this.data.length}return t.prototype.readByte=function(){return this.data[this.pos++]},t.prototype.writeByte=function(e){return this.data[this.pos++]=e},t.prototype.readUInt32=function(){return 16777216*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},t.prototype.writeUInt32=function(e){return this.writeByte(e>>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e)},t.prototype.readInt32=function(){var e;return(e=this.readUInt32())>=2147483648?e-4294967296:e},t.prototype.writeInt32=function(e){return e<0&&(e+=4294967296),this.writeUInt32(e)},t.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},t.prototype.writeUInt16=function(e){return this.writeByte(e>>8&255),this.writeByte(255&e)},t.prototype.readInt16=function(){var e;return(e=this.readUInt16())>=32768?e-65536:e},t.prototype.writeInt16=function(e){return e<0&&(e+=65536),this.writeUInt16(e)},t.prototype.readString=function(e){var n,r;for(r=[],n=0;0<=e?n<e:n>e;n=0<=e?++n:--n)r[n]=String.fromCharCode(this.readByte());return r.join("")},t.prototype.writeString=function(e){var n,r,o;for(o=[],n=0,r=e.length;0<=r?n<r:n>r;n=0<=r?++n:--n)o.push(this.writeByte(e.charCodeAt(n)));return o},t.prototype.readShort=function(){return this.readInt16()},t.prototype.writeShort=function(e){return this.writeInt16(e)},t.prototype.readLongLong=function(){var e,n,r,o,i,s,l,u;return e=this.readByte(),n=this.readByte(),r=this.readByte(),o=this.readByte(),i=this.readByte(),s=this.readByte(),l=this.readByte(),u=this.readByte(),128&e?-1*(72057594037927940*(255^e)+281474976710656*(255^n)+1099511627776*(255^r)+4294967296*(255^o)+16777216*(255^i)+65536*(255^s)+256*(255^l)+(255^u)+1):72057594037927940*e+281474976710656*n+1099511627776*r+4294967296*o+16777216*i+65536*s+256*l+u},t.prototype.writeLongLong=function(e){var n,r;return n=Math.floor(e/4294967296),r=**********&e,this.writeByte(n>>24&255),this.writeByte(n>>16&255),this.writeByte(n>>8&255),this.writeByte(255&n),this.writeByte(r>>24&255),this.writeByte(r>>16&255),this.writeByte(r>>8&255),this.writeByte(255&r)},t.prototype.readInt=function(){return this.readInt32()},t.prototype.writeInt=function(e){return this.writeInt32(e)},t.prototype.read=function(e){var n,r;for(n=[],r=0;0<=e?r<e:r>e;r=0<=e?++r:--r)n.push(this.readByte());return n},t.prototype.write=function(e){var n,r,o,i;for(i=[],r=0,o=e.length;r<o;r++)n=e[r],i.push(this.writeByte(n));return i},t}(),n5=function(){var t;function e(n){var r,o,i;for(this.scalarType=n.readInt(),this.tableCount=n.readShort(),this.searchRange=n.readShort(),this.entrySelector=n.readShort(),this.rangeShift=n.readShort(),this.tables={},o=0,i=this.tableCount;0<=i?o<i:o>i;o=0<=i?++o:--o)r={tag:n.readString(4),checksum:n.readInt(),offset:n.readInt(),length:n.readInt()},this.tables[r.tag]=r}return e.prototype.encode=function(n){var r,o,i,s,l,u,p,g,b,v,d,_,S;for(S in d=Object.keys(n).length,u=Math.log(2),b=16*Math.floor(Math.log(d)/u),s=Math.floor(b/u),g=16*d-b,(o=new Ei).writeInt(this.scalarType),o.writeShort(d),o.writeShort(b),o.writeShort(s),o.writeShort(g),i=16*d,p=o.pos+i,l=null,_=[],n)for(v=n[S],o.writeString(S),o.writeInt(t(v)),o.writeInt(p),o.writeInt(v.length),_=_.concat(v),S==="head"&&(l=p),p+=v.length;p%4;)_.push(0),p++;return o.write(_),r=2981146554-t(o.data),o.pos=l+8,o.writeUInt32(r),o.data},t=function(n){var r,o,i,s;for(n=Ph.call(n);n.length%4;)n.push(0);for(i=new Ei(n),o=0,r=0,s=n.length;r<s;r=r+=4)o+=i.readUInt32();return **********&o},e}(),r5={}.hasOwnProperty,jr=function(t,e){for(var n in e)r5.call(e,n)&&(t[n]=e[n]);function r(){this.constructor=t}return r.prototype=e.prototype,t.prototype=new r,t.__super__=e.prototype,t};wr=function(){function t(e){var n;this.file=e,n=this.file.directory.tables[this.tag],this.exists=!!n,n&&(this.offset=n.offset,this.length=n.length,this.parse(this.file.contents))}return t.prototype.parse=function(){},t.prototype.encode=function(){},t.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},t}();var i5=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return jr(e,wr),e.prototype.tag="head",e.prototype.parse=function(n){return n.pos=this.offset,this.version=n.readInt(),this.revision=n.readInt(),this.checkSumAdjustment=n.readInt(),this.magicNumber=n.readInt(),this.flags=n.readShort(),this.unitsPerEm=n.readShort(),this.created=n.readLongLong(),this.modified=n.readLongLong(),this.xMin=n.readShort(),this.yMin=n.readShort(),this.xMax=n.readShort(),this.yMax=n.readShort(),this.macStyle=n.readShort(),this.lowestRecPPEM=n.readShort(),this.fontDirectionHint=n.readShort(),this.indexToLocFormat=n.readShort(),this.glyphDataFormat=n.readShort()},e.prototype.encode=function(n){var r;return(r=new Ei).writeInt(this.version),r.writeInt(this.revision),r.writeInt(this.checkSumAdjustment),r.writeInt(this.magicNumber),r.writeShort(this.flags),r.writeShort(this.unitsPerEm),r.writeLongLong(this.created),r.writeLongLong(this.modified),r.writeShort(this.xMin),r.writeShort(this.yMin),r.writeShort(this.xMax),r.writeShort(this.yMax),r.writeShort(this.macStyle),r.writeShort(this.lowestRecPPEM),r.writeShort(this.fontDirectionHint),r.writeShort(n),r.writeShort(this.glyphDataFormat),r.data},e}(),tf=function(){function t(e,n){var r,o,i,s,l,u,p,g,b,v,d,_,S,T,A,j,q;switch(this.platformID=e.readUInt16(),this.encodingID=e.readShort(),this.offset=n+e.readInt(),b=e.pos,e.pos=this.offset,this.format=e.readUInt16(),this.length=e.readUInt16(),this.language=e.readUInt16(),this.isUnicode=this.platformID===3&&this.encodingID===1&&this.format===4||this.platformID===0&&this.format===4,this.codeMap={},this.format){case 0:for(u=0;u<256;++u)this.codeMap[u]=e.readByte();break;case 4:for(d=e.readUInt16(),v=d/2,e.pos+=6,i=function(){var Y,le;for(le=[],u=Y=0;0<=v?Y<v:Y>v;u=0<=v?++Y:--Y)le.push(e.readUInt16());return le}(),e.pos+=2,S=function(){var Y,le;for(le=[],u=Y=0;0<=v?Y<v:Y>v;u=0<=v?++Y:--Y)le.push(e.readUInt16());return le}(),p=function(){var Y,le;for(le=[],u=Y=0;0<=v?Y<v:Y>v;u=0<=v?++Y:--Y)le.push(e.readUInt16());return le}(),g=function(){var Y,le;for(le=[],u=Y=0;0<=v?Y<v:Y>v;u=0<=v?++Y:--Y)le.push(e.readUInt16());return le}(),o=(this.length-e.pos+this.offset)/2,l=function(){var Y,le;for(le=[],u=Y=0;0<=o?Y<o:Y>o;u=0<=o?++Y:--Y)le.push(e.readUInt16());return le}(),u=A=0,q=i.length;A<q;u=++A)for(T=i[u],r=j=_=S[u];_<=T?j<=T:j>=T;r=_<=T?++j:--j)g[u]===0?s=r+p[u]:(s=l[g[u]/2+(r-_)-(v-u)]||0)!==0&&(s+=p[u]),this.codeMap[r]=65535&s}e.pos=b}return t.encode=function(e,n){var r,o,i,s,l,u,p,g,b,v,d,_,S,T,A,j,q,Y,le,ae,$,F,J,re,N,k,O,C,X,ne,ue,Q,he,V,me,x,B,M,z,H,G,ee,oe,ve,Se,be;switch(C=new Ei,s=Object.keys(e).sort(function(Ce,ze){return Ce-ze}),n){case"macroman":for(S=0,T=function(){var Ce=[];for(_=0;_<256;++_)Ce.push(0);return Ce}(),j={0:0},i={},X=0,he=s.length;X<he;X++)j[oe=e[o=s[X]]]==null&&(j[oe]=++S),i[o]={old:e[o],new:j[e[o]]},T[o]=j[e[o]];return C.writeUInt16(1),C.writeUInt16(0),C.writeUInt32(12),C.writeUInt16(0),C.writeUInt16(262),C.writeUInt16(0),C.write(T),{charMap:i,subtable:C.data,maxGlyphID:S+1};case"unicode":for(k=[],b=[],q=0,j={},r={},A=p=null,ne=0,V=s.length;ne<V;ne++)j[le=e[o=s[ne]]]==null&&(j[le]=++q),r[o]={old:le,new:j[le]},l=j[le]-o,A!=null&&l===p||(A&&b.push(A),k.push(o),p=l),A=o;for(A&&b.push(A),b.push(65535),k.push(65535),re=2*(J=k.length),F=2*Math.pow(Math.log(J)/Math.LN2,2),v=Math.log(F/2)/Math.LN2,$=2*J-F,u=[],ae=[],d=[],_=ue=0,me=k.length;ue<me;_=++ue){if(N=k[_],g=b[_],N===65535){u.push(0),ae.push(0);break}if(N-(O=r[N].new)>=32768)for(u.push(0),ae.push(2*(d.length+J-_)),o=Q=N;N<=g?Q<=g:Q>=g;o=N<=g?++Q:--Q)d.push(r[o].new);else u.push(O-N),ae.push(0)}for(C.writeUInt16(3),C.writeUInt16(1),C.writeUInt32(12),C.writeUInt16(4),C.writeUInt16(16+8*J+2*d.length),C.writeUInt16(0),C.writeUInt16(re),C.writeUInt16(F),C.writeUInt16(v),C.writeUInt16($),G=0,x=b.length;G<x;G++)o=b[G],C.writeUInt16(o);for(C.writeUInt16(0),ee=0,B=k.length;ee<B;ee++)o=k[ee],C.writeUInt16(o);for(ve=0,M=u.length;ve<M;ve++)l=u[ve],C.writeUInt16(l);for(Se=0,z=ae.length;Se<z;Se++)Y=ae[Se],C.writeUInt16(Y);for(be=0,H=d.length;be<H;be++)S=d[be],C.writeUInt16(S);return{charMap:r,subtable:C.data,maxGlyphID:q+1}}},t}(),Lh=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return jr(e,wr),e.prototype.tag="cmap",e.prototype.parse=function(n){var r,o,i;for(n.pos=this.offset,this.version=n.readUInt16(),i=n.readUInt16(),this.tables=[],this.unicode=null,o=0;0<=i?o<i:o>i;o=0<=i?++o:--o)r=new tf(n,this.offset),this.tables.push(r),r.isUnicode&&this.unicode==null&&(this.unicode=r);return!0},e.encode=function(n,r){var o,i;return r==null&&(r="macroman"),o=tf.encode(n,r),(i=new Ei).writeUInt16(0),i.writeUInt16(1),o.table=i.data.concat(o.subtable),o},e}(),a5=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return jr(e,wr),e.prototype.tag="hhea",e.prototype.parse=function(n){return n.pos=this.offset,this.version=n.readInt(),this.ascender=n.readShort(),this.decender=n.readShort(),this.lineGap=n.readShort(),this.advanceWidthMax=n.readShort(),this.minLeftSideBearing=n.readShort(),this.minRightSideBearing=n.readShort(),this.xMaxExtent=n.readShort(),this.caretSlopeRise=n.readShort(),this.caretSlopeRun=n.readShort(),this.caretOffset=n.readShort(),n.pos+=8,this.metricDataFormat=n.readShort(),this.numberOfMetrics=n.readUInt16()},e}(),o5=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return jr(e,wr),e.prototype.tag="OS/2",e.prototype.parse=function(n){if(n.pos=this.offset,this.version=n.readUInt16(),this.averageCharWidth=n.readShort(),this.weightClass=n.readUInt16(),this.widthClass=n.readUInt16(),this.type=n.readShort(),this.ySubscriptXSize=n.readShort(),this.ySubscriptYSize=n.readShort(),this.ySubscriptXOffset=n.readShort(),this.ySubscriptYOffset=n.readShort(),this.ySuperscriptXSize=n.readShort(),this.ySuperscriptYSize=n.readShort(),this.ySuperscriptXOffset=n.readShort(),this.ySuperscriptYOffset=n.readShort(),this.yStrikeoutSize=n.readShort(),this.yStrikeoutPosition=n.readShort(),this.familyClass=n.readShort(),this.panose=function(){var r,o;for(o=[],r=0;r<10;++r)o.push(n.readByte());return o}(),this.charRange=function(){var r,o;for(o=[],r=0;r<4;++r)o.push(n.readInt());return o}(),this.vendorID=n.readString(4),this.selection=n.readShort(),this.firstCharIndex=n.readShort(),this.lastCharIndex=n.readShort(),this.version>0&&(this.ascent=n.readShort(),this.descent=n.readShort(),this.lineGap=n.readShort(),this.winAscent=n.readShort(),this.winDescent=n.readShort(),this.codePageRange=function(){var r,o;for(o=[],r=0;r<2;r=++r)o.push(n.readInt());return o}(),this.version>1))return this.xHeight=n.readShort(),this.capHeight=n.readShort(),this.defaultChar=n.readShort(),this.breakChar=n.readShort(),this.maxContext=n.readShort()},e}(),s5=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return jr(e,wr),e.prototype.tag="post",e.prototype.parse=function(n){var r,o,i;switch(n.pos=this.offset,this.format=n.readInt(),this.italicAngle=n.readInt(),this.underlinePosition=n.readShort(),this.underlineThickness=n.readShort(),this.isFixedPitch=n.readInt(),this.minMemType42=n.readInt(),this.maxMemType42=n.readInt(),this.minMemType1=n.readInt(),this.maxMemType1=n.readInt(),this.format){case 65536:break;case 131072:var s;for(o=n.readUInt16(),this.glyphNameIndex=[],s=0;0<=o?s<o:s>o;s=0<=o?++s:--s)this.glyphNameIndex.push(n.readUInt16());for(this.names=[],i=[];n.pos<this.offset+this.length;)r=n.readByte(),i.push(this.names.push(n.readString(r)));return i;case 151552:return o=n.readUInt16(),this.offsets=n.read(o);case 196608:break;case 262144:return this.map=(function(){var l,u,p;for(p=[],s=l=0,u=this.file.maxp.numGlyphs;0<=u?l<u:l>u;s=0<=u?++l:--l)p.push(n.readUInt32());return p}).call(this)}},e}(),l5=function(t,e){this.raw=t,this.length=t.length,this.platformID=e.platformID,this.encodingID=e.encodingID,this.languageID=e.languageID},c5=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return jr(e,wr),e.prototype.tag="name",e.prototype.parse=function(n){var r,o,i,s,l,u,p,g,b,v,d;for(n.pos=this.offset,n.readShort(),r=n.readShort(),u=n.readShort(),o=[],s=0;0<=r?s<r:s>r;s=0<=r?++s:--s)o.push({platformID:n.readShort(),encodingID:n.readShort(),languageID:n.readShort(),nameID:n.readShort(),length:n.readShort(),offset:this.offset+u+n.readShort()});for(p={},s=b=0,v=o.length;b<v;s=++b)i=o[s],n.pos=i.offset,g=n.readString(i.length),l=new l5(g,i),p[d=i.nameID]==null&&(p[d]=[]),p[i.nameID].push(l);this.strings=p,this.copyright=p[0],this.fontFamily=p[1],this.fontSubfamily=p[2],this.uniqueSubfamily=p[3],this.fontName=p[4],this.version=p[5];try{this.postscriptName=p[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch{this.postscriptName=p[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=p[7],this.manufacturer=p[8],this.designer=p[9],this.description=p[10],this.vendorUrl=p[11],this.designerUrl=p[12],this.license=p[13],this.licenseUrl=p[14],this.preferredFamily=p[15],this.preferredSubfamily=p[17],this.compatibleFull=p[18],this.sampleText=p[19]},e}(),u5=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return jr(e,wr),e.prototype.tag="maxp",e.prototype.parse=function(n){return n.pos=this.offset,this.version=n.readInt(),this.numGlyphs=n.readUInt16(),this.maxPoints=n.readUInt16(),this.maxContours=n.readUInt16(),this.maxCompositePoints=n.readUInt16(),this.maxComponentContours=n.readUInt16(),this.maxZones=n.readUInt16(),this.maxTwilightPoints=n.readUInt16(),this.maxStorage=n.readUInt16(),this.maxFunctionDefs=n.readUInt16(),this.maxInstructionDefs=n.readUInt16(),this.maxStackElements=n.readUInt16(),this.maxSizeOfInstructions=n.readUInt16(),this.maxComponentElements=n.readUInt16(),this.maxComponentDepth=n.readUInt16()},e}(),f5=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return jr(e,wr),e.prototype.tag="hmtx",e.prototype.parse=function(n){var r,o,i,s,l,u,p;for(n.pos=this.offset,this.metrics=[],r=0,u=this.file.hhea.numberOfMetrics;0<=u?r<u:r>u;r=0<=u?++r:--r)this.metrics.push({advance:n.readUInt16(),lsb:n.readInt16()});for(i=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var g,b;for(b=[],r=g=0;0<=i?g<i:g>i;r=0<=i?++g:--g)b.push(n.readInt16());return b}(),this.widths=(function(){var g,b,v,d;for(d=[],g=0,b=(v=this.metrics).length;g<b;g++)s=v[g],d.push(s.advance);return d}).call(this),o=this.widths[this.widths.length-1],p=[],r=l=0;0<=i?l<i:l>i;r=0<=i?++l:--l)p.push(this.widths.push(o));return p},e.prototype.forGlyph=function(n){return n in this.metrics?this.metrics[n]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[n-this.metrics.length]}},e}(),Ph=[].slice,h5=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return jr(e,wr),e.prototype.tag="glyf",e.prototype.parse=function(){return this.cache={}},e.prototype.glyphFor=function(n){var r,o,i,s,l,u,p,g,b,v;return n in this.cache?this.cache[n]:(s=this.file.loca,r=this.file.contents,o=s.indexOf(n),(i=s.lengthOf(n))===0?this.cache[n]=null:(r.pos=this.offset+o,l=(u=new Ei(r.read(i))).readShort(),g=u.readShort(),v=u.readShort(),p=u.readShort(),b=u.readShort(),this.cache[n]=l===-1?new p5(u,g,v,p,b):new d5(u,l,g,v,p,b),this.cache[n]))},e.prototype.encode=function(n,r,o){var i,s,l,u,p;for(l=[],s=[],u=0,p=r.length;u<p;u++)i=n[r[u]],s.push(l.length),i&&(l=l.concat(i.encode(o)));return s.push(l.length),{table:l,offsets:s}},e}(),d5=function(){function t(e,n,r,o,i,s){this.raw=e,this.numberOfContours=n,this.xMin=r,this.yMin=o,this.xMax=i,this.yMax=s,this.compound=!1}return t.prototype.encode=function(){return this.raw.data},t}(),p5=function(){function t(e,n,r,o,i){var s,l;for(this.raw=e,this.xMin=n,this.yMin=r,this.xMax=o,this.yMax=i,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],s=this.raw;l=s.readShort(),this.glyphOffsets.push(s.pos),this.glyphIDs.push(s.readUInt16()),32&l;)s.pos+=1&l?4:2,128&l?s.pos+=8:64&l?s.pos+=4:8&l&&(s.pos+=2)}return t.prototype.encode=function(){var e,n,r;for(n=new Ei(Ph.call(this.raw.data)),e=0,r=this.glyphIDs.length;e<r;++e)n.pos=this.glyphOffsets[e];return n.data},t}(),g5=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return jr(e,wr),e.prototype.tag="loca",e.prototype.parse=function(n){var r,o;return n.pos=this.offset,r=this.file.head.indexToLocFormat,this.offsets=r===0?(function(){var i,s;for(s=[],o=0,i=this.length;o<i;o+=2)s.push(2*n.readUInt16());return s}).call(this):(function(){var i,s;for(s=[],o=0,i=this.length;o<i;o+=4)s.push(n.readUInt32());return s}).call(this)},e.prototype.indexOf=function(n){return this.offsets[n]},e.prototype.lengthOf=function(n){return this.offsets[n+1]-this.offsets[n]},e.prototype.encode=function(n,r){for(var o=new Uint32Array(this.offsets.length),i=0,s=0,l=0;l<o.length;++l)if(o[l]=i,s<r.length&&r[s]==l){++s,o[l]=i;var u=this.offsets[l],p=this.offsets[l+1]-u;p>0&&(i+=p)}for(var g=new Array(4*o.length),b=0;b<o.length;++b)g[4*b+3]=255&o[b],g[4*b+2]=(65280&o[b])>>8,g[4*b+1]=(16711680&o[b])>>16,g[4*b]=(**********&o[b])>>24;return g},e}(),m5=function(){function t(e){this.font=e,this.subset={},this.unicodes={},this.next=33}return t.prototype.generateCmap=function(){var e,n,r,o,i;for(n in o=this.font.cmap.tables[0].codeMap,e={},i=this.subset)r=i[n],e[n]=o[r];return e},t.prototype.glyphsFor=function(e){var n,r,o,i,s,l,u;for(o={},s=0,l=e.length;s<l;s++)o[i=e[s]]=this.font.glyf.glyphFor(i);for(i in n=[],o)(r=o[i])!=null&&r.compound&&n.push.apply(n,r.glyphIDs);if(n.length>0)for(i in u=this.glyphsFor(n))r=u[i],o[i]=r;return o},t.prototype.encode=function(e,n){var r,o,i,s,l,u,p,g,b,v,d,_,S,T,A;for(o in r=Lh.encode(this.generateCmap(),"unicode"),s=this.glyphsFor(e),d={0:0},A=r.charMap)d[(u=A[o]).old]=u.new;for(_ in v=r.maxGlyphID,s)_ in d||(d[_]=v++);return g=function(j){var q,Y;for(q in Y={},j)Y[j[q]]=q;return Y}(d),b=Object.keys(g).sort(function(j,q){return j-q}),S=function(){var j,q,Y;for(Y=[],j=0,q=b.length;j<q;j++)l=b[j],Y.push(g[l]);return Y}(),i=this.font.glyf.encode(s,S,d),p=this.font.loca.encode(i.offsets,S),T={cmap:this.font.cmap.raw(),glyf:i.table,loca:p,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(n)},this.font.os2.exists&&(T["OS/2"]=this.font.os2.raw()),this.font.directory.encode(T)},t}();Ke.API.PDFObject=function(){var t;function e(){}return t=function(n,r){return(Array(r+1).join("0")+n).slice(-r)},e.convert=function(n){var r,o,i,s;if(Array.isArray(n))return"["+function(){var l,u,p;for(p=[],l=0,u=n.length;l<u;l++)r=n[l],p.push(e.convert(r));return p}().join(" ")+"]";if(typeof n=="string")return"/"+n;if(n!=null&&n.isString)return"("+n+")";if(n instanceof Date)return"(D:"+t(n.getUTCFullYear(),4)+t(n.getUTCMonth(),2)+t(n.getUTCDate(),2)+t(n.getUTCHours(),2)+t(n.getUTCMinutes(),2)+t(n.getUTCSeconds(),2)+"Z)";if({}.toString.call(n)==="[object Object]"){for(o in i=["<<"],n)s=n[o],i.push("/"+o+" "+e.convert(s));return i.push(">>"),i.join(`
`)}return""+n},e}();var La={},Cl,nf;function b5(){return nf||(nf=1,Cl=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),Cl}var El={},Ai={},rf;function Xi(){if(rf)return Ai;rf=1;let t;const e=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return Ai.getSymbolSize=function(r){if(!r)throw new Error('"version" cannot be null or undefined');if(r<1||r>40)throw new Error('"version" should be in range from 1 to 40');return r*4+17},Ai.getSymbolTotalCodewords=function(r){return e[r]},Ai.getBCHDigit=function(n){let r=0;for(;n!==0;)r++,n>>>=1;return r},Ai.setToSJISFunction=function(r){if(typeof r!="function")throw new Error('"toSJISFunc" is not a valid function.');t=r},Ai.isKanjiModeEnabled=function(){return typeof t<"u"},Ai.toSJIS=function(r){return t(r)},Ai}var Il={},af;function jc(){return af||(af=1,function(t){t.L={bit:1},t.M={bit:0},t.Q={bit:3},t.H={bit:2};function e(n){if(typeof n!="string")throw new Error("Param is not a string");switch(n.toLowerCase()){case"l":case"low":return t.L;case"m":case"medium":return t.M;case"q":case"quartile":return t.Q;case"h":case"high":return t.H;default:throw new Error("Unknown EC Level: "+n)}}t.isValid=function(r){return r&&typeof r.bit<"u"&&r.bit>=0&&r.bit<4},t.from=function(r,o){if(t.isValid(r))return r;try{return e(r)}catch{return o}}}(Il)),Il}var Tl,of;function v5(){if(of)return Tl;of=1;function t(){this.buffer=[],this.length=0}return t.prototype={get:function(e){const n=Math.floor(e/8);return(this.buffer[n]>>>7-e%8&1)===1},put:function(e,n){for(let r=0;r<n;r++)this.putBit((e>>>n-r-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(e){const n=Math.floor(this.length/8);this.buffer.length<=n&&this.buffer.push(0),e&&(this.buffer[n]|=128>>>this.length%8),this.length++}},Tl=t,Tl}var jl,sf;function y5(){if(sf)return jl;sf=1;function t(e){if(!e||e<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=e,this.data=new Uint8Array(e*e),this.reservedBit=new Uint8Array(e*e)}return t.prototype.set=function(e,n,r,o){const i=e*this.size+n;this.data[i]=r,o&&(this.reservedBit[i]=!0)},t.prototype.get=function(e,n){return this.data[e*this.size+n]},t.prototype.xor=function(e,n,r){this.data[e*this.size+n]^=r},t.prototype.isReserved=function(e,n){return this.reservedBit[e*this.size+n]},jl=t,jl}var Bl={},lf;function w5(){return lf||(lf=1,function(t){const e=Xi().getSymbolSize;t.getRowColCoords=function(r){if(r===1)return[];const o=Math.floor(r/7)+2,i=e(r),s=i===145?26:Math.ceil((i-13)/(2*o-2))*2,l=[i-7];for(let u=1;u<o-1;u++)l[u]=l[u-1]-s;return l.push(6),l.reverse()},t.getPositions=function(r){const o=[],i=t.getRowColCoords(r),s=i.length;for(let l=0;l<s;l++)for(let u=0;u<s;u++)l===0&&u===0||l===0&&u===s-1||l===s-1&&u===0||o.push([i[l],i[u]]);return o}}(Bl)),Bl}var Ml={},cf;function x5(){if(cf)return Ml;cf=1;const t=Xi().getSymbolSize,e=7;return Ml.getPositions=function(r){const o=t(r);return[[0,0],[o-e,0],[0,o-e]]},Ml}var Rl={},uf;function A5(){return uf||(uf=1,function(t){t.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const e={N1:3,N2:3,N3:40,N4:10};t.isValid=function(o){return o!=null&&o!==""&&!isNaN(o)&&o>=0&&o<=7},t.from=function(o){return t.isValid(o)?parseInt(o,10):void 0},t.getPenaltyN1=function(o){const i=o.size;let s=0,l=0,u=0,p=null,g=null;for(let b=0;b<i;b++){l=u=0,p=g=null;for(let v=0;v<i;v++){let d=o.get(b,v);d===p?l++:(l>=5&&(s+=e.N1+(l-5)),p=d,l=1),d=o.get(v,b),d===g?u++:(u>=5&&(s+=e.N1+(u-5)),g=d,u=1)}l>=5&&(s+=e.N1+(l-5)),u>=5&&(s+=e.N1+(u-5))}return s},t.getPenaltyN2=function(o){const i=o.size;let s=0;for(let l=0;l<i-1;l++)for(let u=0;u<i-1;u++){const p=o.get(l,u)+o.get(l,u+1)+o.get(l+1,u)+o.get(l+1,u+1);(p===4||p===0)&&s++}return s*e.N2},t.getPenaltyN3=function(o){const i=o.size;let s=0,l=0,u=0;for(let p=0;p<i;p++){l=u=0;for(let g=0;g<i;g++)l=l<<1&2047|o.get(p,g),g>=10&&(l===1488||l===93)&&s++,u=u<<1&2047|o.get(g,p),g>=10&&(u===1488||u===93)&&s++}return s*e.N3},t.getPenaltyN4=function(o){let i=0;const s=o.data.length;for(let u=0;u<s;u++)i+=o.data[u];return Math.abs(Math.ceil(i*100/s/5)-10)*e.N4};function n(r,o,i){switch(r){case t.Patterns.PATTERN000:return(o+i)%2===0;case t.Patterns.PATTERN001:return o%2===0;case t.Patterns.PATTERN010:return i%3===0;case t.Patterns.PATTERN011:return(o+i)%3===0;case t.Patterns.PATTERN100:return(Math.floor(o/2)+Math.floor(i/3))%2===0;case t.Patterns.PATTERN101:return o*i%2+o*i%3===0;case t.Patterns.PATTERN110:return(o*i%2+o*i%3)%2===0;case t.Patterns.PATTERN111:return(o*i%3+(o+i)%2)%2===0;default:throw new Error("bad maskPattern:"+r)}}t.applyMask=function(o,i){const s=i.size;for(let l=0;l<s;l++)for(let u=0;u<s;u++)i.isReserved(u,l)||i.xor(u,l,n(o,u,l))},t.getBestMask=function(o,i){const s=Object.keys(t.Patterns).length;let l=0,u=1/0;for(let p=0;p<s;p++){i(p),t.applyMask(p,o);const g=t.getPenaltyN1(o)+t.getPenaltyN2(o)+t.getPenaltyN3(o)+t.getPenaltyN4(o);t.applyMask(p,o),g<u&&(u=g,l=p)}return l}}(Rl)),Rl}var us={},ff;function _h(){if(ff)return us;ff=1;const t=jc(),e=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],n=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return us.getBlocksCount=function(o,i){switch(i){case t.L:return e[(o-1)*4+0];case t.M:return e[(o-1)*4+1];case t.Q:return e[(o-1)*4+2];case t.H:return e[(o-1)*4+3];default:return}},us.getTotalCodewordsCount=function(o,i){switch(i){case t.L:return n[(o-1)*4+0];case t.M:return n[(o-1)*4+1];case t.Q:return n[(o-1)*4+2];case t.H:return n[(o-1)*4+3];default:return}},us}var Fl={},fo={},hf;function S5(){if(hf)return fo;hf=1;const t=new Uint8Array(512),e=new Uint8Array(256);return function(){let r=1;for(let o=0;o<255;o++)t[o]=r,e[r]=o,r<<=1,r&256&&(r^=285);for(let o=255;o<512;o++)t[o]=t[o-255]}(),fo.log=function(r){if(r<1)throw new Error("log("+r+")");return e[r]},fo.exp=function(r){return t[r]},fo.mul=function(r,o){return r===0||o===0?0:t[e[r]+e[o]]},fo}var df;function N5(){return df||(df=1,function(t){const e=S5();t.mul=function(r,o){const i=new Uint8Array(r.length+o.length-1);for(let s=0;s<r.length;s++)for(let l=0;l<o.length;l++)i[s+l]^=e.mul(r[s],o[l]);return i},t.mod=function(r,o){let i=new Uint8Array(r);for(;i.length-o.length>=0;){const s=i[0];for(let u=0;u<o.length;u++)i[u]^=e.mul(o[u],s);let l=0;for(;l<i.length&&i[l]===0;)l++;i=i.slice(l)}return i},t.generateECPolynomial=function(r){let o=new Uint8Array([1]);for(let i=0;i<r;i++)o=t.mul(o,new Uint8Array([1,e.exp(i)]));return o}}(Fl)),Fl}var Ol,pf;function L5(){if(pf)return Ol;pf=1;const t=N5();function e(n){this.genPoly=void 0,this.degree=n,this.degree&&this.initialize(this.degree)}return e.prototype.initialize=function(r){this.degree=r,this.genPoly=t.generateECPolynomial(this.degree)},e.prototype.encode=function(r){if(!this.genPoly)throw new Error("Encoder not initialized");const o=new Uint8Array(r.length+this.degree);o.set(r);const i=t.mod(o,this.genPoly),s=this.degree-i.length;if(s>0){const l=new Uint8Array(this.degree);return l.set(i,s),l}return i},Ol=e,Ol}var Dl={},ql={},Ul={},gf;function kh(){return gf||(gf=1,Ul.isValid=function(e){return!isNaN(e)&&e>=1&&e<=40}),Ul}var kr={},mf;function Ch(){if(mf)return kr;mf=1;const t="[0-9]+",e="[A-Z $%*+\\-./:]+";let n="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";n=n.replace(/u/g,"\\u");const r="(?:(?![A-Z0-9 $%*+\\-./:]|"+n+`)(?:.|[\r
]))+`;kr.KANJI=new RegExp(n,"g"),kr.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),kr.BYTE=new RegExp(r,"g"),kr.NUMERIC=new RegExp(t,"g"),kr.ALPHANUMERIC=new RegExp(e,"g");const o=new RegExp("^"+n+"$"),i=new RegExp("^"+t+"$"),s=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return kr.testKanji=function(u){return o.test(u)},kr.testNumeric=function(u){return i.test(u)},kr.testAlphanumeric=function(u){return s.test(u)},kr}var bf;function Zi(){return bf||(bf=1,function(t){const e=kh(),n=Ch();t.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},t.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},t.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},t.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},t.MIXED={bit:-1},t.getCharCountIndicator=function(i,s){if(!i.ccBits)throw new Error("Invalid mode: "+i);if(!e.isValid(s))throw new Error("Invalid version: "+s);return s>=1&&s<10?i.ccBits[0]:s<27?i.ccBits[1]:i.ccBits[2]},t.getBestModeForData=function(i){return n.testNumeric(i)?t.NUMERIC:n.testAlphanumeric(i)?t.ALPHANUMERIC:n.testKanji(i)?t.KANJI:t.BYTE},t.toString=function(i){if(i&&i.id)return i.id;throw new Error("Invalid mode")},t.isValid=function(i){return i&&i.bit&&i.ccBits};function r(o){if(typeof o!="string")throw new Error("Param is not a string");switch(o.toLowerCase()){case"numeric":return t.NUMERIC;case"alphanumeric":return t.ALPHANUMERIC;case"kanji":return t.KANJI;case"byte":return t.BYTE;default:throw new Error("Unknown mode: "+o)}}t.from=function(i,s){if(t.isValid(i))return i;try{return r(i)}catch{return s}}}(ql)),ql}var vf;function P5(){return vf||(vf=1,function(t){const e=Xi(),n=_h(),r=jc(),o=Zi(),i=kh(),s=7973,l=e.getBCHDigit(s);function u(v,d,_){for(let S=1;S<=40;S++)if(d<=t.getCapacity(S,_,v))return S}function p(v,d){return o.getCharCountIndicator(v,d)+4}function g(v,d){let _=0;return v.forEach(function(S){const T=p(S.mode,d);_+=T+S.getBitsLength()}),_}function b(v,d){for(let _=1;_<=40;_++)if(g(v,_)<=t.getCapacity(_,d,o.MIXED))return _}t.from=function(d,_){return i.isValid(d)?parseInt(d,10):_},t.getCapacity=function(d,_,S){if(!i.isValid(d))throw new Error("Invalid QR Code version");typeof S>"u"&&(S=o.BYTE);const T=e.getSymbolTotalCodewords(d),A=n.getTotalCodewordsCount(d,_),j=(T-A)*8;if(S===o.MIXED)return j;const q=j-p(S,d);switch(S){case o.NUMERIC:return Math.floor(q/10*3);case o.ALPHANUMERIC:return Math.floor(q/11*2);case o.KANJI:return Math.floor(q/13);case o.BYTE:default:return Math.floor(q/8)}},t.getBestVersionForData=function(d,_){let S;const T=r.from(_,r.M);if(Array.isArray(d)){if(d.length>1)return b(d,T);if(d.length===0)return 1;S=d[0]}else S=d;return u(S.mode,S.getLength(),T)},t.getEncodedBits=function(d){if(!i.isValid(d)||d<7)throw new Error("Invalid QR Code version");let _=d<<12;for(;e.getBCHDigit(_)-l>=0;)_^=s<<e.getBCHDigit(_)-l;return d<<12|_}}(Dl)),Dl}var zl={},yf;function _5(){if(yf)return zl;yf=1;const t=Xi(),e=1335,n=21522,r=t.getBCHDigit(e);return zl.getEncodedBits=function(i,s){const l=i.bit<<3|s;let u=l<<10;for(;t.getBCHDigit(u)-r>=0;)u^=e<<t.getBCHDigit(u)-r;return(l<<10|u)^n},zl}var Hl={},Wl,wf;function k5(){if(wf)return Wl;wf=1;const t=Zi();function e(n){this.mode=t.NUMERIC,this.data=n.toString()}return e.getBitsLength=function(r){return 10*Math.floor(r/3)+(r%3?r%3*3+1:0)},e.prototype.getLength=function(){return this.data.length},e.prototype.getBitsLength=function(){return e.getBitsLength(this.data.length)},e.prototype.write=function(r){let o,i,s;for(o=0;o+3<=this.data.length;o+=3)i=this.data.substr(o,3),s=parseInt(i,10),r.put(s,10);const l=this.data.length-o;l>0&&(i=this.data.substr(o),s=parseInt(i,10),r.put(s,l*3+1))},Wl=e,Wl}var Vl,xf;function C5(){if(xf)return Vl;xf=1;const t=Zi(),e=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function n(r){this.mode=t.ALPHANUMERIC,this.data=r}return n.getBitsLength=function(o){return 11*Math.floor(o/2)+6*(o%2)},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(o){let i;for(i=0;i+2<=this.data.length;i+=2){let s=e.indexOf(this.data[i])*45;s+=e.indexOf(this.data[i+1]),o.put(s,11)}this.data.length%2&&o.put(e.indexOf(this.data[i]),6)},Vl=n,Vl}var Gl,Af;function E5(){if(Af)return Gl;Af=1;const t=Zi();function e(n){this.mode=t.BYTE,typeof n=="string"?this.data=new TextEncoder().encode(n):this.data=new Uint8Array(n)}return e.getBitsLength=function(r){return r*8},e.prototype.getLength=function(){return this.data.length},e.prototype.getBitsLength=function(){return e.getBitsLength(this.data.length)},e.prototype.write=function(n){for(let r=0,o=this.data.length;r<o;r++)n.put(this.data[r],8)},Gl=e,Gl}var Yl,Sf;function I5(){if(Sf)return Yl;Sf=1;const t=Zi(),e=Xi();function n(r){this.mode=t.KANJI,this.data=r}return n.getBitsLength=function(o){return o*13},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(r){let o;for(o=0;o<this.data.length;o++){let i=e.toSJIS(this.data[o]);if(i>=33088&&i<=40956)i-=33088;else if(i>=57408&&i<=60351)i-=49472;else throw new Error("Invalid SJIS character: "+this.data[o]+`
Make sure your charset is UTF-8`);i=(i>>>8&255)*192+(i&255),r.put(i,13)}},Yl=n,Yl}var $l={exports:{}},Nf;function T5(){return Nf||(Nf=1,function(t){var e={single_source_shortest_paths:function(n,r,o){var i={},s={};s[r]=0;var l=e.PriorityQueue.make();l.push(r,0);for(var u,p,g,b,v,d,_,S,T;!l.empty();){u=l.pop(),p=u.value,b=u.cost,v=n[p]||{};for(g in v)v.hasOwnProperty(g)&&(d=v[g],_=b+d,S=s[g],T=typeof s[g]>"u",(T||S>_)&&(s[g]=_,l.push(g,_),i[g]=p))}if(typeof o<"u"&&typeof s[o]>"u"){var A=["Could not find a path from ",r," to ",o,"."].join("");throw new Error(A)}return i},extract_shortest_path_from_predecessor_list:function(n,r){for(var o=[],i=r;i;)o.push(i),n[i],i=n[i];return o.reverse(),o},find_path:function(n,r,o){var i=e.single_source_shortest_paths(n,r,o);return e.extract_shortest_path_from_predecessor_list(i,o)},PriorityQueue:{make:function(n){var r=e.PriorityQueue,o={},i;n=n||{};for(i in r)r.hasOwnProperty(i)&&(o[i]=r[i]);return o.queue=[],o.sorter=n.sorter||r.default_sorter,o},default_sorter:function(n,r){return n.cost-r.cost},push:function(n,r){var o={value:n,cost:r};this.queue.push(o),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};t.exports=e}($l)),$l.exports}var Lf;function j5(){return Lf||(Lf=1,function(t){const e=Zi(),n=k5(),r=C5(),o=E5(),i=I5(),s=Ch(),l=Xi(),u=T5();function p(A){return unescape(encodeURIComponent(A)).length}function g(A,j,q){const Y=[];let le;for(;(le=A.exec(q))!==null;)Y.push({data:le[0],index:le.index,mode:j,length:le[0].length});return Y}function b(A){const j=g(s.NUMERIC,e.NUMERIC,A),q=g(s.ALPHANUMERIC,e.ALPHANUMERIC,A);let Y,le;return l.isKanjiModeEnabled()?(Y=g(s.BYTE,e.BYTE,A),le=g(s.KANJI,e.KANJI,A)):(Y=g(s.BYTE_KANJI,e.BYTE,A),le=[]),j.concat(q,Y,le).sort(function($,F){return $.index-F.index}).map(function($){return{data:$.data,mode:$.mode,length:$.length}})}function v(A,j){switch(j){case e.NUMERIC:return n.getBitsLength(A);case e.ALPHANUMERIC:return r.getBitsLength(A);case e.KANJI:return i.getBitsLength(A);case e.BYTE:return o.getBitsLength(A)}}function d(A){return A.reduce(function(j,q){const Y=j.length-1>=0?j[j.length-1]:null;return Y&&Y.mode===q.mode?(j[j.length-1].data+=q.data,j):(j.push(q),j)},[])}function _(A){const j=[];for(let q=0;q<A.length;q++){const Y=A[q];switch(Y.mode){case e.NUMERIC:j.push([Y,{data:Y.data,mode:e.ALPHANUMERIC,length:Y.length},{data:Y.data,mode:e.BYTE,length:Y.length}]);break;case e.ALPHANUMERIC:j.push([Y,{data:Y.data,mode:e.BYTE,length:Y.length}]);break;case e.KANJI:j.push([Y,{data:Y.data,mode:e.BYTE,length:p(Y.data)}]);break;case e.BYTE:j.push([{data:Y.data,mode:e.BYTE,length:p(Y.data)}])}}return j}function S(A,j){const q={},Y={start:{}};let le=["start"];for(let ae=0;ae<A.length;ae++){const $=A[ae],F=[];for(let J=0;J<$.length;J++){const re=$[J],N=""+ae+J;F.push(N),q[N]={node:re,lastCount:0},Y[N]={};for(let k=0;k<le.length;k++){const O=le[k];q[O]&&q[O].node.mode===re.mode?(Y[O][N]=v(q[O].lastCount+re.length,re.mode)-v(q[O].lastCount,re.mode),q[O].lastCount+=re.length):(q[O]&&(q[O].lastCount=re.length),Y[O][N]=v(re.length,re.mode)+4+e.getCharCountIndicator(re.mode,j))}}le=F}for(let ae=0;ae<le.length;ae++)Y[le[ae]].end=0;return{map:Y,table:q}}function T(A,j){let q;const Y=e.getBestModeForData(A);if(q=e.from(j,Y),q!==e.BYTE&&q.bit<Y.bit)throw new Error('"'+A+'" cannot be encoded with mode '+e.toString(q)+`.
 Suggested mode is: `+e.toString(Y));switch(q===e.KANJI&&!l.isKanjiModeEnabled()&&(q=e.BYTE),q){case e.NUMERIC:return new n(A);case e.ALPHANUMERIC:return new r(A);case e.KANJI:return new i(A);case e.BYTE:return new o(A)}}t.fromArray=function(j){return j.reduce(function(q,Y){return typeof Y=="string"?q.push(T(Y,null)):Y.data&&q.push(T(Y.data,Y.mode)),q},[])},t.fromString=function(j,q){const Y=b(j,l.isKanjiModeEnabled()),le=_(Y),ae=S(le,q),$=u.find_path(ae.map,"start","end"),F=[];for(let J=1;J<$.length-1;J++)F.push(ae.table[$[J]].node);return t.fromArray(d(F))},t.rawSplit=function(j){return t.fromArray(b(j,l.isKanjiModeEnabled()))}}(Hl)),Hl}var Pf;function B5(){if(Pf)return El;Pf=1;const t=Xi(),e=jc(),n=v5(),r=y5(),o=w5(),i=x5(),s=A5(),l=_h(),u=L5(),p=P5(),g=_5(),b=Zi(),v=j5();function d(ae,$){const F=ae.size,J=i.getPositions($);for(let re=0;re<J.length;re++){const N=J[re][0],k=J[re][1];for(let O=-1;O<=7;O++)if(!(N+O<=-1||F<=N+O))for(let C=-1;C<=7;C++)k+C<=-1||F<=k+C||(O>=0&&O<=6&&(C===0||C===6)||C>=0&&C<=6&&(O===0||O===6)||O>=2&&O<=4&&C>=2&&C<=4?ae.set(N+O,k+C,!0,!0):ae.set(N+O,k+C,!1,!0))}}function _(ae){const $=ae.size;for(let F=8;F<$-8;F++){const J=F%2===0;ae.set(F,6,J,!0),ae.set(6,F,J,!0)}}function S(ae,$){const F=o.getPositions($);for(let J=0;J<F.length;J++){const re=F[J][0],N=F[J][1];for(let k=-2;k<=2;k++)for(let O=-2;O<=2;O++)k===-2||k===2||O===-2||O===2||k===0&&O===0?ae.set(re+k,N+O,!0,!0):ae.set(re+k,N+O,!1,!0)}}function T(ae,$){const F=ae.size,J=p.getEncodedBits($);let re,N,k;for(let O=0;O<18;O++)re=Math.floor(O/3),N=O%3+F-8-3,k=(J>>O&1)===1,ae.set(re,N,k,!0),ae.set(N,re,k,!0)}function A(ae,$,F){const J=ae.size,re=g.getEncodedBits($,F);let N,k;for(N=0;N<15;N++)k=(re>>N&1)===1,N<6?ae.set(N,8,k,!0):N<8?ae.set(N+1,8,k,!0):ae.set(J-15+N,8,k,!0),N<8?ae.set(8,J-N-1,k,!0):N<9?ae.set(8,15-N-1+1,k,!0):ae.set(8,15-N-1,k,!0);ae.set(J-8,8,1,!0)}function j(ae,$){const F=ae.size;let J=-1,re=F-1,N=7,k=0;for(let O=F-1;O>0;O-=2)for(O===6&&O--;;){for(let C=0;C<2;C++)if(!ae.isReserved(re,O-C)){let X=!1;k<$.length&&(X=($[k]>>>N&1)===1),ae.set(re,O-C,X),N--,N===-1&&(k++,N=7)}if(re+=J,re<0||F<=re){re-=J,J=-J;break}}}function q(ae,$,F){const J=new n;F.forEach(function(C){J.put(C.mode.bit,4),J.put(C.getLength(),b.getCharCountIndicator(C.mode,ae)),C.write(J)});const re=t.getSymbolTotalCodewords(ae),N=l.getTotalCodewordsCount(ae,$),k=(re-N)*8;for(J.getLengthInBits()+4<=k&&J.put(0,4);J.getLengthInBits()%8!==0;)J.putBit(0);const O=(k-J.getLengthInBits())/8;for(let C=0;C<O;C++)J.put(C%2?17:236,8);return Y(J,ae,$)}function Y(ae,$,F){const J=t.getSymbolTotalCodewords($),re=l.getTotalCodewordsCount($,F),N=J-re,k=l.getBlocksCount($,F),O=J%k,C=k-O,X=Math.floor(J/k),ne=Math.floor(N/k),ue=ne+1,Q=X-ne,he=new u(Q);let V=0;const me=new Array(k),x=new Array(k);let B=0;const M=new Uint8Array(ae.buffer);for(let oe=0;oe<k;oe++){const ve=oe<C?ne:ue;me[oe]=M.slice(V,V+ve),x[oe]=he.encode(me[oe]),V+=ve,B=Math.max(B,ve)}const z=new Uint8Array(J);let H=0,G,ee;for(G=0;G<B;G++)for(ee=0;ee<k;ee++)G<me[ee].length&&(z[H++]=me[ee][G]);for(G=0;G<Q;G++)for(ee=0;ee<k;ee++)z[H++]=x[ee][G];return z}function le(ae,$,F,J){let re;if(Array.isArray(ae))re=v.fromArray(ae);else if(typeof ae=="string"){let X=$;if(!X){const ne=v.rawSplit(ae);X=p.getBestVersionForData(ne,F)}re=v.fromString(ae,X||40)}else throw new Error("Invalid data");const N=p.getBestVersionForData(re,F);if(!N)throw new Error("The amount of data is too big to be stored in a QR Code");if(!$)$=N;else if($<N)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+N+`.
`);const k=q($,F,re),O=t.getSymbolSize($),C=new r(O);return d(C,$),_(C),S(C,$),A(C,F,0),$>=7&&T(C,$),j(C,k),isNaN(J)&&(J=s.getBestMask(C,A.bind(null,C,F))),s.applyMask(J,C),A(C,F,J),{modules:C,version:$,errorCorrectionLevel:F,maskPattern:J,segments:re}}return El.create=function($,F){if(typeof $>"u"||$==="")throw new Error("No input text");let J=e.M,re,N;return typeof F<"u"&&(J=e.from(F.errorCorrectionLevel,e.M),re=p.from(F.version),N=s.from(F.maskPattern),F.toSJISFunc&&t.setToSJISFunction(F.toSJISFunc)),le($,re,J,N)},El}var Jl={},Kl={},_f;function Eh(){return _f||(_f=1,function(t){function e(n){if(typeof n=="number"&&(n=n.toString()),typeof n!="string")throw new Error("Color should be defined as hex string");let r=n.slice().replace("#","").split("");if(r.length<3||r.length===5||r.length>8)throw new Error("Invalid hex color: "+n);(r.length===3||r.length===4)&&(r=Array.prototype.concat.apply([],r.map(function(i){return[i,i]}))),r.length===6&&r.push("F","F");const o=parseInt(r.join(""),16);return{r:o>>24&255,g:o>>16&255,b:o>>8&255,a:o&255,hex:"#"+r.slice(0,6).join("")}}t.getOptions=function(r){r||(r={}),r.color||(r.color={});const o=typeof r.margin>"u"||r.margin===null||r.margin<0?4:r.margin,i=r.width&&r.width>=21?r.width:void 0,s=r.scale||4;return{width:i,scale:i?4:s,margin:o,color:{dark:e(r.color.dark||"#000000ff"),light:e(r.color.light||"#ffffffff")},type:r.type,rendererOpts:r.rendererOpts||{}}},t.getScale=function(r,o){return o.width&&o.width>=r+o.margin*2?o.width/(r+o.margin*2):o.scale},t.getImageWidth=function(r,o){const i=t.getScale(r,o);return Math.floor((r+o.margin*2)*i)},t.qrToImageData=function(r,o,i){const s=o.modules.size,l=o.modules.data,u=t.getScale(s,i),p=Math.floor((s+i.margin*2)*u),g=i.margin*u,b=[i.color.light,i.color.dark];for(let v=0;v<p;v++)for(let d=0;d<p;d++){let _=(v*p+d)*4,S=i.color.light;if(v>=g&&d>=g&&v<p-g&&d<p-g){const T=Math.floor((v-g)/u),A=Math.floor((d-g)/u);S=b[l[T*s+A]?1:0]}r[_++]=S.r,r[_++]=S.g,r[_++]=S.b,r[_]=S.a}}}(Kl)),Kl}var kf;function M5(){return kf||(kf=1,function(t){const e=Eh();function n(o,i,s){o.clearRect(0,0,i.width,i.height),i.style||(i.style={}),i.height=s,i.width=s,i.style.height=s+"px",i.style.width=s+"px"}function r(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}t.render=function(i,s,l){let u=l,p=s;typeof u>"u"&&(!s||!s.getContext)&&(u=s,s=void 0),s||(p=r()),u=e.getOptions(u);const g=e.getImageWidth(i.modules.size,u),b=p.getContext("2d"),v=b.createImageData(g,g);return e.qrToImageData(v.data,i,u),n(b,p,g),b.putImageData(v,0,0),p},t.renderToDataURL=function(i,s,l){let u=l;typeof u>"u"&&(!s||!s.getContext)&&(u=s,s=void 0),u||(u={});const p=t.render(i,s,u),g=u.type||"image/png",b=u.rendererOpts||{};return p.toDataURL(g,b.quality)}}(Jl)),Jl}var Xl={},Cf;function R5(){if(Cf)return Xl;Cf=1;const t=Eh();function e(o,i){const s=o.a/255,l=i+'="'+o.hex+'"';return s<1?l+" "+i+'-opacity="'+s.toFixed(2).slice(1)+'"':l}function n(o,i,s){let l=o+i;return typeof s<"u"&&(l+=" "+s),l}function r(o,i,s){let l="",u=0,p=!1,g=0;for(let b=0;b<o.length;b++){const v=Math.floor(b%i),d=Math.floor(b/i);!v&&!p&&(p=!0),o[b]?(g++,b>0&&v>0&&o[b-1]||(l+=p?n("M",v+s,.5+d+s):n("m",u,0),u=0,p=!1),v+1<i&&o[b+1]||(l+=n("h",g),g=0)):u++}return l}return Xl.render=function(i,s,l){const u=t.getOptions(s),p=i.modules.size,g=i.modules.data,b=p+u.margin*2,v=u.color.light.a?"<path "+e(u.color.light,"fill")+' d="M0 0h'+b+"v"+b+'H0z"/>':"",d="<path "+e(u.color.dark,"stroke")+' d="'+r(g,p,u.margin)+'"/>',_='viewBox="0 0 '+b+" "+b+'"',T='<svg xmlns="http://www.w3.org/2000/svg" '+(u.width?'width="'+u.width+'" height="'+u.width+'" ':"")+_+' shape-rendering="crispEdges">'+v+d+`</svg>
`;return typeof l=="function"&&l(null,T),T},Xl}var Ef;function F5(){if(Ef)return La;Ef=1;const t=b5(),e=B5(),n=M5(),r=R5();function o(i,s,l,u,p){const g=[].slice.call(arguments,1),b=g.length,v=typeof g[b-1]=="function";if(!v&&!t())throw new Error("Callback required as last argument");if(v){if(b<2)throw new Error("Too few arguments provided");b===2?(p=l,l=s,s=u=void 0):b===3&&(s.getContext&&typeof p>"u"?(p=u,u=void 0):(p=u,u=l,l=s,s=void 0))}else{if(b<1)throw new Error("Too few arguments provided");return b===1?(l=s,s=u=void 0):b===2&&!s.getContext&&(u=l,l=s,s=void 0),new Promise(function(d,_){try{const S=e.create(l,u);d(i(S,s,u))}catch(S){_(S)}})}try{const d=e.create(l,u);p(null,i(d,s,u))}catch(d){p(d)}}return La.create=e.create,La.toCanvas=o.bind(null,n.render),La.toDataURL=o.bind(null,n.renderToDataURL),La.toString=o.bind(null,function(i,s,l){return r.render(i,l)}),La}var O5=F5();const D5=Tf(O5),q5=["Karachi","Lahore","Islamabad","Rawalpindi","Faisalabad","Multan","Peshawar","Quetta","Sialkot","Hyderabad","Gujranwala","Bahawalpur","Sargodha","Sukkur","Abbottabad","Mardan","Swat","Dera Ghazi Khan","Sheikhupura","Jhelum"],If=async t=>{const e=new Ke,n=t.shopifyOrderData||{},r=n.customer||{},o=n.billing_address||{},i=n.shipping_address||{},s=JSON.stringify({orderNumber:n.order_number,customer:`${r.first_name} ${r.last_name}`,city:o.city,amount:n.total_price,currency:n.currency,phone:i.phone,address:i.address1});try{const l=await D5.toDataURL(s,{width:100,margin:1}),u=e.internal.pageSize.width,p=e.internal.pageSize.height;e.setFillColor(41,128,185),e.rect(0,0,u,40,"F"),e.setTextColor(255,255,255),e.setFontSize(24),e.setFont("helvetica","bold"),e.text("RUSHRR COURIER",u/2,25,{align:"center"}),e.setFontSize(12),e.setFont("helvetica","normal"),e.text("Express Delivery Service",u/2,35,{align:"center"}),e.setTextColor(0,0,0),e.setFontSize(20),e.setFont("helvetica","bold"),e.text("AIRWAY BILL",u/2,55,{align:"center"}),e.setDrawColor(52,73,94),e.setLineWidth(1),e.rect(15,65,u-30,25),e.setFillColor(236,240,241),e.rect(15,65,u-30,25,"F"),e.setTextColor(52,73,94),e.setFontSize(14),e.setFont("helvetica","bold"),e.text(`Order #: ${n.order_number||"N/A"}`,20,75),e.text(`Date: ${new Date().toLocaleDateString()}`,20,85),e.setFontSize(16),e.setFont("helvetica","bold"),e.setTextColor(52,73,94),e.text("CUSTOMER DETAILS",20,105),e.setDrawColor(52,73,94),e.line(20,108,u-20,108),e.setFontSize(12),e.setFont("helvetica","normal"),e.setTextColor(0,0,0),[`Name: ${r.first_name||""} ${r.last_name||""}`,`Email: ${r.email||"N/A"}`,`Phone: ${i.phone||"N/A"}`,`City: ${o.city||"N/A"}`].forEach((b,v)=>{e.text(b,20,120+v*8)}),e.setFontSize(16),e.setFont("helvetica","bold"),e.setTextColor(52,73,94),e.text("SHIPPING ADDRESS",20,160),e.line(20,163,u-20,163),e.setFontSize(12),e.setFont("helvetica","normal"),e.setTextColor(0,0,0),e.text(i.address1||"N/A",20,175),e.text(`${i.city||""}, ${i.country||""}`,20,185),e.text(`Postal Code: ${i.zip||"N/A"}`,20,195),e.setFontSize(16),e.setFont("helvetica","bold"),e.setTextColor(52,73,94),e.text("ORDER SUMMARY",20,215),e.line(20,218,u-20,218),e.setFillColor(241,196,15),e.rect(15,225,u-30,20,"F"),e.setFontSize(14),e.setFont("helvetica","bold"),e.setTextColor(0,0,0),e.text(`Total Amount: ${n.total_price||"0.00"} ${n.currency||"PKR"}`,20,235),e.text(`COD: ${t.codCollected||"N/A"}`,20,242),e.addImage(l,"PNG",u-60,120,40,40),e.setFontSize(10),e.text("Scan for Details",u-55,170,{align:"center"}),e.setFillColor(52,73,94),e.rect(0,p-30,u,30,"F"),e.setTextColor(255,255,255),e.setFontSize(10),e.text("© 2025 Rushrr Courier - Express Delivery Service",u/2,p-15,{align:"center"}),e.text("For support: <EMAIL> | Tel: +92-XXX-XXXXXXX",u/2,p-8,{align:"center"}),e.save(`Airway-Bill-${n.order_number||t.id}.pdf`)}catch(l){console.error("Error generating QR code:",l),alert("Error generating airway bill. Please try again.")}};function U5({token:t}){var re,N,k,O;const[e,n]=de.useState([]),[r,o]=de.useState([]),[i,s]=de.useState(!0),[l,u]=de.useState(null),[p,g]=de.useState(null),[b,v]=de.useState(null),[d,_]=de.useState(!1),[S,T]=de.useState(null);de.useEffect(()=>{(async()=>{try{T(null);const ne=await(await fetch("api/orders",{method:"GET"})).json();ne.success?n(ne.orders):T(ne.error||"Failed to fetch orders")}catch(X){console.error("Failed to fetch orders",X),T("Network error: Unable to fetch orders. Please check your connection.")}finally{s(!1)}})()},[t]);const{selectedResources:A,allResourcesSelected:j,handleSelectionChange:q}=Dd(e),Y=C=>{u(C);const X=z5(C);v(X),g(X)};function le(C,X){var he,V,me,x,B,M,z,H,G,ee;const ne={};C.customerEmail!==X.customerEmail&&(ne.email=X.customerEmail),C.currency!==X.currency&&(ne.currency=X.currency),C.totalPrice!==X.totalPrice&&(ne.total_price=X.totalPrice);const ue={};((he=C.shippingAddress)==null?void 0:he.city)!==((V=X.shippingAddress)==null?void 0:V.city)&&(ue.city=X.shippingAddress.city),((me=C.shippingAddress)==null?void 0:me.phone)!==((x=X.shippingAddress)==null?void 0:x.phone)&&(ue.phone=X.shippingAddress.phone),((B=C.shippingAddress)==null?void 0:B.address1)!==((M=X.shippingAddress)==null?void 0:M.address1)&&(ue.address1=X.shippingAddress.address1),Object.keys(ue).length>0&&(ne.shipping_address=ue);const Q={};return((z=C.billingAddress)==null?void 0:z.city)!==((H=X.billingAddress)==null?void 0:H.city)&&(Q.city=X.billingAddress.city),((G=C.billingAddress)==null?void 0:G.address1)!==((ee=X.billingAddress)==null?void 0:ee.address1)&&(Q.address1=X.billingAddress.address1),Object.keys(Q).length>0&&(ne.billing_address=Q),ne}const ae=async()=>{if(!(l!=null&&l.id)||!p)return;const C=le(p,b);if(Object.keys(C).length===0){alert("No changes to save.");return}try{const X=await fetch(`https://backend.rushr-admin.com/api/orders/update?id=${l.id}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(C)}),ne=await X.json();X.ok?(alert("Order updated successfully!"),u(null)):alert((ne==null?void 0:ne.message)||"Update failed.")}catch(X){console.error("Update error:",X),alert("An error occurred while updating the order.")}},$=async()=>{if(A.length===0){alert("Please select at least one order.");return}_(!0);try{const C=e.filter(Q=>A.includes(Q.id)),X=C.map(Q=>Q.shopifyOrderId),ne=await fetch("https://backend.rushr-admin.com/api/orders/book",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({orderId:X})}),ue=await ne.json();if(ne.ok&&ue.success){for(const he of C)await If(he);o(he=>[...he,...C]);const Q=e.filter(he=>!A.includes(he.id));n(Q),alert(`${C.length} order(s) booked successfully! Airway bills have been downloaded.`)}else alert(ue.message||"Booking failed")}catch(C){console.error("Booking failed:",C),alert("An error occurred while booking orders.")}finally{_(!1)}},F=e.filter(C=>C.status==="unbooked"),J=F.map((C,X)=>{const ne=C.shopifyOrderData||{},ue=ne.customer||{},Q=ne.billing_address||{},he=ne.shipping_address||{};return ce.jsxs(Rt.Row,{id:C.id,selected:A.includes(C.id),position:X,children:[ce.jsx(Rt.Cell,{children:ce.jsx(Ye,{variant:"bodyMd",fontWeight:"medium",children:X+1})}),ce.jsx(Rt.Cell,{children:ce.jsx(or,{tone:"info",children:ne.order_number||"-"})}),ce.jsx(Rt.Cell,{children:ce.jsx(Ye,{variant:"bodyMd",fontWeight:"medium",children:`${ue.first_name||""} ${ue.last_name||""}`})}),ce.jsx(Rt.Cell,{children:ce.jsx(or,{tone:"attention",children:Q.city||"-"})}),ce.jsx(Rt.Cell,{children:ce.jsx(Ye,{variant:"bodyMd",children:C.codCollected??"N/A"})}),ce.jsx(Rt.Cell,{children:ce.jsx(or,{tone:"warning",children:C.status||"Unbooked"})}),ce.jsx(Rt.Cell,{children:ce.jsx(Ye,{variant:"bodyMd",color:"subdued",children:he.address1||"N/A"})}),ce.jsx(Rt.Cell,{children:ce.jsx(Ye,{variant:"bodyMd",fontWeight:"semibold",color:"success",children:`${ne.total_price||"0.00"} ${ne.currency||"PKR"}`})}),ce.jsx(Rt.Cell,{children:ce.jsx(Xr,{size:"slim",onClick:()=>Y(C),children:"Edit"})})]},C.id)});return i?ce.jsx(gs,{fullWidth:!0,children:ce.jsx(Bn,{children:ce.jsx(Bn.Section,{children:ce.jsx(br,{children:ce.jsxs(ut,{padding:"800",textAlign:"center",children:[ce.jsx(xs,{size:"large"}),ce.jsx(ut,{paddingBlockStart:"400",children:ce.jsx(Ye,{variant:"headingMd",children:"Loading orders..."})})]})})})})}):ce.jsxs(gs,{fullWidth:!0,children:[ce.jsxs(Bn,{children:[S&&ce.jsx(Bn.Section,{children:ce.jsx(Yf,{title:"Error",tone:"critical",onDismiss:()=>T(null),children:ce.jsx("p",{children:S})})}),ce.jsx(Bn.Section,{children:ce.jsx(br,{children:ce.jsx(ut,{padding:"600",children:ce.jsxs(Ot,{align:"space-between",blockAlign:"center",children:[ce.jsxs(Ot,{gap:"400",blockAlign:"center",children:[ce.jsx("img",{src:"https://res.cloudinary.com/dgiqiysh5/image/upload/v1750681695/WhatsApp_Image_2025-06-23_at_16.02.36_vyjear.jpg",alt:"Rushrr Logo",style:{height:"50px",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)"}}),ce.jsxs(Ir,{gap:"100",children:[ce.jsx(Ye,{variant:"headingLg",as:"h1",children:"📦 Rushrr Courier Dashboard"}),ce.jsx(Ye,{variant:"bodyMd",color:"subdued",children:"Manage and book orders from your Shopify store with automated airway bill generation"})]})]}),ce.jsx(ut,{children:ce.jsxs(or,{tone:"success",size:"large",children:[F.length," Unbooked Orders"]})})]})})})}),ce.jsx(Bn.Section,{children:ce.jsxs(Ot,{gap:"400",children:[ce.jsx("div",{style:{flex:1},children:ce.jsx(br,{children:ce.jsxs(ut,{padding:"400",textAlign:"center",children:[ce.jsx(Ye,{variant:"headingXl",color:"success",children:F.length}),ce.jsx(Ye,{variant:"bodyMd",color:"subdued",children:"Unbooked Orders"})]})})}),ce.jsx("div",{style:{flex:1},children:ce.jsx(br,{children:ce.jsxs(ut,{padding:"400",textAlign:"center",children:[ce.jsx(Ye,{variant:"headingXl",color:"info",children:r.length}),ce.jsx(Ye,{variant:"bodyMd",color:"subdued",children:"Booked Orders"})]})})})]})}),ce.jsx(Bn.Section,{children:ce.jsxs(br,{children:[ce.jsx(ut,{padding:"400",children:ce.jsxs(Ot,{align:"space-between",blockAlign:"center",children:[ce.jsx(Ye,{variant:"headingMd",children:"Unbooked Orders"}),ce.jsx(Xr,{variant:"primary",onClick:$,loading:d,disabled:A.length===0,size:"large",children:d?"Booking & Generating Bills...":`Book ${A.length} Orders`})]})}),ce.jsx(Rt,{resourceName:{singular:"order",plural:"orders"},itemCount:F.length,selectedItemsCount:j?"All":A.length,onSelectionChange:q,headings:[{title:"#"},{title:"Order #"},{title:"Customer"},{title:"City"},{title:"COD"},{title:"Status"},{title:"Shipping Address"},{title:"Amount"},{title:"Actions"}],selectable:!0,children:J})]})}),r.length>0&&ce.jsx(Bn.Section,{children:ce.jsxs(br,{children:[ce.jsx(ut,{padding:"400",children:ce.jsx(Ye,{variant:"headingMd",children:"Recently Booked Orders"})}),ce.jsx(Rt,{resourceName:{singular:"booked order",plural:"booked orders"},itemCount:r.length,headings:[{title:"#"},{title:"Order #"},{title:"Customer"},{title:"City"},{title:"COD"},{title:"Status"},{title:"Shipping Address"},{title:"Amount"},{title:"Airway Bill"}],selectable:!1,children:r.map((C,X)=>{const ne=C.shopifyOrderData||{},ue=ne.customer||{},Q=ne.billing_address||{},he=ne.shipping_address||{};return ce.jsxs(Rt.Row,{id:`booked-${C.id}`,position:X,children:[ce.jsx(Rt.Cell,{children:ce.jsx(Ye,{variant:"bodyMd",fontWeight:"medium",children:X+1})}),ce.jsx(Rt.Cell,{children:ce.jsx(or,{tone:"info",children:ne.order_number||"-"})}),ce.jsx(Rt.Cell,{children:ce.jsx(Ye,{variant:"bodyMd",fontWeight:"medium",children:`${ue.first_name||""} ${ue.last_name||""}`})}),ce.jsx(Rt.Cell,{children:ce.jsx(or,{tone:"attention",children:Q.city||"-"})}),ce.jsx(Rt.Cell,{children:ce.jsx(Ye,{variant:"bodyMd",children:C.codCollected??"N/A"})}),ce.jsx(Rt.Cell,{children:ce.jsx(or,{tone:"success",children:"Booked"})}),ce.jsx(Rt.Cell,{children:ce.jsx(Ye,{variant:"bodyMd",color:"subdued",children:he.address1||"N/A"})}),ce.jsx(Rt.Cell,{children:ce.jsx(Ye,{variant:"bodyMd",fontWeight:"semibold",color:"success",children:`${ne.total_price||"0.00"} ${ne.currency||"PKR"}`})}),ce.jsx(Rt.Cell,{children:ce.jsx(Xr,{size:"slim",onClick:()=>If(C),tone:"success",children:"Download Bill"})})]},C.id)})})]})})]}),l&&ce.jsx(oc,{open:!0,onClose:()=>u(null),title:`Edit Order #${l.orderNumber}`,primaryAction:{content:"Save Changes",onAction:ae},secondaryActions:[{content:"Cancel",onAction:()=>u(null)}],children:ce.jsx(oc.Section,{children:ce.jsxs(Ir,{gap:"400",children:[ce.jsx(Si,{label:"Customer Name",value:b.customerName||"",onChange:C=>v({...b,customerName:C})}),ce.jsx(Si,{label:"Customer Email",value:b.customerEmail||"",onChange:C=>v({...b,customerEmail:C})}),ce.jsx(d2,{label:"City",options:q5.map(C=>({label:C,value:C})),value:((re=b.shippingAddress)==null?void 0:re.city)||"",onChange:C=>v({...b,shippingAddress:{...b.shippingAddress,city:C}})}),ce.jsx(Si,{label:"Billing Address",value:((N=b.billingAddress)==null?void 0:N.address1)||"",onChange:C=>v({...b,billingAddress:{...b.billingAddress,address1:C}})}),ce.jsx(Si,{label:"Shipping Address",value:((k=b.shippingAddress)==null?void 0:k.address1)||"",onChange:C=>v({...b,shippingAddress:{...b.shippingAddress,address1:C}})}),ce.jsx(Si,{label:"Phone (Shipping)",value:((O=b.shippingAddress)==null?void 0:O.phone)||"",onChange:C=>v({...b,shippingAddress:{...b.shippingAddress,phone:C}})}),ce.jsx(Si,{label:"Total Price",type:"number",value:b.totalPrice||"",onChange:C=>v({...b,totalPrice:C})}),ce.jsx(Si,{label:"Currency",value:b.currency||"",onChange:C=>v({...b,currency:C})})]})})})]})}function z5(t){const e=t.shopifyOrderData||{},n=e.customer||{},r=e.billing_address||{},o=e.shipping_address||{};return{...t,customerName:`${n.first_name||""} ${n.last_name||""}`.trim(),customerEmail:e.email||e.contact_email||"",billingAddress:{address1:r.address1||"",city:r.city||"",country:r.country||"",zip:r.zip||""},shippingAddress:{address1:o.address1||"",city:o.city||"",country:o.country||"",zip:o.zip||"",phone:o.phone||e.phone||""},totalPrice:e.total_price||"",currency:e.currency||""}}function K5(){const{shopifyStoreName:t,shopifyStoreUrl:e}=pd(),[n,r]=de.useState(""),[o,i]=de.useState(null),[s,l]=de.useState(!1),[u,p]=de.useState(!0);de.useEffect(()=>{(async()=>{try{const d=await fetch("https://backend.rushr-admin.com/api/auth/verify-shopify-store",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({shopifyStoreUrl:e})}),_=await d.json();d.ok&&(_!=null&&_.success)&&(_.token&&(await g(_.token),r(_.token)),l(!0))}catch(d){console.error("Error checking store status",d)}finally{p(!1)}})()},[e]);const g=async v=>{try{await fetch("/api/save-token",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:v})})}catch(d){console.error("Error saving token to session:",d)}},b=async()=>{try{const v=await fetch("https://backend.rushr-admin.com/api/auth/verify-api-key",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({apiKey:n,shopifyStoreUrl:e,shopifyStoreName:t})}),d=await v.json();v.ok?(l(!0),i({type:"success",content:"Connection successful!"})):i({type:"error",content:(d==null?void 0:d.error)||"Failed to verify token."})}catch{i({type:"error",content:"Network error or server unavailable."})}};return u?ce.jsx(gs,{fullWidth:!0,children:ce.jsx(Bn,{children:ce.jsx(Bn.Section,{children:ce.jsx(br,{children:ce.jsxs(ut,{padding:"800",textAlign:"center",children:[ce.jsx(xs,{accessibilityLabel:"Checking connection...",size:"large"}),ce.jsx(ut,{paddingBlockStart:"400",children:ce.jsx(Ye,{variant:"headingMd",children:"Checking store connection..."})})]})})})})}):s?ce.jsx(U5,{token:n}):ce.jsx(gs,{fullWidth:!0,children:ce.jsxs(Bn,{children:[ce.jsx(Bn.Section,{children:ce.jsx(br,{children:ce.jsxs(ut,{padding:"600",textAlign:"center",children:[ce.jsx("img",{src:"https://res.cloudinary.com/dgiqiysh5/image/upload/v1750681695/WhatsApp_Image_2025-06-23_at_16.02.36_vyjear.jpg",alt:"Rushrr Logo",style:{height:"80px",marginBottom:"20px",borderRadius:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.15)"}}),ce.jsx(Ye,{variant:"displayMd",as:"h1",children:"Welcome to Rushrr Courier"}),ce.jsx(ut,{paddingBlockStart:"200",children:ce.jsx(Ye,{variant:"bodyLg",color:"subdued",children:"Connect your Shopify store to start managing deliveries with automated airway bill generation"})})]})})}),ce.jsx(Bn.Section,{children:ce.jsxs(Ot,{gap:"600",align:"start",children:[ce.jsx("div",{style:{flex:1},children:ce.jsx(br,{children:ce.jsx(ut,{padding:"500",children:ce.jsxs(Ir,{gap:"400",children:[ce.jsx(Ye,{variant:"headingMd",children:"🚀 Quick Setup Guide"}),ce.jsx(pu,{}),ce.jsxs(Ir,{gap:"300",children:[ce.jsxs(Ot,{gap:"200",blockAlign:"start",children:[ce.jsx(or,{tone:"info",children:"1"}),ce.jsx(Ye,{children:"Get your API token from your merchant dashboard"})]}),ce.jsxs(Ot,{gap:"200",blockAlign:"start",children:[ce.jsx(or,{tone:"info",children:"2"}),ce.jsx(Ye,{children:"Enter the token in the form and save settings"})]}),ce.jsxs(Ot,{gap:"200",blockAlign:"start",children:[ce.jsx(or,{tone:"info",children:"3"}),ce.jsx(Ye,{children:"Access the dashboard after successful verification"})]}),ce.jsxs(Ot,{gap:"200",blockAlign:"start",children:[ce.jsx(or,{tone:"success",children:"✓"}),ce.jsx(Ye,{children:"Start booking orders with auto airway bill generation"})]})]})]})})})}),ce.jsx("div",{style:{flex:1},children:ce.jsx(br,{children:ce.jsx(ut,{padding:"500",children:ce.jsxs(Ir,{gap:"400",children:[ce.jsx(Ye,{variant:"headingMd",children:"🔐 API Configuration"}),ce.jsx(pu,{}),ce.jsx(Si,{label:"API Token",value:n,onChange:v=>r(v),placeholder:"Enter your Rushrr API token",helpText:"You can find this in your Rushrr merchant dashboard"}),ce.jsx(Xr,{variant:"primary",onClick:b,size:"large",fullWidth:!0,children:"🚀 Connect & Verify"}),ce.jsx(ut,{children:ce.jsxs(Ye,{variant:"bodyMd",color:"subdued",children:[ce.jsx("strong",{children:"Default Weight:"})," 0.5 kg"]})}),o&&ce.jsx(Yf,{title:o.content,status:o.type==="success"?"success":"critical"})]})})})})]})}),ce.jsx(Bn.Section,{children:ce.jsx(br,{children:ce.jsxs(ut,{padding:"500",children:[ce.jsx(Ye,{variant:"headingMd",textAlign:"center",children:"✨ Features You'll Get"}),ce.jsx(ut,{paddingBlockStart:"400",children:ce.jsxs(Ot,{gap:"600",align:"start",children:[ce.jsxs("div",{style:{flex:1,textAlign:"center"},children:[ce.jsx(ut,{paddingBlockEnd:"300",children:ce.jsx(Ye,{variant:"headingLg",children:"📦"})}),ce.jsx(Ye,{variant:"headingMd",children:"Order Management"}),ce.jsx(ut,{paddingBlockStart:"200",children:ce.jsx(Ye,{variant:"bodyMd",color:"subdued",children:"View, edit, and manage all your Shopify orders in one place"})})]}),ce.jsxs("div",{style:{flex:1,textAlign:"center"},children:[ce.jsx(ut,{paddingBlockEnd:"300",children:ce.jsx(Ye,{variant:"headingLg",children:"📄"})}),ce.jsx(Ye,{variant:"headingMd",children:"Airway Bills"}),ce.jsx(ut,{paddingBlockStart:"200",children:ce.jsx(Ye,{variant:"bodyMd",color:"subdued",children:"Automatically generate professional airway bills with QR codes"})})]}),ce.jsxs("div",{style:{flex:1,textAlign:"center"},children:[ce.jsx(ut,{paddingBlockEnd:"300",children:ce.jsx(Ye,{variant:"headingLg",children:"⚡"})}),ce.jsx(Ye,{variant:"headingMd",children:"Bulk Booking"}),ce.jsx(ut,{paddingBlockStart:"200",children:ce.jsx(Ye,{variant:"bodyMd",color:"subdued",children:"Book multiple orders at once and download all airway bills"})})]})]})})]})})})]})})}export{K5 as S,Lt as _};
