import{j as a}from"./jsx-runtime-DlxonYWr.js";import{R as r,r as s}from"./index-y-Yv8VR0.js";import{p as I,A as T}from"./styles-B33d-VxP.js";import{u as C,e as F,F as L}from"./components-7DQbiWt_.js";import{c as b,w as d,B as h,I as k,a as P,T as x,i as S,P as v,C as A,b as B,d as D}from"./Page-CD-2c-Tv.js";import"./context-QCCGjp8E.js";import"./context-C0av8SGa.js";var p={Item:"Polaris-FormLayout__Item",grouped:"Polaris-FormLayout--grouped",condensed:"Polaris-FormLayout--condensed"};function f({children:e,condensed:t=!1}){const o=b(p.Item,t?p.condensed:p.grouped);return e?r.createElement("div",{className:o},e):null}function y({children:e,condensed:t,title:o,helpText:n}){const l=s.useId();let c=null,i,u=null,m;n&&(i=`${l}HelpText`,c=r.createElement(P,{id:i,color:"text-secondary"},n)),o&&(m=`${l}Title`,u=r.createElement(x,{id:m,as:"p"},o));const E=s.Children.map(e,j=>d(j,f,{condensed:t}));return r.createElement(h,{role:"group",gap:"200","aria-labelledby":m,"aria-describedby":i},u,r.createElement(k,{gap:"300"},E),c)}const g=s.memo(function({children:t}){return r.createElement(h,{gap:"400"},s.Children.map(t,w))});g.Group=y;function w(e,t){return S(e,y)?e:d(e,f,{key:t})}const O=()=>[{rel:"stylesheet",href:I}];function W(){const e=C(),t=F(),[o,n]=s.useState(""),{errors:l}=t||e;return a.jsx(T,{i18n:e.polarisTranslations,children:a.jsx(v,{children:a.jsx(A,{children:a.jsx(L,{method:"post",children:a.jsxs(g,{children:[a.jsx(x,{variant:"headingMd",as:"h2",children:"Log in"}),a.jsx(B,{type:"text",name:"shop",label:"Shop domain",helpText:"example.myshopify.com",value:o,onChange:n,autoComplete:"on",error:l.shop}),a.jsx(D,{submit:!0,children:"Log in"})]})})})})})}export{W as default,O as links};
